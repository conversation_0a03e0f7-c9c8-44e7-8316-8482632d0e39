import PropTypes from 'prop-types';
// @mui
import { Box, Button, Chip, CircularProgress, InputAdornment, OutlinedInput, Paper, Stack, Toolbar, Typography } from '@mui/material';
import { CSVLink } from 'react-csv';
import { alpha, styled } from '@mui/material/styles';
import { get } from 'lodash';
// component
import ClearIcon from '@mui/icons-material/Clear';
import { useEffect, useRef, useState } from 'react';
import {  useDispatch, useSelector } from 'react-redux';
import { postExportCourse } from 'src/pages/Courses/coursesSlice';
import { setSnackbar } from 'src/Redux/snackbarSlice';
import Iconify from '../../../components/Iconify';

// ----------------------------------------------------------------------

const StyledRoot = styled(Toolbar)(({ theme }) => ({
  minHeight: 86,
  py: 4,
  display: 'flex',
  gap: 16,
  justifyContent: 'space-between',
  padding: theme.spacing(0, 1, 0, 3),
}));

export const StyledSearch = styled(OutlinedInput)(({ theme }) => ({
  width: 240,
  height: 46,
  transition: theme.transitions.create(['box-shadow', 'width'], {
    easing: theme.transitions.easing.easeInOut,
    duration: theme.transitions.duration.shorter,
  }),
  '&.Mui-focused': {
    width: 320,
    boxShadow: theme.customShadows.z8,
  },
  '& fieldset': {
    borderWidth: `1px !important`,
    borderColor: `${alpha(theme.palette.grey[500], 0.32)} !important`,
  },
}));

// ----------------------------------------------------------------------

UserListToolbar.propTypes = {
  numSelected: PropTypes.number,
  filterName: PropTypes.string,
  onFilterName: PropTypes.func,
};

export default function UserListToolbar({ importText, handleImportClick, importcourse, disableSearch, heading, setFilteredUsers, TableData, setFilterName, exportCourse,setUsers, Users, Colleges, selectedColleges, selectedGroups, setSelectedColleges, setSelectedGroups, handleFilter, numSelected, filterName, onFilterName, searchLable, buttonText, buttonHandler, filter }) {
  const dispatch = useDispatch();
  const csvDownloadRef = useRef(null);
  const [chipData, setChipData] = useState([])
  const [exportData, setExportData] = useState([]);
  const [exportLoading, setExportLoading] = useState(false);
  const {selectedCollege} = useSelector(state => state.selectedCollege)
  const clearUserFilter = () => {
    setSelectedColleges([]);
    setSelectedGroups([]);
  }
  useEffect(() => {
    setFilterName("")
  }, [selectedColleges, selectedGroups])

  useEffect(() => {
    if (selectedColleges && selectedGroups) {
      const groups = selectedGroups?.map((group, index) => {
        return {
          label: group,
          type: 'group'
        }
      })
      const colleges = selectedColleges?.map((college, index) => {
        return {
          label: college,
          type: 'college'
        }
      })
      setChipData([...groups, ...colleges])
    }
  }, [selectedGroups, selectedColleges])

  const exportCoursesFunc = async () => {
    setExportLoading(true);
    dispatch(postExportCourse({collegeId: selectedCollege._id})).then(res =>{
      if (res?.payload?.success) {
        setExportData(res?.payload?.data);
        const sucessMessage = get(res, 'payload.message', 'Successfully Exported course')
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'success',
          snackbarMessage: sucessMessage
        }))
        setExportLoading(false);
      } else {
        const errorMessage = get(res, 'payload.response.data.message', 'Something went wrong')
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'error',
          snackbarMessage: errorMessage
        }))
        setExportLoading(false);
      }
    })
  }

  useEffect(()=>{
    if(exportData?.length > 0){
      csvDownloadRef.current.link.click();
    }
  }, [exportData])

  const handleDelete = (chipToDelete) => () => {
    if (chipToDelete?.type === "group") {
      const relatedColleges = Colleges.filter(college => college.groupName === chipToDelete.label).map(colleges => colleges?.name)
      setSelectedColleges(selectedColleges.filter(college => !relatedColleges?.includes(college)))
      setSelectedGroups(selectedGroups.filter(group => group !== chipToDelete?.label))
    } else if (chipToDelete?.type === "college") {
      setSelectedColleges(selectedColleges.filter(college => college !== chipToDelete?.label))
    }
  };
  const ListItem = styled("li")(({ theme }) => ({
    margin: theme.spacing(0.5)
  }));

  return (
    <StyledRoot
      sx={{
        ...(numSelected > 0 && {
          color: 'primary.main',
          bgcolor: 'primary.lighter',
        }),
      }}
    >
      {numSelected > 0 ? (
        <Typography component="div" variant="subtitle1">
          {numSelected} selected
        </Typography>
      ) : (
        disableSearch ? null :
          (<StyledSearch
            value={filterName}
            onChange={onFilterName}
            placeholder={searchLable || "Search user..."}
            startAdornment={
              <InputAdornment position="start">
                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled', width: 20, height: 20 }} />
              </InputAdornment>
            }
          />)
      )}
      {
        heading && disableSearch ?
          (<Typography variant='h5'>
            {heading}
          </Typography>) :
          null
      }
      {/* 
      {numSelected > 0 ? (
        <Tooltip title="Delete">
          <IconButton>
            <Iconify icon="eva:trash-2-fill" />
          </IconButton>
        </Tooltip>
      ) : (
        <Tooltip title="Filter list">
          <IconButton>
            <Iconify icon="ic:round-filter-list" />
          </IconButton>
        </Tooltip>
      )} */}

      <Box flexGrow={1} />
      {(selectedColleges?.length > 0 || selectedGroups?.length > 0) ? (
        <Stack
          direction={"row"}
          gap={1}
        >
          <Paper
            sx={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'center',
              flexWrap: 'wrap',
              listStyle: 'none',
              p: 0.5,
              m: 0,
              maxWidth: 600,
              zIndex: 99,
              maxHeight: 100,
              overflowY: 'scroll',
              border: 'solid',
              borderWidth: `1px !important`,
              borderColor: (theme) => `${alpha(theme.palette.grey[500], 0.32)} !important`,
              '&::-webkit-scrollbar': {
                width: 0
              },
              '&::-webkit-scrollbar-track': {
                boxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)',
                webkitBoxShadow: 'inset 0 0 6px rgba(0,0,0,0.00)'
              },
              '&::-webkit-scrollbar-thumb': {
                backgroundColor: 'rgba(0,0,0,.1)',
                outline: '1px solid slategrey'
              }

            }}
            component="ul"
          >
            {chipData.map((data, index) => {

              return (
                <ListItem key={index}>
                  <Chip
                    size='small'
                    label={data.label}
                    onDelete={data.label === 'React' ? undefined : handleDelete(data)}
                  />
                </ListItem>
              );
            })}
          </Paper>
          <Button
            onClick={clearUserFilter}
            color='error'
            sx={{
              minWidth: 'fit-content'
            }}
          >
            <Iconify
              width={24}
              height={24}
              rotate={2}
              icon={'eva:close-circle-outline'} />
          </Button>
        </Stack>
      ) :
        null}
      {
        importcourse &&
        <Button
          sx={{ color: 'green' }}
          variant="outlined"
          color='inherit'
          startIcon={<Iconify icon="clarity:import-solid" />}
          onClick={importcourse}
          disabled={!Boolean(selectedCollege)}
        >
          Import Courses
        </Button>
      }
      {
        exportCourse && selectedCollege &&
        <>
          <CSVLink filename='courses.csv' data={exportData} target='_blank' ref={csvDownloadRef}/>
          <Button variant="contained" onClick={exportCoursesFunc} startIcon={!exportLoading?<Iconify icon='bxs:file-export'/>: <CircularProgress size={'20px'} sx={{color: '#fff !important'}} />}>
            Export
          </Button>
        </>
      }
      <Stack
        direction="row"
        gap={2}
      >
        {filter &&
          <Button
            color="primary"
            variant="outlined"
            startIcon={<Iconify icon="material-symbols:filter-list-rounded" />}
            onClick={() => {
              // setFilterName("")
              setFilteredUsers(TableData)
              handleFilter()
            }}
          >
            Filter
          </Button>
        }
        {handleImportClick &&
          <Button
            color="primary"
            variant="outlined"
            startIcon={<Iconify width="48" height="48" icon="stash:file-import-duotone" />}
            onClick={() => handleImportClick()}
          >
            {importText || 'Import'}
          </Button>
        }
        {buttonText &&
          <Button
            variant="contained"
            startIcon={<Iconify icon="eva:plus-fill" />}
            onClick={buttonHandler}
          >
            {buttonText} 
          </Button>}
      </Stack>
    </StyledRoot>
  );
}
