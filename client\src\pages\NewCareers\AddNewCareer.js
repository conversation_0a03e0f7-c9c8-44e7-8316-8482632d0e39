import { LoadingButton } from '@mui/lab';
import { Backdrop, Box, Button, Card, CircularProgress, Container, FormControl, Grid, IconButton, InputAdornment, MenuItem, Stack, TextField, Typography, createFilterOptions, debounce, Radio, RadioGroup, FormControlLabel, FormLabel, Checkbox, Chip } from '@mui/material';
import Paper from '@mui/material/Paper';
import Autocomplete from '@mui/material/Autocomplete';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import SearchIcon from '@mui/icons-material/Search';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/material/styles';
import { useFormik } from 'formik';
import { get } from 'lodash';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import Iconify from '../../components/Iconify/Iconify';
import SelectField from '../../components/SelectedField';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { APP_ROUTER_BASE_URL } from '../../utils';
import axiosInstance from '../../utils/axiosInstance';
import { careerValidation } from '../../utils/validationSchemas';
import { getSectors } from '../Sector/SectorSlice';
import { getSubSectors } from '../SubSector/SubSectorSlice';
import { getCareersByType, postCareer } from './newCareerSlice';
import { getRegions } from '../Regions/regionsSlice';
// import Card from '../theme/overrides/Card'


export const SKILLS_TABLE_HEAD = [
    { id: 'name', label: 'Name', alignRight: false },
    { id: 'value', label: 'Value', alignRight: false, },
];

export const skillsRenderCells = ['name', 'value']

export const ABILITIES_TABLE_HEAD = [
    { id: 'name', label: 'Name', alignRight: false },
    { id: 'value', label: 'Value', alignRight: false, },
];

const BROAD_CAREER_OPTIONS = ['IT Technology and Support'];
const CAREER_OPTIONS = ['Software Developer'];
const SPECIALIZATION_OPTIONS = ['Frontend Dev', 'Backend Dev', 'DevOps', 'Game Dev'];

const careerTypes = {
    BROAD_CAREER: "broad_career",
    CAREER: "unit_group",
    SPECIALISED_ROLE: "specialised_role",
};

export const abbilitiesRenderCells = ['name', 'value']

const CreateNewCareer = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const [socOptions, setSocOptions] = useState([])
    const [loading, setLoading] = useState(false)
    const [isTitleLoading, setTitleLoading] = useState(false)
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [Sectors, setSectors] = useState([
        {
            label: '',
            _id: '',
            subSectors: []
        }
    ])
    const [SubSectors, setSubSectors] = useState([])
    const [manualSoc, setManualSoc] = useState('')
    const sectors = useSelector(state => state.sectors.sectors)
    const subSectors = useSelector(state => state.subSectors.subSectors)
    const [careerType, setCareerType] = useState('broad'); // 'broad_career', 'career', 'specialised_role'
    const [broadCareer, setBroadCareer] = useState('');
    const [career, setCareer] = useState([]); // <-- new state for career (unit group)
    const [specializations, setSpecializations] = useState([]);

    useEffect(() => {
        dispatch(getSectors())
        dispatch(getSubSectors())
    }, [])
    useEffect(() => {
        const sectorOptions = sectors?.length ? sectors?.map(sector => ({
            label: sector.name,
            _id: sector._id,
        })) : []
        const subSectorOptions = subSectors?.length ? subSectors?.map(subSector => ({
            label: subSector.name,
            _id: subSector._id,
            sectorId: subSector.sectorId
        })) : []

        setSectors(sectorOptions)
        setSubSectors(subSectorOptions)
    }, [sectors, subSectors])

    const filter = createFilterOptions();
    const regionsState = useSelector((state) => state.regions);
    const { regions, status, error } = regionsState;
    const initialValues = useMemo(() => ({
        // id: '',
        title: '',
        jobZone: '',
        skills: [],
        abilities: [],
        onetCode: "",
        socCode: "",
        tasks: "",
        estimateHours: "",
        estimatePay: "",
        description: "",
        abilitiesExist: '',
        estimateHoursYear: '',
        videoUrl: '',
        hoursMean: '',
        hoursMedian: '',
        salaryMean: '',
        salaryMedian: '',
        estimatePayYear: '',
        predictByQualification: [],
        predictByQualificationYear: '',
        predictByRegion: [],
        predictByRegionYear: '',
        predictByWorktype: [],
        predictByWorktypeYear: '',
        predictByYear: [],
        skillsExist: '',
        sectors: [],
        onetCodeForSkillAbility: '',
        metadata: regions && regions.length > 0 ? regions.map(region => ({
            regionId: region._id,
            hoursMean: '',
            hoursMedian: '',
            salaryMean: '',
            salaryMedian: ''
        })) : []
    }), [regions])
    
    const formik = useFormik({
        initialValues,
         enableReinitialize: true,
        validationSchema: careerValidation,
        onSubmit: (values) => {
            if (careerType === careerTypes.BROAD_CAREER) {
                if (!broadCareer) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: "Please select a Unit Group (parent) for Broad Career."
                    }));
                    setIsSubmitting(false);
                    return;
                }

            }

            const careerPayload = {
                socCode: values?.socCode?.soc || values?.socCode,
                ...values,
                type: careerType,
                // broadCareerId: careerType === careerTypes.CAREER ? broadCareer._id : undefined,
                // broadCareerId: careerType === careerTypes.SPECIALISED_ROLE ? career._id : undefined,
                broadCareerIds: careerType === careerTypes.SPECIALISED_ROLE && career?.length > 0 ? career?.map(career => career?._id) : [],
                specialisedRoleIds: careerType === careerTypes.BROAD_CAREER ? specializations._id : [],
                // parentCareerId: careerType === careerTypes.SPECIALISED_ROLE ? career._id : undefined,
                unitGroupId: careerType === careerTypes.BROAD_CAREER ? broadCareer._id : undefined,
                metadata: values?.metadata?.map((meta) => ({...meta, socCode: values?.socCode?.soc || values?.socCode}))
            };
            if (careerType === careerTypes.BROAD_CAREER && specializations.length > 0) {
                careerPayload.specialisedRoleIds = specializations.map(s => s._id);
            }
            setIsSubmitting(true);
            dispatch(postCareer(careerPayload)).then((res) => {
                if (res.payload.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: "Career Added Successfully"
                    }));
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/careers`);
                } else {
                    const errorMessage = get(res, 'payload.data.msg', 'Something Went Wrong');
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }));
                }
            }).finally(() => setIsSubmitting(false));
        },
    })
  useEffect(() => {
    dispatch(getRegions(true));
  }, []);
    const getSocDetails = async (socCode) => {
        if (socCode.soc !== '' && formik.values.onetCode !== '') {
            setLoading(true)
            try {
                const response = await axiosInstance({
                    url: "careers/getCareerDetails",
                    method: "GET",
                    params: {
                        onetCode: formik.values.onetCode,
                        socCode: socCode?.soc
                    }
                })
                formik.setValues({
                    ...formik.values,
                    description: get(response, 'data.description', ''),
                    tasks: get(response, 'data.tasks', ''),
                    estimateHours: get(response, 'data.estimateHours', ''),
                    estimatePay: get(response, 'data.estimatePay', ''),
                    jobZone: get(response, 'data.jobZone', ''),
                    skills: get(response, 'data.skills', []),
                    abilities: get(response, 'data.abilities', []),
                    abilitiesExist: get(response, 'data.abilitiesExist', ''),
                    estimateHoursYear: get(response, 'data.estimateHoursYear', ''),
                    salaryMean: get(response, 'data.salaryMean', ''),
                    salaryMedian: get(response, 'data.salaryMedian', ''),
                    hoursMean: get(response, 'data.hoursMean', ''),
                    videoUrl: get(response, 'data.videoUrl', ''),
                    hoursMedian: get(response, 'data.hoursMedian', ''),
                    estimatePayYear: get(response, 'data.estimatePayYear', ''),
                    predictByQualification: get(response, 'data.predictByQualification', []),
                    predictByQualificationYear: get(response, 'data.predictByQualificationYear', ''),
                    predictByRegion: get(response, 'data.predictByRegion', []),
                    predictByRegionYear: get(response, 'data.predictByRegionYear', ''),
                    predictByWorktype: get(response, 'data.predictByWorktype', []),
                    predictByWorktypeYear: get(response, 'data.predictByWorktypeYear', ''),
                    predictByYear: get(response, 'data.predictByYear', []),
                    skillsExist: get(response, 'data.skillsExist', ''),
                    socCode: socCode?.soc

                })
            } catch (error) {
                formik.setValues({
                    ...formik.values,
                    description: "",
                    tasks: "",
                    estimateHours: "",
                    salaryMean: "",
                    salaryMedian: "",
                    hoursMean: "",
                    videoUrl: "",
                    hoursMedian: "",
                    estimatePay: "",
                    jobZone: "",
                    skills: [],
                    abilities: [],
                    abilitiesExist: "",
                    estimateHoursYear: "",
                    estimatePayYear: "",
                    predictByQualification: [],
                    predictByQualificationYear: "",
                    predictByRegion: [],
                    predictByRegionYear: "",
                    predictByWorktype: [],
                    predictByWorktypeYear: "",
                    predictByYear: [],
                    skillsExist: "",
                    socCode: ""
                })
                if (manualSoc) {
                    setManualSoc("")
                }
            } finally {
                setLoading(false)
            }
        }
    }

    const style = {
        p: 4,
    };

    const [value, setValue] = useState(null);
    const [inputValue, setInputValue] = useState('');
    const [inputReason, setInputReason] = useState();
    const [options, setOptions] = useState([]);
    const [currentSector, setCurrentSector] = useState(null);
    const [selectedSectors, setSelectedSectors] = useState([]);
    const [filteredSectors, setFilteredSectors] = useState([]);
    const [editOnetToggled, setEditOnetToggled] = useState(false);
    const [broadCareerOptions, setBroadCareerOptions] = useState([]);
    const [careerOptions, setCareerOptions] = useState([]);
    const [specializedRoleOptions, setSpecializedRoleOptions] = useState([]);


    useEffect(() => {
        dispatch(getCareersByType(careerTypes.CAREER)).then(res => {
            if (res.payload && Array.isArray(res.payload.data)) {
                setBroadCareerOptions(res.payload.data);
            }
        });
        dispatch(getCareersByType(careerTypes.BROAD_CAREER)).then(res => {
            if (res.payload && Array.isArray(res.payload.data)) {
                setCareerOptions(res.payload.data);
            }
        });
        dispatch(getCareersByType(careerTypes.SPECIALISED_ROLE)).then(res => {
            if (res.payload && Array.isArray(res.payload.data)) {
                setSpecializedRoleOptions(res.payload.data);
            }
        });
    }, [careerType]);
    const handleCareerTypeChange = (e) => {
        const selectedType = e.target.value;
        setCareerType(selectedType);
        setBroadCareer('');
        setCareer([]);
        setSpecializations([]);

        // Reset options
        setBroadCareerOptions([]);
        setCareerOptions([]);
        setSpecializedRoleOptions([]);
    };

    useEffect(() => {
        formik.setValues({
            ...formik.values,
            sectors: selectedSectors
        })
    }, [selectedSectors])

    const fetch = useMemo(
        () =>
            debounce(async (request, callback) => {
                setTitleLoading(true)
                try {
                    const response = await axiosInstance({
                        url: "careers/getCareersByKeyword",
                        method: "GET",
                        params: {
                            keyword: request.input
                        }
                    })
                    if (response.data) {
                        callback(response.data)
                    }
                }
                catch (error) {
                    console.log("Error ==>", error)
                } finally {
                    setTitleLoading(false)
                }
                // fetch and send results in callback
                // callback(data)
                // autocompleteService.current.getPlacePredictions(request, callback);
            }, 600),
        [],
    );

    useEffect(() => {
        const getOnetDetails = async () => {

            if (formik.values.onetCode !== '') {
                setLoading(true)
                try {
                    const response = await axiosInstance({
                        url: "careers/getSocCodes",
                        method: "GET",
                        params: {
                            onetCode: formik.values.onetCode
                        }
                    })
                    setSocOptions(get(response, 'data[0].socCodes', []))
                } catch (error) {
                    console.log("error ==> ", error)
                } finally {
                    setLoading(false)
                }
            }
        }
        getOnetDetails()
    }, [formik.values.onetCode])

    useEffect(() => {
        // console.log('runnning soc change', formik.values.socCode)
        // const getOnetDetails = async () => {
        //     if (formik.values.socCode !== '' && formik.values.onetCode !== '') {
        //         setLoading(true)
        //         try {
        //             const response = await axiosInstance({
        //                 url: "careers/getCareerDetails",
        //                 method: "GET",
        //                 params: {
        //                     onetCode: formik.values.onetCode,
        //                     socCode: formik.values.socCode
        //                 }
        //             })
        //             formik.setValues({
        //                 ...formik.values,
        //                 description: get(response, 'data.description', ''),
        //                 tasks: get(response, 'data.tasks', ''),
        //                 estimateHours: get(response, 'data.estimateHours', ''),
        //                 estimatePay: get(response, 'data.estimatePay', ''),
        //                 jobZone: get(response, 'data.jobZone', ''),
        //                 skills: get(response, 'data.skills', []),
        //                 abilities: get(response, 'data.abilities', []),
        //                 abilitiesExist: get(response, 'data.abilitiesExist', ''),
        //                 estimateHoursYear: get(response, 'data.estimateHoursYear', ''),
        //                 estimatePayYear: get(response, 'data.estimatePayYear', ''),
        //                 predictByQualification: get(response, 'data.predictByQualification', []),
        //                 predictByQualificationYear: get(response, 'data.predictByQualificationYear', ''),
        //                 predictByRegion: get(response, 'data.predictByRegion', []),
        //                 predictByRegionYear: get(response, 'data.predictByRegionYear', ''),
        //                 predictByWorktype: get(response, 'data.predictByWorktype', []),
        //                 predictByWorktypeYear: get(response, 'data.predictByWorktypeYear', ''),
        //                 predictByYear: get(response, 'data.predictByYear', []),
        //                 skillsExist: get(response, 'data.skillsExist', ''),

        //             })
        //         } catch (error) {
        //             console.log("error ==> ", error)
        //         } finally {
        //             setLoading(false)
        //         }
        //     }
        // }
        // getOnetDetails()
        setEditOnetToggled(false)
    }, [formik.values.socCode])

    useEffect(() => {
        const selectedSectorsIds = selectedSectors.map(sector => sector?._id)
        setFilteredSectors(!!selectedSectors.length ? Sectors.filter(sector => !selectedSectorsIds.includes(sector?._id)) : Sectors)
    }, [selectedSectors, Sectors])

    useEffect(() => {
        let active = true;

        if (inputValue === '') {
            setOptions(value ? [value] : []);
            return undefined;
        }

        if (inputReason === 'input') {
            fetch({ input: inputValue }, (results) => {
                if (active) {
                    let newOptions = [];

                    if (value) {
                        newOptions = [value];
                    }

                    if (results) {
                        newOptions = [...newOptions, ...results];
                    }

                    setOptions(newOptions);
                }
            });
        }


        return () => {
            active = false;
        };
    }, [value, inputValue, fetch]);
    useEffect(() => {
        formik.setValues({
            ...formik.values,
            title: get(value, 'title', ""),
            onetCode: get(value, 'code', ""),
            onetCodeForSkillAbility: get(value, 'code', ""),
            // careerDescription: value?.description || "",
            // salary: value?.salary || "",
            // onetCode: value?.onetCode || "",
            // hoursPerWeek: value?.hoursPerWeek || "",
        })
    }, [value, options])

    const handleOptionChange = (event, newValue, reason) => {
        setOptions(newValue ? [newValue, ...options] : options);
        setValue(newValue);
        setSelectedSectors([])
        formik.resetForm()
        setSocOptions([])
        setEditOnetToggled(false)
        setManualSoc('')
        // formik.setValues({
        //     ...formik.values,
        //     socCode: '',
        //     tasks: '',
        //     description: '',
        //     title: '',
        //     estimateHours: '',
        //     estimatePay: '',
        //     jobZone: '',
        //     skills: '',
        //     abilities: '',
        // })
    }

    const StyledTableCell = styled(TableCell)(({ theme }) => ({
        [`&.${tableCellClasses.head}`]: {
            //   backgroundColor: theme.palette.common.black,
            backgroundColor: theme.palette.common.primary,
            color: theme.palette.common.black,
        },
        [`&.${tableCellClasses.body}`]: {
            fontSize: 14,
        },
    }));

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        // '&:nth-of-type(odd)': {
        //   backgroundColor: theme.palette.action.hover,
        // },

        // hide last border
        '&:last-child td, &:last-child th': {
            border: 0,
        },
    }));
    const addRow = () => {
        if (currentSector) {
            setSelectedSectors([...selectedSectors, {
                ...currentSector,
                subsectors: SubSectors.filter(subSector => subSector.sectorId === currentSector._id)
            }])
            setCurrentSector('')

            // defaultValue={SubSectors.filter(subSector => subSector.sectorId === row._id)}
        } else {
            // alert("else")
        }
    }
    const handleDeleteRow = (row) => {
        setSelectedSectors(selectedSectors.filter(sectors => sectors._id !== row._id))
    }
    const handleManualSoc = () => {
        if (manualSoc) {
            formik.setValues({
                ...formik.values,
                socCode: manualSoc
            })
            getSocDetails(manualSoc)
        }
    }
    const handleSocChange = (event) => {
        if (event.target.value) {
            formik.setValues({
                ...formik.values,
                socCode: event.target.value
            })
            getSocDetails(event.target.value)
        }
    }
    const changeSkillTables = async () => {
        if (formik.values?.onetCodeForSkillAbility) {
            try {
                setLoading(true)
                const response = await axiosInstance({
                    url: "careers/getSkillsAbilities",
                    method: "GET",
                    params: {
                        onetCode: formik.values.onetCodeForSkillAbility
                    }
                })
                // console.log("response",response)
                // console.log("g",group.data)
                const skillAbilities = response.data;
                if (response.data.abilities && response.data.skills) {
                    formik.setValues({
                        ...formik.values,
                        skills: skillAbilities.skills,
                        abilities: skillAbilities.abilities,
                    })
                    setEditOnetToggled(!editOnetToggled)
                }
                // formik.setValues({
                //     ...formik.values,
                //     Name: groupDetails?.name,
                //     groupAddress: groupDetails?.address1,
                //     groupTelNumber: groupDetails?.contactNumber,
                //     groupWebsiteAddress: groupDetails?.website,
                //     groupEmail: groupDetails?.email,
                //     groupCity: groupDetails?.city,
                //     groupState: groupDetails?.state,
                //     groupZip: groupDetails?.zip,
                //     admin: groupDetails?.adminUserId,
                //     id: groupDetails?._id
                // })
            } catch (error) {
                console.log('error => ', error);
                const errorMessage = get(error, 'response.data.message', 'Something went wrong')
            } finally {
                setLoading(false)
            }
        }
    }
    const safeCareerOptions = Array.isArray(careerOptions) ? careerOptions : [];
    return (
        <>
            <Helmet>
                <title> Create Careers | ThinkSkill </title>
            </Helmet>
            <Backdrop
                sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 99999 }}
                open={loading}
            // onClick={handleOpenBackdrop}
            >
                <CircularProgress color="inherit" />
            </Backdrop>
            <Container maxWidth="xl">
                <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                    Add Career
                </Typography>
                <Card>
                    <Box sx={style}>
                        <form onSubmit={formik.handleSubmit}>
                            <Grid container gap={2} rowGap={3} >
                                <Grid item xs={12} md={11.8}>
                                    {/* <OutlinedInput
                                        name='searchTitle'
                                        sx={{ width: '100%' }}
                                        value={formik.values.searchTitle}
                                        onChange={formik.handleChange}
                                        placeholder={"Search Title"}
                                        onBlur={formik.handleBlur}
                                        error={formik.touched.searchTitle && Boolean(formik.errors.searchTitle)}
                                        helperText={formik.touched.searchTitle && formik.errors.searchTitle}
                                        startAdornment={
                                            <InputAdornment position="start">
                                                <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled', width: 20, height: 20 }} />
                                            </InputAdornment>
                                        }
                                        endAdornment={
                                            <Button
                                                variant='contained'
                                                color='info'
                                            >
                                                Search
                                            </Button>
                                        }
                                    /> */}

                                    <Autocomplete
                                        sx={{ width: '100%' }}
                                        getOptionLabel={(option) =>
                                            typeof option === 'string' ? option : option?.title
                                        }
                                        filterOptions={(x) => x}
                                        options={options}
                                        autoComplete
                                        includeInputInList
                                        filterSelectedOptions
                                        value={value}
                                        noOptionsText="No Careers"
                                        onChange={handleOptionChange}
                                        onInputChange={(event, newInputValue, reason) => {
                                            setInputReason(reason)
                                            setInputValue(newInputValue);
                                        }}
                                        renderInput={(params) => (
                                            <TextField
                                                {...params}
                                                placeholder='Start searching here'
                                                label="Search Title"
                                                fullWidth
                                                InputProps={{
                                                    ...params.InputProps,
                                                    endAdornment: (
                                                        <>
                                                            {isTitleLoading ? <CircularProgress color="inherit" size={20} /> : null}
                                                            {params.InputProps.endAdornment}
                                                        </>
                                                    ),
                                                }}
                                            />
                                        )}



                                        renderOption={(props, option) => {
                                            return (
                                                <li {...props} key={option?.code}>
                                                    <Grid container alignItems="center">
                                                        <Grid item sx={{ display: 'flex', width: 44 }}>
                                                            {/* <LocationOnIcon sx={{ color: 'text.secondary' }} /> */}
                                                        </Grid>
                                                        <Grid item sx={{ width: 'calc(100% - 44px)', wordWrap: 'break-word' }}>
                                                            {/* {parts.map((part, index) => ( */}
                                                            <Box
                                                                // key={index}
                                                                component="span"
                                                            // sx={{ fontWeight: part.highlight ? 'bold' : 'regular' }}
                                                            >
                                                                {option.title}
                                                            </Box>
                                                            {/* ))} */}

                                                            <Typography variant="body2">
                                                                <b>Onet Code: </b>{option?.code}
                                                            </Typography>
                                                        </Grid>
                                                    </Grid>
                                                </li>
                                            );
                                        }}
                                    />
                                </Grid>
                                <Grid item xs={12} md={11.8} lg={3.81}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='title'
                                        label="Title"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.title}
                                        onChange={formik.handleChange}
                                        error={formik.touched.title && Boolean(formik.errors.title)}
                                        helperText={formik.touched.title && formik.errors.title}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.81}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        disabled
                                        name='onetCode'
                                        label="O-Net Code"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.onetCode}
                                        onChange={formik.handleChange}
                                        error={formik.touched.onetCode && Boolean(formik.errors.onetCode)}
                                        helperText={formik.touched.onetCode && formik.errors.onetCode}
                                    />
                                </Grid>
                                {/* <Grid item xs={12} md={3.81}>

                                    {(!formik.values.onetCode || !!socOptions.length) ?
                                        <SelectField
                                            name="socCode"
                                            label="SOC Code"
                                            MenuProps={{
                                                autoFocus: false,
                                                disableAutoFocusItem: true,
                                                disableEnforceFocus: true,
                                                disableAutoFocus: true
                                            }}
                                            // onChange={formik.handleChange}
                                            onChange={handleSocChange}
                                            value={formik.values.socCode}
                                            error={formik.touched.socCode && Boolean(formik.errors.socCode)}
                                            helperText={formik.touched.socCode && formik.errors.socCode}
                                            onBlur={formik.handleBlur}
                                        >
                                            <MenuItem value="">
                                                <em>None</em>
                                            </MenuItem>
                                            {!!socOptions && socOptions.map((option, index) =>
                                            (<MenuItem
                                                key={index}
                                                sx={{ pr: '2', minWidth: 'min-content', whiteSpace: 'normal' }}
                                                value={option?.soc}
                                            >
                                                {`${option?.soc} (${option?.title})`}
                                            </MenuItem>)
                                            )}
                                        </SelectField> :
                                        <TextFIeldComponent
                                            name="socCode"
                                            label="SOC Code"
                                            placeholder="Enter Manually"
                                            onChange={(event) => setManualSoc(event.target.value)}
                                            value={manualSoc}
                                            sx={{ width: '100%' }}
                                            // onBlur={formik.handleBlur}
                                            error={formik.touched.socCode && Boolean(formik.errors.socCode)}
                                            helperText={formik.touched.socCode && formik.errors.socCode}
                                            InputProps={{
                                                endAdornment: (
                                                    <InputAdornment position="end">
                                                        <IconButton
                                                            onClick={handleManualSoc}
                                                            edge="end"
                                                            color="primary"
                                                        >
                                                            <SearchIcon />
                                                        </IconButton>
                                                    </InputAdornment>
                                                ),
                                            }}
                                        />2221
                                    }
                                </Grid> */}
                                <Grid item xs={12} md={3.81}>
                                    <Autocomplete
                                        name="socCode"
                                        value={formik.values.socCode}
                                        onChange={(event, newValue) => {
                                            if (typeof newValue === 'string') {
                                                formik.setValues({
                                                    ...formik.values,
                                                    socCode: newValue
                                                })
                                            } else if (newValue && newValue.inputValue) {
                                                // Create a new value from the user input
                                                formik.setValues({
                                                    ...formik.values,
                                                    socCode: newValue.inputValue
                                                })
                                            } else {
                                                formik.setValues({
                                                    ...formik.values,
                                                    socCode: newValue.soc
                                                }).then(() => {
                                                    getSocDetails(newValue)
                                                })
                                            }
                                        }}
                                        filterOptions={(options, params) => {
                                            const filtered = filter(options, params);
                                            const { inputValue } = params;
                                            // Suggest the creation of a new value
                                            const isExisting = options.some((option) => inputValue === option.title);
                                            if (inputValue !== '' && !isExisting) {
                                                filtered.push({
                                                    helperText: "Add",
                                                    title: "",
                                                    soc: inputValue,
                                                });
                                            }

                                            return filtered;
                                        }}
                                        selectOnFocus
                                        clearOnBlur
                                        handleHomeEndKeys
                                        id="SOC Code"
                                        options={socOptions}
                                        getOptionLabel={(option) => {
                                            // Value selected with enter, right from the input
                                            if (typeof option === 'number') {
                                                return option;
                                            }
                                            if (typeof option === 'string') {
                                                return option;
                                            }
                                            // Add "xxx" option created dynamically
                                            if (option.inputValue) {
                                                return option.inputValue;
                                            }
                                            // Regular option

                                            return option.title ? `${option.soc} (${option?.title})` : `${option.soc}`;
                                        }}
                                        // renderOption={(props, option) => <li {...props}>{option?.title ? `${option?.soc} (${option?.title})` : `${option?.helperText && option?.helperText} "${option?.soc}"`}</li>}
                                        renderOption={(props, option) => <li {...props}>{typeof option === 'string' ? option :
                                            // option.title ?
                                            <span><b style={{ fontWeight: 600 }}>{option.soc}</b> {option.title ? `(${option.title})` : ''}</span>}</li>}
                                        // sx={{ width: 300 }}
                                        freeSolo
                                        renderInput={(params) => (
                                            <TextField
                                                error={formik.touched.socCode && Boolean(formik.errors.socCode)}
                                                helperText={formik.touched.socCode && formik.errors.socCode}
                                                onBlur={formik.handleBlur}
                                                {...params}
                                                label="SOC code" />
                                        )}
                                    />
                                </Grid>

                                <Grid item xs={12} md={5.85}>
                                    <TextFIeldComponent
                                        multiline
                                        rows={12}
                                        placeholder="Description"
                                        sx={{ width: '100%' }}
                                        name='description'
                                        label="Description"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.description}
                                        onChange={formik.handleChange}
                                        error={formik.touched.description && Boolean(formik.errors.description)}
                                        helperText={formik.touched.description && formik.errors.description}
                                    />
                                </Grid>

                                <Grid item xs={12} md={5.85}>
                                    <TextFIeldComponent
                                        multiline
                                        rows={12}
                                        placeholder="Tasks"
                                        sx={{ width: '100%' }}
                                        name='tasks'
                                        label="Tasks"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.tasks}
                                        onChange={formik.handleChange}
                                        error={formik.touched.tasks && Boolean(formik.errors.tasks)}
                                        helperText={formik.touched.tasks && formik.errors.tasks}
                                    />
                                </Grid>

                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='videoUrl'
                                        label="Video Url"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.videoUrl}
                                        onChange={formik.handleChange}
                                        error={formik.touched.videoUrl && Boolean(formik.errors.videoUrl)}
                                        helperText={formik.touched.videoUrl && formik.errors.videoUrl}
                                    />
                                </Grid>
                                {/* <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='estimateHours'
                                        label="Hours / Week"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.estimateHours}
                                        onChange={formik.handleChange}
                                        error={formik.touched.estimateHours && Boolean(formik.errors.estimateHours)}
                                        helperText={formik.touched.estimateHours && formik.errors.estimateHours}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='hoursMean'
                                        label="Mean Hours / Week"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.hoursMean}
                                        onChange={formik.handleChange}
                                        error={formik.touched.hoursMean && Boolean(formik.errors.hoursMean)}
                                        helperText={formik.touched.hoursMean && formik.errors.hoursMean}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='hoursMedian'
                                        label="Median Hours / Week"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.hoursMedian}
                                        onChange={formik.handleChange}
                                        error={formik.touched.hoursMedian && Boolean(formik.errors.hoursMedian)}
                                        helperText={formik.touched.hoursMedian && formik.errors.hoursMedian}
                                    />
                                </Grid> */}
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='jobZone'
                                        label="Job Zone"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.jobZone.title}
                                        onChange={formik.handleChange}
                                        error={formik.touched.jobZone && Boolean(formik.errors.jobZone)}
                                        helperText={formik.touched.jobZone && formik.errors.jobZone}
                                    />
                                </Grid>
                                {/* <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='estimatePay'
                                        label="Salary"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.estimatePay}
                                        onChange={formik.handleChange}
                                        error={formik.touched.estimatePay && Boolean(formik.errors.estimatePay)}
                                        helperText={formik.touched.estimatePay && formik.errors.estimatePay}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='salaryMean'
                                        label="Mean Salary"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.salaryMean}
                                        onChange={formik.handleChange}
                                        error={formik.touched.salaryMean && Boolean(formik.errors.salaryMean)}
                                        helperText={formik.touched.salaryMean && formik.errors.salaryMean}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='salaryMedian'
                                        label="Mean Salary"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.salaryMedian}
                                        onChange={formik.handleChange}
                                        error={formik.touched.salaryMedian && Boolean(formik.errors.salaryMedian)}
                                        helperText={formik.touched.salaryMedian && formik.errors.salaryMedian}
                                    />
                                </Grid> */}
                                {formik.values?.metadata?.map((regionMeta, index) => {
                                    const region = regions.find(r => r._id === regionMeta.regionId);

                                    return (
                                        <Box key={regionMeta.regionId} sx={{ mb: 1 }}>
                                            <Typography variant="h6" sx={{ mb: 2 }}>
                                                {region?.name || "Region"}
                                            </Typography>

                                            <Grid container spacing={2}>
                                                <Grid item xs={12} md={3.85}>
                                                    <TextFIeldComponent
                                                        type="number"
                                                        name={`metadata[${index}].hoursMean`}
                                                        label="Mean Hours / Week"
                                                        value={formik.values?.metadata[index].hoursMean}
                                                        onChange={formik.handleChange}
                                                        onBlur={formik.handleBlur}
                                                        error={Boolean(formik.touched?.metadata?.[index]?.hoursMean && formik.errors?.metadata?.[index]?.hoursMean)}
                                                        helperText={formik.touched?.metadata?.[index]?.hoursMean && formik.errors?.metadata?.[index]?.hoursMean}
                                                    />
                                                </Grid>

                                                <Grid item xs={12} md={3.85}>
                                                    <TextFIeldComponent
                                                        type="number"
                                                        name={`metadata[${index}].hoursMedian`}
                                                        label="Median Hours / Week"
                                                        value={formik.values?.metadata[index].hoursMedian}
                                                        onChange={formik.handleChange}
                                                        onBlur={formik.handleBlur}
                                                        error={Boolean(formik.touched?.metadata?.[index]?.hoursMedian && formik.errors?.metadata?.[index]?.hoursMedian)}
                                                        helperText={formik.touched?.metadata?.[index]?.hoursMedian && formik.errors?.metadata?.[index]?.hoursMedian}
                                                    />
                                                </Grid>

                                                <Grid item xs={12} md={3.85}>
                                                    <TextFIeldComponent
                                                        type="number"
                                                        name={`metadata[${index}].salaryMean`}
                                                        label="Mean Salary"
                                                        value={formik.values?.metadata[index].salaryMean}
                                                        onChange={formik.handleChange}
                                                        onBlur={formik.handleBlur}
                                                        error={Boolean(formik.touched?.metadata?.[index]?.salaryMean && formik.errors?.metadata?.[index]?.salaryMean)}
                                                        helperText={formik.touched?.metadata?.[index]?.salaryMean && formik.errors?.metadata?.[index]?.salaryMean}
                                                    />
                                                </Grid>

                                                <Grid item xs={12} md={3.85}>
                                                    <TextFIeldComponent
                                                        type="number"
                                                        name={`metadata[${index}].salaryMedian`}
                                                        label="Median Salary"
                                                        value={formik.values?.metadata[index].salaryMedian}
                                                        onChange={formik.handleChange}
                                                        onBlur={formik.handleBlur}
                                                        error={Boolean(formik.touched?.metadata?.[index]?.salaryMedian && formik.errors?.metadata?.[index]?.salaryMedian)}
                                                        helperText={formik.touched?.metadata?.[index]?.salaryMedian && formik.errors?.metadata?.[index]?.salaryMedian}
                                                    />
                                                </Grid>
                                            </Grid>
                                        </Box>
                                    );
                                })}

                                {formik.values.socCode && <Grid xs={12} >
                                    <Grid container gap={2} alignItems={'center'}>
                                        <Grid md={4} xs={12}>
                                            <Stack gap={2} my={4} direction={"row"}>
                                                <TextFIeldComponent
                                                    sx={{ width: '100%' }}
                                                    name='onetCodeForSkillAbility'
                                                    label="Onet For Skills & Abilities"
                                                    disabled={!Boolean(editOnetToggled)}
                                                    onBlur={formik.handleBlur}
                                                    value={formik.values.onetCodeForSkillAbility}
                                                    onChange={formik.handleChange}
                                                    error={formik.touched.onetCodeForSkillAbility && Boolean(formik.errors.onetCodeForSkillAbility)}
                                                    helperText={formik.touched.onetCodeForSkillAbility && formik.errors.onetCodeForSkillAbility}
                                                />
                                                {/* {!editOnetToggled && <Button
                                                onClick={() => setEditOnetToggled(!editOnetToggled)}
                                            >
                                                Edit
                                            </Button>} */}
                                            </Stack>
                                        </Grid>
                                        <Grid md={4} xs={12}>
                                            {editOnetToggled ? <Stack
                                                direction={'row'}
                                                gap={2}
                                            >
                                                <Button
                                                    onClick={() => setEditOnetToggled(!editOnetToggled)}
                                                    color='error'
                                                    variant='contained'
                                                >
                                                    Cancel
                                                </Button>
                                                <Button
                                                    variant='contained'
                                                    onClick={changeSkillTables}
                                                >
                                                    Change
                                                </Button>
                                            </Stack> :
                                                <Button
                                                    onClick={() => setEditOnetToggled(!editOnetToggled)}
                                                >
                                                    <Iconify icon={'ooui:edit'} sx={{ mr: 1 }} />
                                                </Button>
                                            }
                                        </Grid>
                                    </Grid>
                                </Grid>}

                                {
                                    // !!formik.values.skills.length &&
                                    formik.values.socCode &&
                                    <Grid item xs={5.84}>
                                        <DataTable
                                            // loading={dataLoading}
                                            TableHead={SKILLS_TABLE_HEAD}
                                            TableData={formik.values.skills}
                                            disableActions={"true"}
                                            disableSearch={"true"}
                                            heading={"Skills"}
                                            // filterSearch={handleFilterSearch}
                                            // searchLable={"Search..."}
                                            // handleEdit={editSector}
                                            renderCells={skillsRenderCells}
                                            // handleDelete={handleDeleteSector}
                                            pagination
                                        />
                                    </Grid>}
                                {
                                    // !!formik.values.abilities.length &&
                                    formik.values.socCode &&
                                    <Grid item xs={5.84}>
                                        <DataTable
                                            // loading={dataLoading}
                                            TableHead={ABILITIES_TABLE_HEAD}
                                            TableData={formik.values.abilities}
                                            disableActions={"true"}
                                            heading={"Abilities"}
                                            disableSearch={"true"}
                                            // filterSearch={handleFilterSearch}
                                            // searchLable={"Search..."}
                                            // handleEdit={editSector}
                                            renderCells={abbilitiesRenderCells}
                                            // handleDelete={handleDeleteSector}
                                            pagination
                                        />
                                    </Grid>}

                                <Grid xs={12}>

                                    {!selectedSectors.length ? null :
                                        (<TableContainer component={Paper}>
                                            <Table sx={{ minWidth: 700 }}
                                                MenuProps={{
                                                    autoFocus: false,
                                                    disableAutoFocusItem: true,
                                                    disableEnforceFocus: true,
                                                    disableAutoFocus: true
                                                }}
                                                aria-label="customized table"
                                            >
                                                <TableHead>
                                                    <TableRow>
                                                        <StyledTableCell>Sector</StyledTableCell>
                                                        <StyledTableCell align="left">Sub-Sectors</StyledTableCell>
                                                        <StyledTableCell align="right">Remove</StyledTableCell>
                                                        {/* <StyledTableCell align="right">Carbs&nbsp;(g)</StyledTableCell>
                                                    <StyledTableCell align="right">Protein&nbsp;(g)</StyledTableCell> */}
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {selectedSectors.map((row, index) => (
                                                        <StyledTableRow key={row?._id}>
                                                            <StyledTableCell sx={{ width: 400 }} component="th" scope="row">
                                                                {row?.label}
                                                            </StyledTableCell>
                                                            <StyledTableCell sx={{ width: 600 }} align='left'>
                                                                <FormControl sx={{ width: "100%", mt: 2 }}>
                                                                    <Autocomplete
                                                                        // disableCloseOnSelect
                                                                        isOptionEqualToValue={(option, value) => option._id === value._id}
                                                                        options={SubSectors.filter(subSector => subSector.sectorId === row._id)}
                                                                        value={row.subsectors}
                                                                        onChange={(event, value) => {
                                                                            setSelectedSectors(selectedSectors.map(sector => sector._id === row._id ? { ...sector, subsectors: value } : sector))
                                                                        }}
                                                                        // sx={{ width: 600 }}
                                                                        multiple
                                                                        renderInput={(params) => <TextField {...params} label="Sub-Sectors" />}
                                                                    // label={"Filter selected groups"}
                                                                    // placeHolder={"Groups"}
                                                                    />
                                                                </FormControl>
                                                            </StyledTableCell>
                                                            {/* <StyledTableCell align="right">{row.calories}</StyledTableCell> */}
                                                            <StyledTableCell align="right">
                                                                <Button
                                                                    color='error'
                                                                    onClick={() => handleDeleteRow(row)}
                                                                // sx={{ width: 100 }}
                                                                >
                                                                    <Iconify icon={'eva:trash-2-outline'} sx={{ mr: 1 }} />
                                                                </Button>
                                                            </StyledTableCell>
                                                        </StyledTableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </TableContainer>)}

                                </Grid>
                                <Grid item xs={12}>
                                    <Stack my={2} direction={'row'} >
                                        <Autocomplete
                                            // options={getFilteredSectors()}
                                            options={filteredSectors}
                                            // options={Sectors}
                                            isOptionEqualToValue={(option, value) => option._id === value._id}
                                            value={currentSector}
                                            onChange={(event, value) => {
                                                setCurrentSector(value)
                                                // setSectors(Sectors.filter(sector => sector._id !== value._id))
                                            }}
                                            // getOptionDisabled={(option) =>
                                            //     option === timeSlots[0] || option === timeSlots[2]
                                            // }
                                            sx={{ width: 400 }}
                                            renderInput={(params) => <TextField {...params} label="Sectors" />}
                                        />
                                        <Button
                                            onClick={addRow}
                                        >
                                            Add
                                        </Button>
                                    </Stack>
                                </Grid>

                                <Grid item xs={12}>
                                    <FormControl component="fieldset">
                                        <FormLabel component="legend" sx={{ color: 'black' }}>Career Type</FormLabel>
                                        <RadioGroup
                                            row
                                            value={careerType}
                                            onChange={handleCareerTypeChange}
                                        >
                                            <FormControlLabel value={careerTypes.CAREER} control={<Radio />} label="Unit Group" />
                                            <FormControlLabel value={careerTypes.BROAD_CAREER} control={<Radio />} label="Broad Career" />
                                            <FormControlLabel value={careerTypes.SPECIALISED_ROLE} control={<Radio />} label="Specialised Role" />
                                        </RadioGroup>
                                    </FormControl>
                                </Grid>

                                {/* Broad Career: Single select */}
                                {/* {careerType === 'broad' && (
                                    <Grid item xs={12} md={4}>
                                        <Autocomplete
                                            options={BROAD_CAREER_OPTIONS}
                                            value={broadCareer}
                                            onChange={(e, v) => setBroadCareer(v)}
                                            renderInput={params => <TextField {...params} label="Broad Career" />}
                                        />
                                    </Grid>
                                )} */}

                                {careerType === careerTypes.BROAD_CAREER && (
                                    <>
                                        <Grid item xs={12} md={4}>
                                            <Autocomplete
                                                options={broadCareerOptions}
                                                getOptionLabel={option => option.title || ""}
                                                value={broadCareer}
                                                onChange={(e, v) => setBroadCareer(v)}
                                                renderInput={params => <TextField {...params} label="Unit Group" />}
                                            />
                                        </Grid>
                                        <Grid item xs={12} md={4}>
                                            <Autocomplete
                                                multiple
                                                options={specializedRoleOptions}
                                                getOptionLabel={option => option.title || ""}
                                                value={specializations}
                                                onChange={(e, v) => setSpecializations(v)}
                                                renderInput={params => <TextField {...params} label="Specialized Roles" />}
                                            />
                                        </Grid>
                                    </>
                                )}

                                {careerType === careerTypes.SPECIALISED_ROLE && (
                                    <Grid item xs={12} md={4}>
                                        {/* <Autocomplete
                                            multiple
                                            disableCloseOnSelect
                                            options={[{labe: "sdsa", value: '23'}]}
                                            getOptionLabel={(option) => option?.title || ''}
                                            value={career}
                                            onChange={(e, newValue) => setCareer(newValue)}
                                            renderInput={(params) => (
                                                <TextField {...params} label="Broad Careers" placeholder="Select careers" />
                                            )}
                                        /> */}
                                        <Autocomplete
                                            multiple
                                            options={careerOptions}
                                            getOptionLabel={option => option.title || ""}
                                            value={career}
                                            onChange={(e, v) => setCareer(v)}
                                            renderInput={params => <TextField {...params} label="Broad Careers" />}
                                        />
                                    </Grid>
                                )}

                            </Grid>
                            <Stack
                                direction="row"
                                justifyContent="flex-end"
                            >
                                <Link to={`${APP_ROUTER_BASE_URL}dashboard/careers`}>
                                    <Button
                                        type='button'
                                        variant='contained'
                                        color='error'
                                        sx={{ mt: 4, width: '10ch', mr: 2.5, }}
                                    >
                                        Cancel
                                    </Button>
                                </Link>
                                <LoadingButton
                                    loading={isSubmitting}
                                    type='submit'
                                    variant='contained'
                                    sx={{ mt: 4, width: '10ch', mr: 2.5, }}>
                                    Add
                                </LoadingButton>
                            </Stack>
                        </form>

                    </Box>
                </Card>
            </Container>
        </>
    )
}

export default CreateNewCareer
