import { Box, List, ListItemButton, ListItemIcon, ListItemText, styled } from "@mui/material";
import { Link as RouterLink } from "react-router-dom";
import { icon } from "../layouts/dashboard/nav/config";
import { APP_ROUTER_BASE_URL } from "../utils";

export const StyledNavActions = styled((props) => <ListItemButton disableGutters {...props} />)(({ theme }) => ({
    ...theme.typography.body2,
    height: 36,
    position: 'relative',
    textTransform: 'capitalize',
    color: theme.palette.text.secondary,
    borderRadius: theme.shape.borderRadius,
}));

export const StyledNavActionIcon = styled(ListItemIcon)({
    width: 18,
    height: 18,
    color: 'inherit',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
});

const SidenavItem = (props) => {
    const { title, onClick, iconDetail, link, buttonColor, ...rest } = props
    const Icon = icon(iconDetail)
    return (
        <Box>
            <List disablePadding sx={{ px: 1 }}>
                <StyledNavActions
                    onClick={onClick}
                    component={RouterLink}
                    to={link && `${APP_ROUTER_BASE_URL}${link}`}
                    sx={{
                        '&.active': {
                            color: 'text.primary',
                            bgcolor: 'action.selected',
                            fontWeight: 'fontWeightBold',
                        },
                        color:buttonColor
                    }}
                >
                    <StyledNavActionIcon>{Icon && Icon}</StyledNavActionIcon>
                    <ListItemText
                        disableTypography
                        primary={title}
                        sx={{ mx: 0 }}
                    />
                </StyledNavActions>
            </List>
        </Box>
    )
}

export default SidenavItem