import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import './Report.css';
import logo from 'src/assets/images/thinkskill.png';
import { currencyFormat } from 'src/utils';
import CurrentChart from 'src/assets/images/current-job-chart.jpg';
import { SkilldarChartData } from 'src/assets/data/Dummy';
import RadarChartComponent from '../RadarChart/RadarChartComponent';
import ApexCharts from '../apexcharts/ApexCharts';


const Report = ({roleType}) => {
    const report = useSelector(state => state.upskill.SkillsReport)
    const clgDetails = useSelector((state)=>state.mainLayout.collegeDetails);

    const date = new Date().toLocaleDateString();
    return (
        <>
     
        
        <div className='report-container' style={{width:'100%',display:'flex',flexDirection:'column',justifyContent:'center'}}>
            <div className='full-height'>
                <div className='report-header' style={{display:'flex',justifyContent:'space-between',marginTop:'40px',width:'100%', flexWrap: 'wrap'}}>
                    <div style={{marginTop:'15px', flexBasis: '60%'}}>
                        <h2 style={{fontSize:'20px',fontWeight:'700'}}>Your Skills</h2>
                        <p style={{margin:'0px',fontSize:'12px'}}>{`Report for ${report?.reportForVisitorName} ${' '} compiled on ${date}`}</p>
                        <p style={{margin:'0px',fontSize:'12px'}}>{report?.email}</p>
                    </div>
                    <div  style={{flexBasis:'36%'}}>
                        <img src={clgDetails?.logo ? clgDetails?.logo : logo} style={{width: '100%',marginRight:'0px',marginLeft:'auto'}} alt="Upskill/Reskill" />
                    </div>
                </div>
                <div style={{marginTop:'20px',marginBottom:'15px'}}>
                    <h2>Your Skilldar</h2>
                    <p style={{fontSize:'10px',marginTop:'5px'}}>Your Skilldar shows how skilled you are in each of ten universal employability skills.  Your skill level in each category is represented by the point plotted along each skill axis, ranging from low, nearest the centre, to high, nearest the outer edge.  Your skill level is based on your work history, and is a combination of all of the roles you have performed in your career.</p>
                </div>
                <div className='report-chart-skilldar' 
                    style={{display:'flex',justifyContent:'space-between', alignItems:'center',marginBottom:'20px',marginTop:'10px'}}
                >
                    <div style={{marginTop:'10px',width:'60%'}}>
                            <RadarChartComponent
                                width={300}
                                radarApiDetails={ report?.yourSkills?.skilldarChartData}
                                // radarApiDetails={SkilldarChartData}
                                height={300}
                                compareCareer
                                showlabels
                                // legend
                                chartAnimation={false}
                            />  

                            { report?.yourSkills ?
                            <div style={{display:'flex',flexDirection:'column',alignItems:'center'}}>
                                <div className='report-careerGoal' style={{display:'flex',marginBottom:'10px',alignItems:'center'}}>
                                    <span style={{backgroundColor:`${report?.yourSkills?.skilldarChartData?.series[0].color}`}}/>
                                    <p style={{fontSize:'10px', color: '#666666'}}>{report?.yourSkills?.skilldarChartData?.series[0].name } </p>
                                </div>  
                                {/* <div className='report-careerGoal' style={{display:'flex',alignItems:'center'}}>
                                    <span style={{backgroundColor:`${report?.careerGoal?.skilldarChartData?.series[1].color}`}}/>
                                    <p style={{fontSize:'10px', color: '#666666'}}>{report?.careerGoal?.skilldarChartData?.series[1].name }</p>
                                </div> */}
                            </div> 
                            : null 
                            }

                    </div>
                    <div className='chart-units-wrapper'>
                        <div>
                            <h3 className='skill' style={{fontSize:'14px',overflowClipMargin:'content-box'}}>Skilldar Key</h3>
                            <p className='skill' style={{fontSize:'10px'}} >The level of skill that has been required by the roles you have performed in your career history.</p>
                            <div className='skill-levels'>
                                <p className='skill'><span />High</p>

                                <p className='skill'><span style={{ borderColor: '#FFB55B' }} />Advanced</p>

                                <p className='skill'><span style={{ borderColor: '#2AA24A' }} />Intermediate</p>

                                <p className='skill'><span style={{ borderColor: '#933DA9' }} />Basic</p>

                                <p className='skill'><span style={{ borderColor: '#3067DE' }} />Low</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div className='full-height'>
                <div style={{marginTop: '40px', marginBottom: '10px'}}>
                    <h2  style={{fontSize:'16px'}}>Your Upskill Reskill Careers List</h2>
                    <p style={{fontSize:'10px',marginTop:'5px'}}>These are the careers you selected for comparison.</p>
                </div>
                { report?.careers?.length > 0 &&
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px '}}>
                        <div style={{width:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[0].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[0].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[0].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[0].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[0].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[0]?.courses.length ?
                                    report?.careers[0]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{width:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[0]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                { report?.careers?.length > 1 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px '}}>
                        <div style={{width:'55%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[1].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[1].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[1].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[1].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[1].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[1]?.courses.length ?
                                    report?.careers[1]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{width:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[1]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                {
                    report?.careers?.length > 0 && report?.careers?.length < 3 &&
                    <>
                        <div style={{position:'absolute',right:'50%', transform: 'translateX(50%)',bottom:'170px',display:'flex',alignItems:'center'}}>
                            <p style={{fontSize:'8px',marginRight:'10px',fontWeight:'700'}}>Powered by</p>
                            <img style={{ width: '120px', height: '50px'}} src={logo} alt='Upskill/Reskill' />
                        </div>
                    </>
                }
            </div>
            <div className='full-height' style={{paddingTop: '40px'}}>
                { report?.careers?.length > 2 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px '}}>
                        <div style={{width:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[2].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[2].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[2].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[2].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[2].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[2]?.courses.length ?
                                    report?.careers[2]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{width:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[2]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                { report?.careers?.length > 3 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px '}}>
                        <div style={{width:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[3].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[3].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[3].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[3].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[3].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[3]?.courses.length ?
                                    report?.careers[3]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{width:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[3]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                {
                    report?.careers?.length > 2 && report?.careers?.length < 5 &&
                    <>
                        <div style={{position:'absolute',right:'50%', transform: 'translateX(50%)',bottom:'170px',display:'flex',alignItems:'center'}}>
                            <p style={{fontSize:'8px',marginRight:'10px',fontWeight:'700'}}>Powered by</p>
                            <img style={{ width: '120px', height: '50px'}} src={logo} alt='Upskill/Reskill' />
                        </div>
                    </>
                }
            </div>
            <div className='full-height' style={{paddingTop: '40px'}}>
                { report?.careers?.length > 4 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px '}}>
                        <div style={{width:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[4].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[4].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[4].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[4].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[4].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[4]?.courses.length ?
                                    report?.careers[4]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{width:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[4]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                { report?.careers?.length > 5 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px ', flexWrap: 'nowrap'}}>
                        <div style={{flexBasis:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[5].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[5].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[5].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[5].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[5].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[5]?.courses.length ?
                                    report?.careers[5]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{flexBasis:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[5]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                {
                    report?.careers?.length > 4 && report?.careers?.length < 7 &&
                    <>
                        <div style={{position:'absolute',right:'50%', transform: 'translateX(50%)',bottom:'170px',display:'flex',alignItems:'center'}}>
                            <p style={{fontSize:'8px',marginRight:'10px',fontWeight:'700'}}>Powered by</p>
                            <img style={{ width: '120px', height: '50px'}} src={logo} alt='Upskill/Reskill' />
                        </div>
                    </>
                }
            </div>
            <div className='full-height' style={{paddingTop: '40px'}}>
                { report?.careers?.length > 6 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px '}}>
                        <div style={{width:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[6].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[6].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[6].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[6].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[6].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[6]?.courses.length ?
                                    report?.careers[6]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{width:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[6]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                { report?.careers?.length > 7 &&
                    
                    <div style={{width:'100%',display:'flex',justifyContent:'space-between',borderBottom:'1px solid #ccc',alignItems:'flex-start', marginBottom: '13px' , paddingBottom: '10px ', flexWrap: 'nowrap'}}>
                        <div style={{flexBasis:'54%'}}>
                            <h2  style={{fontSize:'13px', color: clgDetails.secondaryColor,marginBottom:'10px'}}>{report?.careers[7].title}</h2>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Weekly salary</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{currencyFormat(report?.careers[7].estimatePayWeek)}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Transfer Window</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[7].transferWindow}</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Average Weekly Hours</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[7].estimateHours} hours</b>
                            </div>
                            <div style={{display:'flex',justifyContent:'space-between',width:'90%'}}>
                                <p style={{fontSize:'10px',minWidth:'60%'}}>Overall in growth/decline</p>
                                <b style={{fontSize:'10px',color:'black',textAlign:'left',minWidth:'40%'}}>{report?.careers[7].overallGrowth}</b>
                            </div>
                            <div style={{marginTop:'20px',width:'95%'}}>
                                <b style={{backgroundColor:'#000c3b',padding:'4px 5px',color:'white',fontSize:'11px'}}>Supporting Courses (Career Requirements and for Personal Career Development)</b>
                                { report?.careers[7]?.courses.length ?
                                    report?.careers[7]?.courses.map((course,index)=>(
                                    <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4',marginTop:'2px'}}>
                                        <b style={{fontSize:'10px',color:'#000c3b'}}>{course.title || '-'}</b>
                                        {/* <b style={{fontSize:'10px',color:'#000c3b'}}>{course.level || '-'}</b> */}
                                    </div>
                                )):
                                <div style={{display:'flex',padding:'5px',justifyContent:'space-between',backgroundColor:'#f4f4f4'}}>
                                    <b style={{fontSize:'10px',color:'#000c3b'}}>No Courses Found</b>
                                </div>
                            }
                            </div>
                        </div>
                        <div style={{flexBasis:'44%'}}>
                            <div style={{width: '220px',marginTop:'0px'}}>
                                <RadarChartComponent
                                    width='100%'
                                    radarApiDetails={report?.careers[7]?.skilldarChartData}
                                    height={220}
                                    compareCareer
                                    showlabels
                                    chartAnimation={false}
                                />
                            </div>
                        </div>
                    </div>
                }
                {
                    report?.careers?.length > 6 && report?.careers?.length < 9 &&
                    <>
                        <div style={{position:'absolute',right:'50%', transform: 'translateX(50%)',bottom:'170px',display:'flex',alignItems:'center'}}>
                            <p style={{fontSize:'8px',marginRight:'10px',fontWeight:'700'}}>Powered by</p>
                            <img style={{ width: '120px', height: '50px'}} src={logo} alt='Upskill/Reskill' />
                        </div>
                    </>
                }
            </div>
        </div>
        </>
    )
}

export default Report;

Report.propTypes = {
    roleType: PropTypes.string,
};