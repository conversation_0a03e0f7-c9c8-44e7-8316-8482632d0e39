import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import {  Backdrop, Box, Button, CircularProgress, SvgIcon, Tooltip, Typography, styled, tooltipClasses, useMediaQuery, useTheme } from '@mui/material';
import PropTypes from 'prop-types';
import Chart  from 'react-apexcharts';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import HexagonOutlinedIcon from '@mui/icons-material/HexagonOutlined';
import InfoIcon from '@mui/icons-material/Info';
import ApexCharts from 'src/components/apexcharts/ApexCharts';
import { skillLevels } from 'src/assets/data/Dummy';
import { setAlert } from 'src/layouts/main/MainLayoutSlice';
import { useNavigate } from 'react-router';
import { Ai_Video_Url } from 'src/config-global';
import svg from '../../assets/images/speech-bubble.svg'
import svgMobile from '../../assets/images/speech-bubble-mobile.svg'
import SvgIcons from '../svgIcons';
import AiVideo from '../player/AiVideo';

const BootstrapTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: 'black',
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: 'black',
  },
}));


const PolarChartComponent = ({loadRadar,chartAnimation,reskillChart,width,radarChartId,closeLabel,showLabel, openLabel, height,toggleShowSkilldarPopup, skilldar, legend, radarApiHook,careersDetails,radarApiDetails, compareCareer,params,handleClick, showlabels,disableTooltip}) => {
    const [chartIds, setChartIds] = useState(radarChartId);
    const [radarApiData, setRadarApiData] = useState();
    const [radarData, setRadarData] = useState({
        series: [],
        chart: {
          type: "polarArea"
        },
        labels: [],
        stroke: {
          colors: ["#fff"]
        },
        fill: {
          opacity: 0.8
        },
        yaxis: {
            // max: 100,
            // tickAmount: 5,
            // min: 0,
            show: false
          },
        responsive: [
          {
            breakpoint: 480,
            options: {
              chart: {
                width,
                height: 900
              },
              legend: {
                position: "bottom"
              }
            }
          },
          {
            breakpoint: 600,
            options: {
              chart: {
                width,
                height: 700
              },
              legend: {
                position: "bottom"
              }
            }
          }
        ]
    });
    const [showTooltip,setShowTooltip] = useState(true)
    const dispatch = useDispatch();
    const navigate = useNavigate();
  const scrollRef = useRef(null)
    const videoClassOpen = {
      position: 'relative',
      width:'100%',
      zIndex: 999,
      bottom: 0,
      left: '50%',
      mb:4,
      transform: 'translateX(-50%)',
      }
    const videoClassClose ={
      mb:4
    }
    const getRadarData = async(data) => {
        await radarApiHook(data)
      .unwrap()
      .then((payload) => {
        setRadarData((previousData)=>({
            ...previousData,
            series: payload?.series,
            labels: payload?.labels || []
        }))
        loadRadar()
        setRadarApiData(payload)
    }
        )
      .catch((error) => dispatch(setAlert({
        open: true,
        msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
    })))}

    useEffect(()=>{
        if(chartIds && !careersDetails && !compareCareer){
            getRadarData(chartIds);
        }
    }, [chartIds]) // eslint-disable-line react-hooks/exhaustive-deps
    useEffect(()=>{
    }, [scrollRef.current]) // eslint-disable-line react-hooks/exhaustive-deps

    // useEffect(()=>{
    //     if(radarApiData || radarApiDetails){
    //       const data = {
    //         series: careersDetails || compareCareer ? radarApiDetails.series : radarApiData.series,
    //         options: {
    //           fill: {
    //             opacity: 0.6,
    //             colors: []
    //           },
    //           tooltip: { enabled: disableTooltip},
    //           xaxis:{
    //             categories: careersDetails || compareCareer ? radarApiDetails?.options.xaxis.categories :  radarApiData?.options.xaxis.categories,
    //             tickPlacement: 'on',
    //             labels: {
    //               show: showlabels,
    //               style: {
    //                 fontSize: compareCareer ? '6px' : '12px',
    //                 fontWeight: 600,
    //               }
    //             },
    //           },
    //           grid: {
    //             borderColor: '#3067DE'
    //           },
    //           plotOptions: {
    //             radar: {
    //               polygons: {
    //                 strokeColors: ['#FD4645', '#FFB55B', '#2AA24A', '#933DA9', '#3067DE'],
    //               }
    //             }
    //          },
    //          legend: {
    //             show: legend,
    //             showForSingleSeries:true,
    //             position: 'bottom',
    //             horizontalAlign: 'center',
    //             offsetX: 0,
    //             offsetY: 0,
    //             // width: '100%'
    //          },
    //          yaxis: {
    //           max: 100,
    //           tickAmount: 5,
    //           min: 0,
    //           show: false
    //         },
    //         chart: {
    //           animations: {
    //               enabled: chartAnimation,
    //           }
    //         }
    //       }}
    //       setRadarData(data);
    //     }
    //   }, [radarApiData,radarApiDetails]) // eslint-disable-line react-hooks/exhaustive-deps   

      const unlockFullPotential =()=>{
        if(handleClick){
          handleClick()
        }else{
          navigate(`/${params.cg_name}/upskill/career-courses`)
        }

      }
      const thm = useTheme()
      const isMobile = useMediaQuery(thm.breakpoints.down(1025))
  return (
    <>
      <Backdrop
        sx={{ color: '#fff', zIndex: 9 }}
        open={showLabel}
        />
        {radarData?.series.length > 0 && width ?
            <>
        {showLabel &&
        
        <Box className='bubble-chart'  sx={{width:800, height:800, transform:{sm:'translate(-4%,20%)', xs:'translate(-4%,16%)'}, position:'absolute', zIndex:99999}}> 
          <Button onClick={closeLabel} size='small' className='close-speech'>
            close [x]
          </Button>
          <img src={isMobile ? svgMobile : svg} alt='svg'/>
        </Box>
        }
          {skilldar && 
            <Box sx={{ display: 'flex', position: 'absolute', zIndex: '99', top: '20px', left: '20px', alignItems: 'center' }}>
              <Typography sx={{mr:1,color:theme=>`${theme.palette.primary.dark}`,fontWeight:'700',fontSize:'27px !important',textTransform:'none'}} >Your Skilldar</Typography>
              <BootstrapTooltip 
              title="Your skilldar represents the skills you have had to demonstrate throughout your career, to their highest level.
              Now we have this understanding of your employability skills we can find roles that require a similar skill set, and unlock your full potential." placement='bottom' >
                <InfoIcon size='large' sx={{color:theme=>`${theme.palette.primary.dark}`,fontSize:'28px !important'}} />
              </BootstrapTooltip>
            </Box>

            }
       { radarData?.series.length > 0 && 
            //   <ApexCharts className='skilldarChart' style={{minHeight:"450px !important"}} type='radar' 
            //     height={height}
            //      width={width}
            //     sx={{position:'absolute',top:'0px',display:'flex',justifyContent:'center'}}
            //      chartData={radarData}/>
            <Box 
            className='polarChart'
            sx={{
                width:'100%',
                minHeight:"510px !important",
                pt:10
            }}
            >
            <Chart
            options={radarData}
            series={radarData?.series}
            height={height}
            width={width}
            yaxis={radarData?.yaxis}
            // chart= {radarData?.chart}
            // fill={radarData?.fill}
            // stroke={radarData?.stroke}
            responsive={radarData?.responsive}
            type='polarArea'
            // height={height}
            // width={width}
            // className='skilldarChart'
        />
        </Box>
                 }
          
       
                {skilldar &&
                    // <Box className='units-wrapper' sx={{top: `${radarData.series.length <= 1 ? '40px' : '160px'}`,width:'35%', display:'flex', flexDirection:'column', justifyContent:'space-between', backgroundColor: (theme)=> `${theme.palette.primary.main} !important`}}>
                    <Box className='units-wrapper' sx={{top: `${radarData.series.length <= 1 ? '40px' : '160px'}`, width:'35%', display:'flex', flexDirection:'column', justifyContent:'space-between', backgroundColor: (theme)=> `${theme.palette.primary.main} !important`}}>
                        <Box className='skill-levels-wrapper'>
                            <Typography variant='h4' color='white'>Skilldar Keys</Typography>
                            <Typography variant='body1' color='white'>The amount of previous work-related skill, knowledge, experience or training that has been acquired and proven in each category.</Typography>
                            <Box className='skill-levels'>
                              {
                                skillLevels.map((skill)=>(
                                    <Typography key={skill.title} variant='body1' className='skill-level' color='white'>
                                      <HexagonOutlinedIcon sx={{color:`${skill.color}`,backgroundColor:'white',mr:1,fontSize:'18px'}}/> {skill.title}
                                      </Typography>
                                ))
                              }
                            </Box>
                            <Box sx={{mt: 3, textAlign:'center'}} >
                              <Button className='btn noUppercase' sx={{backgroundColor: (theme)=> `${theme.palette.secondary.main} !important`}} onClick={unlockFullPotential}  endIcon={<KeyboardArrowRightIcon fontSize='large' sx={{color:'white'}} />}><Typography color='primary.white' variant='button' >Unlock your full potential</Typography></Button>

                            </Box>
                        </Box>
                        <Box ref={scrollRef} id="section-1" sx={{position:'relative'}}
                        className='ai-video-wrapper'
                        >
                        <Box className='video-position' sx={showLabel ? videoClassOpen : videoClassClose}>

                        <AiVideo
                          disableClick ={showLabel}
                          closeLabel ={closeLabel}
                          position='default'
                          url={reskillChart ? `${Ai_Video_Url}radar-reskill.mp4` : `${Ai_Video_Url}radar-upskill.mp4`}
                        />
                        </Box>
                        </Box>
                    </Box>
                }
            </>

            : <CircularProgress/>
        }
    </>
  )
}

PolarChartComponent.propTypes = {
    height: PropTypes.number,
    width: PropTypes.number,
    radarChartId: PropTypes.object,
    radarApiDetails: PropTypes.object,
    toggleShowSkilldarPopup: PropTypes.func,
    skilldar: PropTypes.any,
    legend: PropTypes.bool,
    radarApiHook: PropTypes.func,
    openLabel: PropTypes.func,
    closeLabel: PropTypes.func,
    loadRadar: PropTypes.func,
    params: PropTypes.object,
    handleClick: PropTypes.func,
    careersDetails: PropTypes.bool,
    compareCareer: PropTypes.bool,
    reskillChart: PropTypes.bool,
    showlabels: PropTypes.bool,
    disableTooltip: PropTypes.bool,
    showLabel: PropTypes.bool,
    chartAnimation: PropTypes.bool
};

export default PolarChartComponent