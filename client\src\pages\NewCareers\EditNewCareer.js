import { LoadingButton } from '@mui/lab';
import { Autocomplete, Backdrop, Box, Button, Card, Chip, CircularProgress, Container, FormControl, FormControlLabel, FormLabel, Grid, LinearProgress, Radio, RadioGroup, Stack, TextField, Typography } from '@mui/material';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/material/styles';
import { useFormik } from 'formik';
import { get } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import DataTable from '../../components/DataTable/DataTable';
import Iconify from '../../components/Iconify/Iconify';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { APP_ROUTER_BASE_URL } from '../../utils';
import axiosInstance from '../../utils/axiosInstance';
import { getSectors } from '../Sector/SectorSlice';
import { getSubSectors } from '../SubSector/SubSectorSlice';
import { updateCareer, getCareersByType } from './newCareerSlice';
import { setSnackbar } from '../../Redux/snackbarSlice';
import { careerValidation } from '../../utils/validationSchemas';
import { getRegions } from '../Regions/regionsSlice';
// import Card from '../theme/overrides/Card'

export const SKILLS_TABLE_HEAD = [
    { id: 'name', label: 'Name', alignRight: false },
    { id: 'value', label: 'Value', alignRight: false, },
];

export const skillsRenderCells = ['name', 'value']

export const ABILITIES_TABLE_HEAD = [
    { id: 'name', label: 'Name', alignRight: false },
    { id: 'value', label: 'Value', alignRight: false, },
];

export const abbilitiesRenderCells = ['name', 'value']
const careerTypes = {
    BROAD_CAREER: "broad_career",
    CAREER: "unit_group",
    SPECIALISED_ROLE: "specialised_role",
};
const BROAD_CAREER_OPTIONS = ['IT Technology and Support'];
const CAREER_OPTIONS = ['Software Developer'];
const SPECIALIZATION_OPTIONS = ['Frontend Dev', 'Backend Dev', 'DevOps', 'Game Dev'];

const EditNewCareer = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const params = useParams()
    const [isUpdating, setIsUpdating] = useState(false);
    // ...inside EditNewCareer component...
    const [careerType, setCareerType] = useState('');
    const [broadCareer, setBroadCareer] = useState('');
    const [career, setCareer] = useState([]);
    const [specializations, setSpecializations] = useState([]);
    const [broadCareerOptions, setBroadCareerOptions] = useState([]);
    const [careerOptions, setCareerOptions] = useState([]);
    const [specializedRoleOptions, setSpecializedRoleOptions] = useState([]);
    const [mergedSpecializedRoleOptions, setMergedSpecializedRoleOptions] = useState([]);


    useEffect(() => {
        dispatch(getCareersByType(careerTypes.CAREER)).then(res => {
            if (res.payload && Array.isArray(res.payload.data)) {
                setBroadCareerOptions(res.payload.data);
            }
        });
        dispatch(getCareersByType(careerTypes.BROAD_CAREER)).then(res => {
            if (res.payload && Array.isArray(res.payload.data)) {
                setCareerOptions(res.payload.data);
            }
        });
        dispatch(getCareersByType(careerTypes.SPECIALISED_ROLE)).then(res => {
            if (res.payload && Array.isArray(res.payload.data)) {
                setSpecializedRoleOptions(res.payload.data);
            }
        });
    }, [dispatch]);
    const regionsState = useSelector((state) => state.regions);
    const { regions, status, error } = regionsState;
    const initialValues = useMemo(() => ({
        // id: '',
        title: '',
        jobZone: '',
        skills: [],
        abilities: [],
        onetCode: "",
        socCode: "",
        tasks: "",
        estimateHours: "",
        estimatePay: "",
        description: "",
        abilitiesExist: '',
        estimateHoursYear: '',
        videoUrl: '',
        hoursMean: '',
        hoursMedian: '',
        salaryMean: '',
        salaryMedian: '',
        estimatePayYear: '',
        predictByQualification: [],
        predictByQualificationYear: '',
        predictByRegion: [],
        predictByRegionYear: '',
        predictByWorktype: [],
        predictByWorktypeYear: '',
        predictByYear: [],
        skillsExist: '',
        sectors: [],
        onetCodeForSkillAbility: '',
        metadata: regions && regions.length > 0 ? regions.map(region => ({
            regionId: region._id,
            hoursMean: '',
            hoursMedian: '',
            salaryMean: '',
            salaryMedian: ''
        })) : []
    }), [regions])
    const formik = useFormik({
        initialValues,
        validationSchema: careerValidation,
        onSubmit: (values) => {
            if (careerType === careerTypes.BROAD_CAREER) {
                if (!broadCareer) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: "Please select a Unit Group (parent) for Broad Career."
                    }));
                    setIsUpdating(false);
                    return;
                }
            }
            const careerPayload = {
                ...values,
                type: careerType,
                // broadCareerId: careerType === careerTypes.CAREER ? broadCareer._id : undefined,
                unitGroupId: careerType === careerTypes.BROAD_CAREER ? broadCareer._id : undefined,
                specialisedRoleIds: careerType === careerTypes.BROAD_CAREER ? specializations._id : [],
                // parentCareerId: careerType === careerTypes.SPECIALISED_ROLE ? career._id : undefined,
                // broadCareerId: careerType === careerTypes.SPECIALISED_ROLE ? career._id : undefined,
                broadCareerIds: careerType === careerTypes.SPECIALISED_ROLE && career?.length > 0 ? career?.map(career => career?._id) : [],
                metadata: values?.metadata?.map((meta) => ({ ...meta, socCode: values?.socCode?.soc || values?.socCode }))
            };

            if (careerType === careerTypes.BROAD_CAREER && specializations.length > 0) {
                careerPayload.specialisedRoleIds = specializations.map(s => s._id);
            }

            delete careerPayload.parentCareer;
            setIsUpdating(true)

            dispatch(updateCareer(careerPayload)).then(res => {
                if (res.payload.success) {
                    const successMessage = get(res, 'payload.message', "Successfully Updated Career")
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: successMessage
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/careers`)
                } else {
                    const errorMessage = get(res, 'payload.data.msg', 'Something Went Wrong')
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }))

                }
            }).finally(() => setIsUpdating(false))
        },
    })
    const style = {
        p: 4,
    };
    const [currentSector, setCurrentSector] = useState(null);
    const [selectedSectors, setSelectedSectors] = useState([]);
    const [filteredSectors, setFilteredSectors] = useState([]);
    const [isLoading, setIsLoading] = useState(false)
    const [Sectors, setSectors] = useState([
        {
            label: '',
            _id: '',
            subSectors: []
        }
    ])
    const [SubSectors, setSubSectors] = useState([])
    const sectors = useSelector(state => state?.sectors?.sectors)
    const subSectors = useSelector(state => state?.subSectors?.subSectors)
    useEffect(() => {
        dispatch(getSectors());
        dispatch(getSubSectors());
        dispatch(getRegions(true));
    }, [])
    useEffect(() => {
        const sectorOptions = sectors?.length ? sectors?.map(sector => ({
            label: sector.name,
            _id: sector._id,
        })) : []
        const subSectorOptions = subSectors?.length ? subSectors?.map(subSector => ({
            label: subSector.name,
            _id: subSector._id,
            sectorId: subSector.sectorId
        })) : []

        setSectors(sectorOptions)
        setSubSectors(subSectorOptions)
    }, [sectors, subSectors])

    useEffect(() => {
        const getCareer = async (id) => {
            setIsLoading(true)
            try {
                const response = await axiosInstance({
                    url: "careers/getByID",
                    method: "GET",
                    params: { id }
                })
                const careerDetails = response.data?.data;
                if (careerDetails?.broadCareerIds && careerDetails?.broadCareerIds?.length > 0) {
                    const selectedCareer = careerOptions.filter(opt => careerDetails?.broadCareerIds.some(id => id === opt._id));
                    setCareer(selectedCareer);
                }

                formik.setValues({ ...careerDetails, id: params?.id })
                setSelectedSectors(careerDetails.sectors)

                setCareerType(careerDetails.type);

                // if (careerDetails.broadCareerId && broadCareerOptions.length) {
                //     const selectedBroad = broadCareerOptions.find(opt => opt._id === careerDetails.broadCareerId);
                //     setBroadCareer(selectedBroad || '');
                // }

                // if ((careerDetails.careerId || careerDetails.parentCareerId) && careerOptions.length) {
                //     const selectedCareer = careerOptions.find(opt => opt._id === (careerDetails.careerId || careerDetails.parentCareerId));
                //     setCareer(selectedCareer || '');
                // }

                // new variants
                if (careerDetails.unitGroupId && broadCareerOptions.length) {
                    const selectedCareer = broadCareerOptions.find(opt => opt._id === careerDetails.unitGroupId);
                    setBroadCareer(selectedCareer || '');
                }
                // if ((careerDetails.broadCareerId) && careerOptions.length) {
                //     const selectedCareer = careerOptions.find(opt => opt._id === careerDetails.broadCareerId);
                //     setCareer(selectedCareer || []);
                // }
                // --- Merge options locally, do not set state ---
                const mergedSpecializedRoleOptions = [
                    ...specializedRoleOptions,
                    ...(careerDetails.specialisedRoles || []).filter(
                        sr => !specializedRoleOptions.some(opt => opt._id === sr._id)
                    )
                ];

                // Set selected specializations by matching IDs
                if (careerDetails.specialisedRoleIds && mergedSpecializedRoleOptions.length) {
                    const selectedSpecs = mergedSpecializedRoleOptions.filter(opt =>
                        (careerDetails.specialisedRoleIds || []).includes(opt._id)
                    );
                    setSpecializations(selectedSpecs);
                }
                // Save merged options to a ref or variable if you want to use it elsewhere
                setMergedSpecializedRoleOptions(mergedSpecializedRoleOptions);
            } catch (error) {
                const errorMessage = error?.response?.data?.msg
                console.log("error get career", errorMessage)
            } finally {
                setIsLoading(false)
            }
        }
        if (
            broadCareerOptions.length ||
            careerOptions.length ||
            specializedRoleOptions.length
        ) {
            getCareer(params?.id)
        }
        // Only depend on the original options, not the merged one!
    }, [
        params?.id,
        broadCareerOptions,
        careerOptions,
        specializedRoleOptions
    ]);
    useEffect(() => {
        const selectedSectorsIds = selectedSectors.map(sector => sector?._id)
        setFilteredSectors(!!selectedSectors.length ? Sectors.filter(sector => !selectedSectorsIds.includes(sector?._id)) : Sectors)
    }, [selectedSectors, Sectors])


    const StyledTableCell = styled(TableCell)(({ theme }) => ({
        [`&.${tableCellClasses.head}`]: {
            //   backgroundColor: theme.palette.common.black,
            backgroundColor: theme.palette.common.primary,
            color: theme.palette.common.black,
        },
        [`&.${tableCellClasses.body}`]: {
            fontSize: 14,
        },
    }));

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        '&:last-child td, &:last-child th': {
            border: 0,
        },
    }));
    const addRow = () => {
        if (currentSector) {
            // setSelectedSectors([...selectedSectors,
            // {
            //     name: currentSector.label,
            //     _id: currentSector._id,
            //     subsectors: SubSectors.filter(subSector => subSector.sectorId === currentSector._id).map(subsector => {
            //         return {
            //             name:subsector?.label,
            //             _id: subsector?._id,
            //         }
            //     })
            // }])
            setSelectedSectors([...selectedSectors,
            {
                ...currentSector,
                subsectors: SubSectors.filter(subSector => subSector.sectorId === currentSector._id)

            }])
            setCurrentSector('')
        } else {
            // alert("else")

        }
    }
    const handleDeleteRow = (row) => {
        setSelectedSectors(selectedSectors.filter(sectors => sectors._id !== row._id))
    }
    const handleSubSectorsChange = (value, row) => {
        const sectorNames = value.map(val => {
            return {
                _id: val._id,
                name: val.label
            }
        })
        setSelectedSectors(selectedSectors.map(sector => sector._id === row._id ? { ...sector, subsectors: value } : sector))
    }
    useEffect(() => {
        formik.setValues({
            ...formik.values,
            sectors: selectedSectors
        })
    }, [selectedSectors])
    return (
        <>
            <Helmet>
                <title> Create Careers | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                    Edit Career
                </Typography>
                <>{isLoading ? <LinearProgress /> :
                    <Card>
                        <Box sx={style}>
                            <form onSubmit={formik.handleSubmit}>
                                <Grid container gap={2} rowGap={3} >

                                    <Grid item xs={12} md={11.8} lg={3.81}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='title'
                                            label="Title"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.title}
                                            onChange={formik.handleChange}
                                            error={formik.touched.title && Boolean(formik.errors.title)}
                                            helperText={formik.touched.title && formik.errors.title}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.81}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            disabled
                                            name='onetCode'
                                            label="O-Net Code"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.onetCode}
                                            onChange={formik.handleChange}
                                            error={formik.touched.onetCode && Boolean(formik.errors.onetCode)}
                                            helperText={formik.touched.onetCode && formik.errors.onetCode}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.81}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            disabled
                                            name='socCode'
                                            label="SOC Code"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.socCode}
                                            onChange={formik.handleChange}
                                            error={formik.touched.socCode && Boolean(formik.errors.socCode)}
                                            helperText={formik.touched.socCode && formik.errors.socCode}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={5.85}>
                                        <TextFIeldComponent
                                            multiline
                                            rows={12}
                                            placeholder="Description"
                                            sx={{ width: '100%' }}
                                            name='description'
                                            label="Description"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.description}
                                            onChange={formik.handleChange}
                                            error={formik.touched.description && Boolean(formik.errors.description)}
                                            helperText={formik.touched.description && formik.errors.description}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={5.85}>
                                        <TextFIeldComponent
                                            multiline
                                            rows={12}
                                            placeholder="Tasks"
                                            sx={{ width: '100%' }}
                                            name='tasks'
                                            label="Tasks"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.tasks}
                                            onChange={formik.handleChange}
                                            error={formik.touched.tasks && Boolean(formik.errors.tasks)}
                                            helperText={formik.touched.tasks && formik.errors.tasks}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='videoUrl'
                                            label="Video Url"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.videoUrl}
                                            onChange={formik.handleChange}
                                            error={formik.touched.videoUrl && Boolean(formik.errors.videoUrl)}
                                            helperText={formik.touched.videoUrl && formik.errors.videoUrl}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='estimateHours'
                                            label="Hours / Week"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.estimateHours}
                                            onChange={formik.handleChange}
                                            error={formik.touched.estimateHours && Boolean(formik.errors.estimateHours)}
                                            helperText={formik.touched.estimateHours && formik.errors.estimateHours}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='jobZone'
                                            label="Job Zone"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.jobZone.title}
                                            onChange={formik.handleChange}
                                            error={formik.touched.jobZone && Boolean(formik.errors.jobZone)}
                                            helperText={formik.touched.jobZone && formik.errors.jobZone}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='hoursMean'
                                            label="Mean Hours / Week"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.hoursMean}
                                            onChange={formik.handleChange}
                                            error={formik.touched.hoursMean && Boolean(formik.errors.hoursMean)}
                                            helperText={formik.touched.hoursMean && formik.errors.hoursMean}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='hoursMedian'
                                            label="Median Hours / Week"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.hoursMedian}
                                            onChange={formik.handleChange}
                                            error={formik.touched.hoursMedian && Boolean(formik.errors.hoursMedian)}
                                            helperText={formik.touched.hoursMedian && formik.errors.hoursMedian}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='estimatePay'
                                            label="Salary"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.estimatePay}
                                            onChange={formik.handleChange}
                                            error={formik.touched.estimatePay && Boolean(formik.errors.estimatePay)}
                                            helperText={formik.touched.estimatePay && formik.errors.estimatePay}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='salaryMean'
                                            label="Mean Salary"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.salaryMean}
                                            onChange={formik.handleChange}
                                            error={formik.touched.salaryMean && Boolean(formik.errors.salaryMean)}
                                            helperText={formik.touched.salaryMean && formik.errors.salaryMean}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='salaryMedian'
                                            label="Mean Salary"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.salaryMedian}
                                            onChange={formik.handleChange}
                                            error={formik.touched.salaryMedian && Boolean(formik.errors.salaryMedian)}
                                            helperText={formik.touched.salaryMedian && formik.errors.salaryMedian}
                                        />
                                    </Grid>
                                    {formik.values?.metadata?.map((regionMeta, index) => {
                                        const region = regions.find(r => r._id === regionMeta.regionId);

                                        return (
                                            <Box key={regionMeta.regionId} sx={{ mb: 1 }}>
                                                <Typography variant="h6" sx={{ mb: 2 }}>
                                                    {region?.name || "Region"}
                                                </Typography>

                                                <Grid container spacing={2}>
                                                    <Grid item xs={12} md={3.85}>
                                                        <TextFIeldComponent
                                                            type="number"
                                                            name={`metadata[${index}].hoursMean`}
                                                            label="Mean Hours / Week"
                                                            value={formik.values?.metadata[index].hoursMean}
                                                            onChange={formik.handleChange}
                                                            onBlur={formik.handleBlur}
                                                            error={Boolean(formik.touched?.metadata?.[index]?.hoursMean && formik.errors?.metadata?.[index]?.hoursMean)}
                                                            helperText={formik.touched?.metadata?.[index]?.hoursMean && formik.errors?.metadata?.[index]?.hoursMean}
                                                        />
                                                    </Grid>

                                                    <Grid item xs={12} md={3.85}>
                                                        <TextFIeldComponent
                                                            type="number"
                                                            name={`metadata[${index}].hoursMedian`}
                                                            label="Median Hours / Week"
                                                            value={formik.values?.metadata[index].hoursMedian}
                                                            onChange={formik.handleChange}
                                                            onBlur={formik.handleBlur}
                                                            error={Boolean(formik.touched?.metadata?.[index]?.hoursMedian && formik.errors?.metadata?.[index]?.hoursMedian)}
                                                            helperText={formik.touched?.metadata?.[index]?.hoursMedian && formik.errors?.metadata?.[index]?.hoursMedian}
                                                        />
                                                    </Grid>

                                                    <Grid item xs={12} md={3.85}>
                                                        <TextFIeldComponent
                                                            type="number"
                                                            name={`metadata[${index}].salaryMean`}
                                                            label="Mean Salary"
                                                            value={formik.values?.metadata[index].salaryMean}
                                                            onChange={formik.handleChange}
                                                            onBlur={formik.handleBlur}
                                                            error={Boolean(formik.touched?.metadata?.[index]?.salaryMean && formik.errors?.metadata?.[index]?.salaryMean)}
                                                            helperText={formik.touched?.metadata?.[index]?.salaryMean && formik.errors?.metadata?.[index]?.salaryMean}
                                                        />
                                                    </Grid>

                                                    <Grid item xs={12} md={3.85}>
                                                        <TextFIeldComponent
                                                            type="number"
                                                            name={`metadata[${index}].salaryMedian`}
                                                            label="Median Salary"
                                                            value={formik.values?.metadata[index].salaryMedian}
                                                            onChange={formik.handleChange}
                                                            onBlur={formik.handleBlur}
                                                            error={Boolean(formik.touched?.metadata?.[index]?.salaryMedian && formik.errors?.metadata?.[index]?.salaryMedian)}
                                                            helperText={formik.touched?.metadata?.[index]?.salaryMedian && formik.errors?.metadata?.[index]?.salaryMedian}
                                                        />
                                                    </Grid>
                                                </Grid>
                                            </Box>
                                        );
                                    })}
                                    <Grid xs={12} >
                                        <Grid container gap={2} alignItems={'center'}>
                                            <Grid md={4} xs={12}>
                                                <Stack gap={2} my={4} direction={"row"}>
                                                    <TextFIeldComponent
                                                        sx={{ width: '100%' }}
                                                        name='onetCodeForSkillAbility'
                                                        label="Onet For Skills & Abilities"
                                                        disabled
                                                        // onBlur={formik.handleBlur}
                                                        value={formik.values.onetCodeForSkillAbility}
                                                    // onChange={formik.handleChange}
                                                    // error={formik.touched.onetCodeForSkillAbility && Boolean(formik.errors.onetCodeForSkillAbility)}
                                                    // helperText={formik.touched.onetCodeForSkillAbility && formik.errors.onetCodeForSkillAbility}
                                                    />
                                                    {/* {!editOnetToggled && <Button
                                                onClick={() => setEditOnetToggled(!editOnetToggled)}
                                            >
                                                Edit
                                            </Button>} */}
                                                </Stack>
                                            </Grid>
                                        </Grid>
                                    </Grid>

                                    {!!formik.values.skills.length && <Grid item xs={5.84}>
                                        <DataTable
                                            // loading={dataLoading}
                                            TableHead={SKILLS_TABLE_HEAD}
                                            TableData={formik.values.skills}
                                            disableActions={"true"}
                                            disableSearch={"true"}
                                            heading={"Skills"}
                                            // filterSearch={handleFilterSearch}
                                            // searchLable={"Search..."}
                                            // handleEdit={editSector}
                                            renderCells={skillsRenderCells}
                                            // handleDelete={handleDeleteSector}
                                            pagination
                                        />
                                    </Grid>}
                                    {!!formik.values.abilities.length && <Grid item xs={5.84}>
                                        <DataTable
                                            // loading={dataLoading}
                                            TableHead={ABILITIES_TABLE_HEAD}
                                            TableData={formik.values.abilities}
                                            disableActions={"true"}
                                            heading={"Abilities"}
                                            disableSearch={"true"}
                                            // filterSearch={handleFilterSearch}
                                            // searchLable={"Search..."}
                                            // handleEdit={editSector}
                                            renderCells={abbilitiesRenderCells}
                                            // handleDelete={handleDeleteSector}
                                            pagination
                                        />
                                    </Grid>}

                                    <Grid xs={12}>

                                        {!selectedSectors.length ? null :
                                            (<TableContainer component={Paper}>
                                                <Table sx={{ minWidth: 700 }}
                                                    aria-label="customized table"
                                                >
                                                    <TableHead>
                                                        <TableRow>
                                                            <StyledTableCell>Sector</StyledTableCell>
                                                            <StyledTableCell align="left">Sub-Sectors</StyledTableCell>
                                                            <StyledTableCell align="right">Remove</StyledTableCell>
                                                            {/* <StyledTableCell align="right">Carbs&nbsp;(g)</StyledTableCell>
                                                    <StyledTableCell align="right">Protein&nbsp;(g)</StyledTableCell> */}
                                                        </TableRow>
                                                    </TableHead>
                                                    <TableBody>
                                                        {selectedSectors.map((row, index) => (
                                                            <StyledTableRow key={row?._id}>
                                                                <StyledTableCell sx={{ width: 400 }} component="th" scope="row">
                                                                    {row?.label}
                                                                </StyledTableCell>
                                                                <StyledTableCell sx={{ width: 600 }} align='left'>
                                                                    <FormControl sx={{ width: "100%", mt: 2 }}>
                                                                        <Autocomplete
                                                                            isOptionEqualToValue={(option, value) => option?._id === value?._id}
                                                                            options={SubSectors.filter(subSector => subSector.sectorId === row._id)}
                                                                            value={row.subsectors}
                                                                            onChange={(event, value) => handleSubSectorsChange(value, row)}
                                                                            multiple
                                                                            renderInput={(params) => <TextField {...params} label="Sub-Sectors" />}
                                                                        />
                                                                    </FormControl>
                                                                </StyledTableCell>
                                                                <StyledTableCell align="right">
                                                                    <Button
                                                                        color='error'
                                                                        onClick={() => handleDeleteRow(row)}
                                                                    // sx={{ width: 100 }}
                                                                    >
                                                                        <Iconify icon={'eva:trash-2-outline'} sx={{ mr: 1 }} />
                                                                    </Button>
                                                                </StyledTableCell>
                                                            </StyledTableRow>
                                                        ))}
                                                    </TableBody>
                                                </Table>
                                            </TableContainer>)}

                                    </Grid>
                                    <Grid item xs={12}>
                                        <Stack my={2} direction={'row'} >
                                            <Autocomplete
                                                // options={getFilteredSectors()}
                                                options={filteredSectors}
                                                // options={Sectors}
                                                isOptionEqualToValue={(option, value) => option?._id === value?._id}
                                                value={currentSector}
                                                onChange={(event, value) => {
                                                    setCurrentSector(value)
                                                    // setSectors(Sectors.filter(sector => sector._id !== value._id))
                                                }}
                                                // getOptionDisabled={(option) =>
                                                //     option === timeSlots[0] || option === timeSlots[2]
                                                // }
                                                sx={{ width: 400 }}
                                                renderInput={(params) => <TextField {...params} label="Sectors" />}
                                            />
                                            <Button
                                                onClick={addRow}
                                            >
                                                Add
                                            </Button>
                                        </Stack>
                                    </Grid>

                                    <Grid item xs={12}>
                                        <FormControl component="fieldset">
                                            <FormLabel component="legend" sx={{ color: 'black' }}>Career Type</FormLabel>
                                            <RadioGroup
                                                row
                                                value={careerType}
                                                onChange={e => {
                                                    setCareerType(e.target.value);
                                                    setBroadCareer('');
                                                    setCareer('');
                                                    setSpecializations([]);
                                                }}
                                            >
                                                <FormControlLabel value={careerTypes.CAREER} control={<Radio />} label="Unit Group" />
                                                <FormControlLabel value={careerTypes.BROAD_CAREER} control={<Radio />} label="Broad Career" />
                                                <FormControlLabel value={careerTypes.SPECIALISED_ROLE} control={<Radio />} label="Specialised Role" />
                                            </RadioGroup>
                                        </FormControl>
                                    </Grid>

                                    {/* {careerType === careerTypes.BROAD_CAREER && (
                                        <Grid item xs={12} md={4}>
                                            <Typography variant="subtitle1" sx={{ mt: 2 }}>
                                                Broad Career type selected. No further options required.
                                            </Typography>
                                        </Grid>
                                    )} */}

                                    {careerType === careerTypes.BROAD_CAREER && (
                                        <>
                                            <Grid item xs={12} md={4}>
                                                <Autocomplete
                                                    options={broadCareerOptions}
                                                    getOptionLabel={option => option.title || ""}
                                                    value={broadCareer}
                                                    onChange={(e, v) => setBroadCareer(v)}
                                                    renderInput={params => <TextField {...params} label="Unit Group" />}
                                                />
                                            </Grid>
                                            <Grid item xs={12} md={4}>
                                                <Autocomplete
                                                    multiple
                                                    options={mergedSpecializedRoleOptions}
                                                    getOptionLabel={option => option.title || ""}
                                                    value={specializations}
                                                    onChange={(e, v) => setSpecializations(v)}
                                                    renderInput={params => <TextField {...params} label="Specialized Roles" />}
                                                />
                                            </Grid>
                                        </>
                                    )}

                                    {careerType === careerTypes.SPECIALISED_ROLE && (
                                        <Grid item xs={12} md={4}>
                                            <Autocomplete
                                                multiple
                                                options={careerOptions}
                                                getOptionLabel={option => option.title || ""}
                                                value={career}
                                                onChange={(e, v) => setCareer(v)}
                                                renderInput={params => <TextField {...params} label="Broad Career" />}
                                            />
                                        </Grid>
                                    )}


                                </Grid>
                                <Stack
                                    direction="row"
                                    justifyContent="flex-end"
                                >
                                    <Link to={`${APP_ROUTER_BASE_URL}dashboard/careers`}>
                                        <Button
                                            type='button'
                                            variant='contained'
                                            color='error'
                                            sx={{ mt: 4, width: '10ch', mr: 2.5, }}
                                        >
                                            Cancel
                                        </Button>
                                    </Link>
                                    <LoadingButton
                                        loading={isUpdating}
                                        type='submit'
                                        variant='contained'
                                        sx={{ mt: 4, width: '10ch', mr: 2.5, }}>
                                        Update
                                    </LoadingButton>
                                </Stack>
                            </form>

                        </Box>
                    </Card>}
                </>
            </Container>
        </>
    )
}

export default EditNewCareer
