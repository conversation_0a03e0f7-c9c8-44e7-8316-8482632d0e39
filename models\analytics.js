const mongoose = require("mongoose");

const eventValues = [
  "LANDING_PAGE",
  "PAGE_LEAVE",
  "GET_STARTED",
  "RESKILL_CLICK",
  "UPSKILL_CLICK",
  "CAREER_VIEW",
  "CAREER_HISTORY",
  "CAREER_GOAL",
  "CAREER_COMPARISON",
  "CAREER_RECOMMEND_IDS",
  "RADARS_COUNT",
  "SKILL_REPORT",
  "COURSE_DETAILS",
  "COURSE_ENQUIRY",
  "COURSE_APPLY",
  "LIST_GRAPH_CLICK",
  "SCATTER_GRAPH_CLICK",
  "DEVICE_INFO",
];

const fromValues = [
  "career-history",
  "skilldar",
  "career-courses",
  "comparison",
  "career-details",
  "landing",
  "careers-page",
  "careers-result-page",
]

const AnalyticsSchema = new mongoose.Schema({
  IP: String,
  collegeId: {
    type: mongoose.Schema.Types.ObjectId, 
    ref:"college"
  },
  event: {
    type: String,
    enum: eventValues
  },
  from: String,
  data: {},
  date: {
    type: Date,
    default: Date.now(),
  },
});

module.exports.AnalyticsSchema = AnalyticsSchema;

class Analytics extends mongoose.Model {}

mongoose.model(Analytics, AnalyticsSchema, "analytics");

module.exports.Analytics = Analytics;
module.exports.eventValues = eventValues;
module.exports.fromValues = fromValues;
