import React, { useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import {
  Backdrop,
  Box,
  Button,
  CircularProgress,
  Grid,
  Stack,
  SvgIcon,
  Tooltip,
  Typography,
  styled,
  tooltipClasses,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import PropTypes from 'prop-types';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import HexagonOutlinedIcon from '@mui/icons-material/HexagonOutlined';
import InfoIcon from '@mui/icons-material/Info';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import ApexCharts from 'src/components/apexcharts/ApexCharts';
import { skillLevels } from 'src/assets/data/Dummy';
import { setAlert } from 'src/layouts/main/MainLayoutSlice';
import { useNavigate, useParams } from 'react-router';
import { Ai_Video_Url } from 'src/config-global';
// import riskillRadarVideo from '../../assets/videos/video-2a.mp4'
// import upskillRadarVideo from '../../assets/videos/video-2b.mp4'
import svg from '../../assets/images/speech-bubble.svg';
import svgMobile from '../../assets/images/speech-bubble-mobile.svg';
import SvgIcons from '../svgIcons';
import AiVideo from '../player/AiVideo';
import SkillCard from './SkillCard';

const BootstrapTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: 'black',
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: 'black',
  },
}));

const RadarChartComponent = ({
  regionId,
  loadRadar,
  chartAnimation,
  reskillChart,
  width,
  radarChartId,
  closeLabel,
  showLabel,
  openLabel,
  height,
  toggleShowSkilldarPopup,
  skilldar,
  legend,
  radarApiHook,
  careersDetails,
  radarApiDetails,
  compareCareer,
  params,
  handleClick,
  showlabels,
  disableTooltip,
}) => {
  const [chartIds, setChartIds] = useState(radarChartId);
  const [radarApiData, setRadarApiData] = useState();
  const [radarData, setRadarData] = useState();
  // const [count, setCount] = useState(0);
  const [showTooltip, setShowTooltip] = useState(true);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  // const [showLabel, setShowLabel] = useState(false);
  // useEffect(() => {
  //   if(count === 0){
  //     openLabel()
  //     setCount(prevCount => prevCount +1)
  //     console.log('runned');
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [])
  const scrollRef = useRef(null);
  const videoClassOpen = {
    position: 'relative',
    width: '100%',
    zIndex: 999,
    bottom: 0,
    left: '50%',
    mb: 4,
    transform: 'translateX(-50%)',
  };
  const videoClassClose = {
    mb: 4,
  };
  // const closeLabel = () => {
  //   setShowLabel(false);
  //   document.body.style.overflow = 'unset';
  // }
  // const openLabel = () => {
  //   setShowLabel(true);
  //   if (typeof window !== 'undefined' && window.document) {
  //     document.body.style.overflow = 'hidden';
  //     window.scrollBy(0, 40);
  //   }
  // }
  const getRadarData = async (data) => {
    await radarApiHook(data)
      .unwrap()
      .then((payload) => {
        loadRadar();
        setRadarApiData(payload);
      })
      .catch((error) =>
        dispatch(
          setAlert({
            open: true,
            msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong',
          })
        )
      );
  };

  // const handleScroll = () => {
  //   const element = document.getElementById('section-1');
  //   if (element) {
  //     element.scrollIntoView({ behavior: 'smooth' });
  //   }
  // };
  const regionParams = useParams();
  const isRegion = !!regionParams?.rg_name;

  // ...(open && {
  //         bgcolor: (theme) => alpha(theme.palette.primary.main, theme.palette.action.focusOpacity),
  //       }),

  useEffect(() => {
    if (chartIds && !careersDetails && !compareCareer) {
      const payload = { ...chartIds, isRegion };
      const { collegeId, ...rest } = payload;
      if (isRegion && regionId) {
        // payload.regionId = regionId;
        getRadarData({ ...rest, regionId });
      } else {
        getRadarData(payload);
      }
    }
  }, [chartIds, isRegion]); // eslint-disable-line react-hooks/exhaustive-deps
  useEffect(() => {
    // const element = document.getElementById('section-1');
    // if (element) {
    //   console.log('elll', element);
    //   element.scrollIntoView({ behavior: 'smooth' });
    // }
    // handleScroll()
    // const element = scrollRef.current
    // if(element){
    //   console.log('element',element);
    //   element.scrollIntoView({ behavior: 'smooth' });
    // }
  }, [scrollRef.current]); // eslint-disable-line react-hooks/exhaustive-deps

  // useEffect(()=>{
  //   if(disableTooltip === false){
  //     setShowTooltip(false)
  //   }else{
  //     setShowTooltip(true)
  //   }
  // },[disableTooltip]) // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (radarApiData || radarApiDetails) {
      const data = {
        series: careersDetails || compareCareer ? radarApiDetails.series : radarApiData.series,
        options: {
          fill: {
            opacity: 0.6,
            colors: [],
          },
          tooltip: { enabled: disableTooltip },
          xaxis: {
            categories: (careersDetails || compareCareer
              ? radarApiDetails?.options.xaxis.categories
              : radarApiData?.options.xaxis.categories
            )?.map((label) => label?.toUpperCase()), // ✅ Make labels uppercase
            tickPlacement: 'on',
            labels: {
              show: showlabels,
              style: {
                fontSize: compareCareer ? '6px' : '12px',
                fontWeight: 700,
                colors: Array(10).fill('#000000')
              },
            },
          },
          grid: {
            borderColor: '#3067DE',
          },
          plotOptions: {
            radar: {
              polygons: {
                strokeColors: ['#FD4645', '#FFB55B', '#2AA24A', '#933DA9', '#3067DE'],
              },
            },
          },
          legend: {
            show: legend,
            showForSingleSeries: true,
            position: 'bottom',
            horizontalAlign: 'center',
            offsetX: 0,
            offsetY: 0,
            // width: '100%'
          },
          yaxis: {
            max: 100,
            tickAmount: 5,
            min: 0,
            show: false,
          },
          chart: {
            animations: {
              enabled: chartAnimation,
            },
          },
        },
      };
      setRadarData(data);
    }
  }, [radarApiData, radarApiDetails]); // eslint-disable-line react-hooks/exhaustive-deps

  const unlockFullPotential = () => {
    if (handleClick) {
      handleClick();
    } else {
      navigate(`/${params.cg_name}/upskill/career-courses`);
    }
  };
  const thm = useTheme();
  const isMobile = useMediaQuery(thm.breakpoints.down(1025));
  const isXs = useMediaQuery(thm.breakpoints.down('sm'));
  return (
    <>
      <Backdrop
        sx={{ color: '#fff', zIndex: 9 }}
        open={showLabel}
      // onClick={closeLabel}
      >
        {/* <Box 
    sx={{
      // background: `url(${svg})`,
      // backgroundRepeat:'no-repeat',
      width:800, 
      height:800,
      position:'absolute',
      bottom:'-10%',
      right:'35%',
      display:'flex',
      alignItems:'center',
      justifyContent:'flex-start'
    }}
    > */}
        {/* <Typography sx={{textAlign:'center', maxWidth:300}}>
      Hear Laicie explain Upskill Reskill’s career and courses explore
      </Typography> */}
        {/* </Box> */}
      </Backdrop>
      {radarData && width ? (
        <>
          {showLabel && (
            <Box
              className="bubble-chart"
              sx={{
                width: 800,
                height: 800,
                transform: { sm: 'translate(-4%,20%)', xs: 'translate(-4%,16%)' },
                position: 'absolute',
                zIndex: 99999,
              }}
            >
              <Button onClick={closeLabel} size="small" className="close-speech">
                close [x]
              </Button>
              <img src={isMobile ? svgMobile : svg} alt="svg" />
              {/* <img style={{width:800, height:800, transform:'translate(-7%,0%)', position:'absolute', zIndex:99999}}  src={svg} alt='svg'/> */}
            </Box>
          )}

          {/* {skilldar &&
            <Box sx={{ display: 'flex', position: 'absolute', zIndex: '99', top: '20px', left: '20px', alignItems: 'center' }}>
              <Typography sx={{ mr: 1, color: theme => `${theme.palette.primary.dark}`, fontWeight: '700', fontSize: '27px !important', textTransform: 'none' }} >Your Skilldar</Typography>
              <BootstrapTooltip
                title="Your skilldar represents the skills you have had to demonstrate throughout your career, to their highest level.
              Now we have this understanding of your employability skills we can find roles that require a similar skill set, and unlock your full potential." placement='bottom' >
                <InfoIcon size='large' sx={{ color: theme => `${theme.palette.primary.dark}`, fontSize: '28px !important' }} />
              </BootstrapTooltip>
            </Box>

          } */}
          <ApexCharts
            className={`skilldarChart ${skilldar && !isXs && 'skilldarClass'}`}
            style={{
              minHeight: !isXs ? '450px !important' : 'unset !important',
              width: isXs && '100% !important',
            }}
            type="radar"
            // height='100%'
            height={height}
            //  width={skilldar && radarData.series.length <= 1 ? width - 120 : width}
            width={isXs ? '100%' : width}
            sx={{ position: 'absolute', top: '0px', display: 'flex', justifyContent: 'center' }}
            chartData={radarData}
            options={radarData?.options}
            series={radarData?.series}
          />
          {skilldar && (
            <Box
              sx={{
                width: 340,
                p: 2,
                textAlign: 'center',
                backgroundColor: '#fff',
              }}
            >
              <Box sx={{ fontWeight: '700', fontSize: '14px !important' }}>SKILL LEVEL</Box>

              <Box sx={{ mt: 1, mb: 4, fontSize: '12px !important' }}>
                The amount of previous work-related skill, knowledge, experience or training that
                has been acquired and proven in each category.
              </Box>

              <Stack spacing={0.5} alignItems="center" sx={{ my: 2 }}>
                {skillLevels.map(({ title, color }) => (
                  <Box key={title} sx={{ textAlign: 'center', lineHeight: 0.8 }}>
                    <svg width="120" height="12">
                      {' '}
                      {/* Increased width from 100 to 120 */}
                      <path
                        d="M10 10 Q60 0 110 10"
                        stroke={color}
                        strokeWidth="2"
                        fill="transparent"
                      />
                    </svg>
                    <Box sx={{ fontSize: '12px', fontWeight: 500 }}>{title}</Box>
                  </Box>
                ))}
              </Stack>

              <Box display="flex" justifyContent="center" alignItems="center" gap={1}>
                <Tooltip title="Skilldar explained">
                  <HelpOutlineIcon fontSize="small" />
                </Tooltip>
                <Box sx={{ fontSize: '10px !important', fontWeight: 700 }}>
                  Skilldar explained
                </Box>
              </Box>
            </Box>
          )}
        </>
      ) : (
        <CircularProgress />
      )}
    </>
  );
};

RadarChartComponent.propTypes = {
  height: PropTypes.number,
  regionId: PropTypes.string,
  width: PropTypes.number,
  radarChartId: PropTypes.object,
  radarApiDetails: PropTypes.object,
  toggleShowSkilldarPopup: PropTypes.func,
  skilldar: PropTypes.any,
  legend: PropTypes.bool,
  radarApiHook: PropTypes.func,
  openLabel: PropTypes.func,
  closeLabel: PropTypes.func,
  loadRadar: PropTypes.func,
  params: PropTypes.object,
  handleClick: PropTypes.func,
  careersDetails: PropTypes.bool,
  compareCareer: PropTypes.bool,
  reskillChart: PropTypes.bool,
  showlabels: PropTypes.bool,
  disableTooltip: PropTypes.bool,
  showLabel: PropTypes.bool,
  chartAnimation: PropTypes.bool,
};

export default RadarChartComponent;
