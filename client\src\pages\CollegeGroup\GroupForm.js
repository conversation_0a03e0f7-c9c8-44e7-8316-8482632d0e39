import { Box, Button, Fade, Grid, Stack } from '@mui/material';
import { useFormik } from 'formik';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { formButton } from '../../utils/cssStyles';
import { groupValidationSchema } from '../../utils/validationSchemas';
import { postCollegeGroup } from './collegeGroupsSlice';
import PhoneInput from '../../components/PhoneInput';

const GroupForm = ({ Groups, setOpenModel, openModel }) => {

    const [isLoading,setIsLoading] = useState("false")
    const formik = useFormik({
        initialValues: {
            Name: '',
            groupAddress: '',
            groupTelNumber: '',
            groupWebsiteAddress:'',
            groupEmail:'',
            // groupAdminUser:''
        },
        onSubmit: (values) => {
            // if (formik.values.Name) {
            //     const ID = Groups[Groups.length - 1].id + 1
            //     dispatch(addGroup({ id: ID, ...values }))
            //     handleClose()
            // }
            try{
                setIsLoading("true")
                const group = {
                    name: values.Name
                }
                dispatch(postCollegeGroup(group))
            } catch(error){
                console.log("error",error)
            } finally{
                setIsLoading("false")
                handleClose()
            }
        
        },
        validationSchema: groupValidationSchema
    })
    const dispatch = useDispatch()
    const handleClose = () => {
        setOpenModel(false)
    }
    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            groupTelNumber: newValue
        })
    }

    return (
        <Fade in={openModel}>
            <Box >
                <form
                    onSubmit={formik.handleSubmit}>
                    <Grid container gap={2}>
                        <Grid item xs={12} md={5.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='Name'
                                label="College Group Name"
                                onBlur={formik.handleBlur}
                                value={formik.values.Name}
                                onChange={formik.handleChange}
                                error={formik.touched.Name && Boolean(formik.errors.Name)}
                                helperText={formik.touched.Name && formik.errors.Name}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                        <PhoneInput
                                sx={{ width: "100%" }}
                                value={formik.values.groupTelNumber}
                                name='groupTelNumber'
                                label="Telephone Number"
                                defaultCountry="GB"
                                onChange={handlePhoneChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.groupTelNumber && Boolean(formik.errors.groupTelNumber)}
                                helperText={formik.touched.groupTelNumber && formik.errors.groupTelNumber}
                            />
                        </Grid>
                        <Grid item xs={11.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='groupAddress'
                                label="College Group Address"
                                onBlur={formik.handleBlur}
                                value={formik.values.groupAddress}
                                onChange={formik.handleChange}
                                error={formik.touched.groupAddress && Boolean(formik.errors.groupAddress)}
                                helperText={formik.touched.groupAddress && formik.errors.groupAddress}
                            />
                        </Grid>
                        <Grid item xs={11.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='groupWebsiteAddress'
                                label="Website Address"
                                onBlur={formik.handleBlur}
                                value={formik.values.groupWebsiteAddress}
                                onChange={formik.handleChange}
                                error={formik.touched.groupWebsiteAddress && Boolean(formik.errors.groupWebsiteAddress)}
                                helperText={formik.touched.groupWebsiteAddress && formik.errors.groupWebsiteAddress}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='groupEmail'
                                label="Email"
                                onBlur={formik.handleBlur}
                                value={formik.values.groupEmail}
                                onChange={formik.handleChange}
                                error={formik.touched.groupEmail && Boolean(formik.errors.groupEmail)}
                                helperText={formik.touched.groupEmail && formik.errors.groupEmail}
                            />
                        </Grid>
                    </Grid>
                    <Stack
                        direction="row"
                        justifyContent="flex-end"
                    >
                        <Button
                            type='submit'
                            variant='contained'
                            sx={formButton}
                            >
                            Add
                        </Button>
                    </Stack>
                </form>
            </Box>
        </Fade >
    )
}

export default GroupForm