import { Box, Button, Fade, FormControl, Stack } from '@mui/material';
import { useEffect, useState } from 'react';
import { MultiSelect } from '../../components/MultiSelect';

const FilterForm = ({ openModel, setOpenModel, Groups, users, setUsers, selectedGroups, setSelectedGroups, Colleges, setSelectedColleges, selectedColleges }) => {
    const [collegeGroupValues, setCollegeGroupValues] = useState([])
    const [collegeValues, setCollegeValues] = useState([])
    const Users = users
    const groupNames = Groups.map(group => group.Name)
    // const collegeNames = (selectedGroups.length !== 0) ? Colleges.filter(College => selectedGroups.includes(College.groupName)).map(college => college.name) : []
    const collegeNames = (collegeGroupValues.length !== 0) ? Colleges.filter(College => collegeGroupValues.includes(College.groupName)).map(college => college.name) : []
    
    const applyFilter = (event) => {
        event.preventDefault()
        setSelectedColleges(collegeValues)
        setSelectedGroups(collegeGroupValues)
        // if (selectedGroups.length !== 0) {
        //     if (selectedColleges.length !== 0) {
        //         setUsers(Users.filter(user => (selectedGroups.includes(user?.groupName) && selectedColleges.includes(user?.collegeName))))
        //     } else {
        //         setUsers(Users.filter(user => selectedGroups.includes(user?.groupName)))
        //     }
        if (collegeGroupValues.length !== 0) {
            if (collegeValues.length !== 0) {
                setUsers(Users.filter(user => (collegeGroupValues.includes(user?.groupName) && collegeValues.includes(user?.collegeName))))
            } else {
                setUsers(Users.filter(user => collegeGroupValues.includes(user?.groupName)))
            }
        } else {
            setUsers(Users)
        }
        setOpenModel(false)
    }
    // useEffect(() => {
    //     setSelectedColleges(currentSelected => (currentSelected.filter(college => {
    //         return (selectedGroups.includes(Colleges.find(College => College.name === college).groupName))
    //     })))
    // }, [selectedGroups])
    useEffect(() => {
        setCollegeValues(currentSelected => (currentSelected.filter(college => {
            return (collegeGroupValues.includes(Colleges.find(College => College.name === college).groupName))
        })))
    }, [collegeGroupValues])

    useEffect(() => {
     if(selectedGroups){
        setCollegeGroupValues(selectedGroups)
     }
     if(selectedColleges){
        setCollegeValues(selectedColleges)
     }
    }, [])
    

    const handleGroupChange = (event, newValue) => {
        // setSelectedGroups(newValue);
        setCollegeGroupValues(newValue)
        // setSelectedGroups(() => newValue);
    };
    const handleCollegeChange = (event, newValue) => {
        setCollegeValues(newValue)
        // setSelectedColleges(newValue);
    };
    const clearFilter = () => {
        setSelectedColleges([])
        setSelectedGroups([])
        cancelFilter()
        setUsers(Users)
    }
    const cancelFilter = () => {
        // clearFilter()
        setCollegeGroupValues([])
        setCollegeValues([])
        setOpenModel(false)
    }
    return (
        <Fade in={openModel}>
            <Box
                height={"fitContent"}
                sx={{ p: 2 }}
            >
                <form onSubmit={applyFilter}>
                    <Box>
                        <Stack >
                            <FormControl sx={{ width: "100%", mt: 2 }}>
                                <MultiSelect
                                    onChange={handleGroupChange}
                                    limitTags={2}
                                    value={collegeGroupValues}
                                    // value={selectedGroups}
                                    options={groupNames}
                                    label={"Filter selected groups"}
                                    placeHolder={"Groups"}
                                />
                            </FormControl>
                            {/* <FormControl disabled={Boolean(selectedGroups.length === 0)} sx={{ width: '100%', mt: 4 }}> */}
                            <FormControl disabled={Boolean(collegeGroupValues.length === 0)} sx={{ width: '100%', mt: 4 }}>
                                <MultiSelect
                                    limitTags={4}
                                    // disabled={Boolean(selectedGroups.length === 0)}
                                    disabled={Boolean(collegeGroupValues.length === 0)}
                                    value={collegeValues}
                                    // value={selectedColleges}
                                    onChange={handleCollegeChange}
                                    options={collegeNames}
                                    label={"Filter selected colleges"}
                                    placeHolder={"Colleges"}
                                />
                            </FormControl>
                        </Stack>
                    </Box>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Box>
                            <Button
                                sx={{ mt: 4 }}
                                type='button'
                                variant='contained'
                                color="error"
                                // onClick={() => setOpenModel(false)}
                                onClick={cancelFilter}
                            >
                                Cancel
                            </Button>
                        </Box>
                        <Box>
                            <Button
                                sx={{ mt: 4 }}
                                type='submit'
                                variant='contained'
                            >
                                Apply Filter
                            </Button>
                            <Button
                                onClick={clearFilter}
                                sx={{ mt: 4, ml: 2 }}
                                type='button'
                                variant="outlined"
                                color="error"
                            >
                                Clear
                            </Button>
                        </Box>
                    </Stack>
                </form>
            </Box>
        </Fade>
    )
}

export default FilterForm