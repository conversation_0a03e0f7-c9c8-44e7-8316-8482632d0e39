import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { get } from 'lodash';
import Cookies from 'universal-cookie';
import axiosInstance from '../../utils/axiosInstance';
import { userLogin } from '../Auth/loginSlice';


const cookies = new Cookies();
const jwtToken = cookies.get("token")
const token = jwtToken || ""
// const headers = {'Authorization': `Bearer ${token}` } 
export const getCollegeGroups = createAsyncThunk('groups/getCollegeGroups', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "collegeGroups/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        const errorResponse = get(error,"response.data","Something went wrong")
        // console.log("error",errorResponse)
        return rejectWithValue(errorResponse)
    }
})

export const postCollegeGroup = createAsyncThunk('groups/postCollegeGroup', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "collegeGroups/add",
            method: "POST",
            data
        })
        response.data.name = data.name
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const editCollegeGroup = createAsyncThunk('groups/editCollegeGroup', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "collegeGroups/update",
            method: "PUT",
            data
        })
        // response.data.name = data.name
        console.log("res from edit college group",response)
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const deleteCollegeGroup = createAsyncThunk('groups/deleteCollegeGroup', async (group) => {
    try {
        const data = {
            id: group.id
        }
        const response = await axiosInstance({
            url: 'collegeGroups/remove',
            method: 'DELETE',
            data,
        })
        // .then(res=> console.log("res",res)).catch(error => console.log('error',error.message))
        response.data.id = group.id
        return response
    } catch (error) {
        console.log("error", error?.response?.data?.msg)
        return error
    }
})

const initialState = {
    groups: [],
    status: 'idle', // 'idle' | 'pending' | 'succeeded' | 'failed'
    error: null
}
const collegeGroupSlice = createSlice({
    name: 'groups',
    initialState,
    reducers: {
        addGroup: (state, action) => {
            state.groups.push(action.payload)
        },
        updateGroups: (state, action) => {

        },
        deleteGroup: (state, action) => {
            state.groups = state.groups.filter(group => group.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getCollegeGroups.pending]: (state) => {
            state.status = "pending"
        },
        [getCollegeGroups.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data;
            const groups = data?.map(group => {
                return {
                    Name: group.name,
                    id: group._id
                }
            }
            )
            state.groups = groups

        },
        [getCollegeGroups.rejected]: (state, action) => {
            state.status = "failed"
            state.error = action.payload
        },
        [postCollegeGroup.fulfilled]: (state, action) => {
            const group = {
                Name: action.payload.name,
                id: action.payload.id
            }
            state.groups.push(group)
        },
        [postCollegeGroup.rejected]: (state, action) => {
        },
        [deleteCollegeGroup.fulfilled]: (state, action) => {
            const response = action.payload
            if (response.status === 200) {
                state.groups = state.groups.filter(group => group.id !== response.data.id)
            }
        },
        [editCollegeGroup.fulfilled] :(state,action) => {
            
        },
        [userLogin.fulfilled]: (state, action) => {
           return initialState
        }
    }
})
export const { addGroup, deleteGroup } = collegeGroupSlice.actions
export default collegeGroupSlice.reducer