import { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// @mui
import { Box, Container, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';
// hooks
import jwtDecode from 'jwt-decode';
import { useLocation, useNavigate } from 'react-router-dom';
import Cookies from 'universal-cookie';
import { useFormik } from 'formik';
import { addUserPermissions } from 'src/Redux/selectedCollegeSlice';
import useResponsive from '../../hooks/useResponsive';
// components
import Logo from '../../components/Logo';
// sections
import { credentialValidation } from '../../utils/validationSchemas';
import { userLogin } from './loginSlice';
import LoginForm from './LoginForm';
import {APP_ROUTER_BASE_URL} from '../../utils';
import <PERSON>Logo from '../../assets/images/UP_RElogo.png';
import ThinkskillLogo from '../../assets/images/thinkskill.png';

// ----------------------------------------------------------------------

const StyledRoot = styled('div')(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    display: 'flex',
  },
}));

const StyledSection = styled('div')(({ theme }) => ({
  width: '100%',
  maxWidth: 480,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  boxShadow: theme.customShadows.card,
  backgroundColor: theme.palette.background.default,
}));

const StyledContent = styled('div')(({ theme }) => ({
  maxWidth: 480,
  margin: 'auto',
  minHeight: '100vh',
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'column',
  padding: theme.spacing(12, 0),
}));

// ----------------------------------------------------------------------

export default function LoginPage() {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const mdUp = useResponsive('up', 'md');
  const location = useLocation();
  const from = location.state?.from?.pathname || "/";
  const formik = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    onSubmit: (values) => {
      dispatch(userLogin(values)).then((res) => {
        const data = res?.payload?.data
        if(data?.user?.collegePermissions){
          localStorage.setItem('currentUserPermissions', JSON.stringify(data?.user?.collegePermissions))
          dispatch(addUserPermissions(data?.user?.collegePermissions))
        }
        if (data?.success) {
          switch (data?.user?.role?.toString()) {
            case "1":
              navigate(`${APP_ROUTER_BASE_URL}`)
              break
            case "2":
              navigate(`${APP_ROUTER_BASE_URL}dashboard/collegegroups`)
              break
            case "3":
              navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges`)
              break
            case "4":
              navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses`)
              break
            default:
              navigate(`${APP_ROUTER_BASE_URL}dashboard/defaultuser`)
          }
        }

      })
    },
    validationSchema: credentialValidation
  })


  return (
    <>
      <Helmet>
        <title> Login | ThinkSkill </title>
      </Helmet>

      <StyledRoot>
        {/* <Logo
          sx={{
            position: 'fixed',
            top: { xs: 16, sm: 24, md: 40 },
            left: { xs: 16, sm: 24, md: 40 },
          }}
        /> */}

        {mdUp && (
          <StyledSection>
            <Typography variant="h3" sx={{ px: 5, mt: 10, mb: 5 }}>
              Hi, Welcome Back
            </Typography>
            <img src="/assets/illustrations/illustration_login.png" alt="login" />
          </StyledSection>
        )}

        <Container maxWidth="sm">
          <StyledContent>
          <Box
              sx={{ maxWidth: 200, cursor: 'pointer', mb: 8, alignSelf: 'center' }}
              onClick={() => navigate(`${APP_ROUTER_BASE_URL}`)}
            >
              <img src={ThinkskillLogo} alt="logo" />
            </Box>
            <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
              Sign in to Horizon
            </Typography>

            <LoginForm
              formik={formik}
            />

            {/* <Typography variant="body2" sx={{ mb: 5 }}> */}
            {/* Don’t have an account? {''}
              <Link variant="subtitle2">Get started</Link> */}
            {/* </Typography> */}

            {/* <Stack direction="row" spacing={2}>
              <Button fullWidth size="large" color="inherit" variant="outlined">
                <Iconify icon="eva:google-fill" color="#DF3E30" width={22} height={22} />
              </Button>

              <Button fullWidth size="large" color="inherit" variant="outlined">
                <Iconify icon="eva:facebook-fill" color="#1877F2" width={22} height={22} />
              </Button>

              <Button fullWidth size="large" color="inherit" variant="outlined">
                <Iconify icon="eva:twitter-fill" color="#1C9CEA" width={22} height={22} />
              </Button>
            </Stack> */}
            {/* 
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                OR
              </Typography>
            </Divider> */}
          </StyledContent>
        </Container>
      </StyledRoot>
    </>
  );
}
