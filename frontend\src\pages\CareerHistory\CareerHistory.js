import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import ArrowForwardIosRoundedIcon from '@mui/icons-material/ArrowForwardIosRounded';
import AssignmentIcon from '@mui/icons-material/Assignment';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { useTheme } from '@mui/material/styles';
import InfoIcon from '@mui/icons-material/Info';
import {
  Box,
  Button,
  CircularProgress,
  Container,
  Tooltip,
  Typography,
  styled,
  tooltipClasses,
} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import axios from 'axios';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { SelectComponent } from 'src/components/hook-form';
import RegionStepper from 'src/components/RegionStepper';
import Cookies from 'js-cookie';
import { get } from 'lodash';
import { resetCompare, setAlert, setGlobalCompare } from 'src/layouts/main/MainLayoutSlice';
import { analytics } from 'src/utils';
import { ArcticonsEmojiSpiderWeb } from '../Reskill_Flow/SpiderIcon';
import {
  addCareerGoal,
  addCareerHistory,
  getCareerHistory,
  useGetCareerGoalsMutation,
  useGetCareersMutation,
  useGetNestedCareersMutation,
} from './CareerHistorySlice';

const BootstrapTooltip = styled(({ className, ...props }) => (
  <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
  [`& .${tooltipClasses.arrow}`]: {
    color: 'black',
  },
  [`& .${tooltipClasses.tooltip}`]: {
    backgroundColor: 'black',
    maxWidth: '220px !important',
  },
}));

export default function CareerHistory() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const params = useParams();
  const CareerHistoryState = useSelector((state) => state.careerHistory.CareerHistory);
  const CareerGoalState = useSelector((state) => state.careerHistory.CareerGoal);
  const collegeId = useSelector((state) => state.mainLayout.collegeDetails?._id);
  // console.log("params", params)
  // console.log("college ID", collegeId)
  const [CareerOptions, setCareerOptions] = useState([]);
  const [IP, setIp] = useState('');
  const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
  const [CareerIds, setCareerIds] = useState();
  const [CareerGoals, setCareerGoals] = useState();
  const [currentRole, setCurrentRole] = useState([]);
  const [careerGoal, setCareerGoal] = useState();
  const [showError, setShowError] = useState(false);
  const [selectFieldCount, setSelectFieldCount] = useState([0]);
  const [innerStepCount, setInnerStepCount] = useState(0);
  const [careerGoalsData, setCareerGoalsData] = useState();
  const [careersData, setCareersData] = useState();
  const [selectedRole, setSelectedRole] = useState('reskill');
  const [getCareers, { isLoading: careersLoading }] = useGetCareersMutation();
  const [getNestedCareers] = useGetNestedCareersMutation();
  const [getCareerGoals, { isLoading: careersGoalsLoading }] = useGetCareerGoalsMutation();
  const [searchInput, setSearchInput] = useState(['']);
  const [fieldOptions, setFieldOptions] = useState({}); // Options per field
  const [fieldLoading, setFieldLoading] = useState({}); // Loading state per field
  const [fieldOpen, setFieldOpen] = useState({}); // Open state per field
  const [searchTimeouts, setSearchTimeouts] = useState({});
  const isRegionData = !!clgDetails?.region;
  const buttonColor = isRegionData ? clgDetails?.region?.button?.bgColor : '';
  const buttonFontColor = isRegionData ? clgDetails?.region?.button?.color : '';
  const fontColor = isRegionData ? clgDetails?.region?.fontColor : '';
  const bgImage = isRegionData ? clgDetails?.region?.bgImage : '';

  const disabledBtn = {
    true: true,
    false: false,
  };
  const CareerHistorySteps = [
    {
      label: 'Career History',
    },
    {
      label: 'Skilldar',
    },
    {
      label: `Career and ${selectedRole === 'reskill' ? 'Reskill' : 'Upskill'} time`,
    },
  ];
  const getIp = async () => {
    const res = await axios.get('https://api.ipify.org/?format=json');
    const Ip = res.data.ip;
    setIp(Ip);
  };
  useEffect(() => {
    dispatch(resetCompare());
    // Clear search options on page load
    setFieldOptions({});
  }, [dispatch]);
  useEffect(() => {
    getIp();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // useEffect(()=>{
  //   if(collegeId && IP){
  //     getCareers();
  //   }
  // },[collegeId, IP])// eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (IP || collegeId) {
      getCareersData();
    }
  }, [IP, collegeId]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (currentRole && currentRole.length > 0) {
      setShowError(false);
      const ids = currentRole
        .filter(Boolean)
        .map((role) => role?.value)
        .filter(Boolean);
      setCareerIds(ids);
      if (CareerHistoryState?.length > 0) {
        currentRole.map((role) => {
          if (role && role.value) {
            CareerHistoryState.map((item) => {
              if (item && item.value && role.value !== item.value) {
                setCareerGoal('');
              }
              return '';
            });
          }
          return '';
        });
      }
    }
    // console.log('Current Role', currentRole);
  }, [currentRole]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleChange = (count, e) => {
    if (currentRole.length === count) {
      setCurrentRole((prev) => [...prev, e]);
    } else if (currentRole.length > count) {
      const newState = currentRole.map((item, index) => {
        if (index === count) {
          item = e;
        }
        return item;
      });
      setCurrentRole(newState);
    }
  };

  const addField = () => {
    const fieldCount = selectFieldCount.length;

    // Update all state arrays/objects atomically
    setSelectFieldCount((prev) => [...prev, fieldCount]);
    setSearchInput((prev) => [...prev, '']);
    setCurrentRole((prev) => [...prev, undefined]);

    // Initialize empty state for new field
    setFieldOptions((prev) => ({ ...prev, [fieldCount]: [] }));
    setFieldLoading((prev) => ({ ...prev, [fieldCount]: false }));
    setFieldOpen((prev) => ({ ...prev, [fieldCount]: false }));

    // Clear any existing timeout for this field
    setSearchTimeouts((prev) => {
      if (prev[fieldCount]) {
        clearTimeout(prev[fieldCount]);
      }
      return { ...prev, [fieldCount]: null };
    });
  };

  const removeField = (count) => {
    // Clear timeout for this field before removing
    if (searchTimeouts[count]) {
      clearTimeout(searchTimeouts[count]);
    }

    // Remove from array-based states (simple filter by index)
    setCurrentRole((prev) => prev.filter((_, index) => index !== count));
    setSearchInput((prev) => prev.filter((_, index) => index !== count));

    // Update selectFieldCount - regenerate indices based on remaining fields
    setSelectFieldCount((prev) => {
      const filtered = prev.filter((_, index) => index !== count);
      return filtered.map((_, index) => index); // Reindex from 0
    });

    // Helper function to reindex object states
    const reindexObjectState = (prevState) => {
      const newState = {};
      let newIndex = 0;

      Object.keys(prevState)
        .map((key) => parseInt(key, 10))
        .sort((a, b) => a - b)
        .forEach((originalIndex) => {
          if (originalIndex !== count) {
            newState[newIndex] = prevState[originalIndex];
            newIndex += 1;
          }
        });

      return newState;
    };

    // Reindex all object-based states
    setFieldOptions(reindexObjectState);
    setFieldLoading(reindexObjectState);
    setFieldOpen(reindexObjectState);
    setSearchTimeouts(reindexObjectState);
  };
  const getGoals = async () => {
    await getCareerGoals({
      IP,
      collegeId,
      careerIds: CareerIds,
    })
      .unwrap()
      .then((payload) => setCareerGoalsData(payload))
      .catch((error) =>
        dispatch(
          setAlert({
            open: true,
            msg: error?.data?.msg
              ? `${error?.status}, ${error?.data?.msg}`
              : 'Something went wrong',
          })
        )
      );
  };
  const getCareersData = async () => {
    await getCareers({
      IP,
      collegeId,
    })
      .unwrap()
      .then((payload) => setCareersData(payload))
      .catch((error) =>
        dispatch(
          setAlert({
            open: true,
            msg: error?.data?.msg
              ? `${error?.status}, ${error?.data?.msg}`
              : 'Something went wrong',
          })
        )
      );
  };
  const token = Cookies.get('feToken');
  useEffect(() => {
    const payload = {
      isRegion: false,
      collegeId: clgDetails?._id,
    };
    if (token && clgDetails?._id) {
      dispatch(getCareerHistory(payload)).then((response) => {
        if (get(response, 'payload.data.currentCareerIds')) {
          const formattedData = get(response, 'payload.data.currentCareerIds')?.map((career) => ({
            label: career?.title,
            value: career?._id,
          }));
          dispatch(addCareerHistory(formattedData));
        }
        if (get(response, 'payload.data.compareCareerIds')) {
          const formattedData = get(response, 'payload.data.compareCareerIds')?.map(
            (career) => career?._id
          );
          dispatch(setGlobalCompare(formattedData));
        }
      });
    }
  }, [token, clgDetails?._id, params?.cg_name, dispatch, navigate]);
  const performSearch = (inputValue, fieldIndex) => {
    // Clear existing timeout for this field
    if (searchTimeouts[fieldIndex]) {
      clearTimeout(searchTimeouts[fieldIndex]);
    }

    const timeoutId = setTimeout(() => {
      setFieldLoading((prev) => ({ ...prev, [fieldIndex]: true }));
      getNestedCareers({ searchTerm: inputValue })
        .unwrap()
        .then((payload) => {
          setFieldOptions((prev) => ({ ...prev, [fieldIndex]: payload || [] }));
        })
        .catch(() => {
          setFieldOptions((prev) => ({ ...prev, [fieldIndex]: [] }));
        })
        .finally(() => {
          setFieldLoading((prev) => ({ ...prev, [fieldIndex]: false }));
        });
    }, 300);

    setSearchTimeouts((prev) => ({ ...prev, [fieldIndex]: timeoutId }));
  };

  const handleInputFocus = (fieldIndex) => {
    // Only clear and reload if the field doesn't have a selected value
    const hasSelectedValue = currentRole[fieldIndex] && currentRole[fieldIndex].value;
    if (!hasSelectedValue) {
      // Clear options and close dropdown when input is focused
      setFieldOptions((prev) => ({ ...prev, [fieldIndex]: [] }));
      setFieldOpen((prev) => ({ ...prev, [fieldIndex]: false }));
      // Clear search input for this field
      setSearchInput((prev) => {
        const updated = [...prev];
        updated[fieldIndex] = '';
        return updated;
      });
      // Small delay to ensure dropdown closes before loading new data
      setTimeout(() => {
        performSearch('', fieldIndex);
      }, 100);
    }
  };

  const handleSearchInputChange = (fieldIndex) => (event, value, reason) => {
    setSearchInput((prev) => {
      const updated = [...prev];
      updated[fieldIndex] = value;
      return updated;
    });

    // Show loading immediately when user types
    if (reason === 'input') {
      setFieldLoading((prev) => ({ ...prev, [fieldIndex]: true }));
      performSearch(value, fieldIndex);
    }

    // If cleared, also clear the selected value
    if (reason === 'clear') {
      setCurrentRole((prev) => {
        const updated = [...prev];
        updated[fieldIndex] = undefined;
        return updated;
      });
      setFieldLoading((prev) => ({ ...prev, [fieldIndex]: false }));
      // Clear search results when input is cleared
      setFieldOptions((prev) => ({ ...prev, [fieldIndex]: [] }));
    }
  };

  const handleClickUpskill = () => {
    if (params?.rg_name) {
      navigate(`/region/${params.rg_name}/upskill/skilldar`);
    } else {
      navigate(`/${params.cg_name}/upskill/skilldar`);
    }
    analytics({
      IP,
      collegeId,
      event: 'UPSKILL_CLICK',
      from: 'career-history',
    });
    dispatch(addCareerHistory(currentRole));
    dispatch(addCareerGoal(careerGoal));
  };
  const handleClickReskill = () => {
    if (currentRole && currentRole.length > 0) {
      analytics({
        IP,
        collegeId,
        event: 'RESKILL_CLICK',
        from: 'career-history',
      });
      dispatch(addCareerHistory(currentRole));
      if (params?.rg_name) {
        navigate(`/region/${params.rg_name}/reskill/skilldar`);
      } else {
        navigate(`/${params.cg_name}/reskill/skilldar`);
      }
    } else {
      setShowError(true);
    }
  };
  const prevStep = () => {
    setInnerStepCount((prev) => prev - 1);
    setSelectedRole('');
  };
  const nextStep = () => {
    setInnerStepCount((prev) => prev + 1);
  };

  useEffect(() => {
    if (CareerHistoryState?.length > 0) {
      setCurrentRole(CareerHistoryState);
      let newCount = selectFieldCount;
      for (let i = 0; i < CareerHistoryState.length - 1; i += 1) {
        if (CareerHistoryState.length <= 1) {
          newCount = [0];
        } else {
          newCount = [...newCount, newCount[i] + 1];
        }
      }
      setSelectFieldCount(newCount);
    }
  }, [CareerHistoryState]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (CareerGoalState) {
      setCareerGoal(CareerGoalState);
    }
  }, [CareerGoalState]);

  useEffect(() => {
    if (currentRole && currentRole.length > 0 && innerStepCount === 2) {
      getGoals();
    }
  }, [innerStepCount]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (careersData && careersData?.length > 0) {
      let newState = [];
      newState = careersData.map((item) => ({ value: item._id, label: item.title }));
      setCareerOptions(newState);
    }
  }, [careersData]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (careerGoalsData) {
      let newState = [];
      newState = careerGoalsData.map((item) => ({ value: item._id, label: item.title }));
      setCareerGoals(newState);
    }
  }, [careerGoalsData]);

  // Cleanup timeouts on unmount
  useEffect(
    () => () => {
      Object.values(searchTimeouts).forEach((timeoutId) => {
        if (timeoutId) clearTimeout(timeoutId);
      });
    },
    [searchTimeouts]
  );
  const getPaddingLeft = (group) => {
    switch (group) {
      case 'career':
        return '16px';
      case 'specialised_role':
        return '32px';
      default:
        return '8px';
    }
  };

  // ...existing code...

  const autocompleteSx = {
    minWidth: '500px !important', // <-- Increased width
    maxWidth: '100%',
    '& .MuiInputBase-root': {
      background: '#fff',
      borderRadius: '8px',
      fontSize: '1rem',
      minHeight: '44px',
    },
    '& .MuiInputBase-input': {
      fontSize: '1rem',
      color: '#222',
      padding: '10px 14px',
    },
    '& .MuiAutocomplete-listbox': {
      fontSize: '0.95rem',
      padding: 0,
      maxHeight: '300px',
      overflowY: 'auto',
    },
    '& .MuiAutocomplete-option': {
      paddingTop: '8px',
      paddingBottom: '8px',
      paddingLeft: '12px',
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      borderBottom: '1px solid #f0f0f0',
    },
    '& .MuiAutocomplete-groupLabel': {
      fontSize: '0.9rem',
      fontWeight: 600,
      color: '#555',
      padding: '8px 12px 4px',
      backgroundColor: '#fafafa',
      position: 'sticky',
      top: 0,
      zIndex: 1,
    },
  };

  // ...rest of your
  const colorTheme = useTheme();

  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    {
      icon: (
        <ArcticonsEmojiSpiderWeb
          width={36}
          height={36}
          color={buttonColor || colorTheme.palette.primary.main}
        />
      ),
      label: 'Results',
    }, // Capitalized if it's a component
  ];

  return (
    <>
      <Helmet>
        <title>Enter Career History</title>
      </Helmet>
      <Box sx={{
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      }} className='page-content-wrapper career-history-page'>
        <Container>
          <Box sx={{ pt: 5 }} className="content">
            {/* <StepperComponent steps={CareerHistorySteps} activeStep={0}/> */}
            <RegionStepper
              steps={steps}
              activeStep={0} // Change to 1 to highlight "Results"
              buttonColor={buttonColor || colorTheme.palette.primary.main}
              buttonFontColor={buttonFontColor || 'white'}
            />
            <Box className="inner-step-wrapper">
              {!careersLoading ? (
                <>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100%',
                    }}
                  >
                    <Button
                      className="step-btn prev"
                      sx={{
                        overflow: 'hidden',
                        height: innerStepCount > 0 ? 'auto' : 0,
                        color: (theme) => buttonColor || theme.palette.secondary.main,
                      }}
                      onClick={prevStep}
                    >
                      <ArrowBackIosIcon />
                    </Button>
                    <Box
                      className="content-wrapper career"
                      sx={{
                        background: (theme) =>
                          isRegionData ? 'transparent' : theme.palette.primary.main,
                      }}
                    >
                      {innerStepCount === 0 && (
                        <>
                          <Box className="head">
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: ' center',
                              }}
                            >
                              <Typography variant="h2" ml="16px" sx={{
                                color: fontColor,
                                fontSize: '80px',
                                fontFamily: '"Merriweather", serif',
                                fontWeight: 900 // Black weight
                              }}>
                                What is your career history?
                              </Typography>
                            </Box>
                            <Typography sx={{ color: fontColor, fontSize: '36px', fontWeight: 700, fontFamily: '"Work Sans", sans-serif', marginTop: 1, marginBottom: 8 }}>
                              Select the closest match from our database, starting with your current
                              role or the one you held most recently if you are unemployed. When you
                              are finished, tap 'Continue'
                            </Typography>
                          </Box>
                          <Box className="fields">
                            {selectFieldCount?.map((count) => (
                              <Box className="field-wrapper input-wrapper" key={count}>
                                {/* <SelectComponent
                                    name="careerHistory"
                                    placeholder="Start typing to find a job title..."
                                    selectOptions={CareerOptions.filter(option => !(currentRole.length > 0 ? currentRole : CareerHistoryState).some(opt => opt.value === option.value))}
                                    value={currentRole[count]} handleChange={(e) => handleChange(count, e)}
                                    showError={showError} errorMessage='Please select atleast one career to identify your skills'
                                  /> */}
                                <Autocomplete
                                  key={`autocomplete-${count}`}
                                  freeSolo
                                  fullWidth
                                  sx={autocompleteSx}
                                  inputValue={searchInput[count] || ''}
                                  onInputChange={handleSearchInputChange(count)}
                                  open={fieldOpen[count] || false}
                                  onOpen={() =>
                                    setFieldOpen((prev) => ({ ...prev, [count]: true }))
                                  }
                                  onClose={() =>
                                    setFieldOpen((prev) => ({ ...prev, [count]: false }))
                                  }
                                  options={(fieldOptions[count] || [])
                                    .flatMap((broad) => [
                                      { ...broad, group: 'broad' },
                                      ...(broad.careers || []).flatMap((career) => [
                                        { ...career, group: 'career', parent: broad.title },
                                        ...(career.specialisedRoles || []).map((role) => ({
                                          ...role,
                                          group: 'specialised_role',
                                          parent: career.title,
                                        })),
                                      ]),
                                    ])
                                    .filter(
                                      (option) =>
                                        !currentRole.some(
                                          (role) => role && role.value === option._id
                                        )
                                    )}
                                  groupBy={(option) => {
                                    if (option.group === 'broad') return 'Broad Career';
                                    if (option.group === 'career') return 'Career';
                                    return 'Specialised Role';
                                  }}
                                  getOptionLabel={(option) => option.title || option?.label || ''}
                                  renderInput={(param) => (
                                    <TextField
                                      {...param}
                                      label="Start typing to find a job title..."
                                      variant="filled"
                                      onFocus={() => handleInputFocus(count)}
                                      sx={{
                                        background: '#fff',
                                        borderRadius: '8px',
                                        fontSize: '1rem',
                                        input: { color: '#222', fontSize: '1rem' },
                                      }}
                                      InputProps={{
                                        ...param.InputProps,
                                        endAdornment: (
                                          <>
                                            <Box
                                              sx={{
                                                backgroundColor: buttonColor, // Yellow
                                                borderTopRightRadius: '8px',
                                                borderBottomRightRadius: '8px',
                                                width: '56px',
                                                height: '100%',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                position: 'absolute',
                                                right: 0,
                                                top: 0,
                                                bottom: 0,
                                                zIndex: 1,
                                                pointerEvents: 'none', // Keeps input clickable
                                              }}
                                            >
                                              {fieldLoading[count] ? (<CircularProgress color="inherit" size={20} />) : (<ExpandMoreIcon sx={{ color: '#000', fontSize: '40px' }} />)}
                                            </Box>
                                            {param.InputProps.endAdornment}
                                          </>
                                        ),
                                      }}
                                    />
                                  )}
                                  renderOption={(props, option) => (
                                    <li
                                      {...props}
                                      style={{
                                        paddingLeft: getPaddingLeft(option.group),
                                        fontSize: '0.95rem',
                                        color: '#222',
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'flex-start',
                                      }}
                                    >
                                      <span
                                        style={{
                                          fontWeight: option.group === 'broad' ? 600 : 400,
                                          fontSize:
                                            option.group === 'broad' ? '1.05rem' : '0.95rem',
                                        }}
                                      >
                                        {option.title}
                                      </span>
                                      {option.parent && (
                                        <Typography
                                          variant="caption"
                                          sx={{ mt: 0.5, color: '#888', fontSize: '0.8rem' }}
                                        >
                                          {option.parent}
                                        </Typography>
                                      )}
                                    </li>
                                  )}
                                  onChange={(e, value) => {
                                    if (value)
                                      handleChange(count, { value: value._id, label: value.title });
                                  }}
                                  value={currentRole[count] || null}
                                  isOptionEqualToValue={(option, value) =>
                                    option._id === value?._id || option._id === value?.value
                                  }
                                  componentsProps={{
                                    clearIndicator: {
                                      sx: {
                                        display: currentRole.length > 1 ? 'none' : 'flex',
                                      },
                                    },
                                  }}
                                />
                                {currentRole?.length > 1 && currentRole[count] ? (
                                  <Button
                                    className="icon-btn less"
                                    onClick={() => removeField(count)}
                                  >
                                    <span style={{ border: '2px solid #fff', color: '#fff' }}>
                                      -
                                    </span>
                                  </Button>
                                ) : (
                                  ''
                                )}
                              </Box>
                            ))}
                            {currentRole?.length > 0 &&
                              currentRole[selectFieldCount.length - 1]?.value ? (
                              <Box sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', pt: 5 }} className="addField-btn-wrapper">
                                <Button
                                  fullWidth
                                  sx={{ justifyContent: 'flex-start' }}
                                  className="icon-btn noUppercase addField"
                                  onClick={addField}
                                >
                                  <span
                                    style={{
                                      backgroundColor: buttonColor || 'transparent',
                                      color: buttonFontColor || 'white',
                                      width: 60, height: 60
                                    }}
                                  >
                                    +
                                  </span>
                                  <Typography
                                    sx={{ color: (theme) => fontColor || theme?.primary?.white, fontWeight: 700 }}
                                  >
                                    Add another role
                                  </Typography>
                                </Button>
                                <Button
                                  fullWidth
                                  sx={{
                                    backgroundColor: (theme) =>
                                      `${buttonColor || theme.palette.secondary.main} !important`,
                                    borderRadius: '8px',
                                    color: buttonFontColor || 'white',
                                    p: 2,
                                    mb: 2,
                                  }}
                                  // onClick={nextStep}
                                  onClick={handleClickReskill}
                                  disabled={
                                    innerStepCount === 1 && !selectedRole
                                      ? disabledBtn.true
                                      : disabledBtn.false
                                  }
                                >
                                  Continue
                                </Button>
                              </Box>
                            ) : (
                              ''
                            )}
                          </Box>
                        </>
                      )}
                      {innerStepCount === 1 && (
                        <>
                          <Typography variant="h2" color="primary.white">
                            What are you here to do today?
                          </Typography>
                          <Box className="btn-wrapper role-select">
                            <Button
                              className="btn"
                              aria-describedby="career-goal"
                              sx={{
                                backgroundColor: (theme) =>
                                  selectedRole === 'upskill' &&
                                  `${buttonColor || theme.palette.secondary.main} !important`,
                                border: (theme) =>
                                  selectedRole === 'upskill'
                                    ? `2px solid ${theme.palette.secondary.main}`
                                    : '2px solid #fff',
                              }}
                              onClick={() => setSelectedRole('upskill')}
                            >
                              <Typography variant="button2" color="#fff">
                                UPSKILL
                              </Typography>
                            </Button>
                            <Button
                              className="btn"
                              aria-describedby="career-goal"
                              sx={{
                                backgroundColor: (theme) =>
                                  selectedRole === 'reskill' &&
                                  `${buttonColor || theme.palette.secondary.main} !important`,
                                border: (theme) =>
                                  selectedRole === 'reskill'
                                    ? `2px solid ${theme.palette.secondary.main}`
                                    : '2px solid #fff',
                              }}
                              onClick={() => setSelectedRole('reskill')}
                            >
                              <Typography variant="button2" color="#fff">
                                RESKILL
                              </Typography>
                            </Button>
                          </Box>
                          <Box className="step-content">
                            {selectedRole === 'upskill' && (
                              <Typography variant="body2" color="primary.white">
                                Level up my career and improve my skills
                              </Typography>
                            )}
                            {selectedRole === 'reskill' && (
                              <Typography variant="body2" color="primary.white">
                                Change up my career and transfer my skills
                              </Typography>
                            )}
                          </Box>
                        </>
                      )}
                      {innerStepCount === 2 && (
                        <>
                          <Typography variant="h2" color="primary.white">
                            Do you have a career goal?
                          </Typography>
                          <Typography variant="capitalize" color="primary.white">
                            Optional
                          </Typography>
                          <Box className="step-content-wrapper">
                            <Typography variant="bold" color="primary.white">
                              Are you aiming for a particular role in the future?
                            </Typography>
                            <Typography variant="body1" color="primary.white">
                              If so, you can enter that here to see where upskilling may be required
                              to help you achieve it successfully. <br />
                              This UPSKILL search is limited to roles in the same sector as your
                              current role.
                              <br /> If the role you are aiming for is in a different sector you
                              should go back and select 'Reskill'
                            </Typography>
                            {!careersGoalsLoading ? (
                              <Box className="field-wrapper">
                                <SelectComponent
                                  name="careerGoal"
                                  placeholder="Start typing to find a job title..."
                                  selectOptions={CareerGoals}
                                  value={careerGoal}
                                  handleChange={(e) => setCareerGoal(e)}
                                />
                              </Box>
                            ) : (
                              <Box sx={{ display: 'flex', justifyContent: 'center', mt: '24px' }}>
                                <CircularProgress sx={{ color: '#fff !important' }} />
                              </Box>
                            )}
                          </Box>
                        </>
                      )}
                    </Box>
                    <Button
                      className="step-btn next"
                      disabled={
                        innerStepCount === 1 && !selectedRole ? disabledBtn.true : disabledBtn.false
                      }
                      sx={{
                        overflow: 'hidden',
                        height:
                          selectedRole !== 'reskill' &&
                            innerStepCount !== 2 &&
                            currentRole?.length > 0
                            ? 'auto'
                            : 0,
                        color: (theme) => buttonColor || theme.palette.secondary.main,
                      }}
                      onClick={handleClickReskill}
                    >
                      <ArrowForwardIosIcon />
                    </Button>
                  </Box>
                  <Box className="btn-wrapper career-history">
                    {selectedRole === 'reskill' && innerStepCount === 1 && (
                      <Button
                        className="btn noUppercase handleHover"
                        sx={{
                          backgroundColor: (theme) =>
                            `${buttonColor || theme.palette.secondary.main} !important`,
                        }}
                        aria-describedby="career-goal"
                        onClick={handleClickReskill}
                      >
                        <Typography
                          variant="button2"
                          color="primary.white"
                          sx={{ display: 'flex', alignItems: 'center' }}
                        >
                          Analyse my skills{' '}
                          <ArrowForwardIosRoundedIcon fontSize="20px" sx={{ marginLeft: '20px' }} />
                        </Typography>
                      </Button>
                    )}
                    {innerStepCount === 2 && (
                      <Button
                        className="btn noUppercase handleHover"
                        sx={{
                          backgroundColor: (theme) =>
                            `${buttonColor || theme.palette.secondary.main} !important`,
                        }}
                        aria-describedby="career-goal"
                        onClick={handleClickUpskill}
                      >
                        <Typography
                          variant="button2"
                          color="primary.white"
                          sx={{ display: 'flex', alignItems: 'center' }}
                        >
                          Analyse my skills{' '}
                          <ArrowForwardIosRoundedIcon fontSize="20px" sx={{ marginLeft: '20px' }} />
                        </Typography>
                      </Button>
                    )}
                  </Box>
                </>
              ) : (
                <Box sx={{ position: 'absolute', top: '42%', left: '48%' }}>
                  <CircularProgress />
                </Box>
              )}
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
}
