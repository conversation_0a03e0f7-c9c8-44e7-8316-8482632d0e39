const mongoose = require('mongoose');
require('dotenv').config();

// Import services
const MessageProcessingService = require('../services/messageProcessingService');
const ModerationService = require('../services/moderationService');
const ChatGPTService = require('../services/chatGPTService');

// Import models for testing
const { ChatSession } = require('../models/chatbotModels');
const { College } = require('../models/college');

/**
 * Test script for Core Services
 * Verifies all services work together without affecting existing system
 */
const testCoreServices = async () => {
  console.log('🧪 Testing Core Services Integration');
  console.log('===================================');

  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
    console.log('✅ Connected to MongoDB');

    // Initialize services
    console.log('\n🔧 Initializing services...');
    const messageService = new MessageProcessingService();
    const moderationService = new ModerationService();
    const chatGPTService = new ChatGPTService();
    console.log('✅ Services initialized');

    // Test 1: Moderation Service
    console.log('\n🛡️ Testing Moderation Service...');
    await testModerationService(moderationService);

    // Test 2: ChatGPT Service
    console.log('\n🤖 Testing ChatGPT Service...');
    await testChatGPTService(chatGPTService);

    // Test 3: Message Processing Service (requires test session)
    console.log('\n💬 Testing Message Processing Service...');
    await testMessageProcessingService(messageService);

    // Test 4: Service Integration
    console.log('\n🔗 Testing Service Integration...');
    await testServiceIntegration(messageService, moderationService, chatGPTService);

    console.log('\n🎉 All core services tests passed successfully!');
    console.log('✅ Services are ready for integration with existing chatbot system');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
};

/**
 * Test Moderation Service
 */
const testModerationService = async (moderationService) => {
  try {
    // Test 1: Clean message
    const cleanResult = await moderationService.moderateMessage('test-session', 'What computer science courses do you offer?');
    console.log(`✅ Clean message test: ${cleanResult.allowed ? 'PASSED' : 'FAILED'}`);

    // Test 2: Off-topic message
    const offTopicResult = await moderationService.moderateMessage('test-session', 'What do you think about politics?');
    console.log(`✅ Off-topic test: ${!offTopicResult.allowed ? 'PASSED' : 'FAILED'}`);

    // Test 3: Spam-like message
    const spamResult = await moderationService.moderateMessage('test-session', 'HELLO!!!! HELLO!!!! HELLO!!!!');
    console.log(`✅ Spam detection test: ${!spamResult.allowed ? 'PASSED' : 'FAILED'}`);

    // Test 4: Too short message
    const shortResult = await moderationService.moderateMessage('test-session', 'hi');
    console.log(`✅ Short message test: ${!shortResult.allowed ? 'PASSED' : 'FAILED'}`);

    console.log('✅ Moderation Service: All tests passed');

  } catch (error) {
    console.error('❌ Moderation Service test failed:', error.message);
  }
};

/**
 * Test ChatGPT Service
 */
const testChatGPTService = async (chatGPTService) => {
  try {
    // Test 1: Service status
    const status = chatGPTService.getServiceStatus();
    console.log(`✅ Service status: ${status.fineTunedModelAvailable ? 'Fine-tuned model available' : 'Using fallback model'}`);
    console.log(`   Fallback model: ${status.fallbackModel}`);

    // Test 2: Model connectivity test
    const testResult = await chatGPTService.testModel();
    console.log(`✅ Model connectivity test: ${testResult.success ? 'PASSED' : 'FAILED'}`);
    if (testResult.success) {
      console.log(`   Model used: ${testResult.modelUsed}`);
      console.log(`   Response type: ${testResult.responseType}`);
      console.log(`   Response length: ${testResult.responseLength} characters`);
    }

    // Test 3: Response generation
    const testMessages = [
      { role: 'system', content: 'You are a helpful educational assistant.' },
      { role: 'user', content: 'Tell me about computer science programs.' }
    ];

    const response = await chatGPTService.generateResponse(testMessages, {
      maxTokens: 100,
      temperature: 0.5
    });

    console.log(`✅ Response generation test: ${response.content ? 'PASSED' : 'FAILED'}`);
    console.log(`   Response preview: ${response.content.substring(0, 100)}...`);
    console.log(`   Model used: ${response.modelUsed}`);
    console.log(`   Response type: ${response.type}`);

    console.log('✅ ChatGPT Service: All tests passed');

  } catch (error) {
    console.error('❌ ChatGPT Service test failed:', error.message);
  }
};

/**
 * Test Message Processing Service
 */
const testMessageProcessingService = async (messageService) => {
  try {
    // Create a test session first
    const testCollege = await College.findOne({}).lean();
    if (!testCollege) {
      console.log('⚠️ No colleges found, skipping message processing test');
      return;
    }

    // Create test session
    const testSession = new ChatSession({
      collegeId: testCollege._id,
      ipAddress: '127.0.0.1',
      userAgent: 'Test Agent',
      status: 'active'
    });

    const savedSession = await testSession.save();
    console.log(`✅ Created test session: ${savedSession._id}`);

    // Test message processing
    const result = await messageService.processMessage(
      savedSession._id.toString(),
      'What programs do you offer in computer science?',
      { maxTokens: 150 }
    );

    console.log(`✅ Message processing test: ${result.success ? 'PASSED' : 'FAILED'}`);
    if (result.success) {
      console.log(`   Response preview: ${result.response.substring(0, 100)}...`);
      console.log(`   Response type: ${result.responseType}`);
      console.log(`   Model used: ${result.modelUsed}`);
    } else {
      console.log(`   Error: ${result.error}`);
    }

    // Test conversation history
    const history = await messageService.getConversationHistory(savedSession._id.toString());
    console.log(`✅ Conversation history test: ${history.length > 0 ? 'PASSED' : 'FAILED'}`);
    console.log(`   Messages in history: ${history.length}`);

    // Cleanup test session
    await ChatSession.findByIdAndDelete(savedSession._id);
    console.log('✅ Cleaned up test session');

    console.log('✅ Message Processing Service: All tests passed');

  } catch (error) {
    console.error('❌ Message Processing Service test failed:', error.message);
  }
};

/**
 * Test Service Integration
 */
const testServiceIntegration = async (messageService, moderationService, chatGPTService) => {
  try {
    console.log('Testing how services work together...');

    // Test 1: Moderation + ChatGPT flow
    const testMessage = 'What are the admission requirements?';
    
    // Step 1: Moderate message
    const moderationResult = await moderationService.moderateMessage('integration-test', testMessage);
    console.log(`✅ Integration moderation: ${moderationResult.allowed ? 'PASSED' : 'FAILED'}`);

    if (moderationResult.allowed) {
      // Step 2: Generate response
      const messages = [
        { role: 'system', content: 'You are a helpful educational assistant.' },
        { role: 'user', content: testMessage }
      ];

      const response = await chatGPTService.generateResponse(messages);
      console.log(`✅ Integration response: ${response.content ? 'PASSED' : 'FAILED'}`);
      console.log(`   Integration flow completed successfully`);
    }

    // Test 2: Service status checks
    const chatGPTStatus = chatGPTService.getServiceStatus();
    console.log(`✅ Service status integration: PASSED`);
    console.log(`   Fine-tuned model available: ${chatGPTStatus.fineTunedModelAvailable}`);

    console.log('✅ Service Integration: All tests passed');

  } catch (error) {
    console.error('❌ Service Integration test failed:', error.message);
  }
};

// Run test if called directly
if (require.main === module) {
  testCoreServices();
}

module.exports = testCoreServices;
