import { Box, Button, CircularProgress, Container, Grid, Typography } from '@mui/material';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import AuthWrapper from 'src/components/AuthWrapper';
import { useParams } from 'react-router';
import { useDispatch, useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import AiVideo from 'src/components/player/AiVideo';
import pageBGImage from 'src/assets/images/new-bg.png';
import main_logo from 'src/assets/images/thinkskill.png';
import { Ai_Video_Url } from 'src/config-global';
import { Link } from 'react-router-dom';
import headerImage from 'src/assets/images/header-image.jpg';
import switchUpImage from 'src/assets/images/switch-up.jpg';
// import { isEmpty } from 'lodash';
import { isEmpty } from 'lodash';
import { getRegionBySlug } from './CareerHistory/CareerHistorySlice';

const LandingPage = () => {
  const { rg_name } = useParams();
  // console.log(rg_name)
  const regions = useSelector((state) => state.careerHistory);
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);
  const dispatch = useDispatch();

  useEffect(() => {
    setLoading(true);
    dispatch(getRegionBySlug(rg_name));
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(data)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, data]);

  const buttonColor = data?.button?.bgColor;
  const buttonFontColor = data?.button?.color;
  const logo = data?.logo || main_logo;
  const partnerLogos = data?.partnerLogos || [];
  const primaryColor = data?.primaryColor || '';
  const fontColor = data?.fontColor || '';
  const secondaryColor = data?.secondaryColor || '';
  const bgImage = data?.bgImage || '';

  // console.log(data)

  if (loading) {
    return (
      <Box minHeight="80vh" display="flex" alignItems="center" justifyContent="center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <>
      <AuthWrapper title="Landing-Page">
        <Box className="page-content-wrapper">
          {/* <Container maxWidth="lg"> */}
          {/* Logo at the top */}
          {/* <Box display="flex" justifyContent="center" alignItems="center" mt={2} mb={2}>
            <img
              src={logo}
              alt="Region Logo"
              style={{ height: 80, objectFit: 'contain', borderRadius: 8, background: '#fff', padding: 8 }}
            />
          </Box> */}
          <Box display="flex" justifyContent="center" alignItems="center" mt={0}>
            <img
              src={headerImage}
              alt="Region Logo"
              style={{ objectFit: 'contain', borderRadius: 8, background: '#fff' }}
            />
          </Box>
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            py={4}
            sx={{
              width: '100%',
              // backgroundImage: `url(${pageBGImage})`,
              backgroundImage: `url(${bgImage})`,
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
              backgroundAttachment: 'fixed',
            }}
          >
            <Container maxWidth="xl">
              <Box pb={5} display="flex" flexDirection="column" alignItems="center">
                <Typography variant="h2" sx={{
                  fontSize: '80px',
                  fontFamily: '"Merriweather", serif',
                  fontWeight: 900, // Black weight
                  color: fontColor,
                  mt: 2
                }}>
                  <b>Welcome to Think Skills.</b>
                </Typography>
                <Typography sx={{ mb: 4, color: fontColor, fontSize: '18px !important', fontWeight: 700, fontFamily: '"Work Sans", sans-serif' }}>
                  Ever to Excel
                </Typography>
                <Typography sx={{ mb: 2, color: fontColor }} component="p">
                  We will help you find the best career options to suit your skills, and the best
                  college courses to help you achieve your goals. First we need to identify your
                  employability skills. <br /> To do this we will take a few minutes to understand
                  your career history.
                </Typography>
                <Button
                  className="linkBtn"
                  sx={{
                    backgroundColor: buttonColor,
                    mb: 2,
                    borderRadius: '8px',
                    p: '20px',
                    my: 3,
                    // width: '100px',
                    '&:hover': {
                      backgroundColor: buttonColor,
                      opacity: 0.9,
                    },
                  }}
                >
                  <Link style={{ color: buttonFontColor }} to={`/region/${rg_name}/career-history`}>
                    Get Started
                  </Link>
                </Button>
                <Grid columnSpacing={4} container width="100%" mt={3}>
                  {/* Left Column */}
                  <Grid item md={7}>
                    <Box sx={{ width: '100%', bgcolor: '#ffffff', pt: 4 }}>
                      <Typography variant="h6" fontWeight="bold" align="center" gutterBottom>
                        Transferable skills
                      </Typography>
                      <Typography
                        variant="subtitle1"
                        fontWeight="bold"
                        align="center"
                        sx={{ fontSize: '16px !important', mt: 2 }}
                      >
                        Build skills that employers are looking for – communication,
                        problem-solving, and adaptability.
                        Learn how these skills are developed through real-world experiences and
                        academic courses.
                      </Typography>
                      <Typography
                        variant="body1"
                        align="center"
                        sx={{ mt: 2, fontSize: '16px !important' }}
                      >
                        College courses offer a foundation for many in-demand careers. Discover how
                        soft skills combined with technical knowledge can help you excel in fields
                        like healthcare, engineering, and digital marketing. College courses offer a
                        foundation for many in-demand careers. Discover how soft skills combined
                        with technical knowledge can help you excel in fields like healthcare,
                      </Typography>
                      {/* Image Placeholder */}
                      <Box
                        sx={{
                          width: '100%',
                          height: 416,
                          backgroundImage: `url(${switchUpImage})`,
                          backgroundSize: 'cover',
                          backgroundRepeat: 'no-repeat',
                          backgroundPosition: 'center',
                          mt: 4,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        {/* <Typography variant="body1">[Image Here]</Typography> */}
                      </Box>
                    </Box>
                  </Grid>
                  {/* Right Column */}
                  <Grid item md={5}>
                    <Box
                      sx={{ p: 2, textAlign: 'center', bgcolor: '#ffffff' }}
                    >
                      <Typography sx={{ fontWeight: 700 }} variant="body1" fontWeight="bold">
                        Already have an account?
                      </Typography>
                      <Button
                        to={`/region/${rg_name}/sign-in`}
                        component={Link}
                        variant="contained"
                        sx={{
                          mt: 1,
                          color: buttonFontColor,
                          backgroundColor: buttonColor,
                          '&:hover': {
                            borderColor: buttonColor,
                            backgroundColor: buttonColor,
                            color: buttonFontColor,
                          },
                        }}
                      >
                        Sign in
                      </Button>
                    </Box>
                    <Box sx={{ border: '2px solid #ffffff', color: '#fff', p: 2, mt: 2 }}>
                      <Typography
                        sx={{ fontSize: '16px !important', fontWeight: 600, color: fontColor }}
                        fontWeight="bold"
                      >
                        WLEP is working to create 25,000 jobs and contribute towards the delivery of
                        21,500 new homes by 2025
                      </Typography>
                    </Box>
                    <Box
                      sx={{
                        p: 2,
                        // border: '1px solid #ccc',
                        backgroundColor: '#ffffff',
                        mt: 2,
                        textAlign: 'center',
                        // color: fontColor,
                      }}
                    >
                      <Typography
                        sx={{ fontSize: '19px !important', fontWeight: 600 }}
                        variant="body2"
                        fontWeight="bold"
                      >
                        How does it work?
                      </Typography>
                      <Typography variant="body1" sx={{ mt: 1, fontSize: '16px !important' }}>
                        Explore pathways from college to career into real-world skills
                        Learn how college courses translate into real-world skills and job
                        opportunities.
                      </Typography>
                      {/* Video Placeholder */}
                      <Container>
                        <Box className="content">
                          <Box sx={{ my: 2 }}>
                            <AiVideo
                              url={`${Ai_Video_Url}landing-page.mp4`}
                              secondaryColor={secondaryColor}
                            />
                          </Box>
                        </Box>
                      </Container>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Container>
          </Box>
          {/* </Container> */}
          {/* Partner Logos in Footer */}
          {/* {partnerLogos.length > 0 && (
            <Box
              sx={{
                width: '100%',
                bgcolor: '#f8f8f8',
                py: 3,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                gap: 4,
                flexWrap: 'wrap',
                borderTop: '1px solid #eee',
              }}
            >
              {partnerLogos.map((partnerLogo, idx) => (
                <img
                  key={idx}
                  src={partnerLogo}
                  alt={`Partner Logo ${idx + 1}`}
                  style={{
                    height: 100,
                    objectFit: 'contain',
                    background: '#fff',
                    borderRadius: 6,
                    padding: 6,
                  }}
                />
              ))}
            </Box>
          )} */}
        </Box>
      </AuthWrapper>
    </>
  );

  // return (
  //   <>
  //     <AuthWrapper title="Landing-Page">
  //       <Box className="page-content-wrapper">
  //         <Container maxWidth="lg">
  //           <Box display="flex" flexDirection="column" alignItems="center">
  //             <Box display="flex" flexDirection="column" alignItems="center">
  //               <Typography sx={{ fontSize: '32px !important' }}>
  //                 <b>For Eagles, By Eagles</b>
  //               </Typography>
  //               <Typography sx={{ mb: 4, fontSize: '20px !important' }}>Ever to Excel</Typography>
  //               <Typography sx={{ mb: 2 }} component="p">
  //                 Boston College's Instagram has been ranked among the top university accounts in
  //                 higher education. See what sets us apart on BC Social.
  //               </Typography>
  //               <Typography sx={{ mb: 2 }} component="p">
  //                 The Common Data Set initiative is a collaborative effort among data providers in
  //                 the higher education community and publishers as represented by the College Board,
  //                 Peterson’s, and U.S. News & World Report.
  //               </Typography>
  //               {/* <Button
  //                 sx={{
  //                   backgroundColor: (theme) => `${theme.palette.primary.main} !important`,
  //                   borderRadius: '8px',
  //                   color: 'white',
  //                   p: 2,
  //                   mb: 2,
  //                 }}
  //                 // fullWidth
  //                 type="submit"
  //               >
  //                 Get Started
  //               </Button> */}
  //               <Button
  //                 className="linkBtn"
  //                 sx={{ backgroundColor: (theme) => `${theme.palette.secondary.main} !important`, mb: 2 }}
  //               >
  //                 <Link to={`/${rg_name}/career-history`}>
  //                   <Typography variant="button2" color="primary.white">
  //                     Get Started
  //                   </Typography>
  //                 </Link>
  //               </Button>
  //               <Grid columnSpacing={4} container width="100%">
  //                 {/* Left Column */}
  //                 <Grid item md={7}>
  //                   <Box sx={{ width: '100%', bgcolor: '#f3f3f3', p: 4 }}>
  //                     <Typography variant="h6" fontWeight="bold" align="center" gutterBottom>
  //                       TRANSFERABLE SKILLS
  //                     </Typography>
  //                     <Typography variant="subtitle1" fontWeight="bold" align="center" sx={{ fontSize: '14px !important', fontWeight: 600 }}>
  //                       Build skills that employers are looking for – communication,
  //                       problem-solving, and adaptability.
  //                     </Typography>
  //                     <Typography
  //                       variant="subtitle1"
  //                       fontWeight="bold"
  //                       align="center"
  //                       sx={{ fontSize: '14px !important', mt: 1, fontWeight: 600 }}
  //                     >
  //                       Learn how these skills are developed through real-world experiences and
  //                       academic courses.
  //                     </Typography>
  //                     <Typography variant="body1" align="center" sx={{ mt: 2, fontSize: '14px !important' }}>
  //                       College courses offer a foundation for many in-demand careers. Discover how
  //                       soft skills combined with technical knowledge can help you excel in fields
  //                       like healthcare, engineering, and digital marketing. College courses offer
  //                       a foundation for many in-demand careers. Discover how soft skills combined
  //                       with technical knowledge can help you excel in fields like healthcare,
  //                     </Typography>

  //                     {/* Image Placeholder */}
  //                     <Box
  //                       sx={{
  //                         width: '100%',
  //                         height: 324,
  //                         bgcolor: '#ccc',
  //                         mt: 4,
  //                         display: 'flex',
  //                         alignItems: 'center',
  //                         justifyContent: 'center',
  //                       }}
  //                     >
  //                       {/* Replace with actual image */}
  //                       <Typography variant="body1">[Image Here]</Typography>
  //                     </Box>
  //                   </Box>
  //                 </Grid>

  //                 {/* Right Column */}
  //                 <Grid item md={5}>
  //                   <Box sx={{ p: 2, border: '1px solid #ccc', textAlign: 'center' }}>
  //                     <Typography variant="body1" fontWeight="bold">
  //                       ALREADY HAVE AN ACCOUNT?
  //                     </Typography>
  //                     <Button to={`/${rg_name}/sign-in`} component={Link} variant="outlined" sx={{ mt: 1 }}>
  //                       Sign in
  //                     </Button>
  //                   </Box>

  //                   <Box sx={{ bgcolor: 'primary.main', color: '#fff', p: 2, mt: 2 }}>
  //                     <Typography sx={{ fontSize: '16px !important', fontWeight: 600 }} fontWeight="bold">
  //                       WLEP is working to create 25,000 jobs and contribute towards the delivery of
  //                       21,500 new homes by 2025
  //                     </Typography>
  //                   </Box>

  //                   <Box sx={{ p: 2, border: '1px solid #ccc', mt: 2, textAlign: 'center' }}>
  //                     <Typography sx={{ fontSize: '18px !important', fontWeight: 600 }} variant="body2" fontWeight="bold">
  //                       HOW DOES IT WORK?
  //                     </Typography>
  //                     <Typography variant="body1" fontWeight="bold" sx={{ mt: 1, fontSize: '14px !important', fontWeight: 600 }}>
  //                       Explore pathways from college to career into real-world skills
  //                     </Typography>
  //                     <Typography variant="body1" sx={{ mt: 1, fontSize: '14px !important' }}>
  //                       Learn how college courses translate into real-world skills and job
  //                       opportunities.
  //                     </Typography>

  //                     {/* Video Placeholder */}
  //                     {clgDetails ? (
  //                       <Container>
  //                         <Box className="content">
  //                           <Box sx={{ my: 1 }}>
  //                             <AiVideo url={`${Ai_Video_Url}landing-page.mp4`} />
  //                           </Box>
  //                         </Box>
  //                       </Container>
  //                     ) : (
  //                       <Box sx={{ position: 'absolute', top: '42%', left: '48%' }}>
  //                         <CircularProgress />
  //                       </Box>
  //                     )}
  //                     {/* <Box
  //                       sx={{
  //                         width: '100%',
  //                         height: 180,
  //                         bgcolor: '#ddd',
  //                         mt: 2,
  //                         display: 'flex',
  //                         alignItems: 'center',
  //                         justifyContent: 'center',
  //                         position: 'relative',
  //                       }}
  //                     >
  //                       <PlayCircleOutlineIcon sx={{ fontSize: 48, color: 'black' }} />
  //                     </Box> */}
  //                   </Box>
  //                 </Grid>
  //               </Grid>
  //             </Box>
  //           </Box>
  //         </Container>
  //       </Box>
  //     </AuthWrapper>
  //   </>
  // );
};

export default LandingPage;
