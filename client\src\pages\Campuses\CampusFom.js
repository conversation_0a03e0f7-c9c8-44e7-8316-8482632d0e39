import { Box, Button, Fade, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select, Stack, TextField, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch } from 'react-redux';
import PhoneInput from '../../components/PhoneInput';
import { addCampuse, postCampus } from './campusesSlice';
import { formButton, formStyle } from '../../utils/cssStyles';
import { campusValidationSchema } from '../../utils/validationSchemas';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';

const CampusForm = ({ Campuses, Groups, Colleges, setOpenModel, openModel }) => {

  const dispatch = useDispatch()
  const formik = useFormik({
    initialValues: {
      name: '',
      collegeName: '',
      groupName: '',
      campusAddress: '',
      campusWebsiteAddress: '',
      campusEmail: '',
      campusTelNumber: ''
    },
    onSubmit: (values) => {
      // const ID = Campuses[Campuses.length - 1].id + 1
      // dispatch(addCampuse({ id: ID, ...values }))
      const campus = {
        name: values.name,
        collegeId: values.collegeName,
        collegeName: Colleges.find(
          college => college?._id === values.collegeName
        )?.name,
        groupName: Groups.find(
          group => group?.id === values.groupName
        )?.Name,
      }
      dispatch(postCampus(campus))
        .then(response => {
          if (response?.payload?.data?.success === false) {
            // error occoured
            const errorMessage = response?.payload?.data?.msg
            console.log("error", errorMessage)
          }
          else if (response?.payload?.success) {
            // succesfully added campus
            // console.log("Campus added")
          }
        })
      handleClose()
    },
    validationSchema: campusValidationSchema
  })
  const handleClose = () => {
    setOpenModel(false)
  }

  const handlePhoneChange = (newValue, info) => {
    formik.setValues({
      ...formik.values,
      campusTelNumber: newValue
    })
  }

  return (
    <Fade in={openModel}>
      <Box >
        <form onSubmit={formik.handleSubmit}>
          <Grid container gap={2} >
            <Grid item xs={12} md={5.8} >
              <FormControl sx={{ minWidth: '100%' }}>
                <InputLabel id="group-label">Group</InputLabel>
                <Select
                  name='groupName'
                  labelId="group-label"
                  value={formik.values.groupName}
                  label="Group *"
                  onChange={formik.handleChange}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {Groups.map(group => (<MenuItem value={group.id} >{group.Name}</MenuItem>))}
                </Select>
                <FormHelperText>{""}</FormHelperText>
              </FormControl>
            </Grid>

            <Grid item xs={12} md={5.8} >
              <FormControl sx={{ minWidth: '100%' }}>
                <InputLabel id="college-label">College</InputLabel>
                <Select
                  name='collegeName'
                  labelId="college-label"
                  value={formik.values.collegeName}
                  label="College *"
                  onChange={formik.handleChange}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {Colleges.map(college => (<MenuItem value={college._id} >{college.name}</MenuItem>))}
                </Select>
                <FormHelperText>{""}</FormHelperText>
              </FormControl>
            </Grid>

            <Grid item xs={11.8} >
              <TextFIeldComponent
                sx={{ width: '100%' }}
                name='name'
                label="Campus Name"
                onBlur={formik.handleBlur}
                value={formik.values.name}
                onChange={formik.handleChange}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
              />
            </Grid>


            <Grid item xs={11.8} >
              <TextFIeldComponent
                sx={{ width: '100%' }}
                name='campusAddress'
                label="Campus Address"
                onBlur={formik.handleBlur}
                value={formik.values.campusAddress}
                onChange={formik.handleChange}
                error={formik.touched.campusAddress && Boolean(formik.errors.campusAddress)}
                helperText={formik.touched.campusAddress && formik.errors.campusAddress}
              />
            </Grid>
            <Grid item xs={11.8} >
              <TextFIeldComponent
                sx={{ width: '100%' }}
                name='campusWebsiteAddress'
                label="Website Address"
                onBlur={formik.handleBlur}
                value={formik.values.campusWebsiteAddress}
                onChange={formik.handleChange}
                error={formik.touched.campusWebsiteAddress && Boolean(formik.errors.campusWebsiteAddress)}
                helperText={formik.touched.campusWebsiteAddress && formik.errors.campusWebsiteAddress}
              />
            </Grid>
            <Grid item xs={12} md={5.8}>
              <TextFIeldComponent
                sx={{ width: '100%' }}
                name='campusEmail'
                label="Email"
                onBlur={formik.handleBlur}
                value={formik.values.campusEmail}
                onChange={formik.handleChange}
                error={formik.touched.campusEmail && Boolean(formik.errors.campusEmail)}
                helperText={formik.touched.campusEmail && formik.errors.campusEmail}
              />
            </Grid>
            <Grid item xs={12} md={5.8}>
              <PhoneInput
                sx={{ width: "100%" }}
                value={formik.values.campusTelNumber}
                name='campusTelNumber'
                label="Telephone Number"
                defaultCountry="GB"
                onChange={handlePhoneChange}
                onBlur={formik.handleBlur}
                error={formik.touched.campusTelNumber && Boolean(formik.errors.campusTelNumber)}
                helperText={formik.touched.campusTelNumber && formik.errors.campusTelNumber}
              />
            </Grid>
          </Grid>
          <Stack direction="row" justifyContent="flex-end">
            <Button
              type='submit'
              variant='contained'
              sx={formButton}>
              Add
            </Button>
          </Stack>
        </form>
      </Box>
    </Fade >
  )
}

export default CampusForm