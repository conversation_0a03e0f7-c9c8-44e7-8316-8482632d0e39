import { useState } from 'react';
// @mui
import { LoadingButton } from '@mui/lab';
import { Alert, Checkbox, FormHelperText, IconButton, InputAdornment, Link, Stack, TextField } from '@mui/material';
// components
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Iconify from '../../components/Iconify';
import { APP_ROUTER_BASE_URL } from '../../utils';

// ----------------------------------------------------------------------

export default function LoginForm({ formik }) {

  const [showPassword, setShowPassword] = useState(false);
  const loading = useSelector(state => state.auth.loading)
  const error = useSelector(state => state.auth.error)
  const navigate = useNavigate()
  return (
    <>
      <form onSubmit={formik.handleSubmit} >
        <Stack spacing={3}>
          {error &&
            <Alert severity='error'>
              {error?.message || "something went wrong"}
            </Alert>}
          <TextField
            name="email"
            label="Email address"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.email && Boolean(formik.errors.email)}
            helperText={formik.touched.email && formik.errors.email}
            InputLabelProps={{
              shrink: true,
            }}
          />

          <TextField
            name="password"
            label="Password"
            value={formik.values.password}
            type={showPassword ? 'text' : 'password'}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.password && Boolean(formik.errors.password)}
            helperText={formik.touched.password && formik.errors.password}
            InputLabelProps={{
              shrink: true,
            }}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={() => {
                    setShowPassword(!showPassword)
                  }} edge="end">
                    <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Stack>
        <FormHelperText sx={{ color: 'error.main', my: 2 }}>{error && error?.response?.data?.message}</FormHelperText>
        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ my: 2 }}>
          {/* <Checkbox name="remember" label="Remember me" /> */}
          <Link 
          onClick={()=>navigate(`${APP_ROUTER_BASE_URL}forgotpassword`)}
          variant="subtitle2"
           underline="hover">
            Forgot password?
          </Link>
        </Stack>

        <LoadingButton
          loading={loading}
          fullWidth
          size="large"
          type="submit"
          variant="contained"
          sx={{ mt: 3 }}
        >
          Login
        </LoadingButton>
      </form>
    </>
  );
}
