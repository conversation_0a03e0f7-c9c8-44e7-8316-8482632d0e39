const mongoose = require("mongoose");
const { messageResponse } = require("../helpers/commonHelper");
const { 
  SERVER_ERROR, 
  NOT_FOUND, 
  INVALID_MISSING,
  CUSTOM, 
} = require("../config/messages");

// Import chatbot models
const { ChatSession } = require("../models/chatbotModels");
const { ChatMessage } = require("../models/chatbotModels");
const { ChatViolation } = require("../models/chatbotModels");

// Import existing models for validation
const { College } = require("../models/college");

// Import new fine-tuning services (Phase 3 enhancement)
const MessageProcessingService = require('../services/messageProcessingService');
const ModerationService = require('../services/moderationService');
const ChatGPTService = require('../services/chatGPTService');
const { cacheService, CACHE_KEYS } = require('../services/cacheService');
const OpenAIFineTuningService = require('../services/openAIFineTuningService');
const FineTuningDataService = require('../services/fineTuningDataService');

// Initialize services (singleton pattern for performance)
let messageProcessingService = null;
let moderationService = null;
let chatGPTService = null;
let fineTuningService = null;
let fineTuningDataService = null;

const getServices = () => {
  if (!messageProcessingService) {
    messageProcessingService = new MessageProcessingService();
    moderationService = new ModerationService();
    chatGPTService = new ChatGPTService();
    fineTuningService = new OpenAIFineTuningService();
    fineTuningDataService = new FineTuningDataService();
  }
  return { messageProcessingService, moderationService, chatGPTService, fineTuningService, fineTuningDataService };
};

/**
 * Validate college ID and ensure it exists
 */
const validateCollege = async (collegeId) => {
  if (!collegeId) {
    return { isValid: false, message: "College ID is required" };
  }

  if (!mongoose.isValidObjectId(collegeId)) {
    return { isValid: false, message: "Invalid college ID format" };
  }

  const college = await College.findById(collegeId);
  if (!college) {
    return { isValid: false, message: "College not found" };
  }

  if (college.status !== 'active') {
    return { isValid: false, message: "College is not active" };
  }

  return { isValid: true, college };
};

/**
 * Get client IP address from request
 */
const getClientIP = (req) => {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         req.ip ||
         'unknown';
};

/**
 * Initialize a new chat session
 * POST /api/chatbot/session/init
 * Body: { collegeId, userAgent? }
 */
const initializeSession = async (req, res) => {
  try {
    const { collegeId, userAgent } = req.body;

    // Validate college
    const collegeValidation = await validateCollege(collegeId);
    if (!collegeValidation.isValid) {
      return messageResponse(INVALID_MISSING, collegeValidation.message, false, 400, null, res);
    }

    // Get client IP
    const ipAddress = getClientIP(req);

    // Create new session
    const newSession = new ChatSession({
      collegeId: collegeValidation.college._id,
      ipAddress,
      userAgent: userAgent || req.headers['user-agent'] || '',
      status: 'active',
      violationCount: 0,
      messageCount: 0
    });

    const savedSession = await newSession.save();

    // Prepare response data
    const responseData = {
      sessionId: savedSession._id,
      collegeInfo: {
        id: collegeValidation.college._id,
        name: collegeValidation.college.name,
        slug: collegeValidation.college.slug
      },
      status: 'active',
      createdAt: savedSession.createdAt
    };

    return messageResponse(
      null,
      "",
      true,
      201,
      responseData,
      res
    );

  } catch (error) {
    console.error('Error initializing chat session:', error);
    return messageResponse(SERVER_ERROR, "Session", false, 500, null, res);
  }
};

/**
 * Get session information
 * GET /api/chatbot/session/:sessionId
 */
const getSession = async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!mongoose.isValidObjectId(sessionId)) {
      return messageResponse(INVALID_MISSING, "Session ID", false, 400, null, res);
    }

    const session = await ChatSession.findById(sessionId).lean();

    if (!session) {
      return messageResponse(NOT_FOUND, "Session", false, 404, null, res);
    }

    // Get college info separately
    const college = await College.findById(session.collegeId).lean();
    if (!college) {
      return messageResponse(NOT_FOUND, "College", false, 404, null, res);
    }

    // Get message count for this session
    const messageCount = await ChatMessage.countDocuments({ sessionId });

    const responseData = {
      sessionId: session._id,
      collegeInfo: {
        id: college._id,
        name: college.name,
        slug: college.slug
      },
      status: session.status,
      messageCount,
      violationCount: session.violationCount,
      lastActivity: session.lastActivity,
      createdAt: session.createdAt
    };

    return messageResponse(
      null,
      "",
      true,
      200,
      responseData,
      res
    );

  } catch (error) {
    console.error('Error getting session:', error);
    return messageResponse(SERVER_ERROR, "Session", false, 500, null, res);
  }
};

/**
 * Get chat history for a session
 * GET /api/chatbot/session/:sessionId/history
 * Query: ?limit=20&page=0
 */
const getChatHistory = async (req, res) => {
  try {
    const { sessionId } = req.params;
    const { limit = 50, page = 1 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    if (!mongoose.isValidObjectId(sessionId)) {
      return messageResponse(INVALID_MISSING, "Session ID", false, 400, null, res);
    }

    // Verify session exists
    const session = await ChatSession.findById(sessionId);
    if (!session) {
      return messageResponse(NOT_FOUND, "Session", false, 404, null, res);
    }

    // Get messages with pagination
    const messages = await ChatMessage.find({ sessionId })
      .select('messageType content moderationFlags responseType createdAt')
      .sort({ createdAt: 1 })
      .skip(skip)
      .limit(parseInt(limit))
      .lean();

    // Get total count
    const totalMessages = await ChatMessage.countDocuments({ sessionId });

    const responseData = {
      sessionId,
      messages,
      pagination: {
        total: totalMessages,
        limit: parseInt(limit),
        page: parseInt(page),
        hasMore: (parseInt(skip) + parseInt(limit)) < totalMessages
      }
    };

    return messageResponse(
      null,
      "",
      true,
      200,
      responseData,
      res
    );

  } catch (error) {
    console.error('Error getting chat history:', error);
    return messageResponse(SERVER_ERROR, "History", false, 500, null, res);
  }
};

/**
 * End a chat session
 * POST /api/chatbot/session/:sessionId/end
 */
const endSession = async (req, res) => {
  try {
    const { sessionId } = req.params;

    if (!mongoose.isValidObjectId(sessionId)) {
      return messageResponse(INVALID_MISSING, "Session ID", false, 400, null, res);
    }

    const session = await ChatSession.findById(sessionId);
    if (!session) {
      return messageResponse(NOT_FOUND, "Session", false, 404, null, res);
    }

    // Update session status
    session.status = 'ended';
    session.lastActivity = new Date();
    await session.save();

    // Get session summary
    const messageCount = await ChatMessage.countDocuments({ sessionId });
    const violationCount = await ChatViolation.countDocuments({ sessionId });

    const responseData = {
      sessionId,
      status: 'ended',
      summary: {
        totalMessages: messageCount,
        totalViolations: violationCount,
        duration: new Date() - session.createdAt
      },
      endedAt: session.lastActivity
    };

    return messageResponse(
      null,
      "",
      true,
      200,
      responseData,
      res
    );

  } catch (error) {
    console.error('Error ending session:', error);
    return messageResponse(SERVER_ERROR, "Session", false, 500, null, res);
  }
};

/**
 * Process user message and generate AI response
 * POST /api/chatbot/message
 * Body: { sessionId, message, options? }
 */
const processMessage = async (req, res) => {
  try {
    const { sessionId, message, options = {} } = req.body;

    // Validate required fields
    if (!sessionId || !message) {
      return messageResponse(INVALID_MISSING, "Session ID and message", false, 400, null, res);
    }

    if(message.length === 1) {
      return messageResponse(CUSTOM, "It seems like your message got cut off. How can I assist you further?", false, 400, null, res);
    }

    if (!mongoose.isValidObjectId(sessionId)) {
      return messageResponse(INVALID_MISSING, "Invalid session ID format", false, 400, null, res);
    }

    // Validate session exists and is active
    const session = await ChatSession.findById(sessionId);
    if (!session) {
      return messageResponse(NOT_FOUND, "Session not found", false, 404, null, res);
    }

    if (session.status !== 'active') {
      return messageResponse(INVALID_MISSING, "Session is not active", false, 400, null, res);
    }

    // Get services
    const { messageProcessingService, moderationService } = getServices();

    // Step 1: Content moderation
    console.log(`🛡️ Moderating message for session: ${sessionId}`);
    const moderationResult = await moderationService.moderateMessage(sessionId, message, {
      strictMode: options.strictMode || false,
      logViolations: true
    });

    if (!moderationResult.allowed) {
      // Save violation message
      const violationMessage = new ChatMessage({
        sessionId,
        messageType: 'system',
        content: moderationResult.message,
        responseType: 'violation',
        moderationFlags: moderationResult.violations.map(v => v.type)
      });
      await violationMessage.save();

      return messageResponse(
        null,
        "Message moderated",
        true,
        200,
        {
          sessionId,
          response: moderationResult.message,
          responseType: 'violation',
          violations: moderationResult.violations,
          blocked: moderationResult.blocked || false
        },
        res
      );
    }

    // Step 2: Process message with AI
    console.log(`💬 Processing message with AI for session: ${sessionId}`);
    const processingResult = await messageProcessingService.processMessage(sessionId, message, {
      includeContext: options.includeContext !== false,
      maxTokens: options.maxTokens || 500,
      temperature: options.temperature || 0.7
    });

    if (!processingResult.success) {
      return messageResponse(
        SERVER_ERROR,
        "Failed to process message",
        false,
        500,
        {
          sessionId,
          error: processingResult.error,
          response: processingResult.response,
          responseType: 'error'
        },
        res
      );
    }

    // Step 3: Return successful response
    const responseData = {
      sessionId,
      response: processingResult.response,
      responseType: processingResult.responseType,
      modelUsed: processingResult.modelUsed,
      usage: processingResult.usage,
      timestamp: new Date()
    };

    return messageResponse(
      null,
      "Message processed successfully",
      true,
      200,
      responseData,
      res
    );

  } catch (error) {
    console.error('❌ Error processing message:', error);
    return messageResponse(SERVER_ERROR, "Message processing failed", false, 500, null, res);
  }
};

/**
 * Get chatbot system status and statistics
 * GET /api/chatbot/admin/status
 */
const getStats = async (req, res) => {
  try {
    const { chatGPTService, fineTuningService } = getServices();

    // Get service status
    const chatGPTStatus = chatGPTService.getServiceStatus();

    // Get deployed models
    const deployedModels = fineTuningService.listDeployedModels();

    // Get recent session statistics
    const totalSessions = await ChatSession.countDocuments();
    const activeSessions = await ChatSession.countDocuments({ status: 'active' });
    const flaggedSessions = await ChatSession.countDocuments({ status: 'flagged' });

    // Get message statistics
    const totalMessages = await ChatMessage.countDocuments();
    const recentMessages = await ChatMessage.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    });

    // Get violation statistics
    const totalViolations = await ChatViolation.countDocuments();
    const recentViolations = await ChatViolation.countDocuments({
      createdAt: { $gte: new Date(Date.now() - 24 * 60 * 60 * 1000) } // Last 24 hours
    });

    const responseData = {
      system: {
        status: 'operational',
        timestamp: new Date(),
        fineTunedModelAvailable: chatGPTStatus.fineTunedModelAvailable,
        fallbackModel: chatGPTStatus.fallbackModel
      },
      models: {
        deployed: deployedModels,
        configuration: chatGPTStatus.configuration
      },
      statistics: {
        sessions: {
          total: totalSessions,
          active: activeSessions,
          flagged: flaggedSessions
        },
        messages: {
          total: totalMessages,
          last24Hours: recentMessages
        },
        violations: {
          total: totalViolations,
          last24Hours: recentViolations
        }
      }
    };

    return messageResponse(null, "", true, 200, responseData, res);

  } catch (error) {
    console.error('❌ Error getting system status:', error);
    return messageResponse(SERVER_ERROR, "Failed to get system status", false, 500, null, res);
  }
};

module.exports = {
  initializeSession,
  getSession,
  getChatHistory,
  endSession,
  processMessage,

  // Admin endpoints
  getStats,
};
