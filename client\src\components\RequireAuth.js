
import jwtDecode from "jwt-decode";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import Cookies from 'universal-cookie';
import { APP_ROUTER_BASE_URL } from "../utils";
import RolewiseNavigation from "./RolewiseNavigation";

const RequireAuth = ({ allowedRoles }) => {
    const cookies = new Cookies();
    const location = useLocation();
    const jwtToken = cookies.get("token")
    const Role = jwtToken && jwtDecode(jwtToken)
    const role = String(Role?.role) || "5"
    // console.log({role,allowedRoles})
    return (
        allowedRoles?.includes(role)
            ? <Outlet />
            : <RolewiseNavigation role={role} />
        // : <Navigate to={`${APP_ROUTER_BASE_URL}login`} state={{ from: location }} replace />
    )
}

export default RequireAuth;