# Horizon AI

## Introduction
- Overview of Horizon AI
  - Mission and Vision
    > Horizon AI is a comprehensive career guidance and educational platform that leverages artificial intelligence to connect students with educational and career opportunities. Our mission is to empower students and institutions with data-driven insights for better career and educational decisions.
  
  - Core Capabilities
    - Career path analysis and recommendations
    - Educational institution management
    - Skills and abilities assessment
    - Analytics and insights generation
    - Multi-tenant architecture supporting various educational institutions
  
  - Technology Stack
    - Backend: Node.js with Express.js
    - Database: MongoDB
    - Authentication: JWT-based security
    - Frontend: React.js
    - API: RESTful architecture

- Key Features
  - AI Model Integration
    - Career path prediction and analysis
    - Skills matching algorithms
    - Educational pathway recommendations
  
  - Real-time Processing
    - Instant career insights
    - Live analytics dashboard
    - Real-time data processing
  
  - Scalable Architecture
    - Microservices-based design
    - Modular component structure
    - Horizontal scaling support
  
  - Security Features
    - JWT-based authentication
    - Role-based access control
    - Secure API endpoints
    - Data encryption

- System Requirements
  - Hardware Requirements
    - Minimum 4GB RAM
    - 2 CPU cores or more
    - 20GB storage space
  
  - Software Dependencies
    - Node.js 14.x or higher
    - MongoDB 4.x or higher
    - npm or yarn package manager
  
  - Network Requirements
    - Stable internet connection
    - Open ports: 8000 (API), 27017 (MongoDB)
  
## Installation
- Prerequisites
  - Required Software
    ```bash
    # Install Node.js (14.x or higher)
    # Install MongoDB (4.x or higher)
    # Install npm or yarn
    ```
  
  - Database Setup
    ```bash
    # MongoDB connection string required in environment variables
    MONGODB_URI=mongodb://localhost:27017/horizon-ai
    ```

- Step-by-step Installation Guide
  1. Clone the repository
     ```bash
     git clone [repository-url]
     cd horizon-ai
     ```
  
  2. Install dependencies
     ```bash
     npm install
     ```
  
  3. Set up environment variables
     ```bash
     cp .env.example .env
     # Edit .env with your configuration
     ```
  
  4. Start the server
     ```bash
     npm run start
     ```

## API Reference
- Authentication
  - API Keys
    > Authentication is handled via JWT tokens. Tokens must be included in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```

  - Authentication Endpoints
    ```
    POST /api/auth/checkToken      # Verify token validity
    POST /api/users/authenticate   # User login
    POST /api/users/forgotPassword # Password recovery
    POST /api/users/resetPassword  # Reset password
    ```

- User Management
  ```
  POST   /api/users/add              # Create new user (College Admin)
  GET    /api/users/get              # List users (College Admin)
  GET    /api/users/getById          # Get user details (Campus Admin)
  PUT    /api/users/update           # Update user (Campus Admin)
  DELETE /api/users/remove           # Delete user (College Admin)
  GET    /api/users/dashboard        # User dashboard data
  GET    /api/users/getAvailableUsers # List available users
  ```

- Educational Institution Management
  - College Groups
    ```
    POST   /api/collegeGroups/add     # Create college group (Super User)
    GET    /api/collegeGroups/get     # List college groups
    GET    /api/collegeGroups/getById # Get group details
    PUT    /api/collegeGroups/update  # Update group
    DELETE /api/collegeGroups/remove  # Delete group (Super User)
    ```
  
  - Colleges
    ```
    POST   /api/colleges/add          # Create college
    GET    /api/colleges/get          # List colleges
    GET    /api/colleges/getById      # Get college details
    PUT    /api/colleges/update       # Update college
    DELETE /api/colleges/remove       # Delete college
    GET    /api/colleges/permissions  # Get college permissions
    ```

  - Campuses
    ```
    POST   /api/campuses/add          # Create campus
    GET    /api/campuses/get          # List campuses
    GET    /api/campuses/getById      # Get campus details
    PUT    /api/campuses/update       # Update campus
    DELETE /api/campuses/remove       # Delete campus
    ```

  - Courses
    ```
    POST   /api/courses/add                    # Add single course
    POST   /api/courses/addBulk                # Bulk course import
    GET    /api/courses/get                    # List courses
    GET    /api/courses/getById                # Get course details
    PUT    /api/courses/update                 # Update course
    DELETE /api/courses/remove                 # Delete course
    DELETE /api/courses/deleteCoursesByCampus  # Bulk delete by campus
    POST   /api/courses/export                 # Export courses
    ```

- Career Management
  - Careers
    ```
    GET    /api/careers/get                # List careers
    GET    /api/careers/getById            # Get career details
    GET    /api/careers/getCareersByKeyword # Search careers
    GET    /api/careers/getCareerDetails   # Get detailed career info
    GET    /api/careers/getSkillsAbilities # Get career skills
    POST   /api/careers/add                # Add career (Super User)
    PUT    /api/careers/update             # Update career (Super User)
    DELETE /api/careers/remove             # Delete career (Super User)
    ```

  - Job Zones
    ```
    POST   /api/jobZones/createJobZones    # Create job zones
    ```

  - O*NET Careers
    ```
    POST   /api/oNetCareers/createONetCareers # Import O*NET careers
    ```

- Skills and Abilities
  ```
  GET    /api/lmiSkillsAbilities/get      # List skills/abilities
  GET    /api/lmiSkillsAbilities/getById  # Get skill details
  PUT    /api/lmiSkillsAbilities/update   # Update skill (Super User)
  ```

- Analytics and Reports
  ```
  POST   /api/analytics/addLogs           # Add analytics logs
  POST   /api/frontend/getSkilldarChartData    # Get skills radar chart
  POST   /api/frontend/getSkillsReportData     # Get skills report
  POST   /api/frontend/getCareersNCoursesDetails # Get career/course details
  POST   /api/frontend/getCompareCareersData    # Compare careers
  POST   /api/frontend/getGoalCareers          # Get career goals
  ```

- Settings
  ```
  GET    /api/setting/get                 # Get system settings
  PUT    /api/setting/update              # Update settings (Super User)
  ```

- Miscellaneous
  ```
  POST   /api/dashboard                   # Dashboard data
  POST   /api/sendFile                    # File upload
  POST   /api/frontend/sendEmail          # Send email
  ```

## Architecture
- System Overview
  > Horizon AI follows a modular architecture with separate services for:
  - User management
  - Career analysis
  - Educational institution management
  - Analytics and reporting
  
- Component Breakdown
  - Frontend Services
    - Admin Dashboard (React.js)
    - User Portal (React.js)
  
  - Backend Services
    - Express.js API server
    - Authentication service
    - Analytics engine
  
  - Database Layer
    - MongoDB for persistent storage
    - Caching layer for performance
  
- Security Architecture
  - Authentication using JWT
  - Role-based access control
  - API rate limiting
  - Request validation middleware

## Contributing
- Development Setup
  1. Fork the repository
  2. Create a feature branch
  3. Install dependencies
  4. Make your changes
  5. Submit a pull request

- Coding Standards
  - Use ESLint for code linting
  - Follow JavaScript Standard Style
  - Write meaningful commit messages
  - Document new features

## Support
- Community Resources
  - Documentation: [Link to docs]
  - Issue Tracker: GitHub Issues
  
- Contact Information
  - Technical Support: <EMAIL>
  - Bug Reports: Create an issue on GitHub
