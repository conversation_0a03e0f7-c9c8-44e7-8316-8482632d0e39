import React from 'react';
import PropTypes from 'prop-types';

export function ArcticonsEmojiSpiderWeb(props) {
    const { color = 'currentColor', style, ...rest } = props;

    return (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width={48}
            height={48}
            viewBox="0 0 48 48"
            {...props}
        >
            <path
                fill="none"
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M24.092 37.187s-4.172-4.347-9.744-3.564c0 0 .127-6.052-3.873-9.453c0 0 4.273-4.411 3.978-9.731c0 0 6.222-.075 9.5-4.047c0 0 4.171 4.347 9.492 4.052c0 0 .074 6.222 4.122 9.738c0 0-4.272 4.41-3.977 9.346c0 0-6.222-.31-9.499 3.661z"
            />
            <path
                fill="none"
                stroke={color}
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M32.789 23.654s-2.404 3.048-2.315 6.538c0 0-4.446.337-6.66 2.523c0 0-2.552-2.634-6.113-2.523c0 0-.602-4.029-2.823-6.376c0 0 2.878-3.016 2.714-6.538c0 0 4.312-.082 6.713-2.688c0 0 2.223 2.715 6.167 2.688c0 0 .094 4.222 2.315 6.376zM5.5 24h37M24 5.5v37m13.08-31.582L10.919 37.081m26.161-.001L10.919 10.919"
            />
        </svg>
    );
}

ArcticonsEmojiSpiderWeb.propTypes = {
    color: PropTypes.string,
    style: PropTypes.object,
};



