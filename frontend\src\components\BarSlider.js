import { styled } from '@mui/system';
import Slider, { SliderThumb } from '@mui/material/Slider';

export const BarSlider = styled(Slider, {
  shouldForwardProp: (prop) => prop !== 'sliderColor' && prop !== 'thumbColor',
})(({ theme, sliderColor, thumbColor }) => ({
  color: sliderColor || '#f0f0f0',
  height: 2,
  '& .MuiSlider-track': {
    border: 'none',
  },
  '& .MuiSlider-thumb': {
    height: 20,
    width: 20,
    backgroundColor: thumbColor || theme.palette.secondary.main || '#fff',
    '&:before': {
      display: 'none',
    },
  },
  '& .MuiSlider-valueLabel': {
    lineHeight: 1.2,
    fontSize: 12,
    background: 'unset',
    padding: 0,
    width: 32,
    height: 32,
    borderRadius: '50% 50% 50% 0',
    backgroundColor: sliderColor || theme.palette.primary.main || '#52af77',
    transformOrigin: 'bottom left',
    transform: 'translate(50%, -100%) rotate(-45deg) scale(0)',
    '&:before': { display: 'none' },
    '&.MuiSlider-valueLabelOpen': {
      transform: 'translate(50%, -100%) rotate(-45deg) scale(1)',
    },
    '& > *': {
      transform: 'rotate(45deg)',
    },
  },
}));
