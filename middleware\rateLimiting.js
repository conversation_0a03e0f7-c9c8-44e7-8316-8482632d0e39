const rateLimit = require('express-rate-limit');
const { messageResponse } = require('../helpers/commonHelper');
const { CUSTOM } = require('../config/messages');

/**
 * Rate limiting configuration for chatbot endpoints
 */

// General chatbot rate limiting
const chatbotRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 10, // Limit each IP to 10 requests per windowMs
  message: {
    success: false,
    statusCode: 429,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req) => {
    // Use IP address as the key
    return req.headers['x-forwarded-for'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.ip ||
           'unknown';
  },
  handler: (req, res) => {
    return res.status(429).json({
      success: false,
      statusCode: 429,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

// Stricter rate limiting for message sending
const messageRateLimit = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minute
  max: 10, // Limit each IP to 10 messages per minute
  message: {
    success: false,
    statusCode: 429,
    message: 'Too many messages sent, please slow down.'
  },
  keyGenerator: (req) => {
    // Combine IP and session for more granular control
    const ip = req.headers['x-forwarded-for'] || 
               req.connection.remoteAddress || 
               req.socket.remoteAddress ||
               (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
               req.ip ||
               'unknown';
    const sessionId = req.body.sessionId || 'no-session';
    return `${ip}:${sessionId}`;
  },
  handler: (req, res) => {
    return res.status(429).json({
      success: false,
      statusCode: 429,
      message: 'Too many messages sent, please slow down.',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

// Rate limiting for session initialization (prevent session spam)
const sessionInitRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 3, // Limit each IP to 3 session initializations per 5 minutes
  message: {
    success: false,
    statusCode: 429,
    message: 'Too many session initialization attempts, please try again later.'
  },
  keyGenerator: (req) => {
    return req.headers['x-forwarded-for'] || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress ||
           (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
           req.ip ||
           'unknown';
  },
  handler: (req, res) => {
    return res.status(429).json({
      success: false,
      statusCode: 429,
      message: 'Too many session initialization attempts, please try again later.',
      retryAfter: Math.round(req.rateLimit.resetTime / 1000)
    });
  }
});

/**
 * Custom rate limiting middleware that tracks violations
 */
const createViolationTrackingRateLimit = (options) => {
  const limiter = rateLimit(options);
  
  return (req, res, next) => {
    limiter(req, res, (err) => {
      if (err) {
        // Log rate limit violation
        console.warn(`Rate limit exceeded for IP: ${req.ip}, Path: ${req.path}`);
        
        // You could also log this to the database if needed
        // await logRateLimitViolation(req.ip, req.path);
      }
      next(err);
    });
  };
};

/**
 * IP-based blocking middleware for severely violating IPs
 */
const ipBlockingMiddleware = (req, res, next) => {
  // This could be enhanced to check a database of blocked IPs
  const blockedIPs = process.env.BLOCKED_IPS ? process.env.BLOCKED_IPS.split(',') : [];
  
  const clientIP = req.headers['x-forwarded-for'] || 
                   req.connection.remoteAddress || 
                   req.socket.remoteAddress ||
                   (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                   req.ip ||
                   'unknown';

  if (blockedIPs.includes(clientIP)) {
    return res.status(403).json({
      success: false,
      statusCode: 403,
      message: 'Access denied from this IP address.'
    });
  }

  next();
};

module.exports = {
  chatbotRateLimit,
  messageRateLimit,
  sessionInitRateLimit,
  createViolationTrackingRateLimit,
  ipBlockingMiddleware
};
