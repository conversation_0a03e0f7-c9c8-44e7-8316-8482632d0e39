const Validator = require("validator");
const bcrypt = require("bcryptjs");
const forAsync = require("../tools/forAsync");
const Sector = require("../models/sector");
const mongoose = require("mongoose");
const nodemailer = require("nodemailer");
const keys = require("../config/keys");

const { College } = require("../models/college");
const { Campus } = require("../models/campus");
const { UserRoles } = require("../models/user");

module.exports.doReqActionOnError = async (error, req, res) => {
  // console.error(error);
  console.log('error.message', error.message);
  console.log('error.cause', error.cause);
  console.log('error.stack', error.stack);
  console.log('error.fileName', error.fileName);
  console.log('error.lineNumber', error.lineNumber);
  // return res.status(500).json({ msg: "Something went wrong, please try again later" });
}

module.exports._validate = (names, args) => {
  let errors = "";
  if (names && names.length > 0) {
    names.map((name) => {

      if (!args[name] || Validator.isEmpty(args[name].trim()) || args[name] === null) {
        return (errors = `${capitalize(name)} field is required`)
      }

      if (name === "email" && !Validator.isEmail(args[name])) {
        return (errors = `${capitalize(name)} is invalid`);
      }
    })
  }
  return errors;
};

const capitalize = (str) => {
  const lower = str.toLowerCase();
  return str.charAt(0).toUpperCase() + lower.slice(1);
}

const isEmpty = (value) =>
  value === undefined ||
  value === null ||
  (typeof value === "object" && Object.keys(value).length === 0) ||
  (typeof value === "string" && value.trim().length === 0);

const sendResponse = (res, result) => {
  const statusCode = (validateResult.statusCode ? statusCode : 400);
  res.status(statusCode).json(validateResult);
}

const generateHash = async(password) => {
  let error;
  let salt;
  let hash;

  [salt, error] = await forAsync(bcrypt.genSalt(10));

  if (error) throw error;

  [hash, error] = await forAsync(bcrypt.hash(password, salt));

  if (error) throw error;

  return hash;
}

const messageResponse = (message, name, success, statusCode, data, res) => {
  let response = { statusCode, success };

  isEmpty(message) ?
    response.data = data :
    response.message = message.replace(new RegExp(':name', 'g'), name)

  if (statusCode === 404) { response.data = data }
  if (statusCode === 400 && !isEmpty(data)) { response.error = data, delete response["data"] }
  if (statusCode === 500) response.error = data.message

  if (res) {
    delete response["statusCode"]
    return res.status(statusCode).json(response)
  }
  else return response
}

const validateSectorsNSubsectors = async(sectors) => {
  if (!sectors || !sectors.length) {
    return { success: false, msg: "Invalid/missing Sector(s)." };
  }

  sectors.forEach(sector => {
    if (!sector.subsectors || !sector.subsectors.length) {

    }
  });

  let sectorIds = [], subsectorIds = [];
  let errors = [];
  let sno = 1;
  sectors.forEach(sector => {

    if(!sector._id || !mongoose.isValidObjectId(sector._id)) {
      errors.push(`Invalid Sector Id at S.No:${sno}`);
    }

    const foundSector = sectorIds.find(sector => sector == sector._id);
    if(foundSector) {
      errors.push(`Duplicate Sector at S.No:${sno}`);
      return;
    }
    sectorIds.push(sector._id);
    
    if(!sector.subsectors || !sector.subsectors.length) {
      errors.push(`Missing Subsectors at S.No:${sno}`);
    }

    let subsectorsOfsector = [];
    sector.subsectors.forEach(subsector => {
      if(!subsector._id || !mongoose.isValidObjectId(subsector._id)) {
        errors.push(`Invalid Subsector Id at S.No:${sno}`);
        return;
      }

      const foundSubsector = subsectorsOfsector.find(subsector => subsector == subsector._id);
      if(foundSubsector) {
        errors.push(`Duplicate Subsector at S.No:${sno}`);
        return;
      } 
      subsectorsOfsector.push(subsector._id);
    });
    subsectorIds.push(...subsectorsOfsector);

    sno++;
  });

  const pipeline = [
    { $match: { _id: { $in: sectorIds } } },
    {
      $lookup: {
        from: "subsectors",
        localField: "_id",
        foreignField: "sectorId",
        as: "subsectors"
      }
    }
  ]
  const sectorData = await Sector.aggregate(pipeline);
  console.log('sectorData', sectorData);


  sectors.push({ sectorId: sector._id, subsectorIds: subsectorsOfsector });

}

const checkSlug = async (model, data) => {
	let {slug, id} = data;
	let notOnlyDashRegex = /[^0-9a-zA-Z-]/g
	if(isEmpty(slug)) return {isValid: false}

	if(slug.match(notOnlyDashRegex)) return {isValid: false};
  if(slug.startsWith('-') || slug.endsWith('-')) return {isValid: false};

  let existingEntry;
  isEmpty(id) ? 
    existingEntry = await model.findOne({slug}) :
    existingEntry = await model.findOne({slug, _id: {$ne: new mongoose.Types.ObjectId(id)}})

  return existingEntry ? 
    {isValid: true, isDuplicate: true} : 
    {isValid: true, isDuplicate: false};
}

const createTransporter = () => {
  const transporter = nodemailer.createTransport({
    service: keys.SMTP_HOST,
    port: keys.SMTP_PORT,
    secure: true,
    requireTLS: true,
    auth: {
      user: keys.SMTP_USER,
      pass: keys.SMTP_PWD
    }
  })
  return transporter
}

const generateCode = (cap) => {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
  let code = "", len = 0;
  while(len !== cap){
    code += characters[Math.floor(Math.random() * characters.length)]
    len++
  }
  return code
}

const getPermissions = async(loguser, res) => {
  if(loguser.role === UserRoles.CAMPUS_ADMIN){
    const campus = await Campus.findById(loguser.campusIds[0])
    if(!campus) {
      return messageResponse(NOT_FOUND, "Campus", false, 404, null, res);
    }
    const college = await College.findById(campus.collegeId)
    if(!college) {
      return messageResponse(NOT_FOUND, "College", false, 404, null, res);
    }
    loguser.collegePermissions = college.permissions
  } else if(loguser.role === UserRoles.COLLEGE_ADMIN) {
    const college = await College.findById(loguser.collegeIds[0])
    if(!college) {
      return messageResponse(NOT_FOUND, "College", false, 404, null, res);
    }
    loguser.collegePermissions = college.permissions
  }
}

module.exports.createTransporter = createTransporter;
module.exports.messageResponse = messageResponse;
module.exports.generateCode = generateCode;
module.exports.checkSlug = checkSlug;
module.exports.generateHash = generateHash;
module.exports.capitalize = capitalize;
module.exports.isEmpty = isEmpty;
module.exports.sendResponse = sendResponse;
module.exports.validateSectorsNSubsectors = validateSectorsNSubsectors;
module.exports.getPermissions = getPermissions;