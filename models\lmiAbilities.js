const { Schema, Model, model } = require("mongoose");

const lmiAbilitiesSchema = new Schema({
  careerID: {
    type: Schema.Types.ObjectId,
    ref: "careers",
  },
  soc: Number,
  onetcode: String,
  scales: [
    {
      id: String,
      abilities: [
        {
          id: String,
          name: String,
          value: Number
        }
      ]
    }
  ]
})

module.exports.lmiAbilitiesSchema = lmiAbilitiesSchema;

class LmiAbilities extends Model {

}

model(LmiAbilities, lmiAbilitiesSchema, "LmiAbilities");

module.exports.LmiAbilities = LmiAbilities;