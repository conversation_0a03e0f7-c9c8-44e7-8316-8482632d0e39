import { LoadingButton } from '@mui/lab';
import { Autocomplete, Box, Button, Card, Container, FormControl, Grid, MenuItem, Stack, TextField, Typography, styled } from '@mui/material';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { useFormik } from 'formik';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import Iconify from '../../components/Iconify/Iconify';
import SelectField from '../../components/SelectedField';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { addCourseValidationSchema } from '../../utils/validationSchemas';
import { getCampuses } from '../Campuses/campusesSlice';
import { getSectors } from '../Sector/SectorSlice';
import { getSubSectors } from '../SubSector/SubSectorSlice';
import { postCourse } from './coursesSlice';
// import { addCareer } from '../Career/careerSlice';
// import Card from '../theme/overrides/Card'

const AddCourse = () => {
    const campuses = useSelector(state => state.campuses.campuses)
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const [Campuses, setCampuses] = useState([])
    const [Sectors, setSectors] = useState([
        {
            label: '',
            _id: '',
            subSectors: []
        }
    ])
    const [SubSectors, setSubSectors] = useState([])
    const [currentSector, setCurrentSector] = useState(null);
    const [selectedSectors, setSelectedSectors] = useState([]);
    const [filteredSectors, setFilteredSectors] = useState([]);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const { selectedCollege } = useSelector(state => state.selectedCollege)

    useEffect(() => {
        const selectedSectorsIds = selectedSectors.map(sector => sector?._id)
        setFilteredSectors(!!selectedSectors.length ? Sectors.filter(sector => !selectedSectorsIds.includes(sector?._id)) : Sectors)
    }, [selectedSectors, Sectors])

    useEffect(() => {
        formik.setValues({
            ...formik.values,
            sectors: selectedSectors
        })
    }, [selectedSectors])
    const sectors = useSelector(state => state.sectors.sectors)
    const subSectors = useSelector(state => state.subSectors.subSectors)
    useEffect(() => {
        dispatch(getSectors())
        dispatch(getSubSectors())
    }, [])
    useEffect(() => {
        const sectorOptions = sectors?.length ? sectors?.map(sector => ({
            label: sector.name,
            _id: sector._id,
        })) : []
        const subSectorOptions = subSectors?.length ? subSectors?.map(subSector => ({
            label: subSector.name,
            _id: subSector._id,
            sectorId: subSector.sectorId
        })) : []

        setSectors(sectorOptions)
        setSubSectors(subSectorOptions)
    }, [sectors, subSectors])

    const formik = useFormik({
        initialValues: {
            title: '',
            description: '',
            code: '',
            level: '',
            pageURL: '',
            applyURL: '',
            enquiryURL: '',
            sectors: [],
            campusId: [],
            duration:'',
            // campuses: []
        },
        validationSchema: addCourseValidationSchema,
        onSubmit: (values) => {
            values.collegeId = selectedCollege && selectedCollege?._id
            setIsSubmitting(true)
            dispatch(postCourse(values)).then(res =>{
                if(res?.payload?.success){
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: "Course Added Successfully"
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/courses`)
                }else{
                    const errorMessage = get(res,'payload.response.data.message','Something went wrong')
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }))
                }
            }).finally(()=>{
                setIsSubmitting(false)
            })
        },
    })
    useEffect(() => {
        // if (selectedCollege) {
          const selected = JSON.parse(localStorage.getItem('selectedCollege'));
          if (selected) {
            dispatch(getCampuses(selected))
          } else {
            dispatch(getCampuses())
          }
        // }
      }, [selectedCollege])
    useEffect(() => {
        const data = []
        if(campuses && campuses.length){
            campuses.map((item,i)=>{
                data.push({label:item.name,_id:item.id})
                return i
            })
        }
        setCampuses(data)
    }, [campuses])

    const StyledTableCell = styled(TableCell)(({ theme }) => ({
        [`&.${tableCellClasses.head}`]: {
            backgroundColor: theme.palette.common.primary,
            color: theme.palette.common.black,
        },
        [`&.${tableCellClasses.body}`]: {
            fontSize: 14,
        },
    }));

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        '&:last-child td, &:last-child th': {
            border: 0,
        },
    }));
    const addRow = () => {
        if (currentSector) {
            setSelectedSectors([...selectedSectors, {
                ...currentSector,
                subsectors: SubSectors.filter(subSector => subSector.sectorId === currentSector._id)
            }])
            setCurrentSector('')

            // defaultValue={SubSectors.filter(subSector => subSector.sectorId === row._id)}
        } else {
            // alert("else")
        }
    }
    const handleDeleteRow = (row) => {
        setSelectedSectors(selectedSectors.filter(sectors => sectors._id !== row._id))
    }

    const selectCampuses =(event,value)=>{
        const ids = []
        value.map((item,i)=>{
            ids.push(item._id)
            return i
        })
        formik.setValues({
            ...formik.values,
            campusId:ids
        })
    }

    return (
        <>
            <Helmet>
                <title> Create Course | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Card>
                    <Box p={4}>
                        <Typography variant="h6" component="h2" sx={{ pb: 3 }} >
                            Add Course
                        </Typography>
                        <form onSubmit={formik.handleSubmit}>
                            <Grid container gap={2} >
                                <Grid item xs={12} md={11.8} lg={5.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='title'
                                        label="Title"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.title}
                                        onChange={formik.handleChange}
                                        error={formik.touched.title && Boolean(formik.errors.title)}
                                        helperText={formik.touched.title && formik.errors.title}
                                    />
                                </Grid>
                                <Grid item xs={12} md={5.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='code'
                                        label="Code"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.code}
                                        onChange={formik.handleChange}
                                        error={formik.touched.code && Boolean(formik.errors.code)}
                                        helperText={formik.touched.code && formik.errors.code}
                                    />
                                </Grid>
                                <Grid item xs={12} md={5.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='level'
                                        label="Level"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.level}
                                        onChange={formik.handleChange}
                                        error={formik.touched.level && Boolean(formik.errors.level)}
                                        helperText={formik.touched.level && formik.errors.level}
                                    />
                                </Grid>
                                <Grid item xs={12} md={5.85} >
                                    <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='duration'
                                            label="Course Duration"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.duration}
                                            onChange={formik.handleChange}
                                            error={formik.touched.duration && Boolean(formik.errors.duration)}
                                            helperText={formik.touched.duration && formik.errors.duration}
                                    />
                                </Grid>
                                <Grid item xs={12} md={11.8}>
                                    <TextFIeldComponent
                                        multiline
                                        rows={12}
                                        placeholder="Description"
                                        sx={{ width: '100%' }}
                                        name='description'
                                        label="Description"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.description}
                                        onChange={formik.handleChange}
                                        error={formik.touched.description && Boolean(formik.errors.description)}
                                        helperText={formik.touched.description && formik.errors.description}
                                    />
                                </Grid>

                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='pageURL'
                                        label="Course URL"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.pageURL}
                                        onChange={formik.handleChange}
                                        error={formik.touched.pageURL && Boolean(formik.errors.pageURL)}
                                        helperText={formik.touched.pageURL && formik.errors.pageURL}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='applyURL'
                                        label="Application URL"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.applyURL}
                                        onChange={formik.handleChange}
                                        error={formik.touched.applyURL && Boolean(formik.errors.applyURL)}
                                        helperText={formik.touched.applyURL && formik.errors.applyURL}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='enquiryURL'
                                        label="Enquiry URL"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.enquiryURL}
                                        onChange={formik.handleChange}
                                        error={formik.touched.enquiryURL && Boolean(formik.errors.enquiryURL)}
                                        helperText={formik.touched.enquiryURL && formik.errors.enquiryURL}
                                    />
                                </Grid>
                                <Grid item xs={12} md={3.85}>
                                    {/* <SelectField
                                        sx={{ width: '100%' }}
                                        name='campusId'
                                        label="Campuses"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusId}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusId && Boolean(formik.errors.campusId)}
                                        helperText={formik.touched.campusId && formik.errors.campusId}
                                    >
                                        <MenuItem value="">
                                            <em>None</em>
                                        </MenuItem>
                                        {campuses?.map(campusId => {
                                            return (<MenuItem
                                                key={campusId?.id}
                                                value={campusId?.id}
                                            >
                                                {campusId?.name}
                                            </MenuItem>)
                                        })}
                                    </SelectField> */}
                                    <Autocomplete
                                        disableCloseOnSelect
                                        // isOptionEqualToValue={(option, value) => option._id === value._id}
                                        isOptionEqualToValue={(option, value) => option._id === value._id}
                                        options={Campuses }
                                        // value={formik.values.campuses}
                                        onChange={(event, value) => {
                                            selectCampuses(event,value)
                                        }}
                                        sx={{ width: "100%" }}
                                        multiple
                                        renderInput={(params) => <TextField {...params} label="Campuses" />}
                                    label={"Filter selected groups"}
                                    placeHolder={"Groups"}
                                    />
                                </Grid>
                                <Grid xs={12}>
                                    {!selectedSectors.length ? null :
                                        (<TableContainer component={Paper}>
                                            <Table sx={{ minWidth: 700 }}
                                                MenuProps={{
                                                    autoFocus: false,
                                                    disableAutoFocusItem: true,
                                                    disableEnforceFocus: true,
                                                    disableAutoFocus: true
                                                }}
                                                aria-label="customized table"
                                            >
                                                <TableHead>
                                                    <TableRow>
                                                        <StyledTableCell>Sector</StyledTableCell>
                                                        <StyledTableCell align="left">Sub-Sectors</StyledTableCell>
                                                        <StyledTableCell align="right">Remove</StyledTableCell>
                                                        {/* <StyledTableCell align="right">Carbs&nbsp;(g)</StyledTableCell>
                                                    <StyledTableCell align="right">Protein&nbsp;(g)</StyledTableCell> */}
                                                    </TableRow>
                                                </TableHead>
                                                <TableBody>
                                                    {selectedSectors.map((row, index) => (
                                                        <StyledTableRow key={row?._id}>
                                                            <StyledTableCell sx={{ width: 400 }} component="th" scope="row">
                                                                {row?.label}
                                                            </StyledTableCell>
                                                            <StyledTableCell sx={{ width: 600 }} align='left'>
                                                                <FormControl sx={{ width: "100%", mt: 2 }}>
                                                                    <Autocomplete
                                                                        // disableCloseOnSelect
                                                                        isOptionEqualToValue={(option, value) => option._id === value._id}
                                                                        options={SubSectors.filter(subSector => subSector.sectorId === row._id)}
                                                                        value={row.subsectors}
                                                                        onChange={(event, value) => {
                                                                            setSelectedSectors(selectedSectors.map(sector => sector._id === row._id ? { ...sector, subsectors: value } : sector))
                                                                        }}
                                                                        // sx={{ width: 600 }}
                                                                        multiple
                                                                        renderInput={(params) => <TextField {...params} label="Sub-Sectors" />}
                                                                    // label={"Filter selected groups"}
                                                                    // placeHolder={"Groups"}
                                                                    />
                                                                </FormControl>
                                                            </StyledTableCell>
                                                            {/* <StyledTableCell align="right">{row.calories}</StyledTableCell> */}
                                                            <StyledTableCell align="right">
                                                                <Button
                                                                    color='error'
                                                                    onClick={() => handleDeleteRow(row)}
                                                                // sx={{ width: 100 }}
                                                                >
                                                                    <Iconify icon={'eva:trash-2-outline'} sx={{ mr: 1 }} />
                                                                </Button>
                                                            </StyledTableCell>
                                                        </StyledTableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                        </TableContainer>)}

                                </Grid>
                                <Grid item xs={12}>
                                    <Stack my={2} direction={'row'} >
                                        <Autocomplete
                                            // options={getFilteredSectors()}
                                            options={filteredSectors}
                                            // options={Sectors}
                                            isOptionEqualToValue={(option, value) => option._id === value._id}
                                            value={currentSector}
                                            onChange={(event, value) => {
                                                setCurrentSector(value)
                                                // setSectors(Sectors.filter(sector => sector._id !== value._id))
                                            }}
                                            // getOptionDisabled={(option) =>
                                            //     option === timeSlots[0] || option === timeSlots[2]
                                            // }
                                            sx={{ width: 400 }}
                                            renderInput={(params) => <TextField {...params} label="Sectors" />}
                                        />
                                        <Button
                                            onClick={addRow}
                                        >
                                            Add
                                        </Button>
                                    </Stack>
                                </Grid>

                            </Grid>
                            <Stack
                                direction="row"
                                justifyContent="flex-end"
                            >
                                <Link to={`${APP_ROUTER_BASE_URL}dashboard/courses`}>
                                    <Button
                                        color='error'
                                        type='button'
                                        variant='contained'
                                        sx={{ mt: 4, width: '10ch', mr: 2.5, }}>
                                        Cancel
                                    </Button>
                                </Link>
                                <LoadingButton
                                    loading={isSubmitting}
                                    type='submit'
                                    variant='contained'
                                    sx={{ mt: 4, width: '10ch', mr: 2.5, }}>
                                    Add
                                </LoadingButton>
                            </Stack>
                        </form>

                    </Box>
                </Card>
            </Container>
        </>
    )
}

export default AddCourse
