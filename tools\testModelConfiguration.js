const mongoose = require('mongoose');
const FineTunedModel = require('../models/fineTunedModel');
const TrainingDataset = require('../models/trainingDataset');
const ModelConfigurationService = require('../services/modelConfigurationService');

/**
 * Model Configuration System Test
 * Tests all configuration functionality
 */

const testModelConfiguration = async () => {
  console.log('🎛️ Starting Model Configuration Test...');
  console.log('=====================================');

  try {
    // Connect to database if not already connected
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/horizon-ai');
      console.log('✅ Connected to MongoDB');
    }

    let testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };

    // Test 1: Configuration Service
    console.log('\n📋 Test 1: Configuration Service');
    try {
      const configService = new ModelConfigurationService();
      
      // Test default configuration
      const defaultConfig = configService.getDefaultConfiguration();
      console.log(`✅ Default configuration loaded: ${Object.keys(defaultConfig).length} settings`);
      
      // Test presets
      const presets = configService.getAvailablePresets();
      console.log(`✅ Available presets: ${presets.join(', ')}`);
      
      // Test preset loading
      const professionalPreset = configService.getConfigurationPreset('professional');
      console.log(`✅ Professional preset loaded: temp=${professionalPreset.temperature}, style=${professionalPreset.responseStyle}`);
      
      // Test validation
      const validConfig = { temperature: 0.8, maxTokens: 300 };
      const validation = configService.validateConfiguration(validConfig);
      console.log(`✅ Configuration validation: ${validation.valid ? 'PASSED' : 'FAILED'}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Configuration Service', status: 'PASSED' });
    } catch (error) {
      console.log(`❌ Configuration service test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Configuration Service', status: 'FAILED', error: error.message });
    }

    // Test 2: Create Test Model with Configuration
    console.log('\n📋 Test 2: Create Test Model with Configuration');
    try {
      // Create test dataset first
      const testDataset = new TrainingDataset({
        name: 'Config Test Dataset',
        version: 'v1.0.0-config-test',
        description: 'Test dataset for configuration testing',
        generationConfig: {
          maxExamplesPerType: 50,
          validationSplit: 0.1,
          examplesPerRecord: 2,
          validateData: true
        },
        files: {
          trainingFile: {
            filename: 'config_test_training.jsonl',
            path: '/test/config/training.jsonl',
            size: 512,
            examples: 45,
            checksum: 'config-test-checksum-123'
          },
          validationFile: {
            filename: 'config_test_validation.jsonl',
            path: '/test/config/validation.jsonl',
            size: 128,
            examples: 5,
            checksum: 'config-test-checksum-456'
          }
        },
        statistics: {
          trainingExamples: 45,
          validationExamples: 5,
          totalRecordsProcessed: 25,
          qualityScore: 0.92
        },
        tags: ['config-test']
      });

      await testDataset.save();
      console.log(`✅ Test dataset created: ${testDataset._id}`);

      // Create test model with custom configuration
      const testModel = new FineTunedModel({
        modelName: 'Configuration Test Model',
        openAIModelId: 'ft:gpt-4o-mini:test:config-model:456',
        openAIJobId: 'ftjob-config-test456',
        baseModel: 'gpt-4o-mini',
        trainingDatasetId: testDataset._id,
        trainingConfig: {
          suffix: 'config-test-model',
          epochs: 3
        },
        trainingResults: {
          status: 'succeeded',
          trainingTokens: 50000,
          completedAt: new Date()
        },
        configuration: {
          temperature: 0.9,
          maxTokens: 400,
          responseStyle: 'friendly',
          systemPrompt: 'You are a friendly test assistant.',
          customInstructions: 'Always be enthusiastic and helpful!'
        },
        version: 'v1.0.0-config',
        description: 'Test model for configuration features'
      });

      await testModel.save();
      console.log(`✅ Test model created with configuration: ${testModel._id}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Create Model with Configuration', status: 'PASSED', id: testModel._id });

      // Test 3: Configuration Methods
      console.log('\n📋 Test 3: Configuration Methods');
      
      // Test getEffectiveConfiguration
      const effectiveConfig = testModel.getEffectiveConfiguration();
      console.log(`✅ Effective configuration: temp=${effectiveConfig.temperature}, maxTokens=${effectiveConfig.maxTokens}`);
      console.log(`✅ Custom settings: style=${effectiveConfig.responseStyle}, prompt="${effectiveConfig.systemPrompt.substring(0, 30)}..."`);
      
      // Test updateConfiguration
      await testModel.updateConfiguration({
        temperature: 0.5,
        maxTokens: 600,
        responseStyle: 'professional'
      });
      
      const updatedConfig = testModel.getEffectiveConfiguration();
      console.log(`✅ Configuration updated: temp=${updatedConfig.temperature}, maxTokens=${updatedConfig.maxTokens}, style=${updatedConfig.responseStyle}`);
      
      // Test resetConfiguration
      await testModel.resetConfiguration();
      const resetConfig = testModel.getEffectiveConfiguration();
      console.log(`✅ Configuration reset: temp=${resetConfig.temperature} (should be default 0.7)`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Configuration Methods', status: 'PASSED' });

      // Test 4: Configuration Presets
      console.log('\n📋 Test 4: Configuration Presets');
      
      const configService = new ModelConfigurationService();
      
      // Apply professional preset
      const professionalConfig = configService.getConfigurationPreset('professional');
      await testModel.updateConfiguration(professionalConfig);
      
      let currentConfig = testModel.getEffectiveConfiguration();
      console.log(`✅ Professional preset applied: style=${currentConfig.responseStyle}, temp=${currentConfig.temperature}`);
      
      // Apply friendly preset
      const friendlyConfig = configService.getConfigurationPreset('friendly');
      await testModel.updateConfiguration(friendlyConfig);
      
      currentConfig = testModel.getEffectiveConfiguration();
      console.log(`✅ Friendly preset applied: style=${currentConfig.responseStyle}, temp=${currentConfig.temperature}`);
      
      // Apply concise preset
      const conciseConfig = configService.getConfigurationPreset('concise');
      await testModel.updateConfiguration(conciseConfig);
      
      currentConfig = testModel.getEffectiveConfiguration();
      console.log(`✅ Concise preset applied: style=${currentConfig.responseStyle}, maxTokens=${currentConfig.maxTokens}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Configuration Presets', status: 'PASSED' });

      // Test 5: Configuration Validation
      console.log('\n📋 Test 5: Configuration Validation');
      
      // Test valid configuration
      const validConfig = {
        temperature: 0.8,
        maxTokens: 500,
        responseStyle: 'detailed'
      };
      
      const validValidation = configService.validateConfiguration(validConfig);
      console.log(`✅ Valid configuration validation: ${validValidation.valid ? 'PASSED' : 'FAILED'}`);
      
      // Test invalid configuration
      const invalidConfig = {
        temperature: 3.0, // Invalid: > 2
        maxTokens: 5000, // Invalid: > 4000
        responseStyle: 'invalid-style' // Invalid enum
      };
      
      const invalidValidation = configService.validateConfiguration(invalidConfig);
      console.log(`✅ Invalid configuration validation: ${!invalidValidation.valid ? 'PASSED' : 'FAILED'}`);
      console.log(`✅ Validation errors found: ${invalidValidation.errors.length}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Configuration Validation', status: 'PASSED' });

      // Test 6: OpenAI Configuration Format
      console.log('\n📋 Test 6: OpenAI Configuration Format');
      
      const testConfig = {
        temperature: 0.7,
        maxTokens: 500,
        topP: 0.9,
        frequencyPenalty: 0.1,
        presencePenalty: 0.2
      };
      
      const openAIConfig = configService.getOpenAIConfiguration(testConfig);
      console.log(`✅ OpenAI format: max_tokens=${openAIConfig.max_tokens}, temperature=${openAIConfig.temperature}`);
      console.log(`✅ OpenAI format: top_p=${openAIConfig.top_p}, frequency_penalty=${openAIConfig.frequency_penalty}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'OpenAI Configuration Format', status: 'PASSED' });

      // Test 7: System Message Generation
      console.log('\n📋 Test 7: System Message Generation');
      
      const systemMessage = configService.getSystemMessage(
        { 
          systemPrompt: 'You are a helpful assistant.',
          customInstructions: 'Be concise and accurate.'
        },
        { 
          college: { name: 'Test University' }
        }
      );
      
      console.log(`✅ System message generated: "${systemMessage.substring(0, 80)}..."`);
      console.log(`✅ Contains college name: ${systemMessage.includes('Test University') ? 'YES' : 'NO'}`);
      console.log(`✅ Contains custom instructions: ${systemMessage.includes('Be concise') ? 'YES' : 'NO'}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'System Message Generation', status: 'PASSED' });

      // Test 8: Cleanup
      console.log('\n📋 Test 8: Cleanup Test Data');
      await FineTunedModel.findByIdAndDelete(testModel._id);
      await TrainingDataset.findByIdAndDelete(testDataset._id);
      console.log(`✅ Test data cleaned up successfully`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Cleanup Test Data', status: 'PASSED' });

    } catch (error) {
      console.log(`❌ Model configuration test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Model Configuration', status: 'FAILED', error: error.message });
    }

    // Test Results Summary
    console.log('\n📊 CONFIGURATION TEST RESULTS');
    console.log('==============================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📊 Total: ${testResults.passed + testResults.failed}`);
    console.log(`🎯 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

    console.log('\n📋 Detailed Results:');
    testResults.tests.forEach((test, index) => {
      const status = test.status === 'PASSED' ? '✅' : '❌';
      console.log(`${status} ${index + 1}. ${test.name}: ${test.status}`);
      if (test.error) {
        console.log(`   Error: ${test.error}`);
      }
      if (test.id) {
        console.log(`   ID: ${test.id}`);
      }
    });

    if (testResults.failed === 0) {
      console.log('\n🎉 ALL CONFIGURATION TESTS PASSED! Model configuration system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }

    return testResults;

  } catch (error) {
    console.error('❌ Configuration test failed:', error);
    return { passed: 0, failed: 1, tests: [{ name: 'Configuration Test', status: 'FAILED', error: error.message }] };
  }
};

// Run test if called directly
if (require.main === module) {
  // Load environment variables
  require('dotenv').config();
  
  testModelConfiguration()
    .then((results) => {
      console.log('\n🏁 Model configuration test completed');
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testModelConfiguration;
