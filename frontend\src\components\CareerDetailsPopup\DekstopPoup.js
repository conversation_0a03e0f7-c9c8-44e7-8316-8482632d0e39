import React, { useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import { useSelector } from 'react-redux';
import { Accordion, Stack, AccordionDetails, AccordionSummary, Box, Button, Divider, CircularProgress, Tabs, Tab, Typography, useTheme } from '@mui/material';
import KeyboardArrowRightOutlinedIcon from '@mui/icons-material/KeyboardArrowRightOutlined';
import ArrowCircleUpOutlinedIcon from '@mui/icons-material/ArrowCircleUpOutlined';
import ArrowCircleDownOutlinedIcon from '@mui/icons-material/ArrowCircleDownOutlined';
import CompareOutlinedIcon from '@mui/icons-material/CompareOutlined';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import CloseIcon from '@mui/icons-material/Close';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ApexCharts from 'src/components/apexcharts/ApexCharts';
import ModalComponent from '../Modal/Modal';
import RadarChartComponent from '../RadarChart/RadarChartComponent';

function TabPanel(props) {
    const { children, value, index, ...other } = props;
    const compareCareers = useSelector(state => state.mainLayout.compareCareers)
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`,
    };
}

export default function DekstopPoup({ isLoading, regionData, children, tabValue, setTabValue, careerNCoursesPopDetails, openCareerDetailsPopup, HandleCloseCareerDetailsPopup, showComparePopup, addToCompare, compareCareersState, getSkillsReport, addToAnalytics, redirectUrl, currentNGoalCareerId, expanded, readMore, setReadMore, HandleOpenRadarPopup, regionalChartData, qualificationsChartData, handleChange }) {
    const darkBlue = "#000c3b"
    const [value, setChangeValue] = useState(0);
    const handleChangeTab = (event, newValue) => {
        setChangeValue(newValue);
    };
    // Called when tab is switched
    const handleRegionTab = (event, newValue) => {
        setTabValue(newValue);
    };
    const colorTheme = useTheme();
    const buttonColor = regionData?.button?.bgColor;
    const buttonFontColor = regionData?.button?.color;
    const logo = regionData?.logo || '';
    const partnerLogos = regionData?.partnerLogos || [];
    const primaryColor = regionData?.primaryColor || colorTheme?.palette.primary.main;
    const fontColor = regionData?.fontColor || ''
    const secondaryColor = regionData?.secondaryColor || colorTheme?.palette?.secondary?.main;

    const steps = useMemo(() => ['National', regionData?.name], [regionData?.name]);
    const [currentStep, setCurrentStep] = useState(0);
    const onStepChange = (stepIndex) => {
        setTabValue(stepIndex);
    };
    return (
        <ModalComponent
            open={openCareerDetailsPopup}
            handleClose={HandleCloseCareerDetailsPopup}
            className='app-popup compare-career careerDetails dekstop'
        >
            <Box sx={{ backgroundColor: `${primaryColor} !important` }} className='popup-wrapper'>
                <Box className='popup-head'>
                    <Box style={{ display: "flex" }}>
                        <Button className='close-icon-btn' onClick={HandleCloseCareerDetailsPopup}><CloseIcon fontSize='small' className='close-career-popup' sx={{ bgcolor: 'white', color: 'black !important', borderRadius: '50%' }} /></Button>
                        <Typography variant='h3' sx={{ color: buttonColor || darkBlue, fontWeight: '700', ml: 2 }}>{careerNCoursesPopDetails?.title}</Typography>
                    </Box>
                    <Tabs
                        value={tabValue}
                        onChange={handleRegionTab}
                        variant="standard"
                        TabIndicatorProps={{ style: { display: 'none' } }}
                        sx={{
                            display: 'flex',
                            gap: 0,
                            backgroundColor: primaryColor,
                            px: 0,
                            py: 0.5,
                            borderRadius: '8px',
                        }}
                    >
                        <Box sx={{ display: 'flex' }}>
                            {steps?.map((label, index) => {
                                let borderRadius = '0';
                                if (index === 0) {
                                    borderRadius = '8px 0 0 8px';
                                } else if (index === steps.length - 1) {
                                    borderRadius = '0 8px 8px 0';
                                }
                                return (
                                    <Button
                                        key={label}
                                        onClick={() => onStepChange(index)}
                                        size='small'
                                        sx={{
                                            width: '100%',
                                            borderRadius, // ✅ no ternary here
                                            textTransform: 'none',
                                            backgroundColor: tabValue === index ? buttonColor : primaryColor,
                                            color: tabValue === index ? buttonFontColor : buttonColor,
                                            fontWeight: tabValue === 700,
                                            border: `1px solid ${buttonColor}`,
                                            borderRight: index !== steps.length - 1 ? 'none' : `1px solid ${buttonColor}`,
                                            px: 6,
                                            py: 1,
                                            '&:hover': {
                                                backgroundColor: tabValue === index ? buttonColor : primaryColor,
                                            },
                                        }}
                                    >
                                        {label}
                                    </Button>
                                )
                            })}
                        </Box>
                        {/* {['National', 'Worcestershire'].map((label, index) => (
                            <Tab
                                key={label}
                                label={label}
                                disableRipple
                                disableFocusRipple
                                sx={{
                                    textTransform: 'none',
                                    fontWeight: '700',
                                    borderRadius: '8px',
                                    minHeight: 32,
                                    minWidth: 120,
                                    px: 2,
                                    py: 0.5,
                                    // minWidth: 'auto',
                                    bgcolor: tabValue === index ? primaryColor : buttonColor,
                                    color: tabValue === index ? buttonColor : buttonFontColor,
                                    border: tabValue === index ? `1px solid ${buttonColor}` : 'none'
                                    // '&:hover': {
                                    //     bgcolor: tabValue === index ? 'primary.dark' : 'warning.light',
                                    // },
                                }}
                            />
                        ))} */}
                    </Tabs>
                    {/* {careerNCoursesPopDetails ?
                        <Box>
                            {currentNGoalCareerId.currentRoleId === careerNCoursesPopDetails.id || currentNGoalCareerId.careerGoalId === careerNCoursesPopDetails.id ?
                                ""
                                :

                                <>
                                    <Button className='career-popup-head-btn' sx={{ backgroundColor: (theme) => `${theme.palette.secondary.main} !important` }}
                                        onClick={() => showComparePopup(careerNCoursesPopDetails.id)}
                                    ><Typography sx={{ textTransform: 'none !important' }} variant='button2' > Compare Careers</Typography><CompareOutlinedIcon sx={{ ml: 3 }} /> </Button>
                                    <Button className='career-popup-head-btn' sx={{ backgroundColor: (theme) => `${theme.palette.secondary.main} !important` }} onClick={() => addToCompare(careerNCoursesPopDetails.id)} ><Typography sx={{ textTransform: 'none !important' }} variant='button2' >{compareCareersState.find(currentCareer => currentCareer === careerNCoursesPopDetails.id) ? "Added" : 'Add to Comparison'}</Typography><CompareOutlinedIcon sx={{ ml: 3 }} /> </Button>
                                </>
                            }
                            <Button onClick={() => getSkillsReport('careers-result-page')} className='career-popup-head-btn' sx={{ backgroundColor: (theme) => `${theme.palette.secondary.main} !important` }}><Typography sx={{ textTransform: 'none !important' }} variant='button2' >Get Skill Report</Typography><FileDownloadOutlinedIcon sx={{ ml: 3 }} /> </Button>
                        </Box>
                        : ''
                    } */}
                </Box>
                {careerNCoursesPopDetails && !isLoading ?
                    <Box className='content-wrapper'>
                        <Box className="row" sx={{ bgcolor: `${primaryColor} !important` }}>
                            <Box sx={{ border: `1px solid ${primaryColor}` }} className="desc-head">
                                {/* <Typography variant='capitalize' className='heading' color='primary.black'>Description</Typography> */}
                                <Typography className='description' sx={{ backgroundColor: (theme) => `${buttonColor || theme.palette.primary.main} !important`, borderBottom: `3px solid ${primaryColor} !important`, marginBottom: '0px !important' }} variant='h5' color={buttonFontColor || 'white'}>Job Description</Typography>
                                <Typography variant='body' color='primary.dark' textAlign='left' sx={{ padding: '10px 20px' }}>{careerNCoursesPopDetails?.description}</Typography>
                            </Box>
                            <Box className='col courses-wrapper'
                                //  sx={{overflow:'hidden'}}
                                sx={{ overflowY: careerNCoursesPopDetails?.courses.length > 9 ? 'scroll' : 'hidden'}}
                            >
                                <Typography className='heading' variant='h5' sx={{ backgroundColor: (theme) => `${buttonColor || theme.palette.primary.main} !important`, borderBottom: `3px solid ${primaryColor} !important`, marginBottom: '0px !important' }} color={buttonFontColor || 'white'}>Colleges with supporting Courses</Typography>
                                {careerNCoursesPopDetails?.courses.length ?
                                    <>
                                        {careerNCoursesPopDetails?.courses?.map((item, index) => (
                                            <Accordion sx={{ marginBottom: '0px !important', border: 'none', borderBottom: `3px solid ${primaryColor} !important` }} expanded={expanded === index} onChange={handleChange(index)} key={index}>
                                                <AccordionSummary
                                                    expandIcon={<KeyboardArrowRightOutlinedIcon sx={{
                                                        color: 'black',          // your desired color
                                                        fontWeight: 'bold',        // makes icon visually stronger
                                                        fontSize: '2.5rem',        // optional: increase size
                                                    }} />}
                                                    aria-controls="supporting-courses"
                                                    id={index}
                                                >
                                                    <Typography variant='body' color='primary.dark' sx={{ fontWeight: 700, padding: '8px 16px' }}>{item.title}</Typography>
                                                </AccordionSummary>
                                                <AccordionDetails
                                                    sx={{
                                                        pb: 0,
                                                        transition: '1ms ease-out',
                                                        transitionDelay: '0.1ms',
                                                        // display : expanded !== item.title ? 'none' : 'inline',
                                                        // visibility : expanded !== item.title ? 'hidden' : 'visible',
                                                        visibility: expanded !== index ? 'hidden' : 'visible',
                                                    }}
                                                >
                                                    <Typography variant='medium' sx={{ padding: '0px 16px' }} className='flex-around' color='primary.dark'>
                                                        <span style={{ fontWeight: 700 }}>Level</span>
                                                        <span style={{ marginLeft: "14px", fontWeight: 400 }}>{item.level}</span>
                                                    </Typography>

                                                    <Typography variant='medium' color='primary.dark' sx={{ padding: '0px 16px', margin: '10px 0' }}>{item.description}</Typography>
                                                    {item?.campus?.length && item?.campus?.map((campus, i) => (
                                                        <Box className="highlighted" sx={{ display: 'flex', justifyContent: 'space-between', marginTop: "2px", bgcolor: "#dfdfdf", p: 0 }} >
                                                            <Stack direction='row' sx={{ ml: 2 }}>
                                                                <Typography sx={{ minWidth: '30%' }} variant='medium' className='flex-around' color='primary.dark'>
                                                                    <span style={{ fontWeight: 700 }}>Duration:</span>
                                                                    <span style={{ marginLeft: "10px", fontWeight: 400 }}>{item?.duration || "2 Years - Full Time"}</span>
                                                                </Typography>
                                                                <Typography sx={{ minWidth: '30%', ml: 5 }} variant='medium' className='flex-around' color='primary.dark'>
                                                                    <span style={{ fontWeight: 700 }}>Campus:</span>
                                                                    <span style={{ marginLeft: "10px", fontWeight: 400 }}>{campus.name || "-"}</span>
                                                                </Typography>
                                                            </Stack>
                                                            <Box className='career-responsive-btns'>
                                                                <Button
                                                                    onClick={() => addToAnalytics({
                                                                        event: "COURSE_ENQUIRY",
                                                                        // course: item.campus[0].id
                                                                        course: item?.id
                                                                    })}
                                                                    disabled={!campus.enquiryURL && true}
                                                                    sx={{ minWidth: "10%", backgroundColor: (theme) => `${theme.palette.secondary.main} !important`, borderRadius: '0px', borderRight: "2px solid white", padding: '5px,15px', height: '100%' }}
                                                                >
                                                                    {redirectUrl(campus.enquiryURL, 'Enquire')}
                                                                </Button>
                                                                <Button
                                                                    onClick={() => addToAnalytics({
                                                                        event: "COURSE_APPLY",
                                                                        // course: item.campus[0].id
                                                                        course: item?.id
                                                                    })}
                                                                    disabled={!campus.applyURL && true}
                                                                    sx={{ minWidth: "10%", backgroundColor: (theme) => `${theme.palette.secondary.main} !important`, borderRadius: '0px', borderRight: "2px solid #dfdfdf", padding: '5px,15px', height: '100%' }}
                                                                >
                                                                    {redirectUrl(campus.applyURL, 'Apply')}
                                                                </Button>
                                                                <Button
                                                                    onClick={() => addToAnalytics({
                                                                        event: "COURSE_DETAILS",
                                                                        // course: item.campus[0].id
                                                                        course: item?.id
                                                                    })}
                                                                    disabled={!campus.pageURL && true}
                                                                    sx={{ minWidth: "10%", backgroundColor: (theme) => `${theme.palette.secondary.main} !important`, borderRadius: '0px', padding: '5px,15px', height: '100%' }}
                                                                >
                                                                    {redirectUrl(campus.pageURL, "More Info")}
                                                                </Button>

                                                            </Box>
                                                        </Box>
                                                    ))}
                                                </AccordionDetails>
                                            </Accordion>
                                        ))}
                                    </>
                                    :
                                    <Typography sx={{ backgroundColor: '#f4f4f4', p: 1, fontSize: '1.1rem', fontWeight: '700' }} color='primary.dark'>No courses found</Typography>
                                }
                            </Box>
                            <Stack direction='row' gap={2} justifyContent="space-between" className='col-wrapper'>
                                <Stack sx={{ flex: 1.3 }}>
                                    <Box className="col " sx={{ minHeight: '224px !important', backgroundColor: (theme) => `${buttonColor || theme.palette.primary.main} !important`, borderRadius: '0 !important', border: `1px solid ${primaryColor} !important` }}>
                                        <Typography variant='h5' className='heading' color={buttonFontColor || 'white'}>Average Salary</Typography>
                                        <Box sx={{ minHeight: '140px !important' }} style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                            <Stack>
                                                <Typography variant='h2' color={buttonFontColor || 'secondary.main'} style={{ textAlign: 'center', fontWeight: '700' }} > £{Number(careerNCoursesPopDetails?.averageSalary?.yearly).toFixed(2)}</Typography>
                                                <Typography variant='h6' style={{ textAlign: 'center' }} color={buttonFontColor || 'white'}>Per year</Typography>
                                            </Stack>
                                            <Divider sx={{ bgcolor: buttonFontColor || 'white', width: '2px', margin: '0 20px', height: '60px', alignSelf: 'center' }} orientation="vertical" variant="middle" flexItem />

                                            <Stack>
                                                <Typography variant='h2' color={buttonFontColor || 'secondary.main'} style={{ textAlign: 'center', fontWeight: '700' }}> £{Number(careerNCoursesPopDetails?.averageSalary?.weekly).toFixed(2)}</Typography>
                                                <Typography variant='h6' style={{ textAlign: 'center' }} color={buttonFontColor || 'white'}>Per week</Typography>
                                            </Stack>
                                        </Box>
                                    </Box>
                                    <Box sx={{ backgroundColor: `${buttonColor} !important`, borderRadius: '0 !important', border: `1px solid ${primaryColor} !important`, minHeight: '224px !important', }} className="col">
                                        <Typography variant='h5' className='heading' color={buttonFontColor || 'primary.dark'}>Average Expected Weekly Hours</Typography>
                                        <Box sx={{ minHeight: '140px !important', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                            <Typography variant='h2' fontWeight={700} color={buttonFontColor || 'secondary.main'}>{careerNCoursesPopDetails?.averageHours}</Typography>
                                        </Box>
                                    </Box>
                                </Stack>
                                <Box className="col" sx={{ flex: 1.7, backgroundColor: 'white !important', borderRadius: '0 !important', border: `1px solid ${primaryColor} !important` }}>
                                    <Typography variant='h5' textAlign='center' className='heading' color='primary.dark'>Typical Work Tasks</Typography>
                                    <ul>
                                        {
                                            careerNCoursesPopDetails?.tasks?.map((item) => <li key={item}><Typography variant='body' color='primary.dark'>{item}</Typography></li>)
                                        }
                                    </ul>
                                </Box>
                            </Stack>
                            <Box className="col transfer-window" sx={{ backgroundColor: (theme) => `${buttonColor || theme.palette.primary.main} !important`, borderRadius: '0px !important', height: '192px', overflowY: 'auto' }}>
                                <Typography className='heading' sx={{ marginBottom: '5px !important' }} color={buttonFontColor || 'white'} variant='h5'>Disclaimer: please read</Typography>
                                <Typography sx={{ textAlign: 'center' }} color={buttonFontColor || 'white'}>
                                    The career paths provided are to give you an idea of the careers that require a skill set that is similar to your own, and that are therefore a good upskill or reskill option for you.  However, they are suggestions only, and require further investigation on a personal level, by you, to make a fully informed choice that suits your unique situation.
                                    {readMore ?
                                        <>
                                            <Typography sx={{ textAlign: 'left' }} color={buttonFontColor || 'white'}>
                                                <br />
                                                The courses that are shown to support careers do so at a range of varying levels.  Some are entry level courses and qualifications that enable access to higher level qualifications.  When displaying these courses, we do not consider any previous education or training on your part, and so you may need to complete multiple courses at different levels to achieve the higher-level qualifications needed before your dream job becomes a reality.<br /><br />
                                                Similarly, we will also display courses that support broader experience within a field of expertise that can enhance existing knowledge as an upskill option, that may also require additional qualifications to enable a successful reskill into a new career.<br /> <br />
                                                Please check entry requirements to all courses, apprenticeships, and careers as this may vary from year to year and across providers.  For further advice and guidance about the courses this college offers please contact them directly, or simply request a skills report featuring your shortlisted careers and courses and the college will contact you to discuss in more detail.
                                            </Typography>
                                        </> : null
                                    }
                                    <Typography sx={{ textAlign: 'left', display: 'inline', textDecoration: 'underline', cursor: 'pointer', marginLeft: readMore ? '0px' : '5px' }} variant='medium' color={buttonFontColor || 'white'} onClick={() => setReadMore(!readMore)}>Click for {readMore ? 'less' : 'more'}</Typography>
                                </Typography>
                            </Box>
                        </Box>
                        <Box className="rowMax " sx={{ borderRadius: '0px !imoprtant' }}>
                            <Box className="col skilldar" sx={{ padding: '30px 0 !important', borderRadius: '0px !imoprtant' }}>
                                <Box sx={{ display: 'flex', justifyContent: 'center', position: 'relative', borderRadius: '0px !imoprtant' }}>
                                    <Typography variant='h5' className='heading' color='primary.dark' sx={{ mr: 1 }}>Skilldar</Typography>
                                    <ZoomInIcon onClick={HandleOpenRadarPopup} fontSize='large' sx={{ position: 'absolute', right: '20px' }} />
                                </Box>
                                <Box className='flex radarWrapper' sx={{ justifyContent: 'center', padding: '0px' }}>
                                    <RadarChartComponent
                                        width='100%'
                                        legend={false}
                                        radarApiDetails={careerNCoursesPopDetails?.skilldarChartData}
                                        height={370}
                                        careersDetails
                                        showlabels={false}
                                        chartAnimation
                                    />
                                </Box>
                            </Box>
                            <Box className="col transfer-window" sx={{ backgroundColor: (theme) => `${buttonColor || theme.palette.primary.main} !important` }}>
                                <Typography className='heading' sx={{ marginBottom: '0px !important' }} color={buttonFontColor || 'white'} variant='h5'>Transfer Window</Typography>
                                <Typography variant='h5' sx={{ margin: '5px 0' }} color={buttonFontColor || 'secondary.main'}>{careerNCoursesPopDetails?.transferWindow?.duration}</Typography>
                                <Typography sx={{ textAlign: 'center' }} variant='medium' color={buttonFontColor || 'white'}>{careerNCoursesPopDetails?.transferWindow?.description}</Typography>
                            </Box>
                            <Box className='col-wrapper'>
                                <Box className="col tab-wrapper">
                                    <Typography className='growth-heading heading' variant="h5" color='primary.dark' >Growth</Typography>
                                    <Typography variant="body1" color='primary.dark' >These charts show predicted demand by {careerNCoursesPopDetails?.growthData?.regionalGrowth?.year}</Typography>
                                    <Box sx={{ width: '100%' }} className='popup-tab' >
                                        <Box sx={{ borderColor: 'divider', mx: 1 }} >
                                            <Tabs
                                                variant='fullWidth'
                                                value={value}
                                                onChange={handleChangeTab}
                                                aria-label="custom styled tabs"
                                                TabIndicatorProps={{ style: { display: 'none' } }} // remove default underline
                                                sx={{
                                                    display: 'flex',
                                                    justifyContent: 'center',
                                                    gap: 1, // spacing between tabs
                                                    mt: 1,
                                                }}
                                            >
                                                {['Growth', 'Qualification', 'Work Type'].map((label, index) => (
                                                    <Tab
                                                        key={label}
                                                        label={label}
                                                        {...a11yProps(index)}
                                                        sx={{
                                                            textTransform: 'none',
                                                            fontWeight: '700',
                                                            borderRadius: '8px',
                                                            px: 2,
                                                            py: 0.5,
                                                            minHeight: '32px',
                                                            minWidth: 'auto',
                                                            bgcolor: value === index ? 'primary.main' : buttonColor,
                                                            color: value === index ? 'common.white' : 'primary.dark',
                                                            '&:hover': {
                                                                bgcolor: value === index ? 'primary.dark' : 'warning.light',
                                                            },
                                                        }}
                                                    />
                                                ))}
                                            </Tabs>
                                        </Box>
                                        <TabPanel value={value} index={0}>
                                            <Box className='tab-content'>
                                                <Typography variant='subtitle1' textAlign='center' color='black' >Overall Change {careerNCoursesPopDetails?.growthData?.overallGrowth?.duration}</Typography>
                                                <Box className="stats-wrapper">
                                                    {/* <Typography color={`${careerNCoursesPopDetails?.growthData?.overallGrowth?.overallGrowthPercentage > 0 ? 'primary.success' : 'primary.error'}`}> */}
                                                    <Typography color='primary.dark'>
                                                        {
                                                            careerNCoursesPopDetails?.growthData?.overallGrowth?.overallGrowthPercentage > 0 ?
                                                                <ArrowCircleUpOutlinedIcon sx={{ fontSize: '70px !important' }} />
                                                                : <ArrowCircleDownOutlinedIcon sx={{ fontSize: '70px !important' }} />
                                                        }
                                                    </Typography>
                                                    <Typography color={buttonFontColor || "secondary.main"} fontWeight={700} variant='h2'>
                                                        {careerNCoursesPopDetails?.growthData?.overallGrowth?.overallGrowthPercentage > 0 ? '+' : null} {careerNCoursesPopDetails?.growthData?.overallGrowth?.overallGrowthNumber}
                                                    </Typography>
                                                    <Typography color={buttonFontColor || "primary.dark"} textAlign='center' variant='h6'>
                                                        {careerNCoursesPopDetails?.growthData?.overallGrowth?.overallGrowthPercentage > 0 ? '+' : null}  {careerNCoursesPopDetails?.growthData?.overallGrowth?.overallGrowthPercentage} %
                                                    </Typography>
                                                </Box>
                                            </Box>
                                        </TabPanel>
                                        {/* <TabPanel value={value} index={1}>
                                            <Box className='tab-content'>
                                                <Typography variant='h5' color='#b0b0b0' fontWeight={500}>Regional Growth {careerNCoursesPopDetails?.growthData?.regionalGrowth?.year}</Typography>
                                                <Box className="stats-wrapper">
                                                    <ApexCharts width='100%' type="bar" height={350} chartData={regionalChartData} />
                                                </Box>
                                            </Box>
                                        </TabPanel> */}
                                        <TabPanel value={value} index={1}>
                                            <Box className='tab-content'>
                                                <Typography variant='h5' color='#b0b0b0' fontWeight={500}>Growth By Qualification Level {careerNCoursesPopDetails?.growthData?.QualificationGrowth?.year}</Typography>
                                                <Box className="stats-wrapper">
                                                    <ApexCharts width='100%' type="bar" height={350} chartData={qualificationsChartData} />
                                                </Box>
                                            </Box>


                                        </TabPanel>
                                        <TabPanel value={value} index={2}>
                                            <Box className='tab-content'>
                                                <Typography variant='h5' color='#b0b0b0' fontWeight={500}>Predicted Workforce In {careerNCoursesPopDetails?.growthData?.workTypeGrowth?.year}</Typography>
                                                <Box className="stats-wrapper">
                                                    <Box className="stats">
                                                        <Typography variant='medium' color='primary.light'>Full-time</Typography>
                                                        <Typography variant='h2' color='primary.black'>{careerNCoursesPopDetails?.growthData?.workTypeGrowth?.fullTime}</Typography>
                                                    </Box>
                                                    <Box className="stats">
                                                        <Typography variant='medium' color='primary.light'>Part-time</Typography>
                                                        <Typography variant='h2' color='primary.black'>{careerNCoursesPopDetails?.growthData?.workTypeGrowth?.partTime}</Typography>
                                                    </Box>
                                                    <Box className="stats">
                                                        <Typography variant='medium' color='primary.light'>Self-Employed</Typography>
                                                        <Typography variant='h2' color='primary.black'>{careerNCoursesPopDetails?.growthData?.workTypeGrowth?.selfEmployeed}</Typography>
                                                    </Box>
                                                </Box>
                                            </Box>
                                        </TabPanel>
                                    </Box>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                    :
                    <Box sx={{ position: 'absolute', top: '42%', left: '48%' }}>
                        <CircularProgress sx={{ color: buttonColor }} />
                    </Box>
                }
            </Box>
        </ModalComponent>
    )
}

DekstopPoup.propTypes = {
    children: PropTypes.node,
    careerNCoursesPopDetails: PropTypes.object,
    openCareerDetailsPopup: PropTypes.func,
    HandleCloseCareerDetailsPopup: PropTypes.func,
    isLoading: PropTypes.bool,
    showComparePopup: PropTypes.func,
    addToCompare: PropTypes.func,
    compareCareersState: PropTypes.node,
    getSkillsReport: PropTypes.func,
    addToAnalytics: PropTypes.func,
    redirectUrl: PropTypes.func,
    currentNGoalCareerId: PropTypes.node,
    expanded: PropTypes.bool,
    readMore: PropTypes.bool,
    setReadMore: PropTypes.func,
    HandleOpenRadarPopup: PropTypes.func,
    regionalChartData: PropTypes.object,
    setTabValue: PropTypes.func,
    tabValue: PropTypes.any,
    qualificationsChartData: PropTypes.object,
    regionData: PropTypes.object,
    handleChange: PropTypes.func
};