const express = require("express");
const bodyParser = require('body-parser');

const connecDB = require("./config/dbConnection");
const path = require("path");
const {messageResponse} = require('./helpers/commonHelper')
const { NOT_FOUND } = require("./config/messages");

connecDB();

const app = express();
const cors = require("cors");

const AuthGuard = require("./guards/auth.guard");

app.use(express.json({limit: '5mb'}));
app.use(bodyParser.json({limit: '35mb'}));
app.use(
  bodyParser.urlencoded({
    extended: true,
    limit: '35mb',
    parameterLimit: 50000,
  }),
);

app.use(function (req, res, next) {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Methods', 'GET,POST,PUT,DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
    next();
  });
  
app.use(cors());

// routings
//

const countries = require('./routes/countries');
app.use('/api/countries', AuthGuard, countries);

const regions = require('./routes/regions');
app.use('/api/regions', AuthGuard, regions);

const collegeGroups = require('./routes/collegeGroups');
app.use('/api/collegeGroups', AuthGuard, collegeGroups);

const colleges = require('./routes/colleges');
app.use('/api/colleges', AuthGuard, colleges);

const campuses = require('./routes/campuses');
app.use('/api/campuses', AuthGuard, campuses);

const users = require('./routes/users');
app.use('/api/users', AuthGuard, users);

const sectors = require('./routes/sectors');
app.use('/api/sectors', AuthGuard, sectors);

const subsectors = require('./routes/subsectors');
app.use('/api/subsectors', AuthGuard, subsectors);

const radarCat = require('./routes/radarCategory');
app.use('/api/radar-category', AuthGuard, radarCat);

const subRadarCat = require('./routes/subRadarCategory');
app.use('/api/sub-radar-category', AuthGuard, subRadarCat);

const careers = require('./routes/careers');
// app.use('/api/careers', AuthGuard, careers);
app.use('/api/careers', careers);

const jobZones = require('./routes/jobZones');
app.use('/api/jobZones', jobZones);

const oNetCareers = require('./routes/oNetCareers');
app.use('/api/oNetCareers', oNetCareers);

const auth = require('./routes/auth');
app.use('/api/auth', auth);

const lmiSkillsAbilities = require('./routes/lmiSkillsAbilities');
app.use('/api/lmiSkillsAbilities', AuthGuard, lmiSkillsAbilities);

const courses = require('./routes/courses');
app.use('/api/courses', AuthGuard, courses);

const frontend = require('./routes/frontend');
app.use('/api/frontend', frontend);

const analytics = require('./routes/analytics');
app.use('/api/analytics', analytics);

const setting = require('./routes/setting');
app.use('/api/setting',AuthGuard, setting);

const open = require("./routes/open")
app.use("/api/v2", open)

const chatbot = require('./routes/chatbot');
app.use('/api/chatbot', chatbot);

// AI Management routes (protected with AuthGuard)
const aiManagement = require('./routes/ai-management');
app.use('/api/ai', AuthGuard, aiManagement);

// please put in end, it cause problem
// throw "No token provided"
const misc = require('./routes/misc');
app.use('/api', AuthGuard, misc);
//

//          
let http = require("http").Server(app);
let serverPort = process.env.PORT || 8000;

if (process.env.NODE_ENV === "production") {
  app.use(express.static(path.join(__dirname, "frontend", "build")));
  app.use(express.static(path.join(__dirname, "client", "build")));
  
  app.get('/admin', (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "build", "index.html"));
  });
  
  app.get('/admin/*', (req, res) => {
    res.sendFile(path.resolve(__dirname, "client", "build", "index.html"));
  });
  // handle API exceptions
  app.get('/api/*', (req, res) => {
    return messageResponse(NOT_FOUND, "API", false, 404, null, res)
  });

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, "frontend", "build", "index.html"));
  });

  console.log('Production server mode on')
} else {
  app.get("/", (req, res) => res.send(`HorizonAI is running on port: ${serverPort}`));
};

http.listen(serverPort, function () {
  console.log("listening in port ", serverPort);
});

console.log('SERVER IS READY.');
