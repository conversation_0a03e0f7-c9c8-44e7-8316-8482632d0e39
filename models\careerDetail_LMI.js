const mongoose = require("mongoose");

const CareerDetailSchema = new mongoose.Schema({
  careerID: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "careers",
  },
  title: String,
  socCode: String,
  description: String,
  tasks: String,
  qualifications: String,
  add_titles: {
    type: [String],
    default: []
  }
});

module.exports.CareerDetailSchema = CareerDetailSchema;

class CareerDetail_LMI extends mongoose.Model { }

mongoose.model(CareerDetail_LMI, CareerDetailSchema, "careerDetail_LMIs");

module.exports.CareerDetail_LMI = CareerDetail_LMI;