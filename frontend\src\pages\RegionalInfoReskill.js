import React, { useEffect, useMemo, useState } from 'react';
import {
  Box,
  Chip,
  Grid,
  Typography,
  Paper,
  Container,
  Card,
  CardMedia,
  CardContent,
  useTheme,
  CircularProgress,
} from '@mui/material';
import AuthWrapper from 'src/components/AuthWrapper';
import StepperComponent from 'src/components/stepper/Stepper';
import { useParams } from 'react-router';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import { useDispatch, useSelector } from 'react-redux';
import Cookies from 'js-cookie';
import { isEmpty, set } from 'lodash';
import AssignmentIcon from '@mui/icons-material/Assignment';
import RegionStepper from 'src/components/RegionStepper';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';
import { Icon } from '@iconify/react';
import { getRegionBySlug, getRegionInfo } from './CareerHistory/CareerHistorySlice';
import { ArcticonsEmojiSpiderWeb } from './Reskill_Flow/SpiderIcon';

const sectors = [
  'Advanced Manufacturing and Engineering (AME)',
  'Construction',
  'Cyber Security',
  'IT and Defence',
  'Agri-Tech',
  'Health and Care',
];

const careers = [
  'Solicitor',
  'Accountant',
  'Insurance Broker',
  'Personal Assistant',
  'Market Research Analyst',
  'Computer Manager',
  'Software Developer',
  'Statistician',
  'Paralegal',
  'Technical Writer',
];

const chipStyle = (filled = false) => ({
  backgroundColor: filled ? '#888' : '#fff',
  color: filled ? '#fff' : '#000',
  border: '1px solid #888',
  fontWeight: 500,
  borderRadius: '24px',
  px: 2,
  py: 1,
  m: 0.5,
});

const RegionalInfoReskill = () => {
  const params = useParams();
  const isRegion = !!params?.rg_name;

  const regions = useSelector((state) => state.careerHistory);
  const [regionData, setData] = useState({});

  const [loading, setLoading] = useState(false);
  const { cg_name, rg_name } = useParams();
  const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
  const [regionInfoData, setRegionInfoData] = useState(null);
  const dispatch = useDispatch();
  // useEffect(() => {
  //   if (regionData?._id) {
  //     const fetchData = async () => {
  //       try {
  //         const resultAction = await dispatch(getRegionInfo(regionData._id));
  //         if (getRegionInfo.fulfilled.match(resultAction)) {
  //           setRegionInfoData(resultAction.payload); // store the result
  //         } else {
  //           console.error("Failed to fetch region by slug", resultAction.payload);
  //         }
  //       } catch (err) {
  //         console.error("Unexpected error:", err);
  //       }
  //     };

  //     fetchData();
  //   }
  // }, [regionData, dispatch]);
  const buttonColor = regionData?.button?.bgColor || clgDetails?.region?.button?.bgColor || '';
  const isRegionData = !!clgDetails?.region;
  const buttonFontColor = regionData?.button?.color || clgDetails?.region?.button?.color || '';
  const logo = regionData?.logo || '';
  const partnerLogos = regionData?.partnerLogos || [];
  const primaryColor = regionData?.primaryColor || '';
  const fontColor = regionData?.fontColor || '';
  const secondaryColor = regionData?.secondary;
  const bgImage = regionData?.bgImage || clgDetails?.region?.bgImage || '';

  // const SpiderIcon = () => <Icon icon="noto:spider-web" color={buttonColor} width="36" height="36" />
  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    {
      icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color={buttonFontColor || 'white'} />,
      label: 'Results',
    }, // Capitalized if it's a component
  ];
  const stepperSteps = useMemo(
    () => [
      {
        label: 'Your Skills',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/skilldar`
          : `/${params.cg_name}/reskill/skilldar`,
      },
      {
        label: 'Your Careers',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/career-courses`
          : `/${params.cg_name}/reskill/career-courses`,
      },
      // {
      //   label: 'Colleges',
      //   link: isRegion ? `/region/${params.rg_name}/reskill/colleges` : `/${params.cg_name}/reskill/colleges`
      // },
      {
        label: 'Your Region',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/regional-info`
          : `/${params.cg_name}/reskill/regional-info`,
      },
    ],
    [params, isRegion]
  );
  const stepperStepsForRegion = useMemo(
    () => [
      {
        label: 'Your Skills',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/skilldar`
          : `/${params.cg_name}/reskill/skilldar`,
      },
      {
        label: 'Your Careers',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/career-courses`
          : `/${params.cg_name}/reskill/career-courses`,
      },
      {
        label: 'Colleges',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/colleges`
          : `/${params.cg_name}/reskill/colleges`,
      },
      {
        label: 'Your Region',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/regional-info`
          : `/${params.cg_name}/reskill/regional-info`,
      },
    ],
    [params, isRegion]
  );

  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);

  useEffect(() => {
    if (rg_name) {
      setLoading(true);
      dispatch(getRegionBySlug(rg_name))
        .then(() => setLoading(false))
        .finally(() => setLoading(false));
    }
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (cg_name) {
      setData(clgDetails?.region);
      return;
    }

    if (regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(regionData)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, regionData, cg_name, clgDetails?.region]);

  const colorTheme = useTheme();
  return (
    <AuthWrapper title="Regional-Info">
      <Box
        sx={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          minHeight: '100vh',
          backgroundAttachment: 'fixed',
          pb: 4,
        }}
        className="page-content-wrapper"
      >
        <Container maxWidth="xl">
          <Box pt={5} className="content">
            <Box pb={2}>
              <RegionStepper
                steps={steps}
                activeStep={1} // Change to 1 to highlight "Results"
                buttonColor={buttonColor || colorTheme.palette.primary.main}
                buttonFontColor={buttonFontColor || 'white'}
              />
            </Box>
            <ThinkSkillsHeader fontColor={isRegionData || isRegion ? 'white' : ''} />
            {/* <StepperComponent steps={stepperSteps} activeStep={3} noIcon /> */}
            <SecondRegionStepper
              steps={isRegion ? stepperStepsForRegion : stepperSteps}
              activeStep={isRegion ? 3 : 2}
              noIcon
            />
          </Box>
          {loading ? (
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '70vh',
              }}
            >
              <CircularProgress />
            </Box>
          ) : (
            <Box sx={{ backgroundColor: 'white', borderRadius: '0 0 12px 12px', mt: 0.2, mb:4 }} p={2} pb={4}>
              {!isEmpty(regionData) ? (
                <Box>
                  <Typography variant="h5" fontWeight="bold" textAlign="center" mt={2} mb={3}>
                    ThinkSkills — {regionData?.name}
                  </Typography>

                  <Typography
                    variant="body1"
                    textAlign="center"
                    maxWidth={1200}
                    mx="auto"
                    color="text.secondary"
                    sx={{
                      color: '#000000',
                    }}
                    mb={4}
                  >
                    {regionData?.description}
                  </Typography>

                  <Grid container spacing={2}>
                    {regionData?.bannerImages?.length > 0 &&
                      regionData.bannerImages.map((img, index) => (
                        <Grid item xs={12} md={12} key={index}>
                          <Card
                            sx={{
                              borderRadius: 2,
                              overflow: 'hidden',
                              boxShadow: 3,
                              mx: 3,
                              // my: 1,
                            }}
                          >
                            <CardMedia
                              component="img"
                              image={img}
                              alt={`banner-${index}`}
                              sx={{
                                width: '100%',
                                aspectRatio: '5 / 1', // ~same as your screenshot
                                objectFit: 'cover',
                              }}
                            />
                          </Card>
                        </Grid>
                      ))}
                  </Grid>
                </Box>
              ) : (
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    height: '70vh',
                    alignItems: 'center',
                  }}
                >
                  <Typography variant="h6" fontWeight="bold" textAlign="center" mb={3}>
                    No Region Data Found
                  </Typography>
                </Box>
              )}
            </Box>
          )}

          {/* <Box p={4}>
            <Typography
              sx={{
                fontSize: '28px !important',
                fontWeight: 'bold !important',
                textAlign: 'start !important',
              }}
            >
              Regional Information
            </Typography>
            <Typography sx={{ fontSize: '16px !important', textAlign: 'start !important' }}>
              The hottest careers and growth sectors in Worcestershire
            </Typography>
            <Typography
              sx={{ fontSize: '14px', mt: 2, maxWidth: '900px', textAlign: 'justify !important' }}
            >
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod bibendum
              laoreet. Proin gravida dolor sit amet lacus accumsan et viverra justo commodo. Proin
              sodales pulvinar tempor. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
              Aenean euismod bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et
              viverra justo commodo.
            </Typography>

            <Grid container spacing={2} mt={5}>
              <Grid item xs={12} md={4}>
                <Typography
                  sx={{ fontSize: '18px', fontWeight: 500, mb: 2, textAlign: 'center !important' }}
                >
                  Key Growth Sectors
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {sectors.map((sector, index) => (
                    <Chip
                      key={index}
                      label={sector}
                      variant={index % 2 === 0 ? 'outlined' : 'filled'}
                      color={index % 2 === 0 ? 'default' : 'primary'}
                      sx={{ fontSize: '13px', borderRadius: '20px' }}
                    />
                  ))}
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Typography
                  sx={{ fontSize: '18px', fontWeight: 500, mb: 2, textAlign: 'center !important' }}
                >
                  Current In-demand Careers
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {careers.map((career, index) => (
                    <Chip
                      key={index}
                      label={career}
                      variant={index % 3 === 0 ? 'outlined' : 'filled'}
                      color={index % 3 === 0 ? 'default' : 'secondary'}
                      sx={{ fontSize: '13px', borderRadius: '20px' }}
                    />
                  ))}
                </Box>
              </Grid>

              <Grid item xs={12} md={4}>
                <Paper elevation={1} sx={{ p: 2, bgcolor: '#f3f3f3' }}>
                  <Typography
                    sx={{ fontSize: '16px', fontWeight: 500, textAlign: 'center !important' }}
                  >
                    Things to consider when selecting an employer
                  </Typography>
                  {[1, 2, 3].map((i) => (
                    <Box key={i} mt={2}>
                      <Typography
                        sx={{ fontSize: '15px !important', fontWeight: 'bold !important' }}
                      >
                        Heading
                      </Typography>
                      <Typography sx={{ fontSize: '14px !important' }}>
                        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aenean euismod
                        bibendum laoreet. Proin gravida dolor sit amet lacus accumsan et viverra
                        justo commodo.
                      </Typography>
                    </Box>
                  ))}
                </Paper>
              </Grid>
            </Grid>
          </Box> */}
        </Container>
      </Box>
    </AuthWrapper>
  );
};

export default RegionalInfoReskill;
