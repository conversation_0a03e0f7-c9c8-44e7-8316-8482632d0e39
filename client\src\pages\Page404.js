import { Helmet } from 'react-helmet-async';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import jwtDecode from "jwt-decode";
import Cookies from 'universal-cookie';
// @mui
import { styled } from '@mui/material/styles';
import { Button, Typography, Container, Box } from '@mui/material';
import { useEffect } from 'react';
import { APP_ROUTER_BASE_URL } from '../utils';

// ----------------------------------------------------------------------

const StyledContent = styled('div')(({ theme }) => ({
  maxWidth: 480,
  margin: 'auto',
  minHeight: '100vh',
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'column',
  padding: theme.spacing(12, 0),
}));

// ----------------------------------------------------------------------

export default function Page404() {
  const cookies = new Cookies();
  const location = useLocation();
  const jwtToken =  cookies.get("token")
  const Role = jwtToken && jwtDecode(jwtToken)
  const role = String(Role?.role) || "5"
  const navigate = useNavigate()

  const navigateHome = () => {
    if(role === '1'){
      navigate(`${APP_ROUTER_BASE_URL}`)
    }else if( role === '2'){
      navigate(`${APP_ROUTER_BASE_URL}dashboard/collegegroups`)
    }else if( role === '3'){
      navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges`)
    }else if( role === '4'){
      navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses`)
    }else{
      navigate(`${APP_ROUTER_BASE_URL}defaultuser`)
    }
  }

  return (
    <>
      <Helmet>
        <title> 404 Page Not Found | ThinkSkill </title>
      </Helmet>

      <Container>
        <StyledContent sx={{ textAlign: 'center', alignItems: 'center' }}>
          <Typography variant="h3" paragraph>
            Sorry, page not found!
          </Typography>

          <Typography sx={{ color: 'text.secondary' }}>
            Sorry, we couldn’t find the page you’re looking for. Perhaps you’ve mistyped the URL? Be sure to check your
            spelling.
          </Typography>

          <Box
            component="img"
            src="/assets/illustrations/illustration_404.svg"
            sx={{ height: 260, mx: 'auto', my: { xs: 5, sm: 10 } }}
          />

          <Button
          //  to="/" size="large" variant="contained" component={RouterLink}
           onClick={navigateHome}
           variant="contained"
           >
            Go to Home
          </Button>
        </StyledContent>
      </Container>
    </>
  );
}
