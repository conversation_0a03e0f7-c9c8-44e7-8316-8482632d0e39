import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useDispatch } from 'react-redux';
import { Box, Button, CircularProgress, Popover, Stack, Table, TableBody, TableCell, TableContainer, TableRow, Typography } from '@mui/material';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import CloseIcon from '@mui/icons-material/Close';
import { setAlert } from 'src/layouts/main/MainLayoutSlice';
import { useGetCompareCareersDataMutation } from 'src/pages/Upskill_Flow/UpskillSlice';
import { skillLevels } from 'src/assets/data/Dummy';
import { currencyFormat } from 'src/utils';
import CareerCard from './CareerCard'
import SkilldarPop from '../SkilldarPopup/SkilldarPop';
import ModalComponent from '../Modal/Modal';


const CompareCareerComponent = ({openPopup, HandleClosePopup, anchorEl, compareCareersData, downloadHandler, showCareerDetailsPopup, removeFromComparison, setCompareCareersData, getSkillsReport, stepType}) => {
    const dispatch = useDispatch();
    const ref = useRef(null);
    const [chartWidth, setChartWidth] = useState('100%');
    const [getCompareCareersData] = useGetCompareCareersDataMutation();
    const [loading, setLoading] = useState(false);
    const [skillRadarPopup, setSkillRadarPopup] = useState(false);
    const [popupDetails, setPopupDetails] = useState({});

    const ShowFullRadar =(data)=>{
        setPopupDetails(data)
        setSkillRadarPopup(true)
    }
    const HandleCloseRadarPopup =()=>{
        setSkillRadarPopup(false)
    }

    const removeCareer = (id) => {
        if(compareCareersData?.length > 0){
            setLoading(true);
            const newState = compareCareersData.filter((career)=> career.id !== id);
            setCompareCareersData(newState);
            setTimeout(() => {
                setLoading(false);
            }, 1200);
        }
    }


    const darkBlue = "#0B0B45"

  return (
    <>     
    <ModalComponent
        id='compare-careers'
        open={openPopup}
        className='app-popup compare-career'
    >
        <Box className='popup-wrapper'>
            <Box className='popup-head compare'>
                <Box sx={{display:'flex', alignItems: 'center', width: '65%'}}>
                    <Stack>
                        <Button sx={{mr:2}} className='close-icon-btn'  onClick={HandleClosePopup}><CloseIcon className='close-career-popup' sx={{bgcolor:darkBlue}}/></Button>
                    </Stack>
                    <Stack>

                        <Typography variant='h3' textAlign='left' color='primary.dark'>Compare Careers</Typography>
                        <Typography variant='body' color='primary.dark'>Compare your favourite {stepType === 'Reskill' ? 'Reskill' : 'Upskill'} career options (limited to {stepType === 'Reskill' ? 'six' : 'seven'} in total)</Typography>
                    </Stack>
                </Box>
                <Button  onClick={()=>getSkillsReport('comparison')} sx={{backgroundColor:(theme)=>`${theme.palette.secondary.main} !important`}}  className='career-popup-head-btn'><Typography variant='button2' sx={{ textTransform:'none',color:'white'}} >Get Skill Report</Typography><FileDownloadOutlinedIcon sx={{ml:3}} /> </Button>
                {/* <Button sx={{marginRight: '50px !important'}} className='icon-btn-outlined' onClick={()=>getSkillsReport('comparision')}><Typography variant='body1' color='primary.light' >Get Skills Report</Typography><FileDownloadOutlinedIcon/></Button> */}
                {/* <Button className='close-btn' onClick={HandleClosePopup}><CloseIcon/></Button> */}
            </Box>
            {
                compareCareersData && !loading?
                <Box sx={{overflowX: 'auto'}} className='popup-content-wrapper'>
                    <Box className='content-wrapper'>
                        <Box className="table-head-row">
                                <Typography  sx={{minHeight:'100px',backgroundColor:'#f3f3f3',marginRight:'1px',marginBottom:'3px'}} variant='medium' color='primary.black' />
                                <Box className='compare-skill' >
                                    <Box sx={{width: '100%'}}>
                                        <Typography variant='capitalize' textAlign='left' color={darkBlue} sx={{marginBottom:'8px'}}>Skill Level</Typography>
                                        {
                                            skillLevels.map((skill)=>(
                                                <Typography  key={skill.title} sx={{marginBottom:'0px !important'}} className='skill-level'  color={darkBlue}><span className='line' style={{borderColor: skill.color,width:'15%',display:'inline-block'}}/>{skill.title}</Typography>
                                            ))
                                        }
                                    </Box>
                                </Box>                            
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Average salary</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Transfer window</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Average weekly hours</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Overall predicted growth/decline</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Regional predicted growth/decline</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Number of courses available</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Course level range</Typography>
                            <Typography className='compare-data' variant='bold' color='white'  bgcolor='primary.main'>Course length range</Typography>
                            <Box
                                sx={{backgroundColor:'#f6f6f6',width:'100%',paddingBottom:'40px',paddingTop:'25px'}}
                            >
                                <Button sx={{backgroundColor:'primary.main',borderRadius:'0px',color:'white',width:'80%',p:2,visibility:'hidden'}}><Typography variant='capitalizeSmall'>Course Details</Typography></Button>
                                <Button className='icon-btn' sx={{width:'100%',backgroundColor:'#f6f6f6',visibility:'hidden'}}><Typography color='primary.light'>Remove</Typography></Button>
                            </Box>
                        </Box> 
                         {compareCareersData.map((item, index)=>(
                            <Box className='career-row' key={index} ref={ref}>
                                <CareerCard
                                    item={item} index={index}
                                    chartWidth={chartWidth}
                                    showCareerDetailsPopup={showCareerDetailsPopup}
                                    removeFromComparison={removeFromComparison}
                                    removeCareer={removeCareer}
                                    currencyFormat={currencyFormat}
                                    ShowFullRadar={ShowFullRadar}
                                />
                            </Box>
                        ))}
                    </Box>
                </Box>
                :
                <Box sx={{position: 'absolute', top: '42%', left: '48%'}}>
                    <CircularProgress/>
                </Box>
            }
   </Box>
        </ModalComponent>
        {skillRadarPopup &&
            <SkilldarPop
                HandleCloseRadarPopup={HandleCloseRadarPopup}
                skillRadarPopup={skillRadarPopup}
                popupDetails={popupDetails}
            />
        }
    </>
  )
}

export default CompareCareerComponent

CompareCareerComponent.propTypes = {
    openPopup: PropTypes.func,
    HandleClosePopup: PropTypes.func,
    anchorEl: PropTypes.bool,
    downloadHandler: PropTypes.func,
    showCareerDetailsPopup: PropTypes.func,
    removeFromComparison: PropTypes.func,
    getSkillsReport: PropTypes.func,
    stepType: PropTypes.string,
    compareCareersData: PropTypes.node,
    setCompareCareersData: PropTypes.func
};