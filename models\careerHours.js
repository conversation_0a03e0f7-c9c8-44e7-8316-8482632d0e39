const { Schema, Model, model } = require("mongoose");


const CareerHoursSchema = new Schema({
  regionID: {
    type: Schema.Types.ObjectId,
    ref: "regions",
  },
  socCode: { // first 3 digits
    type: String,
    default: ""
  },
  meanValue: {
    type: Number,
    default: null
  },
  medianValue: {
    type: Number,
    default: null
  },
})

module.exports.CareerHoursSchema = CareerHoursSchema;

class CareerHours extends Model {

}

model(CareerHours, CareerHoursSchema, "careerHours");

module.exports.CareerHours = CareerHours;