const mongoose = require("mongoose");

const ChatViolationSchema = new mongoose.Schema({
  sessionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ChatSession",
    required: true,
    index: true
  },
  collegeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "college",
    required: true,
    index: true
  },
  violationType: {
    type: String,
    enum: ['profanity', 'irrelevant', 'off-topic', 'spam', 'rate-limit'],
    required: true,
    index: true
  },
  originalMessage: {
    type: String,
    required: true
  },
  detectionMethod: {
    type: String,
    enum: ['bad-words', 'embedding-similarity', 'rate-limiter', 'manual'],
    required: true
  },
  actionTaken: {
    type: String,
    enum: ['warning', 'session-flagged', 'session-ended', 'message-blocked'],
    required: true
  },
  severity: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium'
  },
  ipAddress: {
    type: String,
    required: true
  },
  userAgent: {
    type: String,
    default: ""
  },
  additionalData: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Indexes for analytics and monitoring
ChatViolationSchema.index({ collegeId: 1, createdAt: -1 });
ChatViolationSchema.index({ violationType: 1, createdAt: -1 });
ChatViolationSchema.index({ ipAddress: 1, createdAt: -1 });

// Static methods for analytics
ChatViolationSchema.statics.getViolationsByCollege = function(collegeId, days = 7) {
  const startDate = new Date();
  startDate.setDate(startDate.getDate() - days);
  
  return this.aggregate([
    {
      $match: {
        collegeId: mongoose.Types.ObjectId.isValid(collegeId) ? new mongoose.Types.ObjectId(collegeId) : collegeId,
        createdAt: { $gte: startDate }
      }
    },
    {
      $group: {
        _id: "$violationType",
        count: { $sum: 1 },
        severity: { $push: "$severity" }
      }
    }
  ]);
};

ChatViolationSchema.statics.getTopViolatingIPs = function(limit = 10) {
  return this.aggregate([
    {
      $group: {
        _id: "$ipAddress",
        violationCount: { $sum: 1 },
        violationTypes: { $addToSet: "$violationType" },
        lastViolation: { $max: "$createdAt" }
      }
    },
    { $sort: { violationCount: -1 } },
    { $limit: limit }
  ]);
};

class ChatViolation extends mongoose.Model {}

mongoose.model(ChatViolation, ChatViolationSchema, "chatViolations");

module.exports.ChatViolation = ChatViolation;
module.exports.ChatViolationSchema = ChatViolationSchema;
