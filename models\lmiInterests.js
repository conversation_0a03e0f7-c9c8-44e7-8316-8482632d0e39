const { Schema, Model, model } = require("mongoose");

const lmiInterestsSchema = new Schema({
  careerID: {
    type: Schema.Types.ObjectId,
    ref: "careers",
  },
  soc: Number,
  onetcode: String,
  scales: [
    {
      id: String,
      interests: [
        {
          id: String,
          name: String,
          value: Number
        }
      ]
    }
  ]
})

module.exports.lmiInterestsSchema = lmiInterestsSchema;

class LmiInterests extends Model {

}

model(LmiInterests, lmiInterestsSchema, "LmiInterests");

module.exports.LmiInterests = LmiInterests;