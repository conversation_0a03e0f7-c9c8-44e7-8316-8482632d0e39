const axios = require('axios');
require('dotenv').config();

/**
 * Test the new fine-tuning API endpoints
 */
const testNewAPIs = async () => {
  const baseURL = 'http://localhost:3000';
  
  console.log('🧪 Testing New Fine-Tuning API Endpoints');
  console.log('=========================================');

  try {
    // Test 1: Check initial training data status
    console.log('\n📊 Test 1: Check Training Data Status (Initial)');
    const initialStatus = await axios.get(`${baseURL}/api/chatbot/admin/training-data/status`);
    console.log('✅ Status check successful');
    console.log(`   Ready: ${initialStatus.data.data.ready}`);

    // Test 2: Generate training data
    console.log('\n🚀 Test 2: Generate Training Data');
    const generateResponse = await axios.post(`${baseURL}/api/chatbot/admin/training-data/generate`, {
      maxExamplesPerType: 10,
      validationSplit: 0.2,
      validateData: true,
      examplesPerRecord: 3
    });
    
    console.log('✅ Training data generation successful');
    console.log(`   Training Examples: ${generateResponse.data.data.trainingExamples}`);
    console.log(`   Validation Examples: ${generateResponse.data.data.validationExamples}`);
    console.log(`   Quality Score: ${generateResponse.data.data.qualityScore}`);

    // Test 3: Check training data status after generation
    console.log('\n📊 Test 3: Check Training Data Status (After Generation)');
    const finalStatus = await axios.get(`${baseURL}/api/chatbot/admin/training-data/status`);
    console.log('✅ Status check successful');
    console.log(`   Ready: ${finalStatus.data.data.ready}`);
    
    if (finalStatus.data.data.files.trainingData?.exists) {
      console.log(`   Training Examples: ${finalStatus.data.data.files.trainingData.examples}`);
    }
    if (finalStatus.data.data.files.validationData?.exists) {
      console.log(`   Validation Examples: ${finalStatus.data.data.files.validationData.examples}`);
    }

    // Test 4: Test system status
    console.log('\n📊 Test 4: System Status');
    const systemStatus = await axios.get(`${baseURL}/api/chatbot/admin/status`);
    console.log('✅ System status check successful');
    console.log(`   System Status: ${systemStatus.data.data.system.status}`);

    // Test 5: Test model endpoint
    console.log('\n🧪 Test 5: Test Model');
    const modelTest = await axios.post(`${baseURL}/api/chatbot/admin/test-model`, {
      testMessage: 'Hello, this is a test of the new API endpoints.'
    });
    console.log('✅ Model test successful');
    console.log(`   Test Passed: ${modelTest.data.data.testPassed}`);
    console.log(`   Model Used: ${modelTest.data.data.modelUsed}`);

    console.log('\n🎉 All API tests completed successfully!');
    console.log('✅ Your fine-tuning API endpoints are working correctly');
    console.log('\n📋 Summary:');
    console.log(`   - Training data generated: ${generateResponse.data.data.trainingExamples} examples`);
    console.log(`   - Validation data generated: ${generateResponse.data.data.validationExamples} examples`);
    console.log(`   - Quality score: ${generateResponse.data.data.qualityScore}`);
    console.log(`   - System ready for fine-tuning: ${finalStatus.data.data.ready}`);

  } catch (error) {
    console.error('\n❌ API test failed:', error.response?.data?.message || error.message);
    if (error.response?.data) {
      console.error('   Response data:', error.response.data);
    }
  }
};

// Run test if called directly
if (require.main === module) {
  testNewAPIs();
}

module.exports = testNewAPIs;
