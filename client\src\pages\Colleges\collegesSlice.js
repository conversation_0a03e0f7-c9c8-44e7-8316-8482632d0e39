import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import axiosInstance from '../../utils/axiosInstance'

export const getColleges = createAsyncThunk("colleges/getColleges", async (data, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "colleges/get",
            method: "GET",
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get colleges", error)
        return rejectWithValue(error)
    }
}
)
export const syncCourse = createAsyncThunk("colleges/syncCourse", async (id, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: `colleges/sync/courses?id=${id}`,
            method: "GET",
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get colleges", error)
        return rejectWithValue(error)
    }
}
)

export const postCollege = createAsyncThunk('colleges/postCollege', async (data, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "colleges/add",
            method: "POST",
            data
        })
        // response.data.name = data.name
        response.data.name = data?.name;
        response.data.groupName = data?.groupName ;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const editCollege = createAsyncThunk('colleges/editCollege', async (data, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "colleges/update",
            method: "PUT",
            data
        })
        // response.data.name = data.name
        // response.data.name = data?.name;
        // response.data.groupName = data?.groupName ;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeCollege = createAsyncThunk('colleges/removeCollege', async (data) => {

    try {
        const response = await axiosInstance({
            url: "colleges/remove",
            method: "DELETE",
            data
        })
        response.data.id = data.id
        return response.data;
    } catch (error) {
        return error
    }
})

const collegesSlice = createSlice({
    name: 'colleges',
    initialState: {
        // colleges: [...colleges],
        colleges: [],
        status: 'idle', // 'idle' | 'pending' | 'succeeded' | 'failed'
        error: null,
        courseSyncLoading: false,
    },
    reducers: {
        // addCollege: (state, action) => {
        //     state.colleges.push(action.payload)
        // },
        // updateColleges: (state, action) => {

        // },
        // deleteCollege: (state, action) => {
        //     state.colleges = state.colleges.filter(college => college.id !== action.payload.id)
        // },
    },
    extraReducers: {
        [getColleges.pending]: (state) => {
            state.status = "pending"
        },
        [getColleges.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data
            const colleges = []
            data?.map(singleCollege => {
                const college = {
                    name: singleCollege?.name,
                    _id: singleCollege?._id,
                    groupName: singleCollege?.collegeGroup?.name,
                    groupId: singleCollege?.collegeGroupId,
                    slug:singleCollege?.slug,
                    hasApiKey: singleCollege?.hasApiKey
                }
                colleges.push(college)
                return colleges
            })
            state.colleges = colleges
        },
        [getColleges.rejected]: (state, action) => {
            state.status = "rejected"
            const error = action.payload
            console.log("colleges err", error)
        },
        [syncCourse.pending]: (state) => {
            state.courseSyncLoading = true;
        },
        [syncCourse.fulfilled]: (state, action) => {
            state.courseSyncLoading = false;
        },
        [syncCourse.rejected]: (state, action) => {
            state.courseSyncLoading = false;
        },


        [postCollege.fulfilled]: (state, action) => {
            const data = action.payload
            state.colleges.push(data)
            // console.log("post data",data)
        },
        [postCollege.rejected]: (state, action) => {
            const error = action.payload
            console.log("error post college", error)
        },
        [editCollege.fulfilled]: (state, action) => {
          
        },
        [removeCollege.fulfilled]: (state, action) => {
            // const = action.payload
            const data = action.payload
            // console.log("remove college",data)
            state.colleges = state.colleges.filter(college => college._id !== data.id)
        },
        [removeCollege.rejected]: (state, action) => {
            // const = action.payload
            const error = action.payload
            console.log("remove college error", error)
        },
    }
})
export const { addCollege, deleteCollege } = collegesSlice.actions
export default collegesSlice.reducer