import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance'

export const getSubSectors = createAsyncThunk('subsectors/getSector', async () => {
    try {
        const response = await axiosInstance({
            url: "subsectors/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        console.log("error get users", error)
        return error
    }
})

export const postSubSector = createAsyncThunk("subsectors/addSubSector", async ({ name, sectorId, sectorName, priority }) => {
    try {
        const response = await axiosInstance({
            url: "subsectors/add",
            method: "POST",
            data: { name, sectorId, priority }
        })
        response.data.name = name;
        response.data.priority = priority;
        response.data = { ...response.data, sectorName, sectorId };
        response.data._id = response.data.id
        return response
    } catch (error) {
        console.log("error post users", error)
        return error
    }
})

export const updateSubSector = createAsyncThunk('sectors/updateSubSector', async ({ name, sectorName, sectorId, id, priority }, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "subsectors/update",
            method: "PUT",
            data: { id, name, sectorId, priority }
        })
        response.data = { ...response.data, sectorName, sectorId, priority };
        response.data.name = name;
        response.data._id = id;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeSubSector = createAsyncThunk('sectors/removeSubSectors', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "subsectors/remove",
            method: "DELETE",
            data
        })
        response.data._id = data.id
        response.data.sectorId = data.sectorId
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

const subSectorsSlice = createSlice({
    name: 'subSectors',
    initialState: {
        status: 'idle',
        subSectors: []
    },
    reducers: {
        addSector: (state, action) => {
            state.subSectors.push(action.payload)
        },
        updateSubSector: (state, action) => {

        },
        deleteSector: (state, action) => {
            state.subSectors = state.subSectors.filter(subSector => subSector.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getSubSectors.pending]: (state) => {
            state.status = 'pending'
        },
        [getSubSectors.fulfilled]: (state, action) => {
            state.subSectors = action.payload?.data
            state.status = 'succeded'
        },
        [getSubSectors.rejected]: (state, action) => {
            console.log("error", action.payload)
            state.status = 'rejected'
        },
        [postSubSector.fulfilled]: (state, action) => {
            const { name, id, sectorName, sectorId, priority } = action.payload.data
            const data = { sectorId, name, sectorName, _id: id, priority }
            state.subSectors?.push(data)
            // console.log("post data",action.payload.data)
        },
        [postSubSector.rejected]: (state, action) => {
            const error = action.payload
            console.log("error post sector", error)
        },
        [updateSubSector.fulfilled]: (state, action) => {
            state.subSectors = state.subSectors.map((item) => {
                if (item._id === action.payload._id) {
                    item.name = action.payload.name
                    item.sectorId = action.payload.sectorId
                    item.sectorName = action.payload.sectorName
                    item.priority = action.payload.priority
                    return item
                }
                return item
            })
        },
        [updateSubSector.rejected]: (state, action) => {
            const error = action.payload
            console.log("error update sector", error)
        },
        [removeSubSector.fulfilled]: (state, action) => {
            // const = action.payload
            const data = action.payload
            state.subSectors = state.subSectors?.filter(subSector => subSector._id !== data?._id)
        },
        [removeSubSector.rejected]: (state, action) => {
            // const = action.payload
            const error = action.payload
            console.log("remove sector error", error)
        },
    }
})
export const { addSector } = subSectorsSlice.actions;
export default subSectorsSlice.reducer