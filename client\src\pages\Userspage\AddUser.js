import { LoadingButton } from '@mui/lab';
import Cookies from 'universal-cookie';
import jwtDecode from "jwt-decode";
import { Box, Button, Card, Checkbox, Container, FormControlLabel, FormLabel, Grid, Stack, Switch, TextField, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { get } from 'lodash';
import { useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import PhoneInput from '../../components/PhoneInput';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { CancelButton, label } from '../../utils/cssStyles';
import { userValidationSchema } from '../../utils/validationSchemas';
// import { postCollege } from '../collegesSlice';
import { setSnackbar } from '../../Redux/snackbarSlice';
import noImagePlaceholder from '../../assets/images/no-image-icon-0.jpg';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { postUser } from './usersSlice';

// groups,

const AddUser = () => {
    const cookies = new Cookies();
    const jwtToken = cookies.get("token")
    const Role = jwtToken && jwtDecode(jwtToken)
    const role = String(Role?.role) || "5"

    const [isLoading, setIsLoading] = useState(false);
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const formik = useFormik({
        initialValues: {
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            password: '',
            // confirmPassword: '',
            profile: '',
            isAdmin: false
        },
        validationSchema: userValidationSchema,
        onSubmit: (values) => {
            setIsLoading(true)
            const user = {
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email,
                contactNumber: values.phone,
                password: values.password,
                photo: values.profile,
            }
            if(formik.values.isAdmin){
                user.role = 1
            }
            dispatch(postUser(user)).then(res => {
                if (res?.payload?.data?.success) {
                    // success
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: "Succesfully added User"
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/user`)
                } else {
                    const error = get(res, 'payload.response.data.message', "Something went wrong")
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error
                    }))
                }
                // show error or success message here and navigate
                // const error = res.payload.response.data
            }).finally(() => setIsLoading(false))
        },
    })


    // useEffect(() => {
    //     console.log("image",Image)
    //     if (Image) {
    //         fileReader.readAsDataURL(Image);
    //         fileReader.onload = (event) => {
    //             console.log("result ==> ", event.target.result)
    //             formik.setValues({
    //                 ...formik.values,
    //                 profile: event.target.result
    //             })
    //         }
    //     } else {
    //         formik.setValues({
    //             ...formik.values,
    //             profile: ''
    //         })
    //     }
    // }, [Image])

    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            phone: newValue
        })
    }
    const style = {
        p: 4,
    };

    const removePic = () => {
        formik.setValues({
            ...formik.values,
            profile: ''
        })
        document.getElementById("profilePic").value = ''
    }

    return (
        <Container maxWidth="xl">
            <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                Add User
            </Typography>
            <Grid container wrap='wrap' width={'100%'} gap={2}>
                <Grid item xs={6} lg={2.8} mx={'auto'} >
                    <Card sx={{ minHeight: 330 }} >
                        <Box sx={{
                            p: 4,
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}>
                            {/* <label htmlFor='profilePic' > */}
                            {/* <img className='user-profile' alt='logo' src={Image ? URL.createObjectURL(Image) : noImagePlaceholder} /> */}
                            <img className='user-profile' alt='logo' src={formik.values.profile ? formik.values.profile : noImagePlaceholder} />
                            {/* </label> */}
                            <TextField
                                sx={{ width: '100%', display: 'none' }}
                                id='profilePic'
                                name='profile'
                                // label="Profile Image"
                                onChange={(e) => {
                                    // setImage(e.target.files[0])
                                    const fileReader = new FileReader();
                                    fileReader.readAsDataURL(e.target.files[0]);
                                    fileReader.onload = (event) => {
                                        formik.setValues({
                                            ...formik.values,
                                            profile: event.target.result
                                        })
                                    }
                                }}
                                type='file'
                            />
                        </Box>
                        <Stack
                            direction={'row'}
                            justifyContent={'center'}
                            gap={1}
                            alignItems={'center'}
                        >
                            <Button
                                variant='contained'
                                sx={{ minWidth: 80, px: 0, py: 0 }}
                            >
                                <FormLabel sx={label} htmlFor='profilePic'>
                                    {formik.values.profile ? "Change" : "Add"}
                                </FormLabel>
                                {/* <label htmlFor='profilePic' >
                                    {formik.values.profile ? "Change" : "Add"}
                                </label> */}
                            </Button>
                            {formik.values.profile &&
                                <Button
                                    sx={{ minWidth: 80 }}
                                    variant='contained'
                                    color='error'
                                    onClick={removePic}
                                >
                                    Remove
                                </Button>}
                        </Stack>
                    </Card>
                </Grid>
                <Grid xs={12} lg={8.8}>
                    <Card>
                        <Box sx={style}>
                            <form
                                onSubmit={formik.handleSubmit}
                            >
                                <Grid container gap={2} >
                                    <Grid item xs={12} md={5.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='firstName'
                                            label="First Name"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.firstName}
                                            onChange={formik.handleChange}
                                            error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                                            helperText={formik.touched.firstName && formik.errors.firstName}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            value={formik.values.lastName}
                                            onBlur={formik.handleBlur}
                                            name='lastName'
                                            label="Last Name"
                                            onChange={formik.handleChange}
                                            error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                                            helperText={formik.touched.lastName && formik.errors.lastName}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            value={formik.values.email}
                                            name='email'
                                            type={'email'}
                                            label="Email"
                                            onChange={formik.handleChange}
                                            error={formik.touched.email && Boolean(formik.errors.email)}
                                            helperText={formik.touched.email && formik.errors.email}
                                            onBlur={formik.handleBlur}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8}>
                                        {/* <PhoneInput
                                            sx={{ width: "100%" }}
                                            value={formik.values.phone}
                                            name='phone'
                                            label="Phone"
                                            defaultCountry="GB"
                                            onChange={handlePhoneChange}
                                            onBlur={formik.handleBlur}
                                            error={formik.touched.phone && Boolean(formik.errors.phone)}
                                            helperText={formik.touched.phone && formik.errors.phone}
                                        /> */}
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='phone'
                                            label="Phone"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.phone}
                                            onChange={formik.handleChange}
                                            error={formik.touched.phone && Boolean(formik.errors.phone)}
                                            helperText={formik.touched.phone && formik.errors.phone}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            value={formik.values.password}
                                            name='password'
                                            type={'password'}
                                            label="Password"
                                            onChange={formik.handleChange}
                                            error={formik.touched.password && Boolean(formik.errors.password)}
                                            helperText={formik.touched.password && formik.errors.password}
                                            onBlur={formik.handleBlur}
                                        />
                                    </Grid>
                                </Grid>
                            { role === '1' && <FormControlLabel
                                 labelPlacement="start"
                                  sx={{mt:1, ml:0.2}} 
                                  control={
                                  <Checkbox
                                    value={formik.values.isAdmin}
                                    onChange={(e)=> formik.setValues({
                                        ...formik.values,
                                        isAdmin: e.target.checked
                                    })}
                                  />
                                } 
                                  label="Make this user super admin" 
                                  />}

                                <Stack direction="row" justifyContent="flex-end" >
                                    <Button
                                        type='button'
                                        variant='contained'
                                        sx={CancelButton}
                                        color='error'
                                        onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/user`)}
                                    >
                                        Cancel
                                    </Button>
                                    <LoadingButton
                                        loading={isLoading}
                                        type='submit'
                                        variant='contained'
                                        sx={{ width: '10%', m: 1, mt: 2 }}
                                    >
                                        Add
                                    </LoadingButton>
                                </Stack>
                            </form>
                        </Box>
                    </Card>
                </Grid>
            </Grid>
        </Container>
    )
}

export default AddUser