import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
  Container,
  Typography,
  Box,
  Button,
  Modal,
  IconButton,
  Stack,
} from '@mui/material';
// @mui
import { get } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import useLoading from '../../hooks/useLoading';
import { APP_ROUTER_BASE_URL } from '../../utils';
import CareerForm from './CareerForm';
import { getCareers, removeCareer } from './careerSlice';

// ----------------------------------------------------------------------
export const CAREER_TABLE_HEAD = [
  { id: 'title', label: 'Career Title', alignRight: false },
  { id: 'onetCode', label: 'Onet Code', alignRight: false },
  { id: 'socCode', label: 'SOC Code', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderCareerCells = ['title', 'onetCode', 'socCode']
const deleteTitle = "Delete Career ?"
const deleteDescription = "Are you sure you want to delete this career"

const Careers = () => {
  const dispatch = useDispatch()
  const { careers, status } = useSelector(state => state.career)
  const [Careers, setCareers] = useState([]);

  const navigate = useNavigate()
  // const loading = useLoading(status)

  useEffect(() => {
    setCareers(careers)
  }, [careers])

  useEffect(() => {
    dispatch(getCareers())
  }, [])

  const loading = useLoading(status)

  const handleFilterSearch = (event) => {
    const filteredCareers = Careers?.filter(career => career.title.toLowerCase().includes(event.target.value.toLowerCase()))
    return filteredCareers
  };
 

  const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/old-careers/create-careers`);


  const handleDeleteCareer = (career, handleOpenBackdrop, handleCloseBackdrop) => {
    handleOpenBackdrop()
    dispatch(removeCareer(career?._id)).then(res => {
      if (res.payload.success) {
        const successMessage = get(res, 'payload.message', 'Successfully removed career')
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'success',
          snackbarMessage: successMessage
        }))
      } else {
        const errorMessage = get(res, 'payload.data.message', 'something went wrong')
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'error',
          snackbarMessage: errorMessage
        }))
      }
    }).finally(() => {
      handleCloseBackdrop()
    })
  }

  const editCareer = (career) => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/old-careers/edit/${career?._id}`)
  }

  return (
    <>
      <Helmet>
        <title> Careers | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        {/* <Container> */}
        <Typography variant="h4" gutterBottom mb={3}>
          Careers
        </Typography>
        <DataTable
          pagination
          rowsPerPageProp={10}
          loading={loading}
          TableHead={CAREER_TABLE_HEAD}
          TableData={Careers}
          filterSearch={handleFilterSearch}
          searchLable={"Search by Campus..."}
          buttonText={"New Career"}
          buttonHandler={handleOpen}
          handleEdit={editCareer}
          renderCells={renderCareerCells}
          handleDelete={handleDeleteCareer}
          deleteTitle={deleteTitle}
          deleteDescription={deleteDescription}
        />
        {/* </Container> */}
      </Container>
    </>
  )
}

export default Careers






