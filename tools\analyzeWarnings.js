const fs = require('fs');
const path = require('path');

/**
 * Analyze warnings from validation report
 */
const analyzeWarnings = () => {
  console.log('📊 Analyzing Training Data Warnings');
  console.log('===================================');

  try {
    // Read validation report
    const reportFile = path.join(__dirname, '../data/validation_report.json');
    
    if (!fs.existsSync(reportFile)) {
      console.log('❌ Validation report not found. Generate training data first.');
      return;
    }

    const report = JSON.parse(fs.readFileSync(reportFile, 'utf8'));
    
    console.log('\n📋 Overall Statistics:');
    console.log(`   Total Examples: ${report.statistics?.totalExamples || 'N/A'}`);
    console.log(`   Valid Examples: ${report.statistics?.validExamples || 'N/A'}`);
    console.log(`   Warnings: ${report.warnings?.length || 0}`);
    console.log(`   Errors: ${report.errors?.length || 0}`);
    console.log(`   Quality Score: ${report.report?.qualityScore || 'N/A'}`);

    if (report.warnings && report.warnings.length > 0) {
      console.log('\n⚠️ Warning Analysis:');
      
      // Categorize warnings
      const warningCategories = {};
      report.warnings.forEach(warning => {
        const category = warning.category || 'unknown';
        if (!warningCategories[category]) {
          warningCategories[category] = [];
        }
        warningCategories[category].push(warning);
      });

      // Display warning categories
      Object.entries(warningCategories).forEach(([category, warnings]) => {
        console.log(`\n📂 ${category.toUpperCase()} Warnings (${warnings.length}):`);
        
        // Group similar warnings
        const warningGroups = {};
        warnings.forEach(warning => {
          const key = warning.message.split(':')[0]; // Get the warning type
          if (!warningGroups[key]) {
            warningGroups[key] = 0;
          }
          warningGroups[key]++;
        });

        Object.entries(warningGroups).forEach(([type, count]) => {
          console.log(`   • ${type}: ${count} occurrences`);
        });

        // Show first few examples
        console.log(`   Examples:`);
        warnings.slice(0, 3).forEach(warning => {
          console.log(`     - ${warning.message}`);
        });
        if (warnings.length > 3) {
          console.log(`     ... and ${warnings.length - 3} more`);
        }
      });

      // Calculate warning rate
      const totalExamples = report.statistics?.totalExamples || 1;
      const warningRate = (report.warnings.length / totalExamples) * 100;
      
      console.log('\n📊 Warning Rate Analysis:');
      console.log(`   Warning Rate: ${warningRate.toFixed(1)}%`);
      
      if (warningRate < 20) {
        console.log('   Status: ✅ Excellent quality - ready for training');
      } else if (warningRate < 30) {
        console.log('   Status: ⚠️ Good quality - minor improvements recommended');
      } else if (warningRate < 50) {
        console.log('   Status: 🔧 Moderate quality - improvements needed');
      } else {
        console.log('   Status: ❌ Poor quality - significant improvements required');
      }

      // Recommendations
      console.log('\n💡 Improvement Recommendations:');
      
      if (warningCategories.structure) {
        console.log('   🔧 Structure Issues:');
        console.log('      - Keep responses under 500 characters');
        console.log('      - Use simple conversation patterns (3-5 messages)');
        console.log('      - Follow system → user → assistant flow');
      }
      
      if (warningCategories.content) {
        console.log('   📝 Content Issues:');
        console.log('      - Add more educational keywords');
        console.log('      - Improve sentence structure');
        console.log('      - Increase word variety');
      }
      
      if (warningCategories.duplicates) {
        console.log('   🔄 Duplicate Issues:');
        console.log('      - Add more question variations');
        console.log('      - Use diverse response templates');
        console.log('      - Improve content generation logic');
      }

    } else {
      console.log('\n✅ No warnings found! Your training data quality is excellent.');
    }

    // Quality metrics
    if (report.report) {
      console.log('\n📈 Quality Metrics:');
      console.log(`   Overall Quality: ${report.report.qualityScore || 'N/A'}`);
      console.log(`   Educational Relevance: ${report.report.educationalRelevance || 'N/A'}`);
      console.log(`   Content Diversity: ${report.report.contentDiversity || 'N/A'}`);
      console.log(`   Structure Compliance: ${report.report.structureCompliance || 'N/A'}`);
    }

  } catch (error) {
    console.error('❌ Error analyzing warnings:', error.message);
  }
};

// Run analysis if called directly
if (require.main === module) {
  analyzeWarnings();
}

module.exports = analyzeWarnings;
