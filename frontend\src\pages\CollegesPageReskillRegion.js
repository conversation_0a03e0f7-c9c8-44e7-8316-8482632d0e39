import { Box, CircularProgress, Container, Grid, Link, Typography } from '@mui/material';
import Cookies from 'js-cookie';
import { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router';
import AuthWrapper from 'src/components/AuthWrapper';
import StepperComponent from 'src/components/stepper/Stepper';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import { useDispatch, useSelector } from 'react-redux';
import AssignmentIcon from '@mui/icons-material/Assignment';
import { get, isEmpty } from 'lodash';
import RegionStepper from 'src/components/RegionStepper';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';
import { getColleges, getRegionBySlug } from './CareerHistory/CareerHistorySlice';
import { ArcticonsEmojiSpiderWeb } from './Reskill_Flow/SpiderIcon';

const CollegesPageReskillRegion = () => {
  const params = useParams();
  const isRegion = !!params?.rg_name;

  const stepperSteps = useMemo(
    () => [
      {
        label: 'Your Skills',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/skilldar`
          : `/${params.cg_name}/reskill/skilldar`,
      },
      {
        label: 'Your Careers',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/career-courses`
          : `/${params.cg_name}/reskill/career-courses`,
      },
      {
        label: 'Colleges',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/colleges`
          : `/${params.cg_name}/reskill/colleges`,
      },
      {
        label: 'Your Region',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/regional-info`
          : `/${params.cg_name}/reskill/regional-info`,
      },
    ],
    [params, isRegion]
  );
  const regions = useSelector((state) => state.careerHistory);
  const regionsData = useSelector((state) => state.careerHistory.regions);
  const [regionData, setData] = useState({});
  const [colleges, setColleges] = useState([]);
  const [loading, setLoading] = useState(true);
  const { cg_name, rg_name } = useParams();
  const dispatch = useDispatch();
  const buttonColor = regionData?.button?.bgColor;
  const buttonFontColor = regionData?.button?.color;
  const logo = regionData?.logo || '';
  const partnerLogos = regionData?.partnerLogos || [];
  const primaryColor = regionData?.primaryColor || '';
  const fontColor = regionData?.fontColor || '';
  const secondaryColor = regionData?.secondary;
  const bgImage = regionData?.bgImage || '';
  // const SpiderIcon = () => <Icon icon="noto:spider-web" color={buttonColor} width="36" height="36" />
  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    {
      icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color={buttonFontColor} />,
      label: 'Results',
    }, // Capitalized if it's a component
  ];

  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);

  useEffect(() => {
    setLoading(true);
    dispatch(getRegionBySlug(rg_name));
  }, [rg_name, dispatch]);

  // useEffect(() => {
  //   if (regions?.regions) {
  //     setData(regions?.regions);
  //     if (!isEmpty(regionData)) {
  //       setLoading(false);
  //     }
  //   }
  // }, [regions?.regions, regionData]);

  useEffect(() => {
    if (regionsData) {
      setData(regionsData);
      if (!isEmpty(regionData)) {
        setLoading(false);
      }
    }
  }, [regionsData, regionData]);

  useEffect(() => {
    if (regionsData?._id) {
      dispatch(getColleges({ regionId: regionsData?._id })).then((response) => {
        if (get(response, 'payload.success') && get(response, 'payload.data.colleges')) {
          setColleges(get(response, 'payload.data.colleges'));
        }
      });
    }
  }, [regionsData?._id, dispatch]);

  return (
    <>
      <AuthWrapper title="Colleges">
        <Box
          sx={{
            backgroundImage: `url(${bgImage})`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            minHeight: '100vh',
            backgroundAttachment: 'fixed',
            pb:8,
          }}
          className="page-content-wrapper"
        >
          <Container maxWidth="xl">
            <Box mb={0.2} pt={5} className="content">
              <Box pb={2} >
                <RegionStepper
                  steps={steps}
                  activeStep={1} // Change to 1 to highlight "Results"
                  buttonColor={buttonColor}
                  buttonFontColor={buttonFontColor}
                />
              </Box>
              <ThinkSkillsHeader fontColor="white" />
              <SecondRegionStepper steps={stepperSteps} activeStep={2} noIcon />
            </Box>
            {regions?.isCollegeLoading ? (
              <Box
                sx={{
                  backgroundColor: 'white',
                  height: 700,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <CircularProgress />
              </Box>
            ) : (
              <>
                {colleges && colleges?.length > 0 ? (
                  <Box sx={{ backgroundColor: 'white', p: 3, borderRadius: '0 0 12px 12px' }}>
                    <Typography
                      sx={{ fontSize: '24px !important', fontWeight: 900, mt: 1 }}
                      textAlign="start"
                    >
                      We recommend the following Colleges.
                    </Typography>
                    <Grid rowSpacing={6} container spacing={6} mt={1}>
                      {colleges?.map((college, index) => (
                        <Grid item xs={12} md={4} key={index}>
                          <Box sx={{ textAlign: 'center' }}>
                            {/* Image placeholder */}
                            {/* <Box
                                                sx={{
                                                    width: '100%',
                                                    height: 180,
                                                    bgcolor: '#ccc',
                                                    mb: 2,
                                                }}
                                            /> */}

                            <Box
                              component="img"
                              src={
                                college?.image ||
                                college?.logo ||
                                '/assets/images/placeholder_college.jpg'
                              }
                              alt={college?.name}
                              sx={{
                                width: '100%',
                                aspectRatio: '3 / 2', // Maintain consistent ratio
                                objectFit: 'cover', // Crop overflow while keeping aspect
                                mb: 2,
                                boxShadow: 1, // Optional for subtle elevation
                              }}
                            />

                            <Typography
                              sx={{ fontSize: '18px !important', fontWeight: 700, my:3 }}
                              gutterBottom
                            >
                              {college?.name}
                            </Typography>

                            <Typography
                              sx={{
                                fontSize: '16px !important',
                                fontWeight: 400,
                                textAlign: 'center',
                                whiteSpace: 'pre-line',
                              }}
                            >
                              {college.description}
                            </Typography>

                            <Typography
                              sx={{
                                fontSize: '16px !important',
                                fontWeight: 400,
                                textAlign: 'center',
                                whiteSpace: 'pre-line',
                                mt: 3,
                              }}
                            >
                              Location: {college?.address1 || '-'}
                            </Typography>

                            <Link
                              href={`https://${college?.website}`}
                              target="_blank"
                              sx={{
                                display: 'block',
                                mt: 1,
                                fontWeight: 700,
                                textAlign: 'center',
                                color: 'black',
                                textDecoration: 'underline',
                                '&:hover': {
                                  textDecoration: 'underline', // keep underline on hover too
                                },
                              }}
                            >
                              {college?.website}
                            </Link>
                          </Box>
                        </Grid>
                      ))}
                    </Grid>
                  </Box>
                ) : (
                  <Box
                    sx={{
                      backgroundColor: 'white',
                      p: 2,
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      height: '77vh',
                      borderRadius: '0 0 12px 12px'
                    }}
                  >
                    <Typography
                      sx={{ fontSize: '22px !important', fontWeight: 700, mt: 1 }}
                      textAlign="start"
                    >
                      No Colleges Found
                    </Typography>
                  </Box>
                )}
              </>
            )}
          </Container>
        </Box>
      </AuthWrapper>
    </>
  );
};

export default CollegesPageReskillRegion;
