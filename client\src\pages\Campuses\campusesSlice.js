import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import campuses from '../../_mock/campuses'
import axiosInstance from '../../utils/axiosInstance';

export const getCampuses = createAsyncThunk('campuses/getCampuses', async (college, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "campuses/get",
            method: "GET",
            params:{
                collegeId: college?._id
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const postCampus = createAsyncThunk('campuses/postCampus', async (data, { rejectWithValue }) => {
    // console.log("data", data)
    try {
        const response = await axiosInstance({
            url: "campuses/add",
            method: "POST",
            data
        })
        response.data.name = data?.name;
        response.data.groupName = data?.groupName;
        response.data.collegeName = data?.collegeName;
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})

export const editCampus = createAsyncThunk('campuses/editCampus', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "campuses/update",
            method: "PUT",
            data
        })
        return response.data
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeCampus = createAsyncThunk('campuses/deleteCampus', async (campus, { rejectWithValue }) => {
    try {
        const data = {
            id: campus.id
        }
        const response = await axiosInstance({
            url: 'campuses/remove',
            method: 'DELETE',
            data,
        })
        // .then(res=> console.log("res",res)).catch(error => console.log('error',error.message))
        response.data.id = campus.id
        return response
    } catch (error) {
        console.log("error", error.message)
        return rejectWithValue(error)
    }
})

const campusesSlice = createSlice({
    name: 'campuses',
    initialState: {
        // campuses: [...campuses],
        campuses: [],
        status: 'idle', // 'idle' | 'pending' | 'succeeded' | 'failed',
        error: null,
        postError: null
    },
    reducers: {
        addCampuse: (state, action) => {
            state.campuses.push(action.payload)
        },
        updateCumpuses: (state, action) => {

        },
        deleteCampus: (state, action) => {
            state.campuses = state.campuses.filter(campus => campus.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getCampuses.pending]: (state) => {
            state.status = "pending"
        },
        [getCampuses.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data;
            const campuses = data?.map(campus => {
                return {
                    name: campus?.name,
                    id: campus?._id,
                    collegeName: campus?.college?.name,
                    collegeId: campus?.college?._id,
                    groupName: campus?.collegeGroup?.name,
                    groupId: campus?.collegeGroup?._id,
                }
            }
            )
            state.campuses = campuses
            // console.log("action, fullfiled", data)

        },
        [getCampuses.rejected]: (state, action) => {
            if(action.payload?.response?.data.data){
                state.campuses = action.payload?.response?.data?.data
            }
            state.status = "failed"
            state.error = action.payload
        },
        [postCampus.fulfilled]: (state, action) => {
            // const data = action.payload
            // const campus = {
            //     name: data.name,
            //     collegeName: data.collegeName,
            //     groupName: data.groupName
            // }
            // state.campuses.push(campus)
            
            // console.log("res post campus", action.payload)
        },
        [postCampus.rejected]: (state, action) => {
            // console.log("error post campus", action.payload)
            // state.postError = error.message
        },
        [removeCampus.fulfilled]: (state, action) => {
            // console.log("error post campus", action.payload)
            // state.postError = error.message
            state.campuses = state.campuses.filter(campus => campus.id !== action.payload.data.id)
            // console.log("remove campus",action.payload)
        },
    }
})


export const { addCampuse, deleteCampus } = campusesSlice.actions
export default campusesSlice.reducer