const { Schema, Model, model } = require("mongoose");


const CareerWorktypeSchema = new Schema({
  regionID: {
    type: Schema.Types.ObjectId,
    ref: "regions",
  },
  socCode: { // first 2 digits
    type: String,
    default: ""
  },
  FTValue: {
    type: Number,
    default: null
  },
  PTValue: {
    type: Number,
    default: null
  },
  SEValue: {
    type: Number,
    default: null
  },
})

module.exports.CareerWorktypeSchema = CareerWorktypeSchema;

class CareerWorktype extends Model {

}

model(CareerWorktype, CareerWorktypeSchema, "careerWorktype");

module.exports.CareerWorktype = CareerWorktype;