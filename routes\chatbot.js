const express = require("express");
const router = express.Router();
const chatbotController = require('../controllers/chatbot.controller');

const { chatbotRateLimit, sessionInitRateLimit, messageRateLimit } = require('../middleware/rateLimiting');
const {
  validateCollegeAccess,
  validateSession,
  checkViolationLimit,
  logChatbotRequest,
  setChatbotSecurityHeaders
} = require('../middleware/chatbotAuth');
const AuthGuard = require("../guards/auth.guard");

router.use(setChatbotSecurityHeaders);
router.use(logChatbotRequest);

/**
 * Session Management Routes
 */

// Initialize a new chat session
// POST /api/chatbot/session/init
// Body: { collegeId, userAgent? }
router.post("/session/init",
  sessionInitRateLimit,
  validateCollegeAccess,
  chatbotController.initializeSession
);

// Get session information
// GET /api/chatbot/session/:sessionId
router.get("/session/:sessionId",
  chatbotRateLimit,
  validateSession,
  chatbotController.getSession
);

// Get chat history for a session
// GET /api/chatbot/session/:sessionId/history
// Query: ?limit=20&offset=0
router.get("/session/:sessionId/history",
  chatbotRateLimit,
  validateSession,
  chatbotController.getChatHistory
);

// End a chat session
// POST /api/chatbot/session/:sessionId/end
router.post("/session/:sessionId/end",
  chatbotRateLimit,
  validateSession,
  chatbotController.endSession
);

/**
 * Message Processing Routes (Phase 3 Enhancement)
 */

// Process user message with AI
// POST /api/chatbot/message
// Body: { sessionId, message, options? }
router.post("/message",
  messageRateLimit,
  validateSession, // This middleware will validate the sessionId from body
  checkViolationLimit,
  chatbotController.processMessage
);

/**
 * Admin Routes
 * Note: These should be protected with admin authentication in production
 */

// Get chatbot statistics
// GET /api/chatbot/admin/stats
router.get("/admin/stats",
  chatbotRateLimit,
  AuthGuard,
  chatbotController.getStats
);

module.exports = router;
