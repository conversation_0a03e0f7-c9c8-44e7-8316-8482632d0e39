const mongoose = require("mongoose");

const ONetCareerSchema = new mongoose.Schema({
  href:String,
  code:String,
  title:String,
  jobZone:Number,

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.ONetCareerSchema = ONetCareerSchema;

class ONetCareer extends mongoose.Model {

}

mongoose.model(ONetCareer, ONetCareerSchema, "oNetCareer");

module.exports.ONetCareer = ONetCareer;
