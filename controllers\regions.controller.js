const { default: mongoose } = require("mongoose");
const { College } = require("../models/college");
const { Campus } = require("../models/campus");
const { UserRoles, User } = require("../models/user");
const { Country } = require("../models/country");
const { getAddedBy, getEditedBy } = require("../tools/database");
const commonHelper = require("../helpers/commonHelper");
const { messageResponse, checkSlug } = require("../helpers/commonHelper");
const commonClass = require("../helpers/commonClass");
// const commonClass = require("../helpers/commonClass");
const {
  updateUserRole,
  revokeUserRole,
} = require("../controllers/users.controller");
const {
  UNAUTHORIZED,
  INVALID,
  ADD_ERROR,
  SERVER_ERROR,
  REQUIRED,
  NOT_FOUND,
  EXIST_PERMISSION,
  INVALID_MISSING,
  INVALID_UID,
  INVALID_USER_ROLE,
  DUPLICATE,
  UPDATE_SUCCESS,
  REMOVE_ERROR_ENGAGED,
  REMOVE_SUCCESS,
  INVALID_USER_BELONG,
  IN_USE,
} = require("../config/messages");
const { Region } = require("../models/region");

const validate = async (req, res, action) => {
  try {
    let region;
    if (action == "edit") {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }

      region = await Region.findById(new mongoose.Types.ObjectId(req.body.id));
      if (!region) {
        return messageResponse(NOT_FOUND, "Region", false, 404, null);
      } else if (req.user.role !== UserRoles.SUPER_ADMIN) {
        return messageResponse(EXIST_PERMISSION, "Region", false, 404, null);
      }
    }

    if (!req.body.name) {
      return messageResponse(INVALID_MISSING, "Name", false, 400, null);
    }

    const result = await checkSlug(Region, req.body);
    if (!result.isValid) {
      return messageResponse(INVALID_MISSING, "Slug", false, 400, null);
    } else if (result.isDuplicate) {
      return messageResponse(IN_USE, "Slug", false, 400, null);
    }

    const isValidCollegeGroupIds = req.body.collegeGroupIds.every(
      (collegeGroupId) => mongoose.isValidObjectId(collegeGroupId)
    );
    if (!isValidCollegeGroupIds) {
      return messageResponse(
        INVALID_MISSING,
        "College Group ID",
        false,
        400,
        null
      );
    }

    let query = { name: req.body.name };
    if (action == "edit") {
      query = {
        $and: [{ name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } }],
      };
    }
    const existingRegion = await Region.findOne(query);
    if (existingRegion != null) {
      return messageResponse(DUPLICATE, "", false, 400, null);
    }

    return { success: true, region };
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
};

const addOrEdit = async (req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {
      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      } else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      } else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == "add") {
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newRegion = await Region.create(req.body);

      if (!newRegion)
        return messageResponse(ADD_ERROR, "Region", false, 400, null, res);

      res.status(200).json({ success: true, id: newRegion._id });
    } else {
      req.body.editedBy = getEditedBy(req, "edit");

      const updatedRegion = await Region.findOneAndUpdate(
        { _id: req.body.id },
        req.body,
        { returnOriginal: false }
      );

      if (!updatedRegion)
        return messageResponse(
          EXIST_PERMISSION,
          "Region",
          false,
          404,
          null,
          res
        );

      return messageResponse(UPDATE_SUCCESS, "Region", true, 200, null, res);
    }
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};

const add = async (req, res, next) => {
  return await addOrEdit(req, res, "add");
};
module.exports.add = add;

const get = async (req, res) => {
  try {
    const { system } = req.query
    const pipeline = [
      {
        $lookup: {
          from: "collegeGroups",
          localField: "collegeGroupIds",
          foreignField: "_id",
          as: "collegeGroups",
        },
      },
      {
        $project: {
          "collegeGroup.addedBy": 0,
          "collegeGroup.editedBy": 0,
        },
      },
      {
        $sort: { isSystem: -1 },
      }
    ];

    if(!system) {
      pipeline.unshift({ $match: { isSystem: false } });
    }

    const regions = await Region.aggregate(pipeline);

    if (!regions.length)
      return messageResponse(NOT_FOUND, "Regions", false, 404, null, res);

    return messageResponse(null, "", true, 200, regions, res);
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};
module.exports.get = get;

const getByID = async (req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const pipeline = [
      {
        $match: {
          _id: new mongoose.Types.ObjectId(req.query.id),
        },
      },
      {
        $lookup: {
          from: "collegeGroups",
          localField: "collegeGroupIds",
          foreignField: "_id",
          as: "collegeGroups",
        },
      },
      {
        $project: {
          "collegeGroup.addedBy": 0,
          "collegeGroup.editedBy": 0,
        },
      },
    ];

    const region = await Region.aggregate(pipeline);

    if (!region.length)
      return messageResponse(NOT_FOUND, "Region", false, 404, null, res);

    return messageResponse(null, "", true, 200, region, res);
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};
module.exports.getByID = getByID;

const remove = async (req, res) => {
  try {
    if (!req.body.id) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const region = await Region.findOneAndDelete({ _id: req.body.id });

    if (!region)
      return messageResponse(EXIST_PERMISSION, "Region", false, 404, null, res);

    return messageResponse(REMOVE_SUCCESS, "Region", true, 200, null, res);
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};
module.exports.remove = remove;

const update = async (req, res, next) => {
  return await addOrEdit(req, res, "edit");
};
module.exports.update = update;
