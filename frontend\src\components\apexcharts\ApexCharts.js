import React from 'react';
import PropTypes from 'prop-types';
import Chart  from 'react-apexcharts';

function ApexCharts ({chartData, className,height, width, type})  {
  return (
    <Chart
        options={chartData.options}
        series={chartData.series}
        type={type}
        height={height}
        width={width}
        className={className}
    />
  )
}
ApexCharts.propTypes = {
    chartData: PropTypes.object,
    className: PropTypes.string,
    height: PropTypes.number,
    width: PropTypes.number,
    type: PropTypes.string
};

export default ApexCharts