const mongoose = require("mongoose");

const JobZoneSchema = new mongoose.Schema({
  value: { type: Number },
  href: { type: String },
  title: { type: String },
  experience: { type: String },
  education: { type: String },
  job_training: { type: String },
  job_training_custom: { type: String },
  
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.JobZoneSchema = JobZoneSchema;

class JobZone extends mongoose.Model
{
  
}

mongoose.model(JobZone, JobZoneSchema, "jobZones");

module.exports.JobZone = JobZone;
