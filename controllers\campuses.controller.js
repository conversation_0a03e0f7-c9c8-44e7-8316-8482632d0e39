const { default: mongoose } = require("mongoose");
const { Campus } = require("../models/campus");
const { College } = require("../models/college");
const { Country } = require("../models/country");
const { UserRoles } = require("../models/user");
const { getAddedBy, getEditedBy } = require('../tools/database')
const commonHelper = require("../helpers/commonHelper");
const {messageResponse} = require("../helpers/commonHelper");
const commonClass = require("../helpers/commonClass");
const { updateUserRole, revokeUserRole } = require("../controllers/users.controller");
const { UNAUTHORIZED, INVALID, ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, INVALID_UID, INVALID_USER_ROLE, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS, REMOVE_ERROR_ENGAGED } = require("../config/messages");
const { Course } = require("../models/course");

const validate = async(req, res, action) => {
  try {
    let campus;
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }

      campus = await Campus.findById(new mongoose.Types.ObjectId(req.body.id));
      if (!campus) {
        return messageResponse(NOT_FOUND, "Campus", false, 404, null);
      }
      else if (req.user.role == UserRoles.CAMPUS_ADMIN && campus.adminUserId.toString() != req.user._id.toString()) {
        return messageResponse(EXIST_PERMISSION, "Campus", false, 404, null)
      }
    }

    if (!req.body.name) {
      return messageResponse(INVALID_MISSING, "Name", false, 400, null)
    }

    if (!mongoose.isValidObjectId(req.body.collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null)
    }

    const allowedColleges = await commonClass.getAllowedColleges(req, campus);
    const college = allowedColleges.find(college => college._id == req.body.collegeId);
    if (!college) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null)
    }

    // const selectedCG = await CollegeGroup.findById(new mongoose.Types.ObjectId(req.body.collegeGroupId));
    // if(!selectedCG) {
    //   return { statusCode:400, success: false, msg: "Invalid/missing College Group." };
    // }

    if (req.body.countryId) {
      if (!mongoose.isValidObjectId(req.body.countryId)) {
        return messageResponse(INVALID_MISSING, "Country ID", false, 400, null)
      }

      const selectedCountry = await Country.findById(new mongoose.Types.ObjectId(req.body.countryId));
      if (!selectedCountry) {
        return messageResponse(INVALID_MISSING, "Country", false, 400, null)
      }
    }

    if (req.body.adminUserId) {
      if (!mongoose.isValidObjectId(req.body.adminUserId)) {
        return messageResponse(INVALID_UID, "", false, 400, null)
      }

      if (action == 'edit' && req.user.role == UserRoles.CAMPUS_ADMIN) {
        if (campus.adminUserId != req.body.adminUserId) {
          return messageResponse(UNAUTHORIZED, "Admin User", false, 400, null)
        }
        if (campus.collegeId != req.body.collegeId) {
          return messageResponse(UNAUTHORIZED, "College", false, 400, null)
        }
      }
      else {
        const allowedUsers = await commonClass.getAllowedUsers(req);
        const user = allowedUsers.find(user => user._id == req.body.adminUserId);
        if (user) {
          if (user.role == UserRoles.CAMPUS_ADMIN) {
            let campusIdOtherThanCurrentId = user.campusIds[0];
            if (action == 'edit') {
              campusIdOtherThanCurrentId = user.campusIds.find(cid => cid != req.body.id);
            }
            if (campusIdOtherThanCurrentId) {
              const campusOfSelectedUser = await Campus.findById(campusIdOtherThanCurrentId);
              if (campusOfSelectedUser && campusOfSelectedUser.collegeId != req.body.collegeId) {
                return messageResponse(INVALID_USER_BELONG, "College", false, 400, null)
              }
            }
          }
          else if (user.role != UserRoles.NOT_AVAILABLE) {
            return messageResponse(INVALID_USER_ROLE, "", false, 400, null);
          }
        }
        else {
          return messageResponse(INVALID, "Admin User", false, 400, null)
        }
      }
    }

    let query = { name: req.body.name, collegeId: req.body.collegeId };
    if (action == 'edit') {
      query = { $and: [{ name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } }, { collegeId: { $eq: req.body.collegeId } }] };
    }
    const existingCampus = await Campus.findOne(query).collation( { locale: 'en', strength: 2 } );
    if (existingCampus != null) {
      return messageResponse(DUPLICATE, "", false, 400, null);
    }

    return { success: true, campus };
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {
      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == 'add') {
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newCampus = await Campus.create(req.body)

      if(!newCampus) return messageResponse(ADD_ERROR, "Campus", false, 400, null, res)

      await updateUserRole(req.body.adminUserId, 4, newCampus._id);
      res.status(200).json({ success: true, id: newCampus._id })
    }
    else {
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedCampus = await Campus.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updatedCampus) return messageResponse(EXIST_PERMISSION, "Campus", false, 404, null, res) 

      await updateUserRole(req.body.adminUserId, 4, req.body.id, validateResult.campus);
      return messageResponse(UPDATE_SUCCESS, "Campus", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async(req, res, next) => {
  return await addOrEdit(req, res, 'add');
}
module.exports.add = add;

const get = async(req, res) => {
  try {
    // const pipeline = [
    //   {
    //     $lookup: {
    //       from: "colleges",
    //       localField: "collegeId",
    //       foreignField: "_id",
    //       as: "college"
    //     }
    //   },
    //   {
    //     $unwind: {
    //       path: "$college",
    //     }
    //   },
    //   {
    //     $lookup: {
    //       from: "collegeGroups",
    //       localField: "college.collegeGroupId",
    //       foreignField: "_id",
    //       as: "college.collegeGroup"
    //     }
    //   },
    //   {
    //     $project: {
    //       "college.logo": 0,
    //       "college.addedBy": 0,
    //       "college.editedBy": 0,
    //       "college.status": 0,
    //       "college.defaultEntry": 0,

    //       "college.collegeGroup.addedBy": 0,
    //       "college.collegeGroup.editedBy": 0,
    //       "college.collegeGroup.status": 0,
    //       "college.collegeGroup.defaultEntry": 0,
    //     }
    //   },
    //   {
    //     $unwind: {
    //       path: "$college.collegeGroup",
    //     }
    //   },
    // ]
    // const campuses = await Campus.aggregate(pipeline);

    const allowedCampuses = await commonClass.getAllowedCampuses(req);

    if(!allowedCampuses.length) return messageResponse(NOT_FOUND, "Campuses", false, 404, [], res) 

    return messageResponse(null, "", true, 200, allowedCampuses, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.get = get;


const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const allowedCampuses = await commonClass.getAllowedCampuses(req);

    if(!allowedCampuses.length) return messageResponse(NOT_FOUND, "Campus", false, 404, null, res)

    const campus = allowedCampuses.find(campus => campus._id == req.query.id);
    if (!campus) return messageResponse(EXIST_PERMISSION, "Campus", false, 404, null, res) 

    return messageResponse(null, "", true, 200, campus, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID = getByID;

const remove = async(req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const allowedCampuses = await commonClass.getAllowedCampuses(req);
    // console.log('allowedCampuses', allowedCampuses);
    const reqCampus = allowedCampuses.find(campus => campus._id == req.body.id);
    if (!reqCampus) return messageResponse(EXIST_PERMISSION, "Campus", false, 404, null, res);

    const course = await Course.findOne({ campusId: req.body.id });
    if (course) return messageResponse(REMOVE_ERROR_ENGAGED, "Course", false, 400, null, res);

    const campus = await Campus.findOneAndDelete({ _id: req.body.id, defaultEntry: false })
    if(!campus) return messageResponse(EXIST_PERMISSION, "Campus", false, 404, null, res);

    revokeUserRole(4, req.body.id, campus)
    return messageResponse(REMOVE_SUCCESS, "Campus", true, 200, null, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove = remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit');
};
module.exports.update = update;



