import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { read, utils, writeFile } from 'xlsx';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import { Icon } from '@iconify/react';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/material/styles';
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import EditIcon from '@mui/icons-material/Edit';
// components
import {
    Box, Button, Fade, FormControl, Grid, Stack, TextField, TextareaAutosize, Container,
    Typography,
    Card,
    MenuItem,
    Paper,
    Autocomplete,
    InputAdornment,
    Backdrop,
    CircularProgress,
    Alert,
    AlertTitle,
    Tooltip,
    tooltipClasses
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { get } from 'lodash';
import SelectField from '../../components/SelectedField';
import { blue, formButton, formStyle, green, red } from '../../utils/cssStyles';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
// @mui
import { APP_ROUTER_BASE_URL } from '../../utils';
import { setSnackbar } from '../../Redux/snackbarSlice';
import { getSectors } from '../Sector/SectorSlice';
import { getSubSectors } from '../SubSector/SubSectorSlice';
import axiosInstance from '../../utils/axiosInstance';
// ----------------------------------------------------------------------
const steps = [
    "Import File",
    "Select Columns",
    "Confirm",
];
export const renderCareerCells = ['code', 'title', 'description', 'level', 'campus', 'pageURL', 'applyURL', 'enquiryURL', 'duration',]
const columns = [{ id: 'code', label: "Course Code", alignRight: false },
{ id: 'title', label: " Course Title", alignRight: false },
{ id: 'description', label: "Course Description", alignRight: false },
{ id: 'level', label: "Level", alignRight: false },
{ id: 'campus', label: "Campus", alignRight: false },
{ id: 'pageURL', label: "PageURL", alignRight: false },
{ id: 'applyURL', label: "ApplyURL", alignRight: false },
{ id: 'enquiryURL', label: "EnquiryURL", alignRight: false },
{ id: 'duration', label: 'Duration', alignRight: true, pr: 6 },
{ id: 'sector', label: 'Sector', alignRight: true, pr: 6 },
{ id: 'subsectors', label: 'Sub Sectors', alignRight: true, pr: 6 },
]

const ImportCourses = () => {

    const dispatch = useDispatch()
    const navigate = useNavigate()
    const [allData, setAllData] = useState([]);
    const [sheetcolumn, setSheetColumn] = useState([]);
    const [sheetcolumnData, setSheetColumnData] = useState([]);
    const [fileColumndata, setFileColumnData] = useState([]);
    const [tableData, setTableData] = useState([]);
    const [SubSectors, setSubSectors] = useState([])
    const [value, setvalue] = useState([]);
    const [checkStepTwo, setStepTwo] = useState(false)
    const [counts, setCounts] = useState({
        inserted: 0,
        updated: 0,
        errors: 0,
        total: 0
    })
    const [Sectors, setSectors] = useState([
        {
            label: '',
            _id: '',
            subSectors: []
        }
    ])
    const sectors = useSelector(state => state.sectors.sectors)
    const subSectors = useSelector(state => state.subSectors.subSectors)
    const [activeStep, setActiveStep] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [disableEdit, setDisableEdit] = useState(false)
    const [checkSectorIsSelected, setcheckSectorIsSelected] = useState(true);
    const [InvalidEntries, setInvalidEntries] = useState([]);
    const [ValidEntries, setValidEntries] = useState([]);
    const [UpdatedEntries, setUpdatedEntries] = useState([]);
    const { selectedCollege } = useSelector(state => state.selectedCollege)
    const submitCourses = async (courses) => {
        setIsLoading(true)
        try {
            const response = await axiosInstance({
                url: "courses/addBulk",
                method: "POST",
                data: {
                    collegeId: selectedCollege?._id,
                    courses,
                }
            })
            if (response?.data?.success) {
                setDisableEdit(true)
                if (response?.data?.data?.validEntries) {
                    const entries = response?.data?.data?.validEntries
                    const valid = []
                    const updated = []
                    if (entries?.insert && !!entries.insert?.length) {
                        entries.insert?.map(entry => {
                            valid.push(entry)
                            return entry
                        })
                        // setValidEntries([...ValidEntries,entries.insert])
                    }
                    if (entries?.update && !!entries?.update?.length) {
                        entries.update?.map(entry => {
                            const etryObj = { ...entry, updated: true }
                            updated.push(etryObj)
                            return entry
                        })
                    }
                    setUpdatedEntries(updated)
                    setValidEntries(valid)
                }
            }
            if (response?.data?.data?.invalidEntries) {
                setInvalidEntries(response?.data?.data?.invalidEntries)
            }
            setCounts({
                ...counts,
                errors: response?.data?.errorCount,
                total: response?.data?.totalCount,
                inserted: response?.data?.insertCount,
                updated: response?.data?.updateCount
            })
        } catch (error) {
            console.log('error', error);
            if (error?.response?.data?.data?.invalidEntries) {
                setInvalidEntries(error?.response?.data?.data?.invalidEntries)
                setDisableEdit(true)
            }
            if (error?.response?.data?.message) {
                const errorMessage = get(error, 'response.data.message', 'Something went wrong')
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: errorMessage
                }))
            }
        } finally {
            setIsLoading(false)
        }
    }

    const handleNext = () => {
        if (activeStep === steps.length - 1) {
            // let isSectorNotSelected = false
            // let isSubNotSectorSelected = false
            // tableData.map((data) => {
            //     if (data.sector._id === '') {
            //         isSectorNotSelected = true
            //         setcheckSectorIsSelected(false)
            //     }
            //     else if (!data.subsectors.length) {
            //         isSubNotSectorSelected = true
            //         setcheckSectorIsSelected(false)
            //     }
            //     return data
            // })

            // if (isSectorNotSelected || isSubNotSectorSelected) {
            //     dispatch(setSnackbar({
            //         snackbarOpen: true,
            //         snackbarType: 'error',
            //         snackbarMessage: "Please Select Sectors"
            //     }))
            // } else {
            const courses = tableData.map(row => {
                const sectors = []
                if (!!row?.sector.length) {
                    sectors.push({
                        _id: row.sector._id,
                        label: row.sector.label,
                        subsectors: row.subsectors
                    })
                }
                if (row.applyURL === undefined) {
                    row.applyURL = "";
                }
                if (row.pageURL === undefined) {
                    row.pageURL = "";
                }
                if (row.enquiryURL === undefined) {
                    row.enquiryURL = "";
                }
                return {
                    ...row,
                    sectors
                }
            })
            submitCourses(courses)
            // }

        } else {
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
            if (activeStep === 1) {
                setStepTwo(true)
                getData()
            }
        }

    };
    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
        dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: "If you edit columns all changes will be discarded"
        }))
        setvalue(tableData)
    };

    const handleReset = () => {
        setActiveStep(0);
    };
    useEffect(() => {
        dispatch(getSectors())
        dispatch(getSubSectors())
    }, [])

    useEffect(() => {
        const sectorOptions = sectors?.length ? sectors.map(sector => ({
            label: sector.name,
            _id: sector._id,
        })) : []
        const subSectorOptions = subSectors?.length ? subSectors.map(subSector => ({
            label: subSector.name,
            _id: subSector._id,
            sectorId: subSector.sectorId
        })) : []

        setSectors(sectorOptions)
        setSubSectors(subSectorOptions)
    }, [sectors, subSectors])


    const StyledTableCell = styled(TableCell)(({ theme }) => ({
        [`&.${tableCellClasses.head}`]: {
            backgroundColor: theme.palette.common.primary,
            color: theme.palette.common.black,
        },
        [`&.${tableCellClasses.body}`]: {
            fontSize: 14,
        },
    }));
    const StyledStatusTableCell = styled(TableCell)(({ theme }) => ({
        [`&.${tableCellClasses.head}`]: {
            backgroundColor: theme.palette.common.primary,
            color: theme.palette.common.black,
        },
        [`&.${tableCellClasses.body}`]: {
            fontSize: 14,
            width: "700px"
        },
    }));
    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        '&:last-child td, &:last-child th': {
            border: 0,
        },
    }));

    const CustomWidthTooltip = styled(({ className, ...props }) => (
        <Tooltip {...props} classes={{ popper: className }} />
    ))({
        [`& .${tooltipClasses.tooltip}`]: {
            maxWidth: 400,
            fontSize: '0.75rem'
        },
    });


    const handleImport = (event) => {
        setTableData([])
        const files = event.target.files;
        if (files.length && files[0].type === 'text/csv') {
            const file = files[0];
            const reader = new FileReader();
            reader.onload = (event) => {
                const wb = read(event.target.result);
                const sheets = wb.SheetNames;
                if (sheets.length) {
                    const Alldata = utils.sheet_to_json(wb.Sheets[sheets[0]]);
                    const cols = Object.keys(Alldata[0])
                    setSheetColumn(cols)
                    setAllData(Alldata)
                }
            }
            reader.readAsArrayBuffer(file);
            setActiveStep((prevActiveStep) => prevActiveStep + 1);
        } else {
            dispatch(setSnackbar({
                snackbarOpen: true,
                snackbarType: 'error',
                snackbarMessage: "Import file should be CSV"
            }))
        }
    }

    const handleSelectChange = (event) => {
        const data = fileColumndata
        const index = fileColumndata.findIndex((file) => file.localColumnName === event.localColumnName)
        if (index === -1) {
            data.push(event)
            setFileColumnData([...data])
        } else {
            data[index].ServerColumnName = event.ServerColumnName
            setFileColumnData([...data])
        }
    };

    const getData = () => {
        const rowdata = []
        allData.map((row) => {
            const obj = {}
            fileColumndata.map((data, i) => {
                obj[data.localColumnName] = row[data.ServerColumnName]
                if (checkStepTwo && value.length) {
                    obj.sector = value[i].sector
                    obj.subsectors = value[i].subsectors
                } else {
                    obj.sector = [{ _id: '', label: "" }]
                    obj.subsectors = []
                    obj.subsectorOptions = []
                }
                return data
            })
            rowdata.push(obj)
            return obj;
        })
        setTableData([...rowdata])
    }
    useEffect(() => {
        const data = []
        if (sheetcolumn && sheetcolumn.length) {
            sheetcolumn.map((item, i) => {
                data.push({ name: item, id: item })
                return item
            })
        }
        setSheetColumnData(data)
    }, [sheetcolumn])

    const onSelectChange = (event) => {
        const data = {
            localColumnName: event.target.name,
            ServerColumnName: event.target.value,
        }
        handleSelectChange(data)
    };
    const onSectorChange = (e, i) => {
        const data = tableData
        data[i].sector._id = e.target.value
        const index = Sectors.findIndex((sector) => sector._id === e.target.value)
        data[i].sector.label = Sectors[index].label
        const subSectorArray = []
        SubSectors.map((subSectorData) => {
            if (subSectorData.sectorId === e.target.value) {
                subSectorArray.push({ _id: subSectorData._id, label: subSectorData.label })
            }
            return subSectorData
        })
        data[i].subsectors = subSectorArray
        data[i].subsectorOptions = subSectorArray
        setTableData([...data])
    }
    const EditColumn = () => {
        dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: "If you edit columns all changes will be discarded"
        }))
        setStepTwo(false)
    }
    // const finish=()=>{
    //     let isSectorNotSelected=false
    //     let isSubNotSectorSelected=false
    //     tableData.map((data)=>{
    //  
    //         if(data.sector._id===''){
    //             isSectorNotSelected=true
    //         setcheckSectorIsSelected(false)
    //     }
    //         else if(!data.subsectors.length){
    //             isSubNotSectorSelected=true
    //             setcheckSectorIsSelected(false)
    //         }
    //         return data
    //     })

    //     if(isSectorNotSelected || isSubNotSectorSelected){
    //         dispatch(setSnackbar({
    //             snackbarOpen: true,
    //             snackbarType: 'error',
    //             snackbarMessage: "Please Select Sectors"
    //         }))
    //     }
    // }
    const getCellClass = (row) => {
        if (row) {
            if (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                return red
            }
            if (ValidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                return green
            }
            if (UpdatedEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                return blue
            }
            return null
        }
        return null
    }
    const getCellMessage = (row) => {
        // InvalidEntries ?
        // (InvalidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title))?.error ?
        //     InvalidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title))?.error : (
        //         // ValidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title && entry?.update) ?
        //         //  "Succesfully Updated this Course" :
        //         "Succesfully Imported this Course"
        //         //  )
        //     ))
        // : null
        if (row) {
            if (InvalidEntries?.length > 0) {
                if (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                    return InvalidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title))?.error
                }
            }
            if (ValidEntries?.length > 0) {
                if (ValidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                    return "Course imported Successfully"
                }
            }
            if (UpdatedEntries?.length > 0) {
                if (UpdatedEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                    return "Course updated Successfully"
                }
            }
            return null
        }
        return null
    }
    const getCellIcon = (row) => {
        if (row) {
            if (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                return <Icon
                    color='red'
                    icon="clarity:error-standard-line"
                    width="24"
                    cursor="pointer"
                />
            }
            if (ValidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                return <Icon
                    icon="clarity:success-standard-line"
                    width="24"
                    color='green'
                    cursor="pointer"
                />
            }
            if (UpdatedEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title))) {
                return <Icon
                    // icon="clarity:history-line"
                    icon="ci:check-all"
                    width="24"
                    color='#0f73ee'
                    cursor="pointer"
                />
            }
            return null
        }
        return null
    }

    return (
        <>
            <Helmet>
                <title> Courses | ThinkSkill </title>
            </Helmet>
            <Backdrop
                sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 99999 }}
                open={isLoading}
            >
                <CircularProgress color="inherit" />
            </Backdrop>
            <Container maxWidth="xxl">
                <Typography variant="h4" gutterBottom mb={3}>
                    Import Courses
                </Typography>
                <Card sx={{ pt: 2, pb: 5 }}>

                    <Box sx={{ width: "95%", margin: "auto", height: "auto" }}>
                        <Box sx={{
                            display: 'flex',
                            alignItems: "center",
                            justifyContent: 'center'
                        }}>
                            {!disableEdit ? <Stepper activeStep={activeStep} sx={{ pt: 3, pb: 6, width: '65%', }}>
                                {
                                    steps.map(step => (
                                        <Step>
                                            <StepLabel>{step}</StepLabel>
                                        </Step>
                                    ))
                                }
                            </Stepper> : null}
                        </Box>
                        {activeStep === steps.length ? (
                            <>
                                <Typography sx={{ mt: 2, mb: 1 }}>All steps completed</Typography>

                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "row",
                                        pt: 2,
                                    }}
                                >
                                    <Box sx={{ flex: "1 1 auto" }} />
                                    <Button onClick={handleReset}>Reset</Button>
                                </Box>
                            </>
                        ) : (
                            <>
                                {activeStep === 0 ?
                                    <>
                                        <Grid container gap={2} >
                                            <Grid item xs={12} md={11.8} sx={{ display: 'flex', justifyContent: "center", cursor: "pointer" }} >
                                                <TextFIeldComponent
                                                    // sx={{ width: '100%', display: "none" }}
                                                    sx={{ width: '15%', position: "absolute", zIndex: "1", opacity: "0", }}
                                                    name='selectFile'
                                                    label="Select File"
                                                    id='importFile'
                                                    type="file"
                                                    accept=".csv, application/vnd.ms-excel"
                                                    required onChange={handleImport}
                                                    InputLabelProps={{
                                                        shrink: true,
                                                    }}
                                                />
                                                <Button
                                                    variant='outlined'
                                                    sx={{ minWidth: "15%", position: "absolute", p: 2 }}
                                                    startIcon={<CloudUploadIcon />}

                                                >
                                                    <label htmlFor='importFile' >
                                                        Import File
                                                    </label>
                                                </Button>
                                            </Grid>
                                        </Grid>

                                    </> : activeStep === 1 ?
                                        <>
                                            <Grid item xs={12} md={5.8} sx={{ display: 'grid', placeItems: 'center' }}>
                                                {sheetcolumn.length > 0 ? columns.map((dummy, idx) => (

                                                    <FormControl sx={{ minWidth: '40%', maxWidth: '40%', display: "flex" }}>
                                                        {dummy.id !== 'sector' && dummy.id !== 'subsectors' ? <>
                                                            <Stack alignItems='center' direction='row' gap={3} mb={2}>

                                                                <Typography variant='body' sx={{ minWidth: "30%", mb: 1, fontWeight: "600" }}>{dummy.label}</Typography>
                                                                {!checkStepTwo ? <SelectField
                                                                    sx={{ minWidth: '50%', maxWidth: "50%" }}
                                                                    label={dummy.label}
                                                                    name={dummy.id}
                                                                    value={fileColumndata.filter((data) => (data.localColumnName === dummy.id ? data.ServerColumnName : null))[0]?.ServerColumnName}
                                                                    size="small"

                                                                    onChange={onSelectChange}

                                                                >
                                                                    {
                                                                        dummy.id === 'enquiryURL' || dummy.id === 'pageURL' || dummy.id === 'applyURL' ?
                                                                            <MenuItem value='-'>
                                                                                <em>None</em>
                                                                            </MenuItem>
                                                                            : null
                                                                    }
                                                                    {sheetcolumnData.map(sheetcolumn =>
                                                                    (<MenuItem
                                                                        key={sheetcolumn.id}
                                                                        value={sheetcolumn.id}
                                                                    // disabled={check(sheetcolumn.id)}
                                                                    >
                                                                        {sheetcolumn.name}
                                                                    </MenuItem>))}

                                                                </SelectField> : <TextFIeldComponent
                                                                    InputProps={{
                                                                        endAdornment: (
                                                                            <InputAdornment position="start">
                                                                                <EditIcon sx={{ cursor: 'pointer' }} onClick={EditColumn} />
                                                                            </InputAdornment>
                                                                        ),
                                                                    }}
                                                                    value={fileColumndata.filter((data) => (data.localColumnName === dummy.id ? data.ServerColumnName : null))[0]?.ServerColumnName}

                                                                />
                                                                }
                                                            </Stack>
                                                        </> : null
                                                        }
                                                    </FormControl>


                                                )) : null}
                                            </Grid>

                                        </> : activeStep === 2 ?
                                            <>
                                                {disableEdit &&
                                                    <>
                                                        <Box
                                                            sx={{
                                                                display: "flex",
                                                                flexDirection: "column",
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                // gap: 2,
                                                                pb: 2,
                                                            }}
                                                        >
                                                            <Alert severity="info">
                                                                <Typography variant='subtitle2' color='#1890FF'>
                                                                    Rows marked as red color are invalid and rows marked in green and blue are succesfully added and updated as courses respectively
                                                                </Typography>
                                                            </Alert>
                                                        </Box>
                                                        <Stack sx={{ width: '100%' }} justifyContent='center' mb={2} ml={1} direction='row' gap={2}>
                                                            <Alert severity="success">
                                                                <Typography variant='body2'>
                                                                    Courses Added : <b>{counts?.inserted}</b>
                                                                </Typography>
                                                            </Alert>
                                                            <Alert
                                                                icon={<Icon
                                                                    icon="ci:check-all"
                                                                    color='#0f73ee'
                                                                    cursor="pointer"
                                                                />
                                                                }
                                                                severity="info">
                                                                <Typography variant='body2'>
                                                                    Courses Updated : <b>{counts?.updated}</b>
                                                                </Typography>
                                                            </Alert>
                                                            <Alert severity="error">
                                                                <Typography variant='body2'>
                                                                    Courses Rejected : <b>{counts?.errors}</b>
                                                                </Typography>
                                                            </Alert>

                                                        </Stack>
                                                    </>
                                                }
                                                {tableData && tableData.length ?
                                                    <TableContainer component={Paper}>
                                                        <Table sx={{ minWidth: 700 }}
                                                            MenuProps={{
                                                                autoFocus: false,
                                                                disableAutoFocusItem: true,
                                                                disableEnforceFocus: true,
                                                                disableAutoFocus: true
                                                            }}
                                                            aria-label="customized table"
                                                            dense table
                                                        >
                                                            <TableHead >
                                                                <TableRow>
                                                                    {disableEdit ?
                                                                        <StyledTableCell sx={{ minWidth: '200px' }}>Status</StyledTableCell> :
                                                                        null
                                                                    }
                                                                    {columns.map((column) => (
                                                                        <StyledTableCell sx={{ minWidth: '200px' }}>{column.label}</StyledTableCell>
                                                                    ))}
                                                                </TableRow>
                                                            </TableHead>
                                                            <TableBody>
                                                                {tableData.map((row, i) => (
                                                                    <StyledTableRow>
                                                                        {disableEdit && <StyledTableCell
                                                                            // sx={(InvalidEntries && InvalidEntries?.length > 0) ? (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title)) ?
                                                                            //     red : green) : green}
                                                                            sx={getCellClass(row)}
                                                                        >
                                                                            <CustomWidthTooltip
                                                                                title={
                                                                                    // InvalidEntries ?
                                                                                    // (InvalidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title))?.error ?
                                                                                    //     InvalidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title))?.error : (
                                                                                    //         // ValidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title && entry?.update) ?
                                                                                    //         //  "Succesfully Updated this Course" :
                                                                                    //         "Succesfully Imported this Course"
                                                                                    //         //  )
                                                                                    //     ))
                                                                                    // : null
                                                                                    getCellMessage(row)
                                                                                }
                                                                                arrow
                                                                                placement="right-start"

                                                                            >
                                                                                {
                                                                                    getCellIcon(row)
                                                                                    // InvalidEntries?.find(entry => (entry?.code === row?.code && entry?.title === row?.title))?.error ?
                                                                                    //     <Icon
                                                                                    //         color='red'
                                                                                    //         icon="clarity:error-standard-line"
                                                                                    //         width="24"
                                                                                    //         cursor="pointer"
                                                                                    //     /> :
                                                                                    //     <Icon
                                                                                    //         icon="clarity:success-standard-line"
                                                                                    //         width="24"
                                                                                    //         color='green'
                                                                                    //         cursor="pointer"
                                                                                    //     />
                                                                                }
                                                                            </CustomWidthTooltip>
                                                                        </StyledTableCell>}
                                                                        {
                                                                            renderCareerCells.map((renderCell, index) => (
                                                                                <>
                                                                                    {/* <StyledTableCell sx={{ maxWidth: '150px', wordWrap: "break-word" }}> */}
                                                                                    <StyledTableCell
                                                                                        sx={getCellClass(row)}
                                                                                    // sx={InvalidEntries && (InvalidEntries?.length > 0) ? (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title)) ?
                                                                                    //     red : green) : green}
                                                                                    >
                                                                                        {row[renderCell] || '-'}
                                                                                    </StyledTableCell>
                                                                                </>

                                                                            ))}
                                                                        <StyledTableCell
                                                                            // sx={InvalidEntries && (InvalidEntries?.length > 0) ? (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title)) ?
                                                                            //     red : green) : null}
                                                                            sx={getCellClass(row)}
                                                                        >
                                                                            <SelectField
                                                                                label="Sector"
                                                                                name="sector"
                                                                                value={row.sector._id}
                                                                                sx={{ minWidth: '100%' }}
                                                                                size="small"
                                                                                onChange={(e) => onSectorChange(e, i)}
                                                                                disabled={disableEdit}
                                                                            >

                                                                                {Sectors.map(sector =>
                                                                                (
                                                                                    <MenuItem
                                                                                        key={sector._id}
                                                                                        value={sector._id}

                                                                                    >
                                                                                        {sector.label}
                                                                                    </MenuItem>))}
                                                                            </SelectField>
                                                                        </StyledTableCell>
                                                                        <StyledTableCell
                                                                            // sx={InvalidEntries && (InvalidEntries?.length > 0) ? (InvalidEntries?.some(entry => (entry?.code === row?.code && entry?.title === row?.title)) ?
                                                                            //     red : green) : null}
                                                                            sx={getCellClass(row)}
                                                                        >
                                                                            <FormControl sx={{ minWidth: '100%', }}>
                                                                                <Autocomplete
                                                                                    isOptionEqualToValue={(option, value) => {
                                                                                        return option._id === value._id
                                                                                    }}
                                                                                    options={row.sector._id ? row.subsectorOptions : []}
                                                                                    value={row.subsectors
                                                                                    }
                                                                                    sx={{ minWidth: '100%' }}
                                                                                    onChange={(event, value) => {
                                                                                        tableData[i].subsectors = value
                                                                                        setTableData([...tableData])
                                                                                    }}
                                                                                    size="small"
                                                                                    multiple
                                                                                    limitTags={1}
                                                                                    disabled={disableEdit}
                                                                                    renderInput={(params) => <TextField sx={{ minWidth: "100%" }} size="small"{...params} label="Sub-Sectors" />}
                                                                                />
                                                                            </FormControl>
                                                                        </StyledTableCell>
                                                                    </StyledTableRow>
                                                                ))}
                                                            </TableBody>
                                                        </Table>
                                                    </TableContainer>
                                                    : null
                                                }

                                            </> : null
                                }

                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "row",
                                        pt: 2,
                                        mt: 10
                                    }}
                                >
                                    <Box sx={{ flex: "1 1 auto" }} />
                                    {disableEdit ?
                                        null : <Button
                                            color="inherit"
                                            disabled={activeStep === 0 || activeStep === 1}
                                            onClick={handleBack}
                                            variant='contained'
                                            sx={{ mr: 2 }}
                                        >
                                            Back
                                        </Button>}


                                    {
                                        disableEdit ?
                                            null :
                                            <Button onClick={handleNext} variant='contained'
                                                disabled={fileColumndata.length === 9 ? false : 'true'}
                                            >
                                                {activeStep === steps.length - 1 ? "Upload" : "Next"}
                                            </Button>}
                                </Box>
                                <Box
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        justifyContent: 'flex-end',
                                        alignItems: 'end',
                                        gap: 2,
                                        pt: 2,
                                    }}
                                >
                                    {disableEdit &&
                                        <Link
                                            to={`${APP_ROUTER_BASE_URL}dashboard/courses`}
                                        >
                                            <Button
                                                variant='contained'
                                            >
                                                Finish
                                            </Button>
                                        </Link>}
                                </Box>
                            </>
                        )}
                    </Box>
                </Card>
            </Container>
        </>
    )
}

export default ImportCourses






