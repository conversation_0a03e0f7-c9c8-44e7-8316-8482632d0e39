import axios from "axios";
// import { useDispatch } from "react-redux";
import Swal from "sweetalert2";
import Cookies from 'universal-cookie';
import { APP_ROUTER_BASE_URL } from ".";
import '../Styles.css';

const cookies = new Cookies()

// const cookies = new Cookies();
// const jwtToken =  cookies.get("token")
// const token = jwtToken || ""
// const headers = { 'Authorization': `Bearer ${token}` }
const axiosInstance = axios.create({
  baseURL: 'http://localhost:3000/api/',
  // baseURL: '/api/',
  headers: {
    //  Authorization: `<Auth Token>`,
    "Content-Type": "application/json",
    // timeout : 1000,
  },
});

axiosInstance.interceptors.request.use((config) => {
  const cookies = new Cookies();
  const jwtToken = cookies.get("token")
  const token = jwtToken || ""

  // store.dispatch(setSnackbar({
  //   snackbarOpen: true,
  //   snackbarType: 'error',
  //   snackbarMessage: "something went wrong"
  // }))

  // checking if accessToken exists
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    // 'Authorization': `Bearer ${token}`
  }
  return config;
});


axiosInstance.interceptors.response.use((response) => {
  return response;
}, (error) => {
  if (error.response.status === 401) {
    console.log("errpr in 401")
    // cookies.remove("token", { path: `${APP_ROUTER_BASE_URL}` })
    cookies.remove("token", { path: '/' })
    Swal.fire({
      title: 'Session Expired!',
      text: 'Please Login Again',
      icon: 'error',
      confirmButtonText: 'Log in'
    }).then(()=>{
      window.location = `${APP_ROUTER_BASE_URL}login`;
    })
    return Promise.reject(error);
  }
  return Promise.reject(error);

});

export default axiosInstance;
