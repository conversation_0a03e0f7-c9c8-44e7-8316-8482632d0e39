import { MuiTelInput, matchIsValidTel } from "mui-tel-input";
import { useState } from "react";

const PhoneInput = (props) => {
    const {...rest} = props;
    const [value, setValue] = useState("");
    const [info, setInfo] = useState(null);

    const handleChange = (newValue, info) => {
        // matchIsValidTel(newValue)
        // setValue(newValue);
        // setInfo(info);
    };

    return (
        <MuiTelInput
        forceCallingCode 
            {...rest}
        />
    );
}

export default PhoneInput