import CloseIcon from "@mui/icons-material/Close";
import UploadFileIcon from '@mui/icons-material/UploadFile';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Card,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Grid,
  IconButton,
  InputAdornment,
  LinearProgress,
  MenuItem,
  Modal,
  Stack,
  Typography
} from '@mui/material';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import DataTable from 'react-data-table-component';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import SelectField from 'src/components/SelectedField';
import { setSnackbar } from '../../Redux/snackbarSlice';
import Iconify from '../../components/Iconify/Iconify';
import useLoading from '../../hooks/useLoading';
import { StyledSearch } from '../../sections/@dashboard/user/UserListToolbar';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { getRegions } from '../Regions/regionsSlice';
import StatsTabs from './StatsTab';
import { exportCareer, exportMetaFile, getNestedCareers, importCareer, importCareersMeta, removeCareer } from './newCareerSlice';

export const CAREERS_TABLE_HEAD = [
  { id: 'title', label: 'Career', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const careerStyles = {
  rows: {
    style: {
      minHeight: '70px',
      fontSize: '1rem', // Increased font size
      cursor: 'pointer', // Make row look clickable
    },
  },
  headRow: {
    style: {
      backgroundColor: '#f4f6f8',
      minHeight: '70px',
      borderBottomWidth: '1px',
      borderBottomStyle: 'solid',
      fontSize: '1rem', // Increased font size for header
    },
  },
  headCells: {
    style: {
      paddingRight: '8px',
      color: 'black',
      fontWeight: 'bold',
    },
  },
  cells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
    },
  },
};

export const subCareerStyles = {
  rows: {
    style: {
      minHeight: '70px',
      backgroundColor: '#f4f6f887',
      paddingLeft: 70,
      fontSize: '1.05rem', // Increased font size for sub rows
      borderBottomWidth: '0px !important',
      cursor: 'pointer', // Make sub row look clickable
    },
  },
  headCells: {
    style: {
      paddingLeft: '50px',
      paddingRight: '8px',
      color: 'black',
      fontWeight: 'bold',
      backgroundColor: '#f4f6f887',
      fontSize: '15px',
    },
  },
  headRow: {
    style: {
      borderBottom: '1px solid #00000012 !important',
    },
  },
  cells: {
    style: {
      paddingLeft: '8px',
      paddingRight: '8px',
      minHeight: '50px',
      paddingBlock: '10px',
    },
  },
};

export const specializedStyles = {
  rows: {
    style: {
      minHeight: '70px',
      backgroundColor: '#f4f6f887',
      paddingLeft: 70,
      fontSize: '1.05rem', // Increased font size for sub rows
      borderBottomWidth: '0px !important',
      cursor: 'pointer', // Make sub row look clickable
    },
  },
  headCells: {
    style: {
      paddingLeft: '120px',
      paddingRight: '8px',
      color: 'black',
      fontWeight: 'bold',
      backgroundColor: '#f4f6f887',
      fontSize: '15px',
      minHeight: '50px',
    },
  },
  headRow: {
    style: {
      borderBottom: '1px solid #00000012 !important',
    },
  },
  cells: {
    style: {
      paddingLeft: '80px',
      paddingRight: '8px',
      minHeight: '50px',
      paddingBlock: '10px',
    },
  },
};

const NewCareers = () => {
  const dispatch = useDispatch();
  const { newCareers, status, importLoading, exportLoading, exportMetaLoading } = useSelector((state) => state.newCareers);
  const [Careers, setCareers] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [open, setOpen] = useState(false);
  const [exportOpen, setExportOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [metaOpen, setMetaOpen] = useState(false);
  const [selectedMetaFile, setSelectedMetaFile] = useState(null);
  const [regionId, setRegionId] = useState(null);
  const navigate = useNavigate();
  const loading = useLoading(status);

  const style = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    width: 400,
    bgcolor: 'background.paper',
    borderRadius: 3,
    boxShadow: 24,
    p: 4,
  };

  useEffect(() => {
    setCareers(newCareers);
  }, [newCareers]);

  useEffect(() => {
    dispatch(getNestedCareers());
  }, []);
  const handleImportMetaClick = () => setMetaOpen(true);
  const handleCloseMeta = () => {
    setMetaOpen(false);
    setSelectedMetaFile(null);
    setRegionId(null);
  };
  const handleExportClick = () => setExportOpen(true);
  const handleCloseExport = () => {
    setExportOpen(false);
    setRegionId(null);
  };
  const handleImportClick = () => setOpen(true);
  const handleClose = () => {
    setOpen(false);
    setSelectedFile(null);
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.name.endsWith('.xlsx')) {
      setSelectedFile(file);
    } else {
      alert('Please upload a valid .xlsx file');
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.name.endsWith('.xlsx')) {
      setSelectedFile(file);
    }
  };
  const handleMetaFileChange = (e) => {
    const file = e.target.files[0];
    if (file && file.name.endsWith('.xlsx')) {
      setSelectedMetaFile(file);
    } else {
      alert('Please upload a valid .xlsx file');
    }
  };

  const handleMetaDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file && file.name.endsWith('.xlsx')) {
      setSelectedMetaFile(file);
    }
  };
  const [isStatsOpen, setStatsOpen] = useState(false);
  const [stateData, setStateData] = useState({});

  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const [selectedCareer, setSelectedCareer] = useState(null);
  const regionsState = useSelector((state) => state.regions);
  const { regions } = regionsState;

  useEffect(() => {
    dispatch(getRegions(true));
  }, []);

  const handleOpenDeleteModal = (career, event) => {
    event.stopPropagation(); // Prevent row click event
    setSelectedCareer(career);
    setOpenDeleteModal(true);
  };

  const handleCloseDeleteModal = () => {
    setOpenDeleteModal(false);
    setSelectedCareer(null);
  };

  const handleConfirmDeleteModal = () => {
    if (selectedCareer) {
      handleDeleteCareer(selectedCareer);
    }
    handleCloseDeleteModal();
  };

  const handleOpenPopup = (payload) => {
    setStateData(() => payload);
    setStatsOpen(() => true)
  }
  const handleClosePopup = (payload) => {
    setStatsOpen(() => false)
  }

  const handleUploadFile = async () => {
    // Handle upload logic here
    dispatch(importCareer(selectedFile)).then((response) => {
      if (get(response, 'payload.success')) {
        const successMessage = get(response, 'payload.message', 'Successfully Imported career');
        dispatch(
          setSnackbar({
            snackbarOpen: true,
            snackbarType: 'success',
            snackbarMessage: successMessage,
          })
        );
        handleClose();
        if (get(response, 'payload.data')) {
          handleOpenPopup(get(response, 'payload.data'))
        }
        dispatch(getNestedCareers());
      } else {
        const errorMessage = get(response, 'payload.message', 'something went wrong');
        dispatch(
          setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: errorMessage,
          })
        );
      }
    });
  };
  const handleUploadMetaFile = async () => {
    // Handle upload logic here
    dispatch(importCareersMeta({selectedMetaFile, regionId})).then((response) => {
      if (get(response, 'payload.success')) {
        const successMessage = get(response, 'payload.message', 'Successfully imported careers metadata');
        dispatch(
          setSnackbar({
            snackbarOpen: true,
            snackbarType: 'success',
            snackbarMessage: successMessage,
          })
        );
        handleCloseMeta();
        dispatch(getNestedCareers());
      } else {
        const errorMessage = get(response, 'payload.message', 'something went wrong');
        dispatch(
          setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: errorMessage,
          })
        );
      }
    });
  };
  const notify = () =>
    dispatch(
      setSnackbar({
        snackbarOpen: true,
        snackbarType: 'success',
        snackbarMessage: 'Career exported successfully',
      })
    );
  const notifyMetaData = () =>
    dispatch(
      setSnackbar({
        snackbarOpen: true,
        snackbarType: 'success',
        snackbarMessage: 'Careers Metadata exported successfully',
      })
    );
  const handleExportFile = async () => {
    dispatch(exportCareer({ notify }));
  };
  const handleExportMetaFile = async () => {
    dispatch(exportMetaFile({regionId, notify: notifyMetaData})).then().finally(() => handleCloseExport());
  };
  const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/careers/create-careers`);

  const handleDeleteCareer = (career) => {
    dispatch(removeCareer({ id: career._id, type: career.type })).then((res) => {
      if (res.payload.success) {
        const successMessage = get(res, 'payload.message', 'Successfully removed career');
        dispatch(
          setSnackbar({
            snackbarOpen: true,
            snackbarType: 'success',
            snackbarMessage: successMessage,
          })
        );
      } else {
        const errorMessage = get(res, 'payload.data.message', 'something went wrong');
        dispatch(
          setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: errorMessage,
          })
        );
      }
    });
  };

  const editCareer = (career) => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/careers/edit/${career?._id}`);
  };

  // Search filter
  const filteredCareers = Careers.filter(
    (broad) =>
      broad.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (broad.careers &&
        broad.careers.some(
          (career) =>
            career.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (career.specialisedRoles &&
              career.specialisedRoles.some((role) => role.title?.toLowerCase().includes(searchTerm.toLowerCase())))
        ))
  );

  // Table columns
  const columns = [
    {
      name: 'Unit Groups',
      selector: (row) => row?.title,
      sortable: true,
    },
    {
      name: '',
      button: true,
      cell: (row) => (
        <Button onClick={() => editCareer(row)}>
          <Iconify icon={'eva:edit-fill'} />
        </Button>
      ),
    },
    {
      button: true,
      cell: (row) => (
        <Button color="error" onClick={(event) => handleOpenDeleteModal(row, event)}>
          <Iconify icon={'eva:trash-2-outline'} />
        </Button>
      ),
    },
  ];

  const SubColumns = [
    {
      name: 'Broad Career',
      selector: (row) => row?.title,
      sortable: true,
    },
    {
      name: '',
      button: true,
      cell: (row) => (
        <Button onClick={() => editCareer(row)}>
          <Iconify icon={'eva:edit-fill'} />
        </Button>
      ),
    },
    {
      button: true,
      cell: (row) => (
        <Button color="error" onClick={(event) => handleOpenDeleteModal(row, event)}>
          <Iconify icon={'eva:trash-2-outline'} />
        </Button>
      ),
    },
  ];

  const SpecialisedColumns = [
    {
      name: 'Specialised Role',
      selector: (row) => row?.title,
      sortable: true,
    },
    {
      name: '',
      button: true,
      cell: (row) => (
        <Button onClick={() => editCareer(row)}>
          <Iconify icon={'eva:edit-fill'} />
        </Button>
      ),
    },
    {
      button: true,
      cell: (row) => (
        <Button color="error" onClick={(event) => handleOpenDeleteModal(row, event)}>
          <Iconify icon={'eva:trash-2-outline'} />
        </Button>
      ),
    },
  ];

  // Specialised Roles Table
  const SpecialisedExpandedComponent = ({ data }) => {
    return (
      <DataTable
        columns={SpecialisedColumns}
        data={data.specialisedRoles}
        customStyles={specializedStyles}
        noHeader
        dense
        noDataComponent="No Specialised Roles Found"
      />
    );
  };

  // Careers Table (child)
  const CareersExpandedComponent = ({ data }) => (
    <DataTable
      columns={SubColumns}
      data={data.careers}
      customStyles={subCareerStyles}
      expandableRows
      expandableRowsComponent={SpecialisedExpandedComponent}
      expandOnRowClicked
      noHeader
      dense
      noDataComponent="No Careers Found"
    // expandableRowDisabled={(row) => !row.specialisedRoles || row.specialisedRoles.length === 0}
    />
  );

  // SubHeader (search bar)
  const subHeaderComponent = (
    <div
      style={{
        display: 'flex',
        columnGap: '8px',
        alignItems: 'center',
        justifyContent: 'space-between',
        width: '100%',
      }}
    >
      <StyledSearch
        sx={{ my: 1 }}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={'Search...'}
        startAdornment={
          <InputAdornment position="start">
            <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled', width: 20, height: 20 }} />
          </InputAdornment>
        }
      />
      <Box flexGrow={1} />
      <LoadingButton
        color="primary"
        variant="outlined"
        startIcon={<Iconify width="48" height="48" icon="pajamas:export" />}
        onClick={() => handleExportClick()}
        loading={exportMetaLoading}
      >
        Export Metadata
      </LoadingButton>
      <Button
        color="primary"
        variant="outlined"
        startIcon={<Iconify width="48" height="48" icon="pajamas:import" />}
        onClick={() => handleImportMetaClick()}
      >
        Import Metadata
      </Button>
      <LoadingButton
        color="primary"
        variant="outlined"
        startIcon={<Iconify width="48" height="48" icon="pajamas:export" />}
        onClick={() => handleExportFile()}
        loading={exportLoading}
      >
        Export Careers
      </LoadingButton>
      <Button
        color="primary"
        variant="outlined"
        startIcon={<Iconify width="48" height="48" icon="pajamas:import" />}
        onClick={() => handleImportClick()}
      >
        Import Careers
      </Button>
      <Button variant="contained" onClick={handleOpen} startIcon={<Iconify icon="eva:plus-fill" />}>
        New Career
      </Button>
    </div>
  );

  const progressComponent = (
    <Box sx={{ width: 'inherit' }}>
      <LinearProgress />
    </Box>
  );

  return (
    <>
      <Helmet>
        <title> Careers | ThinkSkill </title>
      </Helmet>
      <Dialog open={openDeleteModal} onClose={handleCloseDeleteModal}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          Are you sure you want to delete this career <b>{selectedCareer?.title ? `: "${selectedCareer.title}"` : ''}? </b>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteModal} color="primary">Cancel</Button>
          <Button onClick={handleConfirmDeleteModal} color="error">Delete</Button>
        </DialogActions>
      </Dialog>
      <Dialog open={isStatsOpen} onClose={handleClosePopup} maxWidth="md" fullWidth>
        <DialogTitle>
          Stats Summary
          <IconButton
            aria-label="close"
            onClick={handleClosePopup}
            sx={{
              position: "absolute",
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>

        <DialogContent dividers>
          <StatsTabs data={stateData} />
        </DialogContent>

        <DialogActions>
          <Button
            onClick={handleClosePopup}
            variant="contained" color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>
      <Modal open={open} onClose={handleClose}>
        <Box sx={style}>
          <Typography variant="h6" mb={2}>
            Upload Excel File
          </Typography>

          {/* Download Template Button */}
          <Box mb={2} textAlign="right">
            <Button
              variant="text"
              size="small"
              href="/files/careers_import_sheet.xlsx" // Adjust path if different
              download
              sx={{ textTransform: 'none' }}
            >
              Download Template
            </Button>
          </Box>

          <Box
            onDrop={handleDrop}
            onDragOver={(e) => e.preventDefault()}
            sx={{
              border: '2px dashed #ccc',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              mb: 2,
            }}
          >
            <UploadFileIcon color="action" sx={{ fontSize: 40 }} />
            <Typography variant="body1" mt={1}>
              Drag & drop your .xlsx file here
            </Typography>
            <Typography variant="body2" color="text.secondary">
              or
            </Typography>
            <Button variant="outlined" component="label" sx={{ mt: 1 }}>
              Browse
              <input type="file" hidden accept=".xlsx" onChange={handleFileChange} />
            </Button>
          </Box>

          {selectedFile && (
            <Typography variant="body2" color="text.primary">
              Selected File: {selectedFile.name}
            </Typography>
          )}

          <Stack direction="row" spacing={2} justifyContent="flex-end" mt={3}>
            <Button onClick={handleClose}>Cancel</Button>
            <LoadingButton
              variant="contained"
              disabled={!selectedFile}
              onClick={handleUploadFile}
              loading={importLoading}
            >
              Upload
            </LoadingButton>
          </Stack>
        </Box>
      </Modal>
      <Modal open={metaOpen} onClose={handleCloseMeta}>
        <Box sx={style}>
          <Typography variant="h6" mb={2}>
            Import Careers Metadata
          </Typography>

          {/* Download Template Button */}
          <Box mb={2} textAlign="right">
            <Button
              variant="text"
              size="small"
              href="/files/careers_metadata_import_template.xlsx" // Adjust path if different
              download
              sx={{ textTransform: 'none' }}
            >
              Download Template
            </Button>
          </Box>
          <Box mb={2} textAlign="left">
            <SelectField
              sx={{ width: '100%' }}
              label="Select Region"
              value={regionId}
              onChange={(event, value) => setRegionId(event?.target?.value)}
            >
              {regions?.map(user => {
                return (<MenuItem value={user?._id}>
                  {user?.name}
                </MenuItem>)
              })}
            </SelectField>
          </Box>

          <Box
            onDrop={handleMetaDrop}
            onDragOver={(e) => e.preventDefault()}
            sx={{
              border: '2px dashed #ccc',
              borderRadius: 2,
              p: 3,
              textAlign: 'center',
              cursor: 'pointer',
              mb: 2,
            }}
          >
            <UploadFileIcon color="action" sx={{ fontSize: 40 }} />
            <Typography variant="body1" mt={1}>
              Drag & drop your .xlsx file here
            </Typography>
            <Typography variant="body2" color="text.secondary">
              or
            </Typography>
            <Button variant="outlined" component="label" sx={{ mt: 1 }}>
              Browse
              <input type="file" hidden accept=".xlsx" onChange={handleMetaFileChange} />
            </Button>
          </Box>

          {selectedMetaFile && (
            <Typography variant="body2" color="text.primary">
              Selected File: {selectedMetaFile.name}
            </Typography>
          )}

          <Stack direction="row" spacing={2} justifyContent="flex-end" mt={3}>
            <Button onClick={handleCloseMeta}>Cancel</Button>
            <LoadingButton
              variant="contained"
              disabled={!selectedMetaFile || !regionId}
              onClick={handleUploadMetaFile}
              loading={importLoading}
            >
              Upload
            </LoadingButton>
          </Stack>
        </Box>
      </Modal>
      <Modal open={exportOpen} onClose={handleCloseExport}>
        <Box sx={style}>
          <Typography variant="h6" mb={2}>
            Export Careers Metadata
          </Typography>

          {/* Download Template Button */}
          <Box mb={2} textAlign="left">
            <SelectField
              sx={{ width: '100%' }}
              label="Select Region"
              value={regionId}
              onChange={(event, value) => setRegionId(event?.target?.value)}
            >
              {regions?.map(user => {
                return (<MenuItem value={user?._id}>
                  {user?.name}
                </MenuItem>)
              })}
            </SelectField>
          </Box>

          <Stack direction="row" spacing={2} justifyContent="flex-end" mt={3}>
            <Button onClick={handleCloseExport}>Cancel</Button>
            <LoadingButton
              variant="contained"
              disabled={!regionId}
              onClick={handleExportMetaFile}
              loading={exportMetaLoading}
            >
              Export
            </LoadingButton>
          </Stack>
        </Box>
      </Modal>

      <Container maxWidth="xl">
        <Typography variant="h4">Careers</Typography>
        <Grid container>
          <Grid item xs={12}>
            <Card sx={{ p: 0, my: 3, width: '100%' }}>
              <DataTable
                columns={columns}
                data={filteredCareers}
                customStyles={careerStyles}
                expandableRows
                expandableRowsComponent={CareersExpandedComponent}
                expandOnRowClicked
                highlightOnHover
                subHeader
                subHeaderComponent={subHeaderComponent}
                subHeaderAlign="left"
                progressPending={loading}
                progressComponent={progressComponent}
                pagination
                Import
                Careers={'Import Careers'}
                handleImportClick={handleImportClick}
                noDataComponent={
                  <Typography variant="subtitle1" color="text.secondary" sx={{ mb: 2 }}>
                    No Careers Found
                  </Typography>
                }
              // expandableRowDisabled={(row) => !row.careers || row.careers.length === 0}
              />
            </Card>
          </Grid>
        </Grid>
      </Container>
    </>
  );
};

export default NewCareers;
