const axios = require('axios');
require('dotenv').config();

/**
 * Test Fine-Tuning API Endpoints
 * Tests the new API endpoints for the complete fine-tuning workflow
 */
const testFineTuningAPIs = async () => {
  const apiBaseURL = process.env.API_BASE_URL || 'http://localhost:3000';
  
  console.log('🧪 Testing Fine-Tuning API Endpoints');
  console.log('====================================');
  console.log(`📊 API Base URL: ${apiBaseURL}`);

  try {
    // Test 1: Check training data status
    console.log('\n📊 Test 1: Check Training Data Status');
    await testTrainingDataStatus(apiBaseURL);

    // Test 2: Generate training data (small sample)
    console.log('\n🚀 Test 2: Generate Training Data');
    await testGenerateTrainingData(apiBaseURL);

    // Test 3: Check training data status again
    console.log('\n📊 Test 3: Check Training Data Status (After Generation)');
    await testTrainingDataStatus(apiBaseURL);

    // Test 4: List fine-tuning jobs
    console.log('\n📋 Test 4: List Fine-Tuning Jobs');
    await testListFineTuningJobs(apiBaseURL);

    // Test 5: Test model endpoint
    console.log('\n🧪 Test 5: Test Model');
    await testModelEndpoint(apiBaseURL);

    console.log('\n🎉 All Fine-Tuning API tests completed!');
    console.log('✅ APIs are ready for your admin panel integration');

  } catch (error) {
    console.error('\n❌ API testing failed:', error.message);
  }
};

/**
 * Test training data status endpoint
 */
const testTrainingDataStatus = async (baseURL) => {
  try {
    const response = await axios.get(`${baseURL}/api/chatbot/admin/training-data/status`);
    
    if (response.data.success) {
      console.log('✅ Training Data Status: SUCCESS');
      console.log(`   Ready: ${response.data.data.ready}`);
      
      const files = response.data.data.files;
      if (files.trainingData?.exists) {
        console.log(`   Training Examples: ${files.trainingData.examples}`);
      }
      if (files.validationData?.exists) {
        console.log(`   Validation Examples: ${files.validationData.examples}`);
      }
      if (files.validationReport?.exists) {
        console.log(`   Quality Score: ${files.validationReport.qualityScore}`);
      }
    } else {
      console.log('❌ Training Data Status: FAILED');
      console.log(`   Error: ${response.data.message}`);
    }
  } catch (error) {
    console.log('❌ Training Data Status: ERROR');
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
  }
};

/**
 * Test generate training data endpoint
 */
const testGenerateTrainingData = async (baseURL) => {
  try {
    console.log('   Generating training data (small sample for testing)...');
    
    const response = await axios.post(`${baseURL}/api/chatbot/admin/training-data/generate`, {
      maxExamplesPerType: 5, // Small sample for testing
      validationSplit: 0.2,
      validateData: true,
      examplesPerRecord: 2
    });
    
    if (response.data.success) {
      console.log('✅ Generate Training Data: SUCCESS');
      console.log(`   Training Examples: ${response.data.data.trainingExamples}`);
      console.log(`   Validation Examples: ${response.data.data.validationExamples}`);
      console.log(`   Records Processed: ${response.data.data.totalRecordsProcessed}`);
      console.log(`   Quality Score: ${response.data.data.qualityScore || 'N/A'}`);
    } else {
      console.log('❌ Generate Training Data: FAILED');
      console.log(`   Error: ${response.data.message}`);
    }
  } catch (error) {
    console.log('❌ Generate Training Data: ERROR');
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
  }
};

/**
 * Test list fine-tuning jobs endpoint
 */
const testListFineTuningJobs = async (baseURL) => {
  try {
    const response = await axios.get(`${baseURL}/api/chatbot/admin/fine-tuning/jobs`);
    
    if (response.data.success) {
      console.log('✅ List Fine-Tuning Jobs: SUCCESS');
      console.log(`   Total Jobs: ${response.data.data.total}`);
      
      if (response.data.data.jobs.length > 0) {
        const latestJob = response.data.data.jobs[0];
        console.log(`   Latest Job: ${latestJob.id} (${latestJob.status})`);
        if (latestJob.fine_tuned_model) {
          console.log(`   Model ID: ${latestJob.fine_tuned_model}`);
        }
      } else {
        console.log('   No fine-tuning jobs found');
      }
    } else {
      console.log('❌ List Fine-Tuning Jobs: FAILED');
      console.log(`   Error: ${response.data.message}`);
    }
  } catch (error) {
    console.log('❌ List Fine-Tuning Jobs: ERROR');
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
  }
};

/**
 * Test model endpoint
 */
const testModelEndpoint = async (baseURL) => {
  try {
    const response = await axios.post(`${baseURL}/api/chatbot/admin/test-model`, {
      testMessage: 'Hello, this is a test of the API endpoints.'
    });
    
    if (response.data.success) {
      console.log('✅ Test Model: SUCCESS');
      console.log(`   Test Passed: ${response.data.data.testPassed}`);
      console.log(`   Model Used: ${response.data.data.modelUsed}`);
      console.log(`   Response Type: ${response.data.data.responseType}`);
    } else {
      console.log('❌ Test Model: FAILED');
      console.log(`   Error: ${response.data.message}`);
    }
  } catch (error) {
    console.log('❌ Test Model: ERROR');
    console.log(`   Error: ${error.response?.data?.message || error.message}`);
  }
};

// Run test if called directly
if (require.main === module) {
  testFineTuningAPIs();
}

module.exports = testFineTuningAPIs;
