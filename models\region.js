const mongoose = require("mongoose");
const { CollegeGroup, CollegeGroupSchema } = require("./collegeGroup");

const RegionSchema = mongoose.Schema({
  name: String,
  description: String,
  slug: String,
  collegeGroupIds: [{ type: mongoose.Schema.Types.ObjectId, ref:mongoose.model(CollegeGroup, CollegeGroupSchema) }],
  logo: String,
  partnerLogos: [String],
  button: {
    color: String,
    bgColor: String,
  },
  bannerImages: [String],
  bgImage: String,
  fontColor: String,
  primaryColor: String,
  secondaryColor: String,
  addedBy: Object,
  editedBy: Object,
  isSystem: { type: Boolean, default: false },
})

module.exports.RegionSchema = RegionSchema;

class Region extends mongoose.Model
{
}

mongoose.model(Region, RegionSchema, "regions");

module.exports.Region = Region;