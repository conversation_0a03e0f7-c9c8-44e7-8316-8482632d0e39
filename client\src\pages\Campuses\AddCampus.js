import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, Container, FormControl, FormHelperText, Grid, InputLabel, MenuItem, Select, Stack, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import PhoneInput from '../../components/PhoneInput';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import SelectField from '../../components/SelectedField';
import SelectComponent from '../../components/SelectComponent';
import useAuth from '../../hooks/useAuth';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { CancelButton, formButton } from '../../utils/cssStyles';
import { campusValidationSchema } from '../../utils/validationSchemas';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getColleges } from '../Colleges/collegesSlice';
import { postCampus } from './campusesSlice';
import { getAvailableUsers } from '../Userspage/usersSlice';


const AddCampus = () => {
    const [Groups, setGroups] = useState([])
    const [loading, setLoading] = useState(false)
    const [Colleges, setColleges] = useState([])
    const groups = useSelector(state => state.collegeGroups.groups)
    const colleges = useSelector(state => state.colleges.colleges)
    const { availableUsers } = useSelector(state => state.users)
    const navigate = useNavigate()
    const filteredCollege = JSON.parse(localStorage.getItem('selectedCollege'));
    
    useEffect(() => {
        if (role === "1" || role === "2") {
            setGroups(groups)
        }
    }, [groups])
    const { role } = useAuth()

    useEffect(() => {
        if (role === '1' || role === '2' || role === '3') {
            dispatch(getColleges())
        }
        if (role === '1' || role === '2') {
            dispatch(getCollegeGroups())
        }
    }, [role])

    useEffect(() => {
        const params = {
            role: '4',
            includeIds: ""
        }
        dispatch(getAvailableUsers(params))
    }, [])

    useEffect(() => {
        if (role !== "1" && role !== "2") {
            setGroups([
                colleges && colleges.length && {
                    id: colleges[0].groupId,
                    Name: colleges[0].groupName
                }
            ])
        }
    }, [role, colleges])

    useEffect(() => {
        if (role !== "1" && role !== "2") {
            formik.values.groupName = colleges && colleges[0]?.groupId
        }
    }, [role, colleges])

    const dispatch = useDispatch()
    const formik = useFormik({
        initialValues: {
            name: '',
            collegeName: '',
            groupName: '',
            campusAddress: '',
            campusWebsiteAddress: '',
            campusEmail: '',
            campusTelNumber: '',
            campusCity: '',
            campusState: '',
            campusZip: '',
            admin: ''
        },
        onSubmit: (values) => {
            const campus = {
                name: values.name,
                // collegeId: values.collegeName,
                collegeId: filteredCollege?._id,
                collegeName: Colleges.find(college => college?._id === values.collegeName)?.name,
                // groupName: Groups.find(group => group?.id === values.groupName)?.Name,
                // adminUserId: values.admin,
                contactNumber: values.campusTelNumber,
                email: values.campusEmail,
                website: values.campusWebsiteAddress,
                city: values.campusCity,
                state: values.campusState,
                zip: values.campusZip,
                address1: values.campusAddress
            }
            if(values?.admin){
                campus.adminUserId = values.admin
            }
            if(filteredCollege){
            setLoading(true)
                dispatch(postCampus(campus))
                    .then(response => {
                        if (response?.payload?.success) {
                            // succesfully added campus
                            dispatch(setSnackbar({
                                snackbarOpen: true,
                                snackbarType: 'success',
                                snackbarMessage: "Succesfully added Campus"
                            }))
                            navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses`)
                        } else {
                            // error occoured
                            const errorMessage = response?.payload?.data?.message
                            console.log("error", errorMessage)
                            dispatch(setSnackbar({
                                snackbarOpen: true,
                                snackbarType: 'error',
                                snackbarMessage: errorMessage || "Something went wrong!"
                            }))
                        }
                    }).finally(() => {
                        setLoading(false)
                    })
                handleClose()
            }else{
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'info',
                    snackbarMessage: "Please Select a college from dropdown"
                }))
            }
        },
        validationSchema: campusValidationSchema
    })
    useEffect(() => {
        formik.values.collegeName = ""
    }, [formik.values.groupName])

    const groupName = formik?.values?.groupName
    useEffect(() => {
        const filteredColleges = colleges.filter(college => college.groupId === formik.values.groupName)
        setColleges(filteredColleges)
    }, [colleges, groupName])
    const handleClose = () => {
        // setOpenModel(false)
    }

    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            campusTelNumber: newValue
        })
    }


    const style = {
        p: 4,
    };
    return (
        <div>
            <Container maxWidth="lg">
                <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                    Add Campus
                </Typography>
                <Card>
                    <Box sx={style} >
                        <form onSubmit={formik.handleSubmit}>
                            <Grid container gap={2} >
                                {/* <Grid item xs={12} md={5.8} lg={3.8}>
                                    <SelectField
                                        sx={{ width: '100%' }}
                                        name='groupName'
                                        label="Group"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.groupName}
                                        onChange={formik.handleChange}
                                        error={formik.touched.groupName && Boolean(formik.errors.groupName)}
                                        helperText={formik.touched.groupName && formik.errors.groupName}
                                        disabled={!(role === "1" || role === "2")}
                                    >
                                        <MenuItem value="">
                                            <em>None</em>
                                        </MenuItem>
                                        {Groups.map(group => (<MenuItem
                                            key={group.id}
                                            value={group.id}
                                        >
                                            {group.Name}
                                        </MenuItem>))}
                                    </SelectField>
                                </Grid>

                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    <SelectField
                                        sx={{ width: '100%' }}
                                        name='collegeName'
                                        label="College"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.collegeName}
                                        onChange={formik.handleChange}
                                        error={formik.touched.collegeName && Boolean(formik.errors.collegeName)}
                                        helperText={formik.touched.collegeName && formik.errors.collegeName}
                                    >
                                        <MenuItem value="">
                                            <em>None</em>
                                        </MenuItem>
                                        {Colleges.map(college => (<MenuItem
                                            key={college._id}
                                            value={college._id}
                                        >
                                            {college.name}
                                        </MenuItem>))}
                                    </SelectField>
                                </Grid> */}

                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='name'
                                        label="Name"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.name}
                                        onChange={formik.handleChange}
                                        error={formik.touched.name && Boolean(formik.errors.name)}
                                        helperText={formik.touched.name && formik.errors.name}
                                    />
                                </Grid>

                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusEmail'
                                        label="Email"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusEmail}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusEmail && Boolean(formik.errors.campusEmail)}
                                        helperText={formik.touched.campusEmail && formik.errors.campusEmail}
                                    />
                                </Grid>

                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    {/* <PhoneInput
                                        sx={{ width: "100%" }}
                                        value={formik.values.campusTelNumber}
                                        name='campusTelNumber'
                                        label="Number"
                                        defaultCountry="GB"
                                        onChange={handlePhoneChange}
                                        onBlur={formik.handleBlur}
                                        error={formik.touched.campusTelNumber && Boolean(formik.errors.campusTelNumber)}
                                        helperText={formik.touched.campusTelNumber && formik.errors.campusTelNumber}
                                    /> */}

                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusTelNumber'
                                        label="Number"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusTelNumber}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusTelNumber && Boolean(formik.errors.campusTelNumber)}
                                        helperText={formik.touched.campusTelNumber && formik.errors.campusTelNumber}
                                    />
                                </Grid>

                                <Grid item xs={12} md={11.8} >
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusAddress'
                                        label="Address"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusAddress}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusAddress && Boolean(formik.errors.campusAddress)}
                                        helperText={formik.touched.campusAddress && formik.errors.campusAddress}
                                    />
                                </Grid>
                                <Grid item xs={12} md={11.8} >
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusWebsiteAddress'
                                        label="Website Address"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusWebsiteAddress}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusWebsiteAddress && Boolean(formik.errors.campusWebsiteAddress)}
                                        helperText={formik.touched.campusWebsiteAddress && formik.errors.campusWebsiteAddress}
                                    />
                                </Grid>

                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusCity'
                                        label="City"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusCity}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusCity && Boolean(formik.errors.campusCity)}
                                        helperText={formik.touched.campusCity && formik.errors.campusCity}
                                    />
                                </Grid>
                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusState'
                                        label="Country"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusState}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusState && Boolean(formik.errors.campusState)}
                                        helperText={formik.touched.campusState && formik.errors.campusState}
                                    />
                                </Grid>
                                <Grid item xs={12} md={5.8} lg={3.8}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='campusZip'
                                        label="Post Code"
                                        // type="number"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.campusZip}
                                        onChange={formik.handleChange}
                                        error={formik.touched.campusZip && Boolean(formik.errors.campusZip)}
                                        helperText={formik.touched.campusZip && formik.errors.campusZip}
                                    />
                                </Grid>
                                <Grid item xs={11.8} md={5.8} lg={3.8} >
                                    {/* <SelectComponent
                                        menuValues={["firstName", 'lastName']}
                                        menuValue={"_id"}
                                        labelId="admin-label"
                                        label="Admin *"
                                        inputLabel="Admin"
                                        menuItems={availableUsers}
                                        sx={{ width: '100%' }}
                                        name='admin'
                                        onBlur={formik.handleBlur}
                                        defaultValue={formik.values?.admin}
                                        value={formik.values?.admin}
                                        onChange={formik.handleChange}
                                        labelColor={formik.touched.admin && formik.errors.admin && 'error'}
                                        labelError={formik.touched.admin && formik.errors.admin}
                                        error={formik.touched.admin && Boolean(formik.errors.admin)}
                                        helperText={formik.touched.admin && formik.errors.admin}
                                    /> */}
                                    <SelectField
                                        sx={{ width: '100%' }}
                                        name='admin'
                                        label="Admin"
                                        onBlur={formik.handleBlur}
                                        value={formik.values.admin}
                                        onChange={formik.handleChange}
                                        error={formik.touched.admin && Boolean(formik.errors.admin)}
                                        helperText={formik.touched.admin && formik.errors.admin}
                                    >
                                        <MenuItem value="">
                                            <em>None</em>
                                        </MenuItem>
                                        {availableUsers?.map(user => {
                                            return (<MenuItem value={user?._id}>
                                                {`${user?.firstName} ${user?.lastName}`}
                                            </MenuItem>)
                                        })}
                                    </SelectField>
                                </Grid>
                            </Grid>
                            <Stack direction="row" justifyContent="flex-end">
                                <Button
                                    type='button'
                                    variant='contained'
                                    color='error'
                                    onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses`)}
                                    sx={CancelButton}>
                                    Cancel
                                </Button>
                                <LoadingButton
                                    loading={loading}
                                    type='submit'
                                    variant='contained'
                                    sx={formButton}>
                                    Add
                                </LoadingButton>
                            </Stack>
                        </form>
                    </Box>
                </Card>
            </Container>
        </div>
    )
}

export default AddCampus