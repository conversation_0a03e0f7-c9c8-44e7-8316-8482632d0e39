const mongoose = require("mongoose");

const SectorSchema = new mongoose.Schema({
  name: String,
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
  priority: { type: Boolean, default: false },
});

module.exports.SectorSchema = SectorSchema;

class Sector extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await Sector.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await Sector.create(entry);
    }
  }
}

mongoose.model(Sector, SectorSchema, "sectors");

module.exports.Sector = Sector;

