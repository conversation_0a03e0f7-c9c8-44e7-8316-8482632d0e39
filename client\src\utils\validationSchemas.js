import { matchIsValidTel } from 'mui-tel-input';
import * as yup from 'yup';
import { required, trim, validEmail, validPhoneNumber, emailRequired, validTelephoneNumber, selectCollegeGroupFirst, minPassword } from './config';

// const urlMatch = /^((http|https):\/\/)?(www.)?(?!.*(http|https|www.))[a-zA-Z0-9_-]+(\.[a-zA-Z]+)+(\/)?.([\w[a-zA-Z-_%@?]+)*([^\w[a-zA-Z0-9_-]+=\w+(&[a-zA-Z0-9_]+=\w+)*)?$/;
// const urlMatch =  /(?:https?):\/\/(\w+:?\w*)?(\S+)(:\d+)?(\/|\/([\w#!:.?+=&%!]))?/;
const urlMatch = /[-a-zA-Z0-9@:%_.~#?&//=]{2,256}\.[a-z]{2,4}\b(\/[-a-zA-Z0-9@:%_.~#?&//=]*)?/gi;

export const userValidationSchema = yup.object({
    // phone: yup
    //     .string()
    //     .test
    //     ("test-name", validPhoneNumber,
    //         (value) => {
    //             if (value) {
    //                 return matchIsValidTel(value)
    //             }
    //             return true
    //         }),
    firstName: yup
        .string()
        .required(`First name ${required}`)
        .trim(trim)
        .strict(true),
    lastName: yup
        .string()
        .required(`Last name ${required}`)
        .trim(trim)
        .strict(true),
    email: yup
        .string()
        .email(validEmail)
        .required(emailRequired)
        .trim(trim)
        .strict(true),
    password: yup
        .string()
        .required(`Password ${required}`)
        .trim(trim)
        .strict(true),
})
export const profileValidation = yup.object({
    firstName: yup
        .string()
        .required(`First name ${required}`)
        .trim(trim)
        .strict(true),
    lastName: yup
        .string()
        .required(`Last name ${required}`)
        .trim(trim)
        .strict(true),
    email: yup
        .string()
        .email(validEmail)
        .required(emailRequired)
        .trim(trim)
        .strict(true),
})
export const editUserValidationSchema = yup.object({
    // phone: yup
    //     .string()
    //     .test
    //     ("test-name", validPhoneNumber,
    //         (value) => {
    //             if (value) {
    //                 return matchIsValidTel(value)
    //             }
    //             return true
    //         }),
    firstName: yup
        .string()
        .required(`First name ${required}`)
        .trim(trim)
        .strict(true),
    lastName: yup
        .string()
        .required(`Last name ${required}`)
        .trim(trim)
        .strict(true),
    email: yup
        .string()
        .email(validEmail)
        .required(emailRequired)
        .trim(trim)
        .strict(true)
})

export const addCourseValidationSchema = yup.object({
    title: yup
        .string()
        .required(`Title ${required}`)
        .trim(trim)
        .strict(true),
    code: yup
        .string()
        .required(`Code ${required}`)
        .trim(trim)
        .strict(true),
    // campusId: yup
    //     .string()
    //     .required(`Campus ${required}`)
    //     .trim(trim)
    //     .strict(true),
    pageURL: yup
        .string(),
    // .matches(urlMatch, "Course URL should be a valid URL"),
    applyURL: yup
        .string(),
    // .matches(urlMatch, "Application URL should be a valid URL"),
    enquiryURL: yup
        .string(),
    // .matches(urlMatch, "Enquiry URL should be a valid URL"),
})

export const resetPasswordValidation = yup.object({
    oldPassword: yup
        .string()
        .required(`Current Password ${required}`),
    newPassword: yup
        .string()
        .required(`New Password ${required}`),
    confirmPassword: yup
        .string()
        .required(`Confirm Password ${required}`),
})

export const collegeValidationSchema = yup.object({
    name: yup
        .string()
        .required(`College name ${required}`)
        .trim(trim)
        .strict(true),
    groupName: yup
        .string()
        .required(selectCollegeGroupFirst)
        .trim(trim)
        .strict(true),
    // collegeTelNumber: yup
    //     .string()
    //     .test
    //     ("test-name", validTelephoneNumber,
    //         (value) => {
    //             if (value) {
    //                 return matchIsValidTel(value)
    //             }
    //             return true
    //         }),
    // collegeTelNumber: yup
    //     .number()
    //     .required(`Phone Number ${required}`),
    // admin: yup
    //     .string()
    //     .required(`Admin ${required}`)
    //     .trim(trim)
    //     .strict(true),
    slug: yup
        .string()
        .required('Please provide a unique slug')
        .trim(trim)
        .strict(true)

    // collegeAddress: yup
    //     .string()
    //     .required("College Address is required")
    //     .trim('Cannot include leading and trailing spaces')
    //     .strict(true),
    // collegeEmail: yup
    //     .string()
    //     .email("Enter a valid email")
    //     .required("Email is required ")
    //     .trim('Cannot include leading and trailing spaces')
    //     .strict(true),
})

export const groupValidationSchema = yup.object({
    Name: yup
        .string()
        .required(`Group name ${required}`)
        .trim(trim)
        .strict(true),
    // groupTelNumber: yup
    //     .string()
    //     .test
    //     ("test-name", validTelephoneNumber,
    //         (value) => {
    //             if (value) {
    //                 return matchIsValidTel(value)
    //             }
    //             return true
    //         }),
    // groupTelNumber: yup
    //     .number()
    //     .required(`Phone Number ${required}`),
    admin: yup
        .string()
        .required(`Admin ${required}`)
        .trim(trim)
        .strict(true)
})

export const campusValidationSchema = yup.object({
    name: yup
        .string()
        .required(`Campus name ${required}`)
        .trim(trim)
        .strict(true),
    // collegeName: yup
    //     .string()
    //     .required(`College ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // groupName: yup
    //     .string()
    //     .required(`Group ${required}`)
    //     .trim(trim)
    //     .strict(true),

    // admin: yup
    //     .string()
    //     .required("Admin is required")
    //     .trim('Cannot include leading and trailing spaces')
    //     .strict(true),

    // campusTelNumber: yup
    //     .string()
    //     .test("test-name", "Enter valid Phone Number",
    //         (value) => {
    //             if (value) {
    //                 return matchIsValidTel(value)
    //             }
    //             return true
    //         }),
    // campusTelNumber: yup
    //     .number().required(`Phone Number ${required}`)
})

export const careerValidation = yup.object({
    title: yup
        .string()
        .required(`Title ${required}`)
        .trim(trim)
        .strict(true),
    // socCode: yup.string()
    //     .required(`SOC code ${required}`),
    onetCode: yup
        .string()
        .required(`ONET code ${required}`)
        .trim(trim)
        .strict(true),

    // careerDescription: yup
    //     .string()
    //     .required(`Description ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // knowledge: yup
    //     .string()
    //     .required(`Knowledge ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // intrest: yup
    //     .string()
    //     .required(`Intrest ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // jobZone: yup
    //     .string()
    //     .required(`JobZone ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // salary: yup
    //     .string()
    //     .required(`Salary ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // cluster: yup
    //     .string()
    //     .required(`Cluster ${required}`)
    //     .trim(trim)
    //     .strict(true),
    // pathway: yup
    //     .string()
    //     .required(`Pathway ${required}`)
    //     .trim(trim)
    //     .strict(true),

    // sectors: yup.array().of(
    //     yup.array().shape({
    //         subsectors: yup.array().min(1, 'Subsectors are required')
    //     })
    // ),
    // sectors: yup.array().min(1, 'sectors required')
})

export const credentialValidation = yup.object({
    email: yup
        .string()
        .email(validEmail)
        .required(emailRequired)
        .trim(trim)
        .strict(true),
    password: yup
        .string()
        .required(`Password ${required}`)
        .min(5, minPassword)
        .trim(trim)
        .strict(true),
})
export const addSectorsValidationSchema = yup.object({
    name: yup
        .string()
        .required(`Sector name ${required}`)
        .trim(trim)
        .strict(true),
})

export const AddEditSubSectorsValidationSchema = yup.object({
    name: yup
        .string()
        .required(`Sub-sector name ${required}`)
        .trim(trim)
        .strict(true),
    sector: yup
        .string()
        .required(`Sector ${required}`)
        .trim(trim)
        .strict(true),
})

export const addRadarCategoryValidationSchema = yup.object({
    title: yup
        .string()
        .required(`Radar category name ${required}`)
        .trim(trim)
        .strict(true),
})

export const skillAbilitiesRadarCategoryValidationSchema = yup.object({
    radarCategory: yup
        .string()
        .required(`Radar category name ${required}`)
        .trim(trim)
        .strict(true),
    radarSubCategory: yup
        .string()
        .required(`Radar sub-category name ${required}`)
        .trim(trim)
        .strict(true),
})
export const skillAbilitiesSubRadarCategoryValidationSchema = yup.object({
    radarCategory: yup
        .string()
        .required(`Radar category name ${required}`)
        .trim(trim)
        .strict(true),
})

export const addRadarSubCategoryValidationSchema = yup.object({
    title: yup
        .string()
        .required(`Radar sub-category name ${required}`)
        .trim(trim)
        .strict(true),
    radarCategory: yup
        .string()
        .required(`Radar category ${required}`)
        .trim(trim)
        .strict(true),
})

export const settingValidation = yup.object({
    skillpercentage: yup
        .number()
        .required(`Please enter a value between 3 - 30`)
        .min(3, 'Please enter a value between 3 - 30')
        .max(30, 'Please enter a value between 3 - 30'),
    skillstomatch: yup
        .number()
        .required(`Please enter a value between 1 - 10`)
        .min(1, 'Please enter a value between 1 - 10')
        .max(10, 'Please enter a value between 1 - 10'),
})
