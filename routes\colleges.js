const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const collegesController = require('../controllers/colleges.controller');
const CollegeAdminGuard = require("../guards/collegeAdmin.guard");
const CollegeGroupAdminGuard = require("../guards/collegeGroupAdmin.guard");
const CampusAdminGuard = require("../guards/campusAdmin.guard");

router.post("/add", CollegeGroupAdminGuard, collegesController.add);

router.get("/get", CampusAdminGuard , collegesController.get);

router.get("/getById", CollegeAdminGuard, collegesController.getByID);

router.get("/permissions", CollegeAdminGuard, collegesController.permissions);

router.get("/sync/courses", CollegeAdminGuard, collegesController.syncCourses);

router.delete("/remove", CollegeGroupAdminGuard, collegesController.remove);

router.put("/update", CollegeAdminGuard, collegesController.update);

module.exports = router;