import { createSlice } from '@reduxjs/toolkit'


const selectedCollegeSlice = createSlice({
    name: 'selectedCollege',
    initialState: {
        selectedCollege: null,
        currentUserPermissions: null
    },
    reducers: {
        addSelectedCollege: (state,action) => {
            state.selectedCollege = action.payload
        },
        removeSelectedCollege: (state,action) => {
            state.selectedCollege = null
        },
        addUserPermissions: (state,action) => {
            state.currentUserPermissions = action.payload
        },
        removeUserPermissions: (state,action) => {
            state.currentUserPermissions = null
        }
        // addCollege: (state, action) => {
        //     state.colleges.push(action.payload)
        // },
        // updateColleges: (state, action) => {

        // },
        // deleteCollege: (state, action) => {
        //     state.colleges = state.colleges.filter(college => college.id !== action.payload.id)
        // },
    },
})
export const { addSelectedCollege, removeSelectedCollege, removeUserPermissions, addUserPermissions } = selectedCollegeSlice.actions
export default selectedCollegeSlice.reducer