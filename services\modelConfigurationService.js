/**
 * Model Configuration Service
 * Manages model configurations and provides fallback configurations
 */

class ModelConfigurationService {
  constructor() {
    // Default configuration for fallback/base models
    this.defaultConfiguration = {
      // Response Generation Settings
      maxTokens: 500,
      temperature: 0.7,
      topP: 1,
      frequencyPenalty: 0,
      presencePenalty: 0,
      
      // System Behavior Settings
      systemPrompt: "You are a helpful assistant for college information and guidance. Provide accurate, helpful information about courses, programs, admissions, and college services.",
      maxRetries: 3,
      timeoutMs: 30000,
      
      // Content Moderation Settings
      moderationEnabled: true,
      moderationStrict: false,
      
      // Response Formatting
      includeContext: true,
      maxContextMessages: 10,
      
      // Custom Settings
      customInstructions: "",
      responseStyle: "professional"
    };

    // Configuration presets for different use cases
    this.configurationPresets = {
      professional: {
        temperature: 0.7,
        responseStyle: "professional",
        systemPrompt: "You are a professional college advisor. Provide accurate, formal information about academic programs and services.",
        customInstructions: "Always maintain a professional tone and provide detailed, accurate information."
      },
      
      friendly: {
        temperature: 0.8,
        responseStyle: "friendly",
        systemPrompt: "You are a friendly college assistant. Help students with their questions in a warm, approachable manner.",
        customInstructions: "Use a warm, friendly tone while being helpful and informative."
      },
      
      concise: {
        temperature: 0.5,
        maxTokens: 300,
        responseStyle: "concise",
        systemPrompt: "You are a concise college information assistant. Provide brief, direct answers to student questions.",
        customInstructions: "Keep responses brief and to the point while being helpful."
      },
      
      detailed: {
        temperature: 0.6,
        maxTokens: 800,
        responseStyle: "detailed",
        systemPrompt: "You are a comprehensive college advisor. Provide thorough, detailed information about all aspects of college life and academics.",
        customInstructions: "Provide comprehensive, detailed responses with examples and additional context when helpful."
      },
      
      highCreativity: {
        temperature: 1.0,
        topP: 0.9,
        responseStyle: "custom",
        systemPrompt: "You are a creative college assistant. Help students explore their options with innovative suggestions and creative thinking.",
        customInstructions: "Be creative and suggest innovative approaches while maintaining accuracy."
      },
      
      conservative: {
        temperature: 0.3,
        topP: 0.8,
        moderationStrict: true,
        responseStyle: "professional",
        systemPrompt: "You are a conservative college advisor. Provide safe, well-established information and guidance.",
        customInstructions: "Stick to well-established facts and avoid speculative or creative suggestions."
      }
    };
  }

  /**
   * Get default configuration
   * @returns {Object} Default configuration object
   */
  getDefaultConfiguration() {
    return { ...this.defaultConfiguration };
  }

  /**
   * Get configuration preset
   * @param {string} presetName - Name of the preset
   * @returns {Object} Configuration preset merged with defaults
   */
  getConfigurationPreset(presetName) {
    const preset = this.configurationPresets[presetName];
    if (!preset) {
      throw new Error(`Configuration preset '${presetName}' not found`);
    }
    
    return {
      ...this.defaultConfiguration,
      ...preset
    };
  }

  /**
   * Get all available presets
   * @returns {Array} List of available preset names
   */
  getAvailablePresets() {
    return Object.keys(this.configurationPresets);
  }

  /**
   * Validate configuration object
   * @param {Object} config - Configuration to validate
   * @returns {Object} Validation result with errors if any
   */
  validateConfiguration(config) {
    const errors = [];
    const warnings = [];

    // Validate numeric ranges
    if (config.maxTokens !== undefined) {
      if (typeof config.maxTokens !== 'number' || config.maxTokens < 1 || config.maxTokens > 4000) {
        errors.push('maxTokens must be a number between 1 and 4000');
      }
    }

    if (config.temperature !== undefined) {
      if (typeof config.temperature !== 'number' || config.temperature < 0 || config.temperature > 2) {
        errors.push('temperature must be a number between 0 and 2');
      }
      if (config.temperature > 1.5) {
        warnings.push('High temperature (>1.5) may produce unpredictable responses');
      }
    }

    if (config.topP !== undefined) {
      if (typeof config.topP !== 'number' || config.topP < 0 || config.topP > 1) {
        errors.push('topP must be a number between 0 and 1');
      }
    }

    if (config.frequencyPenalty !== undefined) {
      if (typeof config.frequencyPenalty !== 'number' || config.frequencyPenalty < -2 || config.frequencyPenalty > 2) {
        errors.push('frequencyPenalty must be a number between -2 and 2');
      }
    }

    if (config.presencePenalty !== undefined) {
      if (typeof config.presencePenalty !== 'number' || config.presencePenalty < -2 || config.presencePenalty > 2) {
        errors.push('presencePenalty must be a number between -2 and 2');
      }
    }

    if (config.maxRetries !== undefined) {
      if (typeof config.maxRetries !== 'number' || config.maxRetries < 1 || config.maxRetries > 10) {
        errors.push('maxRetries must be a number between 1 and 10');
      }
    }

    if (config.timeoutMs !== undefined) {
      if (typeof config.timeoutMs !== 'number' || config.timeoutMs < 5000 || config.timeoutMs > 120000) {
        errors.push('timeoutMs must be a number between 5000 and 120000');
      }
    }

    if (config.maxContextMessages !== undefined) {
      if (typeof config.maxContextMessages !== 'number' || config.maxContextMessages < 1 || config.maxContextMessages > 50) {
        errors.push('maxContextMessages must be a number between 1 and 50');
      }
    }

    // Validate string lengths
    if (config.systemPrompt !== undefined) {
      if (typeof config.systemPrompt !== 'string' || config.systemPrompt.length > 2000) {
        errors.push('systemPrompt must be a string with maximum 2000 characters');
      }
    }

    if (config.customInstructions !== undefined) {
      if (typeof config.customInstructions !== 'string' || config.customInstructions.length > 1000) {
        errors.push('customInstructions must be a string with maximum 1000 characters');
      }
    }

    // Validate enums
    if (config.responseStyle !== undefined) {
      const validStyles = ['professional', 'friendly', 'concise', 'detailed', 'custom'];
      if (!validStyles.includes(config.responseStyle)) {
        errors.push(`responseStyle must be one of: ${validStyles.join(', ')}`);
      }
    }

    // Validate booleans
    const booleanFields = ['moderationEnabled', 'moderationStrict', 'includeContext'];
    booleanFields.forEach(field => {
      if (config[field] !== undefined && typeof config[field] !== 'boolean') {
        errors.push(`${field} must be a boolean value`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Merge configuration with defaults
   * @param {Object} config - Configuration to merge
   * @returns {Object} Merged configuration
   */
  mergeWithDefaults(config = {}) {
    return {
      ...this.defaultConfiguration,
      ...config
    };
  }

  /**
   * Apply configuration preset to existing configuration
   * @param {Object} baseConfig - Base configuration
   * @param {string} presetName - Preset to apply
   * @returns {Object} Configuration with preset applied
   */
  applyPreset(baseConfig, presetName) {
    const preset = this.getConfigurationPreset(presetName);
    return {
      ...baseConfig,
      ...preset
    };
  }

  /**
   * Get configuration for OpenAI API call
   * @param {Object} config - Model configuration
   * @returns {Object} OpenAI API compatible configuration
   */
  getOpenAIConfiguration(config) {
    const effectiveConfig = this.mergeWithDefaults(config);
    
    return {
      max_tokens: effectiveConfig.maxTokens,
      temperature: effectiveConfig.temperature,
      top_p: effectiveConfig.topP,
      frequency_penalty: effectiveConfig.frequencyPenalty,
      presence_penalty: effectiveConfig.presencePenalty
    };
  }

  /**
   * Get system message for conversation
   * @param {Object} config - Model configuration
   * @param {Object} context - Additional context (college info, etc.)
   * @returns {string} System message
   */
  getSystemMessage(config, context = {}) {
    const effectiveConfig = this.mergeWithDefaults(config);
    let systemMessage = effectiveConfig.systemPrompt;

    // Add college-specific context if available
    if (context.college) {
      systemMessage += ` You are specifically helping students with information about ${context.college.name}.`;
    }

    // Add custom instructions if provided
    if (effectiveConfig.customInstructions) {
      systemMessage += ` Additional instructions: ${effectiveConfig.customInstructions}`;
    }

    return systemMessage;
  }
}

module.exports = ModelConfigurationService;
