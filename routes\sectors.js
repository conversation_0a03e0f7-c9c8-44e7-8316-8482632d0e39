const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const sectorsController = require('../controllers/sectors.controller')
const SuperUserGuard = require('../guards/super-user.guard')


router.post("/add", SuperUserGuard, sectorsController.add);

router.get("/get", sectorsController.get);

router.get("/getById", sectorsController.getByID);

router.get("/getSubsectors", sectorsController.getSubsectors);

router.delete("/remove", SuperUserGuard, sectorsController.remove);

router.put("/update", SuperUserGuard, sectorsController.update);

module.exports = router;