const { default: mongoose } = require("mongoose");
const settingSchema = require("../models/settingSchema");
const { messageResponse } = require("../helpers/commonHelper");
const commonHelper = require("../helpers/commonHelper");
const { getAddedBy, getEditedBy } = require("../tools/database");

const {
  NOT_FOUND,
  SERVER_ERROR,
  INVALID_MISSING,
  ADD_ERROR,UPDATE_SUCCESS
} = require("../config/messages");

const getSetting = async (req, res) => {
  try {
    const data = await settingSchema.findOne({});

    if (!data) {
      return messageResponse(NOT_FOUND, "Setting_data", false, 404, null, res);
    }

    return messageResponse(null,"", true, 200, data, res);
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};

const updateSetting = async (req, res) => {
  try {
    if (!req.body.skillMatchingPercentage) {
      return messageResponse(
        INVALID_MISSING,
        "skillMatchingPercentage",
        false,
        400,
        null
      );
    }
    if (!req.body.noOfSkillsToMatch) {
      return messageResponse(
        INVALID_MISSING,
        "noOfSkillsToMatch",
        false,
        400,
        null
      );
    }

    const data = await settingSchema.findOne({});

    if (!data) {
      const addedBy = getAddedBy(req, "add");
      req.body.addedBy = addedBy;
      const createdSetting = await settingSchema.create(req.body);
      if (!createdSetting)
        return messageResponse(ADD_ERROR, "Setting", false, 400, null, res);
     else return res
        .status(201)
        .json({ success: true, createdSetting: createdSetting });
    } else {
      req.body.editedBy = getEditedBy(req, "edit");
      const updateSetting = await settingSchema.findByIdAndUpdate(
        data._id,
        {
          $set: req.body,
        },
        { new: true }
      );

      if (!updateSetting)
        return messageResponse(UPDATE_ERROR, "Setting", false, 404, null, res);
      else
        return messageResponse(UPDATE_SUCCESS, "Setting", true, 200, updateSetting, res);
    }
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};

module.exports.getSetting = getSetting;
module.exports.updateSetting = updateSetting;
