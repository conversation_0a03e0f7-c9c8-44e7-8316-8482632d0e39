import { Box, Button, IconButton, Typography } from '@mui/material'
import React, { useState } from 'react'
import { useNavigate } from 'react-router'
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

const PrivacyPolicy = () => {
    const [policy, setPolicy] = useState("")
    const navigate = useNavigate()
    return (
        <>
            <Box
                sx={{
                    p: 3,
                    width:'80%',
                    display:'flex',
                    flexDirection:"column",
                    justifyContent:"center",
                    textAlign:"center",
                    marginInline: 'auto',
                }}
            >
                <Box  sx={{textAlign:'left'}}>
                    <IconButton  onClick={()=>navigate(-1)}>
                            <ArrowBackIcon/>
                    </IconButton>
                </Box>
                    <Typography variant='h2' sx={{textDecoration:'underline'}} >
                        Privacy Policy
                    </Typography>
                <Typography sx={{textAlign:'initial',mt:5}} >
                    Last updated: June 30, 2023

                    This Privacy Policy describes Our policies and procedures on the collection, use and disclosure of Your information when You use the Service and tells You about Your privacy rights and how the law protects You.

                    We use Your Personal data to provide and improve the Service. By using the Service, You agree to the collection and use of information in accordance with this Privacy Policy.
                </Typography>

                <Typography variant='h3'>
                Interpretation and Definitions
                </Typography>
                <Typography sx={{textAlign:'initial',mt:5}}>
                The words of which the initial letter is capitalized have meanings defined under the following conditions. The following definitions shall have the same meaning regardless of whether they appear in singular or in plural.
                </Typography>
                <Box sx={{width:'70%',alignContent:'center'}}>

                <ul style={{textAlign:"initial"}}>
                    <li style={{marginTop:'10px'}}>
                    Account means a unique account created for You to access our Service or parts of our Service.
                    </li>
                    <li  style={{marginTop:'10px'}}>
                    Cookies are small files that are placed on Your computer, mobile device or any other device by a website, containing the details of Your browsing history on that website among its many uses.
                    </li>
                    <li  style={{marginTop:'10px'}}>
                    Cookies are small files that are placed on Your computer, mobile device or any other device by a website, containing the details of Your browsing history on that website among its many uses.
                    </li>
                    <li  style={{marginTop:'10px'}}>
                    Device means any device that can access the Service such as a computer, a cellphone or a digital tablet.
                    </li>
                    <li  style={{marginTop:'10px'}}>
                    Personal Data is any information that relates to an identified or identifiable individual.
                    </li>
                    <li  style={{marginTop:'10px'}}>
                    Service Provider means any natural or legal person who processes the data on behalf of the Company. It refers to third-party companies or individuals employed by the Company to facilitate the Service, to provide the Service on behalf of the Company, to perform services related to the Service or to assist the Company in analyzing how the Service is used.
                    </li >
                    <li  style={{marginTop:'10px'}}>
                    You means the individual accessing or using the Service, or the company, or other legal entity on behalf of which such individual is accessing or using the Service, as applicable.
                    </li>
                </ul>
                </Box>
            </Box>
        </>
    )
}

export default PrivacyPolicy
