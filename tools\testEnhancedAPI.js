const mongoose = require('mongoose');
const axios = require('axios');
require('dotenv').config();

// Import models for testing
const { ChatSession } = require('../models/chatbotModels');
const { College } = require('../models/college');

/**
 * Test script for Enhanced Chatbot API
 * Tests the complete flow from session creation to AI message processing
 */
const testEnhancedAPI = async () => {
  console.log('🧪 Testing Enhanced Chatbot API');
  console.log('===============================');

  let testSession = null;
  let testCollege = null;

  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
    console.log('✅ Connected to MongoDB');

    // Get a test college
    testCollege = await College.findOne({}).lean();
    if (!testCollege) {
      throw new Error('No colleges found in database for testing');
    }
    console.log(`📚 Using test college: ${testCollege.name}`);

    // Test 1: Session Initialization (existing functionality)
    console.log('\n🔧 Testing Session Initialization...');
    testSession = await testSessionInit(testCollege._id);

    // Test 2: Message Processing (new functionality)
    console.log('\n💬 Testing Message Processing...');
    await testMessageProcessing(testSession.sessionId);

    // Test 3: Admin Status (new functionality)
    console.log('\n👨‍💼 Testing Admin Status...');
    await testAdminStatus();

    // Test 4: Model Testing (new functionality)
    console.log('\n🧪 Testing Model Testing...');
    await testModelTesting();

    // Test 5: Session History (existing functionality)
    console.log('\n📜 Testing Session History...');
    await testSessionHistory(testSession.sessionId);

    // Test 6: Content Moderation
    console.log('\n🛡️ Testing Content Moderation...');
    await testContentModeration(testSession.sessionId);

    console.log('\n🎉 All Enhanced API tests passed successfully!');
    console.log('✅ The enhanced chatbot system is working correctly');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup test session
    if (testSession) {
      try {
        await ChatSession.findByIdAndDelete(testSession.sessionId);
        console.log('✅ Cleaned up test session');
      } catch (cleanupError) {
        console.warn('⚠️ Failed to cleanup test session:', cleanupError.message);
      }
    }

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
};

/**
 * Test session initialization
 */
const testSessionInit = async (collegeId) => {
  try {
    const response = await makeAPICall('POST', '/api/chatbot/session/init', {
      collegeId: collegeId.toString(),
      userAgent: 'Test Agent'
    });

    if (response.success && response.data.sessionId) {
      console.log('✅ Session initialization: PASSED');
      console.log(`   Session ID: ${response.data.sessionId}`);
      console.log(`   College: ${response.data.collegeInfo.name}`);
      return response.data;
    } else {
      throw new Error('Session initialization failed');
    }
  } catch (error) {
    console.error('❌ Session initialization: FAILED');
    throw error;
  }
};

/**
 * Test message processing
 */
const testMessageProcessing = async (sessionId) => {
  try {
    const testMessages = [
      'What computer science courses do you offer?',
      'Tell me about admission requirements.',
      'What are the campus locations?'
    ];

    for (const message of testMessages) {
      console.log(`   Testing message: "${message}"`);
      
      const response = await makeAPICall('POST', '/api/chatbot/message', {
        sessionId: sessionId,
        message: message,
        options: {
          maxTokens: 200,
          temperature: 0.7
        }
      });

      if (response.success && response.data.response) {
        console.log(`   ✅ Response received (${response.data.responseType})`);
        console.log(`   📝 Preview: ${response.data.response.substring(0, 100)}...`);
        console.log(`   🤖 Model: ${response.data.modelUsed}`);
      } else {
        throw new Error(`Message processing failed for: ${message}`);
      }

      // Wait between messages to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('✅ Message processing: PASSED');
  } catch (error) {
    console.error('❌ Message processing: FAILED');
    throw error;
  }
};

/**
 * Test admin status endpoint
 */
const testAdminStatus = async () => {
  try {
    const response = await makeAPICall('GET', '/api/chatbot/admin/status');

    if (response.success && response.data.system) {
      console.log('✅ Admin status: PASSED');
      console.log(`   System status: ${response.data.system.status}`);
      console.log(`   Fine-tuned model available: ${response.data.system.fineTunedModelAvailable}`);
      console.log(`   Total sessions: ${response.data.statistics.sessions.total}`);
      console.log(`   Total messages: ${response.data.statistics.messages.total}`);
    } else {
      throw new Error('Admin status failed');
    }
  } catch (error) {
    console.error('❌ Admin status: FAILED');
    throw error;
  }
};

/**
 * Test model testing endpoint
 */
const testModelTesting = async () => {
  try {
    const response = await makeAPICall('POST', '/api/chatbot/admin/test-model', {
      testMessage: 'Hello, this is a test message.'
    });

    if (response.success) {
      console.log('✅ Model testing: PASSED');
      console.log(`   Test passed: ${response.data.testPassed}`);
      console.log(`   Model used: ${response.data.modelUsed}`);
      console.log(`   Response type: ${response.data.responseType}`);
    } else {
      throw new Error('Model testing failed');
    }
  } catch (error) {
    console.error('❌ Model testing: FAILED');
    throw error;
  }
};

/**
 * Test session history
 */
const testSessionHistory = async (sessionId) => {
  try {
    const response = await makeAPICall('GET', `/api/chatbot/session/${sessionId}/history`);

    if (response.success && response.data.messages) {
      console.log('✅ Session history: PASSED');
      console.log(`   Messages in history: ${response.data.messages.length}`);
      console.log(`   Total messages: ${response.data.pagination.total}`);
    } else {
      throw new Error('Session history failed');
    }
  } catch (error) {
    console.error('❌ Session history: FAILED');
    throw error;
  }
};

/**
 * Test content moderation
 */
const testContentModeration = async (sessionId) => {
  try {
    // Test with inappropriate content
    const response = await makeAPICall('POST', '/api/chatbot/message', {
      sessionId: sessionId,
      message: 'This is spam spam spam spam spam!!!'
    });

    if (response.success) {
      if (response.data.responseType === 'violation') {
        console.log('✅ Content moderation: PASSED');
        console.log('   Inappropriate content was properly blocked');
      } else {
        console.log('⚠️ Content moderation: PARTIAL');
        console.log('   Message was processed but may not have been flagged');
      }
    } else {
      throw new Error('Content moderation test failed');
    }
  } catch (error) {
    console.error('❌ Content moderation: FAILED');
    throw error;
  }
};

/**
 * Make API call helper
 */
const makeAPICall = async (method, endpoint, data = null) => {
  const baseURL = process.env.API_BASE_URL || 'http://localhost:3000';
  const url = `${baseURL}${endpoint}`;

  try {
    let response;
    
    if (method === 'GET') {
      response = await axios.get(url);
    } else if (method === 'POST') {
      response = await axios.post(url, data);
    } else {
      throw new Error(`Unsupported method: ${method}`);
    }

    return response.data;
  } catch (error) {
    if (error.response) {
      // API returned an error response
      console.error(`API Error (${error.response.status}):`, error.response.data);
      return error.response.data;
    } else {
      // Network or other error
      console.error('Network Error:', error.message);
      throw error;
    }
  }
};

// Run test if called directly
if (require.main === module) {
  testEnhancedAPI();
}

module.exports = testEnhancedAPI;
