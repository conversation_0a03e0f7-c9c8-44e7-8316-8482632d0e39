import { <PERSON>rowserRouter } from 'react-router-dom';
import { He<PERSON>etProvider } from 'react-helmet-async';
import { useEffect } from 'react';
// routes
import Router from './routes';
// theme
import ThemeProvider from './theme';
// components
import { StyledChart } from './components/Chart';
import ScrollToTop from './components/Scroll-to-top';
import CustomizedSnackbars from './components/Snackbar';

// ----------------------------------------------------------------------

export default function App() {

  return (
    <HelmetProvider>
      <BrowserRouter>
        <CustomizedSnackbars />
        <ThemeProvider>
          <ScrollToTop />
          <StyledChart />
          <Router />
        </ThemeProvider>
      </BrowserRouter>
    </HelmetProvider>
  );
}
