const OpenAI = require('openai');

// Import services
const OpenAIFineTuningService = require('./openAIFineTuningService');

/**
 * ChatGPT Integration Service
 * Handles fine-tuned model calls, conversation management, and response formatting
 * Provides seamless integration with existing chatbot infrastructure
 */
class ChatGPTService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.fineTuningService = new OpenAIFineTuningService();
    
    // Model configuration
    this.defaultModel = 'gpt-4o-mini';
    this.fineTunedModelAlias = 'default';
    this.maxTokens = 500;
    this.temperature = 0.7;
    this.maxRetries = 3;
    
    // Response configuration
    this.maxResponseLength = 1000;
    this.responseTimeout = 30000; // 30 seconds
  }

  /**
   * Generate response using fine-tuned model
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - Generated response
   */
  async generateResponse(messages, options = {}) {
    const {
      maxTokens = this.maxTokens,
      temperature = this.temperature,
      collegeContext = null,
      useFineTuned = true
    } = options;

    console.log('🤖 Generating ChatGPT response...');
    console.log(`📊 Messages in context: ${messages.length}`);

    try {
      // Determine which model to use
      const modelInfo = this.selectModel(useFineTuned);
      console.log(`🎯 Using model: ${modelInfo.modelId} (${modelInfo.type})`);

      // Prepare messages with proper formatting
      const formattedMessages = this.formatMessages(messages, collegeContext);

      // Generate response with retry logic
      const response = await this.generateWithRetry(formattedMessages, {
        model: modelInfo.modelId,
        maxTokens,
        temperature
      });

      // Process and validate response
      const processedResponse = this.processResponse(response, modelInfo);

      console.log('✅ Response generated successfully');
      console.log(`📊 Tokens used: ${processedResponse.usage?.total_tokens || 'N/A'}`);
      console.log(`📝 Response length: ${processedResponse.content.length} characters`);

      return processedResponse;

    } catch (error) {
      console.error('❌ Response generation failed:', error);
      return this.generateFallbackResponse(error, collegeContext);
    }
  }

  /**
   * Select appropriate model for response generation
   * @param {boolean} useFineTuned - Prefer fine-tuned model
   * @returns {Object} - Model information
   */
  selectModel(useFineTuned = true) {
    if (useFineTuned) {
      const deployedModel = this.fineTuningService.getDeployedModel(this.fineTunedModelAlias);
      if (deployedModel && deployedModel.status === 'active') {
        return {
          modelId: deployedModel.modelId,
          type: 'fine-tuned',
          deployedAt: deployedModel.deployedAt
        };
      }
    }

    return {
      modelId: this.defaultModel,
      type: 'fallback',
      deployedAt: null
    };
  }

  /**
   * Format messages for API call
   * @param {Array} messages - Raw messages
   * @param {Object} collegeContext - College information
   * @returns {Array} - Formatted messages
   */
  formatMessages(messages, collegeContext = null) {
    const formattedMessages = [...messages];

    // Enhance system message with college context if available
    if (collegeContext && formattedMessages.length > 0 && formattedMessages[0].role === 'system') {
      const enhancedSystemMessage = this.enhanceSystemMessage(
        formattedMessages[0].content,
        collegeContext
      );
      formattedMessages[0] = {
        role: 'system',
        content: enhancedSystemMessage
      };
    }

    // Ensure messages don't exceed token limits
    return this.truncateMessages(formattedMessages);
  }

  /**
   * Enhance system message with college context
   * @param {string} baseMessage - Base system message
   * @param {Object} collegeContext - College information
   * @returns {string} - Enhanced system message
   */
  enhanceSystemMessage(baseMessage, collegeContext) {
    const contextAddition = `

College Information:
- Name: ${collegeContext.name}
- Location: ${collegeContext.city || 'Multiple locations'}${collegeContext.state ? `, ${collegeContext.state}` : ''}
- Website: ${collegeContext.website || 'Contact college directly'}
- Contact: ${collegeContext.contactNumber || collegeContext.email || 'Available through college website'}

Always prioritize ${collegeContext.name}-specific information when available.
If you don't have specific information about ${collegeContext.name}, acknowledge this and suggest contacting them directly.
`;

    return baseMessage + contextAddition;
  }

  /**
   * Truncate messages to fit token limits
   * @param {Array} messages - Messages to truncate
   * @returns {Array} - Truncated messages
   */
  truncateMessages(messages) {
    // Simple truncation - keep system message and recent messages
    // More sophisticated token counting could be implemented here
    const maxMessages = 15;
    
    if (messages.length <= maxMessages) {
      return messages;
    }

    // Keep system message (first) and recent messages
    const systemMessage = messages[0];
    const recentMessages = messages.slice(-maxMessages + 1);
    
    return [systemMessage, ...recentMessages];
  }

  /**
   * Generate response with retry logic
   * @param {Array} messages - Formatted messages
   * @param {Object} options - API options
   * @returns {Promise<Object>} - API response
   */
  async generateWithRetry(messages, options) {
    let lastError = null;
    
    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`🔄 API attempt ${attempt}/${this.maxRetries}`);
        
        const response = await Promise.race([
          this.openai.chat.completions.create({
            model: options.model,
            messages: messages,
            max_tokens: options.maxTokens,
            temperature: options.temperature,
            presence_penalty: 0.1,
            frequency_penalty: 0.1,
            stream: false
          }),
          new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Request timeout')), this.responseTimeout)
          )
        ]);

        return response;

      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Attempt ${attempt} failed:`, error.message);
        
        // If fine-tuned model fails on first attempt, try fallback
        if (attempt === 1 && options.model !== this.defaultModel) {
          console.log('🔄 Switching to fallback model');
          options.model = this.defaultModel;
        }
        
        // Wait before retry (exponential backoff)
        if (attempt < this.maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  /**
   * Process and validate API response
   * @param {Object} response - Raw API response
   * @param {Object} modelInfo - Model information
   * @returns {Object} - Processed response
   */
  processResponse(response, modelInfo) {
    if (!response.choices || response.choices.length === 0) {
      throw new Error('No response choices returned');
    }

    const choice = response.choices[0];
    let content = choice.message?.content || '';

    // Validate and clean content
    content = this.validateAndCleanContent(content);

    return {
      content: content,
      type: modelInfo.type,
      modelUsed: modelInfo.modelId,
      usage: response.usage,
      finishReason: choice.finish_reason,
      timestamp: new Date()
    };
  }

  /**
   * Validate and clean response content
   * @param {string} content - Raw content
   * @returns {string} - Cleaned content
   */
  validateAndCleanContent(content) {
    if (!content || typeof content !== 'string') {
      throw new Error('Invalid response content');
    }

    // Trim whitespace
    content = content.trim();

    // Check minimum length
    if (content.length < 10) {
      throw new Error('Response too short');
    }

    // Check maximum length
    if (content.length > this.maxResponseLength) {
      content = content.substring(0, this.maxResponseLength - 3) + '...';
    }

    // Remove any potential harmful content (basic sanitization)
    content = content.replace(/\[SYSTEM\]/gi, '[INFO]');
    content = content.replace(/\[ADMIN\]/gi, '[INFO]');

    return content;
  }

  /**
   * Generate fallback response when main generation fails
   * @param {Error} error - Original error
   * @param {Object} collegeContext - College information
   * @returns {Object} - Fallback response
   */
  generateFallbackResponse(error, collegeContext = null) {
    const collegeName = collegeContext?.name || 'the college';
    const contactInfo = collegeContext?.website || collegeContext?.contactNumber || 'their website';

    let fallbackMessage = '';

    if (error.message.includes('timeout')) {
      fallbackMessage = `I apologize for the delay in responding. Please try asking your question again, or contact ${collegeName} directly at ${contactInfo} for immediate assistance.`;
    } else if (error.message.includes('rate limit')) {
      fallbackMessage = `I'm currently experiencing high demand. Please wait a moment and try again, or contact ${collegeName} directly for immediate assistance.`;
    } else {
      fallbackMessage = `I apologize, but I'm having trouble processing your request right now. Please contact ${collegeName} directly at ${contactInfo} for assistance, or try rephrasing your question.`;
    }

    return {
      content: fallbackMessage,
      type: 'error',
      modelUsed: 'fallback',
      usage: null,
      finishReason: 'error',
      timestamp: new Date(),
      error: error.message
    };
  }

  /**
   * Test model connectivity and response quality
   * @param {string} modelId - Model to test (optional)
   * @returns {Promise<Object>} - Test results
   */
  async testModel(modelId = null) {
    console.log('🧪 Testing model connectivity...');

    try {
      const testModel = modelId || this.selectModel(true).modelId;
      const testMessages = [
        {
          role: 'system',
          content: 'You are a helpful educational assistant.'
        },
        {
          role: 'user',
          content: 'Hello, can you help me with information about courses?'
        }
      ];

      const response = await this.generateResponse(testMessages, {
        maxTokens: 100,
        temperature: 0.5,
        useFineTuned: modelId ? false : true
      });

      return {
        success: true,
        modelUsed: response.modelUsed,
        responseType: response.type,
        responseLength: response.content.length,
        usage: response.usage,
        timestamp: response.timestamp
      };

    } catch (error) {
      console.error('❌ Model test failed:', error);
      return {
        success: false,
        error: error.message,
        timestamp: new Date()
      };
    }
  }

  /**
   * Get service status and statistics
   * @returns {Object} - Service status
   */
  getServiceStatus() {
    const deployedModel = this.fineTuningService.getDeployedModel(this.fineTunedModelAlias);
    
    return {
      fineTunedModelAvailable: !!deployedModel,
      fineTunedModelId: deployedModel?.modelId || null,
      fallbackModel: this.defaultModel,
      configuration: {
        maxTokens: this.maxTokens,
        temperature: this.temperature,
        maxRetries: this.maxRetries,
        responseTimeout: this.responseTimeout
      },
      timestamp: new Date()
    };
  }

  /**
   * Update service configuration
   * @param {Object} config - New configuration
   * @returns {boolean} - Update success
   */
  updateConfiguration(config) {
    try {
      if (config.maxTokens && config.maxTokens > 0 && config.maxTokens <= 2000) {
        this.maxTokens = config.maxTokens;
      }
      
      if (config.temperature && config.temperature >= 0 && config.temperature <= 2) {
        this.temperature = config.temperature;
      }
      
      if (config.maxRetries && config.maxRetries > 0 && config.maxRetries <= 5) {
        this.maxRetries = config.maxRetries;
      }

      console.log('✅ Configuration updated successfully');
      return true;

    } catch (error) {
      console.error('❌ Configuration update failed:', error);
      return false;
    }
  }
}

module.exports = ChatGPTService;
