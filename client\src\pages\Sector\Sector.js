import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
    Backdrop,
    Box,
    Button,
    Card,
    CircularProgress,
    Container,
    FormControl,
    FormControlLabel,
    Grid,
    InputAdornment,
    LinearProgress,
    Stack,
    Switch,
    Typography
} from '@mui/material';
// @mui
import { LoadingButton } from '@mui/lab';
import { useFormik } from 'formik';
// import DataTable from 'react-data-table-component';
import { get } from 'lodash';
import DataTable from 'react-data-table-component';
import { setSnackbar } from '../../Redux/snackbarSlice';
import SelectComponent from '../../components/SelectComponent';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import useLoading from '../../hooks/useLoading';
import { CancelButton } from '../../utils/cssStyles';
import { AddEditSubSectorsValidationSchema, addSectorsValidationSchema } from '../../utils/validationSchemas';
// import DataTable from '../../components/DataTable/DataTable';
import ConfirmDialog from '../../components/ConfirmDialog';
import Iconify from '../../components/Iconify/Iconify';
import { StyledSearch } from '../../sections/@dashboard/user/UserListToolbar';
import { getSubSectors, postSubSector, removeSubSector, updateSubSector } from '../SubSector/SubSectorSlice';
import { getSectors, postSector, removeSector, updateSector } from './SectorSlice';

// ----------------------------------------------------------------------
export const SECTORS_TABLE_HEAD = [
    { id: 'name', label: 'Sector', alignRight: false },
    { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderRadarCategoryCells = ['name']

export const subSectorStyles = {
    rows: {
        style: {
            minHeight: '52px', // override the row height
            backgroundColor: "#f4f6f887",
            paddingLeft: 70,
            fontSize: '0.87rem',
            borderBottomWidth: '0px !important',
        }
    },
    headCells: {
        style: {
            paddingLeft: '50px', // override the cell padding for head cells
            paddingRight: '8px',
            color: "black",
            fontWeight: 'bold',
            backgroundColor: '#f4f6f887',
        },
    },
    headRow: {
        style: {
            borderBottom: '1px solid #00000012 !important',
        },
    },
    cells: {
        style: {
            paddingLeft: '8px', // override the cell padding for data cells
            paddingRight: '8px',
        },
    },
};

export const sectorStyles = {

    rows: {
        style: {
            minHeight: '52px', // override the row height
            // backgroundColor: "#f4f6f8",
            // paddingLeft: 50,
            fontSize: '0.87rem'

        }
    },
    headRow: {
        style: {
            backgroundColor: '#f4f6f8',
            minHeight: '52px',
            borderBottomWidth: '1px',
            borderBottomStyle: 'solid',
            fontSize: '0.89rem'
        },
    },
    headCells: {
        style: {
            // paddingLeft: '20px', // override the cell padding for head cells
            paddingRight: '8px',
            color: "black",
            fontWeight: 'bold',
        },
    },
    cells: {
        style: {
            paddingLeft: '8px', // override the cell padding for data cells
            paddingRight: '8px',
        },
    },
};

const Sector = () => {
    const dispatch = useDispatch()
    const sectorsState = useSelector(state => state.sectors)
    const subSectorsState = useSelector(state => state.subSectors)
    const [Sectors, setSectors] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [SubSectors, setSubSectors] = useState([]);
    const [loading, setLoading] = useState(false)
    const [SubSectorLoading, setSubSectorLoading] = useState(false)
    const [editSectorDetails, setEditSectorDetails] = useState('');
    const [editSubSectorDetails, setEditSubSectorDetails] = useState('');
    const [openBackdrop, setOpenBackdrop] = useState(false);
    const [open, setOpen] = useState(false);
    const [selectedField, setSelectedField] = useState()
    const [openSubsector, setOpenSubSector] = useState(false)
    const { sectors, status, error } = sectorsState;
    const { subSectors, status: subStatus } = subSectorsState
    const sectorsLoading = useLoading(status)

    const onFilterName = (event) => {
        setSearchTerm(event.target.value)
    }
    const filteredSectors = Sectors.filter(
        sector => sector.name && sector.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            sector.subsectors && sector.subsectors.some(subsector => subsector.name.toLowerCase().includes(searchTerm.toLowerCase())),
    );
    const handleCloseBackdrop = () => {
        setOpenBackdrop(false);
    };
    const handleOpenBackdrop = () => {
        setOpenBackdrop(true);
    };


    const columns = [
        {
            name: 'Sector',
            selector: row => row.name,
            sortable: true,
        },
        {
            name: '',
            button: true,
            cell: (row) =>
                <Button
                    onClick={() => editSector(row)}
                >
                    <Iconify icon={'eva:edit-fill'} />
                </Button>
        },
        {
            button: true,
            cell: (row) =>
                <>
                    <Button
                        color='error'
                        // onClick={() => handleDeleteSector(row)}
                        onClick={() => {
                            setSelectedField(row)
                            setOpen(true)
                        }}
                    >
                        <Iconify icon={'eva:trash-2-outline'} />
                    </Button>
                </>
        },
    ];
    const SubColumns = [
        {
            name: 'Sub-Sectors',
            selector: row => row.name,
            sortable: true,
        },
        {
            name: '',
            button: true,
            cell: (row) =>
                <Button
                    onClick={() => editSubSector(row)}
                >
                    <Iconify icon={'eva:edit-fill'} />
                </Button>
        },
        {
            button: true,
            cell: (row) =>
                <>
                    <Button
                        color='error'
                        // onClick={() => handleDeleteSubSector(row)}
                        onClick={() => {
                            setSelectedField(row)
                            setOpenSubSector(true)
                        }}
                    >
                        <Iconify icon={'eva:trash-2-outline'} />
                    </Button>
                </>
        },
    ];

    const data = Sectors

    const rowData = Sectors.map(sector => ({
        name: sector.name
    }))
    const addSectorformik = useFormik({
        initialValues: {
            name: '',
        },
        onSubmit: (values, { resetForm }) => {
            setLoading(true)
            let data = {
                name: values.name,
            }
            if (editSectorDetails) {
                data = {
                    ...data,
                    id: editSectorDetails._id ? editSectorDetails._id : editSectorDetails.id
                }
            }
            dispatch(editSectorDetails ? updateSector(data) : postSector(data)).then(res => {
                if (res?.payload?.success || res?.payload?.data?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: `Succesfully ${editSectorDetails ? 'Updated' : 'Added'} Sector`
                    }))
                }
                const error = res?.payload?.response?.data
                if (error?.success === false) {
                    console.log(error?.msg)
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error?.msg || "Something went wrong!"
                    }))
                }
            }).finally(() => {
                setLoading(false)
                setEditSectorDetails('');
            })
            resetForm({ values: '' })
        },
        validationSchema: addSectorsValidationSchema
    })

    const subSectorformik = useFormik({
        initialValues: {
            name: '',
            sector: '',
            priority: false
        },
        onSubmit: (values, { resetForm }) => {
            setSubSectorLoading(true)
            let sectorName = '';
            let data = {
                name: values.name,
                priority: values.priority,
                sectorId: values.sector,
                sectorName: Sectors.map((sector) => {
                    if (sector._id === values.sector) {
                        sectorName = sector.name
                        return sectorName
                    }
                    return ''
                }).toString()
            }
            data.sectorName = sectorName;
            if (editSubSectorDetails) {
                data = {
                    ...data,
                    id: editSubSectorDetails._id ? editSubSectorDetails._id : editSubSectorDetails.id
                }
            }
            dispatch(editSubSectorDetails ? updateSubSector(data) : postSubSector(data)).then(res => {
                if (res?.payload?.success || res?.payload?.data?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: `Succesfully ${editSubSectorDetails ? 'Updated' : 'Added'} Sub-Sector`
                    }))
                    if (editSubSectorDetails) {
                        const SubSectorsData = SubSectors.filter((subSector) => {
                            return subSector._id !== editSubSectorDetails._id
                        })
                        SubSectorsData.push(data);
                        setSubSectors(SubSectorsData);
                    }
                }
                const error = res?.payload?.response?.data
                if (error?.success === false) {
                    console.log(error?.msg)
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error?.msg || "Something went wrong!"
                    }))
                }
            }).finally(() => {
                setSubSectorLoading(false)
                setEditSectorDetails('');
            })
            resetForm({ values: '' })
        },
        validationSchema: AddEditSubSectorsValidationSchema
    })

    useEffect(() => {
        dispatch(getSectors());
        dispatch(getSubSectors());
    }, [])
    useEffect(() => {
        if (!!sectors) {
            setSectors([...sectors])
        }
    }, [sectors])
    useEffect(() => {
        if (subSectors && subSectors.length > 0) {
            setSubSectors([...subSectors])
        }
    }, [subSectors])

    const handleFilterSearch = (event) => {
        const filteredSector = Sectors.filter(sector => sector.name.toLowerCase().includes(event.target.value.toLowerCase()))
        return filteredSector
    };

    const handleDeleteSector = (sector) => {
        setSearchTerm('')
        handleOpenBackdrop()
        const data = {
            id: sector.id ? sector.id : sector._id
        }
        dispatch(removeSector(data)).then(res => {
            if (res?.payload?.success) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Sector Deleted Succesfully"
                }))
            } else {
                const errorMessage = get(res, 'payload.response.data.message', 'Something went wrong')
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: errorMessage
                }))
                console.log('Something went wrong!!', res)
            }
        }).finally(() => {
            handleCloseBackdrop();
        })
    }

    const handleDeleteSubSector = (subSector) => {
        setSearchTerm('')
        handleOpenBackdrop();
        const data = {
            id: get(subSector, '_id', ''),
            sectorId: get(subSector, 'sectorId', '')
        }
        dispatch(removeSubSector(data)).then(res => {
            if (res?.payload?.success) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Sub-Sector Deleted Succesfully"
                }))
            } else {
                const errorMessage = get(res, 'payload.response.data.message', 'Something Went Wrong')
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: errorMessage
                }))
                console.log('Something went wrong!!', res)
            }
        }).finally(() => {
            handleCloseBackdrop();
        })
    }

    const editSector = (sector) => {
        setEditSectorDetails(sector);
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }

    const handleCancel = () => {
        setEditSectorDetails('');
        addSectorformik.setValues({
            name: ''
        })
    }
    const noDataComponent = (data) =>
        <>
            <Typography
                my={4}
                variant='subtitle2'>
                No Sub-Sectors Found For <b>{data.name}</b>
            </Typography>
        </>

    const editSubSector = (subSector) => {
        setEditSubSectorDetails(subSector);
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }

    const handleCancelSub = () => {
        setEditSubSectorDetails('');
        subSectorformik.setValues({
            name: '',
            sector: '',
            priority: false
        })
    }

    useEffect(() => {
        addSectorformik.setValues({
            ...addSectorformik.values,
            name: editSectorDetails?.name
        })
    }, [editSectorDetails])

    useEffect(() => {
        subSectorformik.setValues({
            ...subSectorformik.values,
            name: editSubSectorDetails?.name,
            sector: editSubSectorDetails.sectorId,
            priority: editSubSectorDetails.priority,
        })
    }, [editSubSectorDetails])

    const ExpandedComponent = ({ data }) =>
        <DataTable
            columns={SubColumns}
            data={data.subsectors}
            customStyles={subSectorStyles}
            noDataComponent={noDataComponent(data)}

        // noTableHead
        />
    const subHeaderComponent = (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <StyledSearch
                sx={{ my: 1 }}
                value={searchTerm}
                onChange={onFilterName}
                placeholder={"Search..."}
                startAdornment={
                    <InputAdornment position="start">
                        <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled', width: 20, height: 20 }} />
                    </InputAdornment>
                }
            />
            <Box flexGrow={1} />
        </div>
    );

    const progressComponent =
        <Box sx={{ width: 'inherit' }}>
            <LinearProgress />
        </Box>

    const handleDeleteSectorField = (selectedField) => {
        handleDeleteSector(selectedField)
        setOpen(false)
    }
    const handleDeleteSubSectorField = (selectedField) => {
        handleDeleteSubSector(selectedField)
        setOpenSubSector(false)
    }
    return (
        <>
            <Helmet>
                <title> Sector | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Backdrop
                    sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
                    open={openBackdrop}
                >
                    <CircularProgress color="inherit" />
                </Backdrop>
                <ConfirmDialog
                    title={"Delete Sector?"}
                    open={open}
                    setOpen={setOpen}
                    // handleClose={closeDialog}
                    selectedField={selectedField}
                    onConfirm={handleDeleteSectorField}
                >
                    <Typography variant='body1'>
                        {"Are You sure you want to delete this Sector"}
                    </Typography>
                </ConfirmDialog>
                <ConfirmDialog
                    title={"Delete Subsector?"}
                    open={openSubsector}
                    setOpen={setOpenSubSector}
                    // handleClose={closeDialog}
                    selectedField={selectedField}
                    onConfirm={handleDeleteSubSectorField}
                >
                    <Typography variant='body1'>
                        {"Are You sure you want to delete this Sub-Sector"}
                    </Typography>
                </ConfirmDialog>
                <Typography variant="h4">
                    Sectors
                </Typography>
                <Grid container justifyContent={'space-between'} gap={2} flexWrap={'nowrap'}>
                    {/* <Grid item lg={7}> */}
                    {/* <Typography variant="h4" gutterBottom mb={3}>
                            Sectors
                        </Typography> */}
                    {/* <DataTable
                            deleteDescription={"Are you sure want to delete this sector ?"}
                            deleteTitle={"Delete Sector ?"}
                            loading={dataLoading}
                            TableHead={SECTORS_TABLE_HEAD}
                            TableData={Sectors}
                            filterSearch={handleFilterSearch}
                            searchLable={"Search..."}
                            handleEdit={editSector}
                            renderCells={renderRadarCategoryCells}
                            handleDelete={handleDeleteSector}
                            pagination
                        /> */}
                    {/* </Grid> */}



                    <Grid item xs={9}>
                        <Card
                            sx={{ p: 0, my: 3, width: '95%' }}
                        >
                            <DataTable
                                columns={columns}
                                data={filteredSectors}
                                customStyles={sectorStyles}
                                expandableRows
                                expandableRowsComponent={ExpandedComponent}
                                expandOnRowClicked="false"
                                highlightOnHover
                                subHeader
                                subHeaderComponent={subHeaderComponent}
                                subHeaderAlign="left"
                                progressPending={sectorsLoading}
                                progressComponent={progressComponent}
                                // noHeader
                                // expandOnRowDoubleClicked="false"
                                // expandableRowsHideExpander="false"
                                pagination
                                noDataComponent="No Sectors Found"
                            />
                        </Card>
                    </Grid>
                    <Grid xs={4}>
                        <Grid container>
                            <Grid item lg={12}>
                                <Typography variant="button" gutterBottom mb={3}>
                                    {editSectorDetails ? 'Edit Sector' : 'Add Sector'}
                                </Typography>
                                <Card sx={{ mt: 2 }}>
                                    <Box sx={{ p: 4 }}>
                                        <form onSubmit={addSectorformik.handleSubmit}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='name'
                                                label="Name"
                                                // onBlur={addSectorformik.handleBlur}
                                                value={addSectorformik.values?.name}
                                                onChange={addSectorformik.handleChange}
                                                error={addSectorformik.touched.name && Boolean(addSectorformik.errors.name)}
                                                helperText={addSectorformik.touched.name && addSectorformik.errors.name}
                                            />
                                            <Stack direction="row" justifyContent="flex-end" >
                                                {editSectorDetails ?
                                                    <Button
                                                        type='button'
                                                        variant='contained'
                                                        sx={CancelButton}
                                                        color='error'
                                                        onClick={() => handleCancel()}
                                                    >
                                                        Cancel
                                                    </Button> : ''
                                                }

                                                <LoadingButton
                                                    loading={loading}
                                                    type='submit'
                                                    variant='contained'
                                                    sx={{ width: '10%', m: 1, mt: 2 }}
                                                >
                                                    {editSectorDetails ? 'Save' : 'Add'}
                                                </LoadingButton>
                                            </Stack>
                                        </form>
                                    </Box>
                                </Card>
                            </Grid>
                            <Grid item mt={4} lg={12}>
                                <Typography variant="button" gutterBottom mb={3}>
                                    {editSubSectorDetails ? 'Edit Sub-Sector' : 'Add Sub-Sector'}
                                </Typography>
                                <Card sx={{ mt: 2 }}>
                                    <Box sx={{ p: 4 }}>
                                        <form onSubmit={subSectorformik.handleSubmit}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='name'
                                                label="Name"
                                                // onBlur={subSectorformik.handleBlur}
                                                value={subSectorformik.values.name}
                                                onChange={subSectorformik.handleChange}
                                                error={subSectorformik.touched.name && Boolean(subSectorformik.errors.name)}
                                                helperText={subSectorformik.touched.name && subSectorformik.errors.name}
                                            />
                                            <FormControl sx={{ marginTop: '20px', width: '100%' }}>
                                                <SelectComponent
                                                    menuName={"name"}
                                                    menuValue={"_id"}
                                                    labelId="sector-label"
                                                    label="Sector *"
                                                    inputLabel="Sector"
                                                    disableNone
                                                    menuItems={Sectors}
                                                    sx={{ width: '100%' }}
                                                    name='sector'
                                                    // onBlur={subSectorformik.handleBlur}
                                                    defaultValue={subSectorformik.values?.sector}
                                                    value={subSectorformik.values?.sector}
                                                    onChange={subSectorformik.handleChange}
                                                    labelColor={subSectorformik.touched.sector && subSectorformik.errors.sector && 'error'}
                                                    labelError={subSectorformik.touched.sector && subSectorformik.errors.sector}
                                                    error={subSectorformik.touched.sector && Boolean(subSectorformik.errors.sector)}
                                                    helperText={subSectorformik.touched.sector && subSectorformik.errors.sector}
                                                />
                                            </FormControl>
                                            <FormControlLabel
                                                control={
                                                    <Switch
                                                        name="priority"
                                                        checked={subSectorformik.values.priority}
                                                        onChange={(e) =>
                                                            subSectorformik.setFieldValue('priority', e.target.checked)
                                                        }
                                                        color="primary"
                                                    />
                                                }
                                                label="Priority"
                                                sx={{ marginTop: '20px' }}
                                            />
                                            <Stack direction="row" justifyContent="flex-end" >
                                                {editSubSectorDetails ?
                                                    <Button
                                                        type='button'
                                                        variant='contained'
                                                        sx={CancelButton}
                                                        color='error'
                                                        onClick={() => handleCancelSub()}
                                                    >
                                                        Cancel
                                                    </Button> : ''
                                                }

                                                <LoadingButton
                                                    loading={SubSectorLoading}
                                                    type='submit'
                                                    variant='contained'
                                                    sx={{ width: '10%', m: 1, mt: 2 }}
                                                >
                                                    {editSubSectorDetails ? 'Save' : 'Add'}
                                                </LoadingButton>
                                            </Stack>
                                        </form>
                                    </Box>
                                </Card>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Container>
        </>
    )
}

export default Sector






