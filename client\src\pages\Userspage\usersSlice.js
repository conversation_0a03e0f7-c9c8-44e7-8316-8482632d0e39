import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import users from '../../_mock/users'
import axiosInstance from '../../utils/axiosInstance'
import { getUserRole } from '../../utils'

export const getUsers = createAsyncThunk("users/getUsers", async (data,{rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "users/get",
            method: "GET",
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get users", error)
        return rejectWithValue(error)
    }
})
export const getAvailableUsers = createAsyncThunk("users/getAvailableUsers", async (params,{rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "users/getAvailableUsers",
            method: "GET",
            params
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get available users", error)
        return rejectWithValue(error)
    }
})

export const postUser = createAsyncThunk("users/addUser", async (data) => {
    try {
        const response = await axiosInstance({
            url: "users/add",
            method: "POST",
            data
        })
        return response
    } catch (error) {
        console.log("error post users", error)
        return error
    }
})
export const editUser = createAsyncThunk("users/editUser", async (data,{rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "users/update",
            method: "PUT",
            data
        })
        // console.log("Edit user response",response)
        return response.data
    } catch (error) {
        console.log("error edit users", error)
        return rejectWithValue(error?.response) 
    }
})


export const removeUser = createAsyncThunk('users/removeUSer', async (data) => {

    try {
        const response = await axiosInstance({
            url: "users/remove",
            method: "DELETE",
            data
        })
        response.data.id = data?.id
        // response.data.id = data.id
        return response.data;
    } catch (error) {
        return error
    }
})

const usersSlice = createSlice({
    name: 'users',
    initialState: {
        status:'idle',
        users: [],
        availableUsers: []
    },
    reducers: {
        addUser: (state, action) => {
            state.users.push(action.payload)
        },
        updateUser: (state, action) => {

        },
        deleteUser: (state, action) => {
            state.users = state.users.filter(user => user.id !== action.payload.id)
        },
    },
    extraReducers:{
        [getUsers.pending]: (state) => {
            state.status = 'pending'
        },
        [getUsers.fulfilled]: (state, action) => {
            state.users = action.payload.data?.map(user => {
                return {
                    firstName: user?.firstName,
                    lastName: user?.lastName,
                    role: user?.role,
                    email: user?.email,
                    status: user?.status,
                    _id: user?._id,
                    contactNumber: user?.contactNumber,
                    collegeName: user?.colleges?.name,
                    groupName: user?.collegeGroups?.name,
                    campusName: user?.campuses?.name,
                    photo: user?.photo,
                    userRole: getUserRole(user?.role),
                }
            })
            state.status = 'succeded'
        },
        [getUsers.rejected]: (state, action) => {
            console.log("error",action.payload)
            state.status = 'rejected'
        },
        [postUser.fulfilled]: (state, action) => {
            // console.log('full postuser',action.payload)
        },
        [editUser.fulfilled]: (state, action) => {
            // console.log('full postuser',action.payload)
        },
        [removeUser.fulfilled]: (state, action) => {
            state.users = state.users.filter(user => user._id !== action.payload?.id)
        },
        [getAvailableUsers.fulfilled] : (state, action) => {
            state.availableUsers = action.payload.data
        }


    }
})
export const { addUser,deleteUser } = usersSlice.actions
export default usersSlice.reducer