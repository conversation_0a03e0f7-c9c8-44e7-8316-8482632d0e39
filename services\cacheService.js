/**
 * Cache Service for Performance Optimization
 * Implements in-memory caching for frequently accessed data
 */

class CacheService {
  constructor() {
    this.cache = new Map();
    this.ttl = new Map(); // Time to live for cache entries
    this.defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL
    
    // Start cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Set cache entry with TTL
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in milliseconds
   */
  set(key, value, ttl = this.defaultTTL) {
    this.cache.set(key, value);
    this.ttl.set(key, Date.now() + ttl);
  }

  /**
   * Get cache entry
   * @param {string} key - Cache key
   * @returns {any} Cached value or null
   */
  get(key) {
    const expiry = this.ttl.get(key);

    if (!expiry || Date.now() > expiry) {
      // Entry expired
      this.delete(key);
      // Import performance monitor here to avoid circular dependency
      try {
        const { performanceMonitor } = require('./performanceMonitorService');
        performanceMonitor.recordCacheMiss();
      } catch (e) {
        // Ignore if performance monitor not available
      }
      return null;
    }

    // Import performance monitor here to avoid circular dependency
    try {
      const { performanceMonitor } = require('./performanceMonitorService');
      performanceMonitor.recordCacheHit();
    } catch (e) {
      // Ignore if performance monitor not available
    }

    return this.cache.get(key);
  }

  /**
   * Delete cache entry
   * @param {string} key - Cache key
   */
  delete(key) {
    this.cache.delete(key);
    this.ttl.delete(key);
  }

  /**
   * Clear all cache
   */
  clear() {
    this.cache.clear();
    this.ttl.clear();
  }

  /**
   * Get cache statistics
   * @returns {Object} Cache stats
   */
  getStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
      memoryUsage: this.getMemoryUsage()
    };
  }

  /**
   * Estimate memory usage
   * @returns {number} Estimated memory usage in bytes
   */
  getMemoryUsage() {
    let size = 0;
    for (const [key, value] of this.cache) {
      size += JSON.stringify(key).length + JSON.stringify(value).length;
    }
    return size;
  }

  /**
   * Start cleanup interval to remove expired entries
   */
  startCleanupInterval() {
    setInterval(() => {
      const now = Date.now();
      for (const [key, expiry] of this.ttl) {
        if (now > expiry) {
          this.delete(key);
        }
      }
    }, 60000); // Cleanup every minute
  }

  /**
   * Cache wrapper for async functions
   * @param {string} key - Cache key
   * @param {Function} fn - Async function to cache
   * @param {number} ttl - Time to live
   * @returns {Promise<any>} Cached or fresh result
   */
  async wrap(key, fn, ttl = this.defaultTTL) {
    const cached = this.get(key);
    if (cached !== null) {
      return cached;
    }

    const result = await fn();
    this.set(key, result, ttl);
    return result;
  }

  /**
   * Invalidate cache entries by pattern
   * @param {string} pattern - Pattern to match keys
   */
  invalidatePattern(pattern) {
    const regex = new RegExp(pattern);
    for (const key of this.cache.keys()) {
      if (regex.test(key)) {
        this.delete(key);
      }
    }
  }
}

// Singleton instance
const cacheService = new CacheService();

// Predefined cache keys and TTLs
const CACHE_KEYS = {
  DEPLOYED_MODEL: 'deployed_model',
  COLLEGE: (id) => `college_${id}`,
  SESSION: (id) => `session_${id}`,
  CONVERSATION_CONTEXT: (id) => `context_${id}`,
  MODEL_CONFIG: (id) => `model_config_${id}`,
  SYSTEM_PROMPT: (collegeId) => `system_prompt_${collegeId}`
};

const CACHE_TTL = {
  DEPLOYED_MODEL: 10 * 60 * 1000, // 10 minutes (models don't change often)
  COLLEGE: 30 * 60 * 1000, // 30 minutes (college info rarely changes)
  SESSION: 5 * 60 * 1000, // 5 minutes (session info can change)
  CONVERSATION_CONTEXT: 2 * 60 * 1000, // 2 minutes (conversation changes frequently)
  MODEL_CONFIG: 15 * 60 * 1000, // 15 minutes (config changes occasionally)
  SYSTEM_PROMPT: 20 * 60 * 1000 // 20 minutes (prompts rarely change)
};

module.exports = {
  CacheService,
  cacheService,
  CACHE_KEYS,
  CACHE_TTL
};
