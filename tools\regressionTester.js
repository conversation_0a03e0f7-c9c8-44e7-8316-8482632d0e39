const mongoose = require('mongoose');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import all test suites
const CompleteSystemTester = require('./testCompleteSystem');
const PerformanceTester = require('./performanceTester');
const ModelQualityTester = require('./modelQualityTester');

// Import models for baseline testing
const { ChatSession } = require('../models/chatbotModels');
const { ChatMessage } = require('../models/chatbotModels');
const { College } = require('../models/college');

/**
 * Regression Testing Suite
 * Ensures new changes don't break existing functionality
 * Compares current system performance against baseline metrics
 */
class RegressionTester {
  constructor() {
    this.apiBaseURL = process.env.API_BASE_URL || 'http://localhost:3000';
    this.baselineFile = path.join(__dirname, '../test-reports/baseline-metrics.json');
    this.testResults = {
      regressionTests: {},
      baselineComparison: {},
      functionalityTests: {},
      performanceRegression: {},
      startTime: null,
      endTime: null
    };
  }

  /**
   * Run complete regression test suite
   */
  async runRegressionTests(options = {}) {
    const {
      createNewBaseline = false,
      performanceThreshold = 0.2, // 20% performance degradation threshold
      functionalityThreshold = 0.95, // 95% functionality success rate threshold
      includePerformanceRegression = true,
      includeFunctionalityRegression = true,
      includeQualityRegression = true
    } = options;

    console.log('🔄 Starting Regression Testing Suite');
    console.log('====================================');
    console.log(`📊 Configuration:`);
    console.log(`   Create New Baseline: ${createNewBaseline}`);
    console.log(`   Performance Threshold: ${(performanceThreshold * 100).toFixed(1)}%`);
    console.log(`   Functionality Threshold: ${(functionalityThreshold * 100).toFixed(1)}%`);

    this.testResults.startTime = new Date();

    try {
      // Load or create baseline metrics
      const baseline = await this.loadOrCreateBaseline(createNewBaseline);

      // Test 1: Functionality Regression
      if (includeFunctionalityRegression) {
        await this.runFunctionalityRegression(baseline, functionalityThreshold);
      }

      // Test 2: Performance Regression
      if (includePerformanceRegression) {
        await this.runPerformanceRegression(baseline, performanceThreshold);
      }

      // Test 3: Quality Regression
      if (includeQualityRegression) {
        await this.runQualityRegression(baseline);
      }

      // Generate regression report
      await this.generateRegressionReport(baseline);

      this.testResults.endTime = new Date();
      const duration = this.testResults.endTime - this.testResults.startTime;

      console.log('\n🎉 Regression Testing Completed!');
      console.log('=================================');
      console.log(`⏱️ Total Duration: ${Math.round(duration / 1000)}s`);

      // Determine overall regression status
      const regressionDetected = this.analyzeRegressionResults();
      if (regressionDetected) {
        console.log('⚠️ REGRESSION DETECTED - Review results before deployment');
      } else {
        console.log('✅ NO REGRESSION DETECTED - System is stable');
      }

      return this.testResults;

    } catch (error) {
      console.error('❌ Regression testing failed:', error);
      throw error;
    }
  }

  /**
   * Load existing baseline or create new one
   */
  async loadOrCreateBaseline(createNew = false) {
    if (createNew || !fs.existsSync(this.baselineFile)) {
      console.log('\n📊 Creating new baseline metrics...');
      return await this.createBaseline();
    } else {
      console.log('\n📊 Loading existing baseline metrics...');
      const baseline = JSON.parse(fs.readFileSync(this.baselineFile, 'utf8'));
      console.log(`✅ Loaded baseline from ${new Date(baseline.createdAt).toLocaleDateString()}`);
      return baseline;
    }
  }

  /**
   * Create new baseline metrics
   */
  async createBaseline() {
    console.log('🔧 Running baseline tests...');

    try {
      // Run system tests for baseline
      const systemTester = new CompleteSystemTester();
      const systemResults = await systemTester.runCompleteTests({
        includeStressTests: false,
        cleanupAfterTests: true
      });

      // Run performance tests for baseline
      const performanceTester = new PerformanceTester();
      const performanceResults = await performanceTester.runPerformanceTests({
        maxConcurrentUsers: 5,
        testDurationSeconds: 30
      });

      // Run quality tests for baseline
      const qualityTester = new ModelQualityTester();
      const qualityResults = await qualityTester.runModelQualityTests({
        testIterations: 2
      });

      const baseline = {
        createdAt: new Date(),
        version: process.env.npm_package_version || '1.0.0',
        systemTests: {
          totalTests: systemResults.totalTests,
          passedTests: systemResults.passedTests,
          successRate: systemResults.passedTests / systemResults.totalTests
        },
        performance: {
          averageResponseTime: this.extractAverageResponseTime(performanceResults),
          throughput: performanceResults.throughputTests?.requestsPerSecond || 0,
          concurrencySuccess: this.extractConcurrencySuccess(performanceResults)
        },
        quality: {
          overallScore: qualityResults.summary?.overallQualityScore || 0,
          qualityTests: this.extractQualityMetrics(qualityResults.qualityTests),
          accuracyTests: this.extractAccuracyMetrics(qualityResults.accuracyTests)
        }
      };

      // Save baseline
      const baselineDir = path.dirname(this.baselineFile);
      if (!fs.existsSync(baselineDir)) {
        fs.mkdirSync(baselineDir, { recursive: true });
      }
      fs.writeFileSync(this.baselineFile, JSON.stringify(baseline, null, 2), 'utf8');

      console.log('✅ Baseline metrics created and saved');
      return baseline;

    } catch (error) {
      console.error('❌ Baseline creation failed:', error);
      throw error;
    }
  }

  /**
   * Test functionality regression
   */
  async runFunctionalityRegression(baseline, threshold) {
    console.log('\n🔧 Running Functionality Regression Tests...');

    try {
      const systemTester = new CompleteSystemTester();
      const currentResults = await systemTester.runCompleteTests({
        includeStressTests: false,
        cleanupAfterTests: true
      });

      const currentSuccessRate = currentResults.passedTests / currentResults.totalTests;
      const baselineSuccessRate = baseline.systemTests.successRate;
      const regression = baselineSuccessRate - currentSuccessRate;

      this.testResults.functionalityTests = {
        currentSuccessRate: currentSuccessRate,
        baselineSuccessRate: baselineSuccessRate,
        regression: regression,
        regressionPercentage: (regression / baselineSuccessRate) * 100,
        threshold: threshold,
        passed: currentSuccessRate >= threshold,
        regressionDetected: regression > (1 - threshold)
      };

      console.log(`   Current Success Rate: ${(currentSuccessRate * 100).toFixed(1)}%`);
      console.log(`   Baseline Success Rate: ${(baselineSuccessRate * 100).toFixed(1)}%`);
      console.log(`   Regression: ${(regression * 100).toFixed(1)}%`);
      
      if (this.testResults.functionalityTests.regressionDetected) {
        console.log('   ⚠️ FUNCTIONALITY REGRESSION DETECTED');
      } else {
        console.log('   ✅ No functionality regression');
      }

    } catch (error) {
      console.error('❌ Functionality regression test failed:', error);
      this.testResults.functionalityTests = { error: error.message };
    }
  }

  /**
   * Test performance regression
   */
  async runPerformanceRegression(baseline, threshold) {
    console.log('\n⚡ Running Performance Regression Tests...');

    try {
      const performanceTester = new PerformanceTester();
      const currentResults = await performanceTester.runPerformanceTests({
        maxConcurrentUsers: 5,
        testDurationSeconds: 30
      });

      const currentResponseTime = this.extractAverageResponseTime(currentResults);
      const baselineResponseTime = baseline.performance.averageResponseTime;
      const responseTimeRegression = (currentResponseTime - baselineResponseTime) / baselineResponseTime;

      const currentThroughput = currentResults.throughputTests?.requestsPerSecond || 0;
      const baselineThroughput = baseline.performance.throughput;
      const throughputRegression = (baselineThroughput - currentThroughput) / baselineThroughput;

      this.testResults.performanceRegression = {
        responseTime: {
          current: currentResponseTime,
          baseline: baselineResponseTime,
          regression: responseTimeRegression,
          regressionDetected: responseTimeRegression > threshold
        },
        throughput: {
          current: currentThroughput,
          baseline: baselineThroughput,
          regression: throughputRegression,
          regressionDetected: throughputRegression > threshold
        },
        threshold: threshold
      };

      console.log(`   Response Time - Current: ${currentResponseTime}ms, Baseline: ${baselineResponseTime}ms`);
      console.log(`   Throughput - Current: ${currentThroughput} req/s, Baseline: ${baselineThroughput} req/s`);
      
      if (this.testResults.performanceRegression.responseTime.regressionDetected ||
          this.testResults.performanceRegression.throughput.regressionDetected) {
        console.log('   ⚠️ PERFORMANCE REGRESSION DETECTED');
      } else {
        console.log('   ✅ No performance regression');
      }

    } catch (error) {
      console.error('❌ Performance regression test failed:', error);
      this.testResults.performanceRegression = { error: error.message };
    }
  }

  /**
   * Test quality regression
   */
  async runQualityRegression(baseline) {
    console.log('\n🧠 Running Quality Regression Tests...');

    try {
      const qualityTester = new ModelQualityTester();
      const currentResults = await qualityTester.runModelQualityTests({
        testIterations: 2
      });

      const currentOverallScore = currentResults.summary?.overallQualityScore || 0;
      const baselineOverallScore = baseline.quality.overallScore;
      const qualityRegression = baselineOverallScore - currentOverallScore;

      this.testResults.qualityRegression = {
        overallScore: {
          current: currentOverallScore,
          baseline: baselineOverallScore,
          regression: qualityRegression,
          regressionDetected: qualityRegression > 0.1 // 10% quality drop threshold
        },
        qualityTests: this.compareQualityMetrics(
          this.extractQualityMetrics(currentResults.qualityTests),
          baseline.quality.qualityTests
        ),
        accuracyTests: this.compareAccuracyMetrics(
          this.extractAccuracyMetrics(currentResults.accuracyTests),
          baseline.quality.accuracyTests
        )
      };

      console.log(`   Overall Quality - Current: ${(currentOverallScore * 100).toFixed(1)}%, Baseline: ${(baselineOverallScore * 100).toFixed(1)}%`);
      
      if (this.testResults.qualityRegression.overallScore.regressionDetected) {
        console.log('   ⚠️ QUALITY REGRESSION DETECTED');
      } else {
        console.log('   ✅ No quality regression');
      }

    } catch (error) {
      console.error('❌ Quality regression test failed:', error);
      this.testResults.qualityRegression = { error: error.message };
    }
  }

  /**
   * Extract average response time from performance results
   */
  extractAverageResponseTime(performanceResults) {
    const responseTests = performanceResults.responseTimeTests || {};
    const times = Object.values(responseTests).map(test => test.averageMs).filter(t => t);
    return times.length > 0 ? Math.round(times.reduce((a, b) => a + b, 0) / times.length) : 0;
  }

  /**
   * Extract concurrency success rate
   */
  extractConcurrencySuccess(performanceResults) {
    const concurrencyTests = performanceResults.concurrencyTests || {};
    const successRates = Object.values(concurrencyTests).map(test => 
      parseFloat(test.successRate?.replace('%', '')) || 0
    );
    return successRates.length > 0 ? successRates.reduce((a, b) => a + b, 0) / successRates.length : 0;
  }

  /**
   * Extract quality metrics
   */
  extractQualityMetrics(qualityTests) {
    const metrics = {};
    Object.entries(qualityTests || {}).forEach(([test, results]) => {
      metrics[test] = results.averageScore || 0;
    });
    return metrics;
  }

  /**
   * Extract accuracy metrics
   */
  extractAccuracyMetrics(accuracyTests) {
    const metrics = {};
    Object.entries(accuracyTests || {}).forEach(([test, results]) => {
      metrics[test] = results.averageAccuracy || 0;
    });
    return metrics;
  }

  /**
   * Compare quality metrics
   */
  compareQualityMetrics(current, baseline) {
    const comparison = {};
    Object.keys(baseline || {}).forEach(test => {
      const currentScore = current[test] || 0;
      const baselineScore = baseline[test] || 0;
      comparison[test] = {
        current: currentScore,
        baseline: baselineScore,
        regression: baselineScore - currentScore,
        regressionDetected: (baselineScore - currentScore) > 0.5 // 0.5 point drop threshold
      };
    });
    return comparison;
  }

  /**
   * Compare accuracy metrics
   */
  compareAccuracyMetrics(current, baseline) {
    const comparison = {};
    Object.keys(baseline || {}).forEach(test => {
      const currentAccuracy = current[test] || 0;
      const baselineAccuracy = baseline[test] || 0;
      comparison[test] = {
        current: currentAccuracy,
        baseline: baselineAccuracy,
        regression: baselineAccuracy - currentAccuracy,
        regressionDetected: (baselineAccuracy - currentAccuracy) > 0.1 // 10% accuracy drop threshold
      };
    });
    return comparison;
  }

  /**
   * Analyze regression results
   */
  analyzeRegressionResults() {
    let regressionDetected = false;

    // Check functionality regression
    if (this.testResults.functionalityTests?.regressionDetected) {
      regressionDetected = true;
    }

    // Check performance regression
    if (this.testResults.performanceRegression?.responseTime?.regressionDetected ||
        this.testResults.performanceRegression?.throughput?.regressionDetected) {
      regressionDetected = true;
    }

    // Check quality regression
    if (this.testResults.qualityRegression?.overallScore?.regressionDetected) {
      regressionDetected = true;
    }

    return regressionDetected;
  }

  /**
   * Generate regression report
   */
  async generateRegressionReport(baseline) {
    console.log('\n📋 Generating Regression Report...');
    
    const report = {
      summary: {
        testDate: new Date(),
        baselineDate: baseline.createdAt,
        regressionDetected: this.analyzeRegressionResults(),
        totalDuration: this.testResults.endTime - this.testResults.startTime
      },
      baseline: baseline,
      currentResults: {
        functionalityTests: this.testResults.functionalityTests,
        performanceRegression: this.testResults.performanceRegression,
        qualityRegression: this.testResults.qualityRegression
      },
      recommendations: this.generateRegressionRecommendations()
    };
    
    // Save report to file
    const reportDir = path.join(__dirname, '../test-reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `regression-test-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
    
    console.log(`📋 Regression report saved: ${reportFile}`);
    return report;
  }

  /**
   * Generate regression recommendations
   */
  generateRegressionRecommendations() {
    const recommendations = [];

    if (this.testResults.functionalityTests?.regressionDetected) {
      recommendations.push('Functionality regression detected - review failed tests and fix issues before deployment');
    }

    if (this.testResults.performanceRegression?.responseTime?.regressionDetected) {
      recommendations.push('Response time regression detected - optimize slow endpoints');
    }

    if (this.testResults.performanceRegression?.throughput?.regressionDetected) {
      recommendations.push('Throughput regression detected - investigate system bottlenecks');
    }

    if (this.testResults.qualityRegression?.overallScore?.regressionDetected) {
      recommendations.push('Model quality regression detected - review training data and model configuration');
    }

    if (recommendations.length === 0) {
      recommendations.push('No regression detected - system is stable and ready for deployment');
    }

    return recommendations;
  }
}

// Command line interface
const runRegressionTests = async () => {
  const args = process.argv.slice(2);
  const tester = new RegressionTester();

  const options = {
    createNewBaseline: args.includes('--create-baseline'),
    performanceThreshold: parseFloat(args.find(arg => arg.startsWith('--perf-threshold='))?.split('=')[1]) || 0.2,
    functionalityThreshold: parseFloat(args.find(arg => arg.startsWith('--func-threshold='))?.split('=')[1]) || 0.95
  };

  try {
    await tester.runRegressionTests(options);
  } catch (error) {
    console.error('❌ Regression testing failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runRegressionTests();
}

module.exports = RegressionTester;
