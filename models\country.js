const mongoose = require("mongoose");

const CountrySchema = new mongoose.Schema({
  name: String,
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.CountrySchema = CountrySchema;

class Country extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      
      const count = await Country.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await Country.create(entry);
    }
  }
}

mongoose.model(Country, CountrySchema, "countries");

module.exports.Country = Country;

