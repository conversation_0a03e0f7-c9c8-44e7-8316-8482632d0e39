import { Box, Button, Fade, FormControl, Grid, Stack, TextField, TextareaAutosize, Typography } from '@mui/material';
import { formButton, formStyle } from '../../utils/cssStyles';
import { groupValidationSchema } from '../../utils/validationSchemas';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import SelectComponent from '../../components/SelectComponent';


const SkillsForm = ({ Groups, Groups1, setOpenModel, openModel,formik}) => {
    return (
        <Fade in={openModel}>
            <Box >
                <form onSubmit={formik.handleSubmit}>
                    <Grid container gap={2} >
                        <Grid item xs={12} md={5.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='lmiName'
                                label="LMI NAME "
                                onBlur={formik.handleBlur}
                                value={formik.values.lmiName}
                                onChange={formik.handleChange}
                                disabled
                                InputLabelProps={{
                                    shrink: true,
                                }}   
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='lmiId'
                                label="LMI ID "
                                onBlur={formik.handleBlur}
                                value={formik.values.lmiId}
                                onChange={formik.handleChange}
                                disabled
                                InputLabelProps={{
                                    shrink: true,
                                }}
                            />
                        </Grid>
                        <Grid item xs={6} md={11.8}>
                            <FormControl sx={{ minWidth: '100%' }}>
                                <SelectComponent
                                    name="radarCategory"
                                    label="Radar Category"
                                    menuName={"name"}
                                    menuValue={"_id"}
                                    menuItems={Groups}
                                    labelId="radar-category"
                                    onBlur={formik.handleBlur}
                                    value={formik.values.radarCategory}
                                    inputLabel="Radar Category"
                                    disableNone={'true'}
                                    // onChange={formik.handleChange}
                                    onChange={formik.handleChange}
                                    error={formik.touched.radarCategory && Boolean(formik.errors.radarCategory)}
                                    labelColor={formik.touched.radarCategory && formik.errors.radarCategory && 'error'}
                                    labelError={formik.touched.radarCategory && formik.errors.radarCategory}
                                    helperText={formik.touched.radarCategory && formik.errors.radarCategory}
                                />
                            </FormControl>
                        </Grid>
                        <Grid item xs={6} md={11.8}>
                            <FormControl sx={{ minWidth: '100%' }}>
                                <SelectComponent
                                    name="radarSubCategory"
                                    label="Radar Sub Category"
                                    menuName={"name"}
                                    menuValue={"_id"}
                                    menuItems={Groups1}
                                    disable={formik.values.radarCategory ? false:'true'}
                                    labelId="radar-sub-category"
                                    onBlur={formik.handleBlur}
                                    value={formik.values.radarSubCategory}
                                    disableNone={'true'}
                                    inputLabel="Radar Sub Category"
                                    onChange={formik.handleChange}
                                    error={formik.touched.radarSubCategory && Boolean(formik.errors.radarSubCategory)}
                                    labelColor={formik.touched.radarSubCategory && formik.errors.radarSubCategory && 'error'}
                                    labelError={formik.touched.radarSubCategory && formik.errors.radarSubCategory}
                                    helperText={formik.touched.radarSubCategory && formik.errors.radarSubCategory}
                                />
                            </FormControl>
                        </Grid>
                    </Grid>
                    <Stack
                        direction="row"
                        justifyContent="flex-end"
                    >
                        <Button
                            type='submit'
                            variant='contained'
                            sx={formButton}>
                            Update
                        </Button>
                    </Stack>
                </form>
            </Box>
        </Fade >
    )
}

export default SkillsForm