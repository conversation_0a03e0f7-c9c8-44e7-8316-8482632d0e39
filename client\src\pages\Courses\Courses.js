import { useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
  Container,
  Typography,
  Button
} from '@mui/material';
// @mui
import { get } from 'lodash';
import { Icon } from '@iconify/react';
import { useNavigate } from 'react-router-dom';
import DataTable from '../../components/DataTable/DataTable';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { getCourses, postExportCourse, removeCourse } from './coursesSlice';
import { setSnackbar } from '../../Redux/snackbarSlice';
import useLoading from '../../hooks/useLoading';

// ----------------------------------------------------------------------
export const COURSE_TABLE_HEAD = [
  { id: 'careerTitle', label: 'Title', alignRight: false },
  { id: 'code', label: 'Code', alignRight: false },
  { id: 'level', label: 'Level', alignRight: false },
  { id: 'campusName', label: 'Campus', alignRight: false },
  { id: 'sectorOrSubsector', label: 'Sector/Subsector', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

const sectorOrSub = (row) => (
  row.sectorOrSubsector ? <Icon icon="mdi:success" /> : <Icon icon="radix-icons:cross-1" />
)

export const renderCourseCell = ['title', 'code', 'level', 'campusName', sectorOrSub]
const deleteTitle = "Delete Course ?"
const deleteDescription = "Are you sure you want to delete this course"

const Courses = () => {
  const dispatch = useDispatch()
  const { courses, status } = useSelector(state => state.courses)
  const [openModel, setOpenModel] = useState(false);
  const [Courses, setCourses] = useState([]);
  const [exportData, setExportData] = useState([]);
  const navigate = useNavigate()
  const { selectedCollege } = useSelector(state => state.selectedCollege)
  const csvDownloadRef = useRef(null);
  const loading = useLoading(status)
  useEffect(() => {
    setCourses([...courses])
  }, [courses])
  useEffect(() => {
    // if (selectedCollege) {
    const selected = JSON.parse(localStorage.getItem('selectedCollege'));
    if (selected) {
      dispatch(getCourses(selected))
    } else {
      dispatch(getCourses())
    }
    // }
  }, [selectedCollege])


  const handleFilterSearch = (event) => {
    const filteredCourses = Courses.filter(course => course?.title?.toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.level?.toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.campusName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.code?.toLowerCase().includes(event.target.value.toLowerCase())
    )
    return filteredCourses
  };

  // const handleOpen = () => setOpenModel(true);
  const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/courses/add`);

  const handleClose = () => {
    setOpenModel(false)
  };
  const handleCourseImport = () => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/importcourses`)
  };

  const handleDeleteCourse = (course) => {
    dispatch(removeCourse(course._id)).then(res => {
      if (res?.payload?.success) {
        const sucessMessage = get(res, 'payload.message', 'Successfully removed course')
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'success',
          snackbarMessage: sucessMessage
        }))

      } else {
        const errorMessage = get(res, 'payload.response.data.message', 'Something went wrong')
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'error',
          snackbarMessage: errorMessage
        }))
      }
    })
  }
  const editCourse = (course) => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/courses/edit/${course?._id}`)
  }

  return (
    <>
      <Helmet>
        <title> Courses | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        <Typography variant="h4" gutterBottom mb={3}>
          Courses
        </Typography>
        <DataTable
          loading={loading}
          TableHead={COURSE_TABLE_HEAD}
          TableData={Courses}
          filterSearch={handleFilterSearch}
          searchLable={"Search..."}
          buttonText={selectedCollege ? "New Course" : ""}
          handleEdit={editCourse}
          buttonHandler={handleOpen}
          renderCells={renderCourseCell}
          handleDelete={handleDeleteCourse}
          importcourse={handleCourseImport}
          deleteTitle={deleteTitle}
          deleteDescription={deleteDescription}
          pagination="true"
          rowsPerPageProp={10}
          exportCourse
          // exportData={exportData}
          // exportCoursesFunc={exportCoursesFunc}
          csvDownloadRef={csvDownloadRef}
        />
        {/* </Container> */}
      </Container>
    </>
  )
}

export default Courses






