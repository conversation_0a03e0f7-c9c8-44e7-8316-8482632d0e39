import { Box, Button, Fade, FormControl, Grid, Stack } from '@mui/material';
import { useFormik } from 'formik';
import { matchIsValidTel } from 'mui-tel-input';
import { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import PhoneInput from '../../components/PhoneInput';
import SelectComponent from '../../components/SelectComponent';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { formButton } from '../../utils/cssStyles';
import { userValidationSchema } from '../../utils/validationSchemas';
import { addUser } from './usersSlice';

const UserForm = ({ Users, Groups, Colleges, setOpenModel, openModel }) => {
    const [filteredColleges, setFilteredColleges] = useState([])


    const formik = useFormik({
        initialValues: {
            FirstName: '',
            LastName: '',
            groupName: '',
            collegeName: '',
            email: '',
            phone: ''
        },
        onSubmit: (values) => {
            if (formik.values.collegeName || formik.values.groupName) {
                const ID = Users[Users.length - 1].id + 1
                dispatch(addUser({ id: ID, ...values }))
                handleClose()
            }
        },
        validationSchema: userValidationSchema
    })
    const dispatch = useDispatch()
    const handleClose = () => {
        setOpenModel(false)
    }
    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            phone: newValue
        })
    }


    useEffect(() => {
        setFilteredColleges(Colleges.filter(college => college.groupName === formik.values.groupName))
    }, [formik.values.groupName])
    return (
        <Fade in={openModel}>
            <Box >
                <form
                    onSubmit={formik.handleSubmit}
                >
                    <Grid container gap={2} >
                        <Grid item xs={12} md={5.8} >
                            <FormControl sx={{ minWidth: "100%" }}>
                                <SelectComponent
                                    name='groupName'
                                    labelId="group-label"
                                    value={formik.values.groupName}
                                    inputLabel="Group"
                                    label="Group *"
                                    onChange={formik.handleChange}
                                    onBlur={formik.handleBlur}
                                    menuItems={Groups}
                                    menuName={"Name"}
                                    menuValue={"Name"}
                                    error={formik.touched.groupName && Boolean(formik.errors.groupName)}
                                    labelColor={formik.touched.groupName && formik.errors.groupName && 'error'}
                                    labelError={formik.touched.groupName && formik.errors.groupName}
                                    helperText={formik.touched.groupName && formik.errors.groupName}
                                />
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <FormControl sx={{ width: '100%' }}>
                                <SelectComponent
                                    disabled={Boolean(!formik.values.groupName)}
                                    name='collegeName'
                                    labelId="college-label"
                                    value={formik.values.collegeName}
                                    label="College *"
                                    inputLabel="College"
                                    onChange={formik.handleChange}
                                    menuName={"name"}
                                    menuValue={"name"}
                                    menuItems={filteredColleges}
                                />
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='FirstName'
                                label="First Name"
                                onBlur={formik.handleBlur}
                                value={formik.values.FirstName}
                                onChange={formik.handleChange}
                                error={formik.touched.FirstName && Boolean(formik.errors.FirstName)}
                                helperText={formik.touched.FirstName && formik.errors.FirstName}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                value={formik.values.LastName}
                                onBlur={formik.handleBlur}
                                name='LastName'
                                label="Last Name"
                                onChange={formik.handleChange}
                                error={formik.touched.LastName && Boolean(formik.errors.LastName)}
                                helperText={formik.touched.LastName && formik.errors.LastName}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                value={formik.values.email}
                                name='email'
                                type={'email'}
                                label="Email"
                                onChange={formik.handleChange}
                                error={formik.touched.email && Boolean(formik.errors.email)}
                                helperText={formik.touched.email && formik.errors.email}
                                onBlur={formik.handleBlur}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <PhoneInput
                                sx={{ width: "100%" }}
                                value={formik.values.phone}
                                name='phone'
                                label="Phone"
                                defaultCountry="GB"
                                onChange={handlePhoneChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.phone && Boolean(formik.errors.phone)}
                                helperText={formik.touched.phone && formik.errors.phone}
                            />
                        </Grid>
                    </Grid>

                    <Stack direction="row" justifyContent="flex-end" >
                        <Button
                            type='submit'
                            variant='contained'
                            sx={formButton}
                        >
                            Add
                        </Button>
                    </Stack>
                </form>
            </Box>
        </Fade >
    )
}

export default UserForm