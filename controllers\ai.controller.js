const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Import models
const TrainingDataset = require('../models/trainingDataset');
const FineTunedModel = require('../models/fineTunedModel');

// Import services
const FineTuningDataService = require('../services/fineTuningDataService');
const OpenAIFineTuningService = require('../services/openAIFineTuningService');
const ModelConfigurationService = require('../services/modelConfigurationService');

// Import utilities
const { messageResponse } = require('../helpers/commonHelper');
const { INVALID_MISSING, SERVER_ERROR, CUSTOM, NOT_FOUND } = require('../config/messages');

// Initialize services (singleton pattern)
let fineTuningDataService = null;
let openAIFineTuningService = null;
let modelConfigurationService = null;

const getServices = () => {
  if (!fineTuningDataService) {
    fineTuningDataService = new FineTuningDataService();
    openAIFineTuningService = new OpenAIFineTuningService();
    modelConfigurationService = new ModelConfigurationService();
  }
  return { fineTuningDataService, openAIFineTuningService, modelConfigurationService };
};

/**
 * TRAINING DATA MANAGEMENT
 */

/**
 * Generate training data from database
 * POST /api/ai/training-data/generate
 */
const generateTrainingData = async (req, res) => {
  try {
    const { 
      maxExamplesPerType = 1000, 
      validationSplit = 0.1, 
      validateData = true, 
      examplesPerRecord = 3 
    } = req.body;
    const { _id: userId } = req.user

    console.log('🚀 Starting training data generation via AI API...');
    
    const { fineTuningDataService } = getServices();
    
    // Generate training data
    const result = await fineTuningDataService.generateEnhancedTrainingData({
      maxExamplesPerType: parseInt(maxExamplesPerType),
      validationSplit: parseFloat(validationSplit),
      validateData: validateData,
      examplesPerRecord: parseInt(examplesPerRecord)
    });

    // Create versioned directory for files
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const datasetId = new Date().getTime().toString();
    const datasetDir = path.join(__dirname, '../data/training-datasets', `dataset_${timestamp}_${datasetId}`);
    
    if (!fs.existsSync(datasetDir)) {
      fs.mkdirSync(datasetDir, { recursive: true });
    }
    
    // Save files with metadata
    const files = {};
    
    // Save training data
    const trainingFile = path.join(datasetDir, 'training.jsonl');
    const trainingJSONL = result.trainingData.map(example => JSON.stringify(example)).join('\n');
    fs.writeFileSync(trainingFile, trainingJSONL, 'utf8');
    files.trainingFile = {
      filename: 'training.jsonl',
      path: trainingFile,
      size: fs.statSync(trainingFile).size,
      examples: result.trainingData.length,
      checksum: crypto.createHash('sha256').update(trainingJSONL).digest('hex')
    };
    
    // Save validation data
    const validationFile = path.join(datasetDir, 'validation.jsonl');
    const validationJSONL = result.validationData.map(example => JSON.stringify(example)).join('\n');
    fs.writeFileSync(validationFile, validationJSONL, 'utf8');
    files.validationFile = {
      filename: 'validation.jsonl',
      path: validationFile,
      size: fs.statSync(validationFile).size,
      examples: result.validationData.length,
      checksum: crypto.createHash('sha256').update(validationJSONL).digest('hex')
    };
    
    // Save statistics
    if (result.statistics) {
      const statsFile = path.join(datasetDir, 'statistics.json');
      const statsContent = JSON.stringify(result.statistics, null, 2);
      fs.writeFileSync(statsFile, statsContent, 'utf8');
      files.statisticsFile = {
        filename: 'statistics.json',
        path: statsFile,
        size: fs.statSync(statsFile).size
      };
    }
    
    // Save validation report
    if (result.validationResults) {
      const reportFile = path.join(datasetDir, 'report.json');
      const reportContent = JSON.stringify(result.validationResults, null, 2);
      fs.writeFileSync(reportFile, reportContent, 'utf8');
      files.reportFile = {
        filename: 'report.json',
        path: reportFile,
        size: fs.statSync(reportFile).size
      };
    }

    // Create database record
    const dataset = new TrainingDataset({
      name: `Training Dataset ${new Date().toLocaleString()}`,
      version: `v${Date.now()}`,
      description: `Generated with ${result.trainingData.length} training examples`,
      generatedBy: userId,
      generationConfig: {
        maxExamplesPerType: parseInt(maxExamplesPerType),
        validationSplit: parseFloat(validationSplit),
        examplesPerRecord: parseInt(examplesPerRecord),
        validateData: validateData
      },
      files: files,
      statistics: {
        trainingExamples: result.trainingData.length,
        validationExamples: result.validationData.length,
        totalRecordsProcessed: result.extractionMetadata?.statistics?.totalRecords || 0,
        qualityScore: result.validationResults?.report?.qualityScore || null,
        contentBreakdown: result.statistics || {}
      },
      validation: {
        warnings: result.validationResults?.warnings?.length || 0,
        errors: result.validationResults?.errors?.length || 0,
        warningRate: result.validationResults?.warnings?.length ? 
          (result.validationResults.warnings.length / result.trainingData.length) : 0,
        qualityScore: result.validationResults?.report?.qualityScore || null
      },
    });

    await dataset.save();

    console.log('✅ Training data generation completed and saved to database');
    console.log(`📁 Dataset ID: ${dataset._id}`);
    console.log(`📁 Files saved to: ${datasetDir}`);

    return messageResponse(
      null,
      "Training data generated and saved successfully",
      true,
      200,
      {
        datasetId: dataset._id,
        trainingExamples: result.trainingData.length,
        validationExamples: result.validationData.length,
        totalRecordsProcessed: result.extractionMetadata?.statistics?.totalRecords || 0,
        qualityScore: result.validationResults?.report?.qualityScore || null,
        statistics: result.statistics,
        files: {
          trainingFile: files.trainingFile.filename,
          validationFile: files.validationFile.filename,
          statisticsFile: files.statisticsFile?.filename,
          reportFile: files.reportFile?.filename
        },
        timestamp: new Date()
      },
      res
    );

  } catch (error) {
    console.error('❌ Error generating training data:', error);
    return messageResponse(SERVER_ERROR, "Training data generation failed", false, 500, { error: error.message }, res);
  }
};

/**
 * List all training datasets
 * GET /api/ai/training-datasets
 */
const listTrainingDatasets = async (req, res) => {
  try {
    const { 
      status = 'active', 
      limit = 10, 
      page = 1, 
      sortBy = 'createdAt', 
      sortOrder = 'desc' 
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    const query = status !== 'all' ? { status } : {};
    
    const datasets = await TrainingDataset.find(query)
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip);

    const total = await TrainingDataset.countDocuments(query);

    return messageResponse(
      null,
      "Training datasets retrieved successfully",
      true,
      200,
      {
        datasets: datasets.map(dataset => ({
          id: dataset._id,
          name: dataset.name,
          version: dataset.version,
          description: dataset.description,
          status: dataset.status,
          statistics: dataset.statistics,
          validation: dataset.validation,
          createdAt: dataset.createdAt,
          isReadyForTraining: dataset.isReadyForTraining()
        })),
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error listing training datasets:', error);
    return messageResponse(SERVER_ERROR, "Failed to list training datasets", false, 500, { error: error.message }, res);
  }
};

// Placeholder for other controller methods
const getTrainingDataset = async (req, res) => {
  try {
    const { id } = req.params;

    const dataset = await TrainingDataset.findById(id);
    if (!dataset) {
      return messageResponse(INVALID_MISSING, "Training dataset not found", false, 404, null, res);
    }

    // Check if files exist on disk
    const filesStatus = {
      trainingFile: fs.existsSync(dataset.files.trainingFile.path),
      validationFile: fs.existsSync(dataset.files.validationFile.path),
      statisticsFile: dataset.files.statisticsFile ? fs.existsSync(dataset.files.statisticsFile.path) : false,
      reportFile: dataset.files.reportFile ? fs.existsSync(dataset.files.reportFile.path) : false
    };

    return messageResponse(
      null,
      "Training dataset retrieved successfully",
      true,
      200,
      {
        dataset: {
          id: dataset._id,
          name: dataset.name,
          version: dataset.version,
          description: dataset.description,
          generatedBy: dataset.generatedBy,
          generatedAt: dataset.generatedAt,
          generationConfig: dataset.generationConfig,
          statistics: dataset.statistics,
          validation: dataset.validation,
          status: dataset.status,
          createdAt: dataset.createdAt,
          updatedAt: dataset.updatedAt,
          isReadyForTraining: dataset.isReadyForTraining(),
          totalExamples: dataset.totalExamples,
          fileCount: dataset.fileCount
        },
        files: {
          trainingFile: {
            ...dataset.files.trainingFile,
            exists: filesStatus.trainingFile
          },
          validationFile: {
            ...dataset.files.validationFile,
            exists: filesStatus.validationFile
          },
          statisticsFile: dataset.files.statisticsFile ? {
            ...dataset.files.statisticsFile,
            exists: filesStatus.statisticsFile
          } : null,
          reportFile: dataset.files.reportFile ? {
            ...dataset.files.reportFile,
            exists: filesStatus.reportFile
          } : null
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error getting training dataset:', error);
    return messageResponse(SERVER_ERROR, "Failed to get training dataset", false, 500, { error: error.message }, res);
  }
};

const downloadDatasetFile = async (req, res) => {
  try {
    const { id, type } = req.params;

    const dataset = await TrainingDataset.findById(id);
    if (!dataset) {
      return messageResponse(INVALID_MISSING, "Training dataset not found", false, 404, null, res);
    }

    // Validate file type
    const validTypes = ['training', 'validation', 'statistics', 'report'];
    if (!validTypes.includes(type)) {
      return messageResponse(INVALID_MISSING, "Invalid file type", false, 400, null, res);
    }

    // Get file info based on type
    let fileInfo = null;
    switch (type) {
      case 'training':
        fileInfo = dataset.files.trainingFile;
        break;
      case 'validation':
        fileInfo = dataset.files.validationFile;
        break;
      case 'statistics':
        fileInfo = dataset.files.statisticsFile;
        break;
      case 'report':
        fileInfo = dataset.files.reportFile;
        break;
    }

    if (!fileInfo) {
      return messageResponse(INVALID_MISSING, `${type} file not found in dataset`, false, 404, null, res);
    }

    // Check if file exists on disk
    if (!fs.existsSync(fileInfo.path)) {
      return messageResponse(SERVER_ERROR, `${type} file not found on disk`, false, 404, null, res);
    }

    // Set appropriate headers for download
    const filename = fileInfo.filename;
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', fileInfo.size);

    // Stream the file
    const fileStream = fs.createReadStream(fileInfo.path);
    fileStream.pipe(res);

    fileStream.on('error', (error) => {
      console.error('❌ Error streaming file:', error);
      if (!res.headersSent) {
        return messageResponse(SERVER_ERROR, "Error downloading file", false, 500, { error: error.message }, res);
      }
    });

  } catch (error) {
    console.error('❌ Error downloading dataset file:', error);
    if (!res.headersSent) {
      return messageResponse(SERVER_ERROR, "Failed to download file", false, 500, { error: error.message }, res);
    }
  }
};

const previewDataset = async (req, res) => {
  try {
    const { id } = req.params;
    const { lines = 100, type = 'training' } = req.query;

    const dataset = await TrainingDataset.findById(id);
    if (!dataset) {
      return messageResponse(INVALID_MISSING, "Training dataset not found", false, 404, null, res);
    }

    // Validate file type
    const validTypes = ['training', 'validation'];
    if (!validTypes.includes(type)) {
      return messageResponse(INVALID_MISSING, "Invalid file type for preview", false, 400, null, res);
    }

    // Get file info
    const fileInfo = type === 'training' ? dataset.files.trainingFile : dataset.files.validationFile;
    if (!fileInfo) {
      return messageResponse(INVALID_MISSING, `${type} file not found in dataset`, false, 404, null, res);
    }

    // Check if file exists
    if (!fs.existsSync(fileInfo.path)) {
      return messageResponse(SERVER_ERROR, `${type} file not found on disk`, false, 404, null, res);
    }

    // Read and parse the file
    const fileContent = fs.readFileSync(fileInfo.path, 'utf8');
    const allLines = fileContent.split('\n').filter(line => line.trim());
    const maxLines = Math.min(parseInt(lines), allLines.length);
    const previewLines = allLines.slice(0, maxLines);

    // Parse JSONL content
    const parsedExamples = [];
    const parseErrors = [];

    previewLines.forEach((line, index) => {
      try {
        const parsed = JSON.parse(line);
        parsedExamples.push({
          lineNumber: index + 1,
          content: parsed
        });
      } catch (parseError) {
        parseErrors.push({
          lineNumber: index + 1,
          error: parseError.message,
          rawLine: line.substring(0, 100) + (line.length > 100 ? '...' : '')
        });
      }
    });

    return messageResponse(
      null,
      "Dataset preview retrieved successfully",
      true,
      200,
      {
        dataset: {
          id: dataset._id,
          name: dataset.name,
          version: dataset.version
        },
        preview: {
          fileType: type,
          totalLines: allLines.length,
          previewLines: maxLines,
          examples: parsedExamples,
          parseErrors: parseErrors,
          fileInfo: {
            filename: fileInfo.filename,
            size: fileInfo.size,
            totalExamples: fileInfo.examples
          }
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error previewing dataset:', error);
    return messageResponse(SERVER_ERROR, "Failed to preview dataset", false, 500, { error: error.message }, res);
  }
};

const createFineTuningJob = async (req, res) => {
  try {
    const { trainingDatasetId, suffix, epochs = 3, baseModel = 'gpt-4o-mini' } = req.body;

    if (!trainingDatasetId) {
      return messageResponse(INVALID_MISSING, "Training dataset ID is required", false, 400, null, res);
    }

    if (!suffix) {
      return messageResponse(INVALID_MISSING, "Model suffix is required", false, 400, null, res);
    }

    console.log('🚀 Creating fine-tuning job via AI API...');

    // Get training dataset from database
    const dataset = await TrainingDataset.findById(trainingDatasetId);
    if (!dataset) {
      return messageResponse(INVALID_MISSING, "Training dataset not found", false, 404, null, res);
    }

    if (!dataset.isReadyForTraining()) {
      return messageResponse(INVALID_MISSING, "Training dataset is not ready for training", false, 400, null, res);
    }

    // Check if files exist
    if (!fs.existsSync(dataset.files.trainingFile.path)) {
      return messageResponse(SERVER_ERROR, "Training file not found on disk", false, 500, null, res);
    }

    // Read training data
    const trainingData = fs.readFileSync(dataset.files.trainingFile.path, 'utf8')
      .split('\n')
      .filter(line => line.trim())
      .map(line => JSON.parse(line));

    // Read validation data if it exists
    let validationData = null;
    if (dataset.files.validationFile && fs.existsSync(dataset.files.validationFile.path)) {
      validationData = fs.readFileSync(dataset.files.validationFile.path, 'utf8')
        .split('\n')
        .filter(line => line.trim())
        .map(line => JSON.parse(line));
    }

    // Create model record first
    const model = new FineTunedModel({
      modelName: `${suffix} Model v${Date.now()}`,
      trainingDatasetId: dataset._id,
      trainingConfig: {
        suffix: suffix,
        epochs: parseInt(epochs),
        hyperparameters: {}
      },
      baseModel: baseModel,
      openAIJobId: 'pending', // Will be updated after OpenAI call
      openAIModelId: 'pending' // Will be updated when training completes
    });

    await model.save();

    const { openAIFineTuningService } = getServices();

    // Create the fine-tuning job with OpenAI
    const jobResult = await openAIFineTuningService.createFineTuningJob(trainingData, validationData, {
      suffix: suffix,
      hyperparameters: {
        n_epochs: parseInt(epochs)
      }
    });

    // Update model with OpenAI job details
    model.openAIJobId = jobResult.jobId;
    model.trainingResults.status = jobResult.status || 'validating_files';
    await model.save();

    console.log('✅ Fine-tuning job created via AI API');

    return messageResponse(
      null,
      "Fine-tuning job created successfully",
      true,
      200,
      {
        modelId: model._id,
        jobId: jobResult.jobId,
        status: jobResult.status,
        trainingDataset: {
          id: dataset._id,
          name: dataset.name,
          version: dataset.version
        },
        trainingFile: jobResult.trainingFileId,
        validationFile: jobResult.validationFileId,
        estimatedCompletionTime: "30-45 minutes",
        createdAt: model.createdAt
      },
      res
    );

  } catch (error) {
    console.error('❌ Error creating fine-tuning job:', error);
    return messageResponse(SERVER_ERROR, "Fine-tuning job creation failed", false, 500, { error: error.message }, res);
  }
};

const listFineTuningJobs = async (req, res) => {
  try {
    const {
      status,
      limit = 10,
      page = 1,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Build query
    const query = {};
    if (status) {
      query['trainingResults.status'] = status;
    }

    const models = await FineTunedModel.find(query)
      .populate('trainingDatasetId')
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip);

    const total = await FineTunedModel.countDocuments(query);

    return messageResponse(
      null,
      "Fine-tuning jobs retrieved successfully",
      true,
      200,
      {
        jobs: models.map(model => ({
          id: model._id,
          jobId: model.openAIJobId,
          modelName: model.modelName,
          status: model.trainingResults.status,
          baseModel: model.baseModel,
          trainingConfig: model.trainingConfig,
          trainingResults: model.trainingResults,
          trainingDataset: model.trainingDatasetId ? {
            id: model.trainingDatasetId._id,
            name: model.trainingDatasetId.name,
            version: model.trainingDatasetId.version
          } : null,
          createdAt: model.createdAt,
          completedAt: model.trainingResults.completedAt,
          isReadyForDeployment: model.isReadyForDeployment()
        })),
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error listing fine-tuning jobs:', error);
    return messageResponse(SERVER_ERROR, "Failed to list fine-tuning jobs", false, 500, { error: error.message }, res);
  }
};

const getFineTuningJobStatus = async (req, res) => {
  try {
    const { jobId } = req.params;

    // Find model by OpenAI job ID
    const model = await FineTunedModel.findOne({ openAIJobId: jobId })
      .populate('trainingDatasetId');

    if (!model) {
      return messageResponse(INVALID_MISSING, "Fine-tuning job not found", false, 404, null, res);
    }

    // Get latest status from OpenAI if job is still running
    if (['validating_files', 'running'].includes(model.trainingResults.status)) {
      try {
        const { openAIFineTuningService } = getServices();
        const openAIStatus = await openAIFineTuningService.getJobStatus(jobId);
        console.log(openAIStatus)

        // Update model if status changed
        if (openAIStatus.status !== model.trainingResults.status) {
          model.trainingResults.status = openAIStatus.status;

          if (openAIStatus.status === 'succeeded') {
            model.openAIModelId = openAIStatus.fineTunedModel;
            model.trainingResults.completedAt = openAIStatus.finishedAt;
            model.trainingResults.trainingTokens = openAIStatus.trainedTokens;
          } else if (openAIStatus.status === 'failed') {
            model.trainingResults.errorMessage = openAIStatus.error?.message || 'Training failed';
            model.status = 'failed';
          }

          await model.save();
        }
      } catch (openAIError) {
        console.warn('⚠️ Failed to get latest status from OpenAI:', openAIError.message);
      }
    }

    return messageResponse(
      null,
      "Fine-tuning job status retrieved successfully",
      true,
      200,
      {
        job: {
          id: model._id,
          jobId: model.openAIJobId,
          modelName: model.modelName,
          openAIModelId: model.openAIModelId,
          status: model.trainingResults.status,
          baseModel: model.baseModel,
          trainingConfig: model.trainingConfig,
          trainingResults: model.trainingResults,
          deploymentStatus: model.deploymentStatus,
          trainingDataset: model.trainingDatasetId ? {
            id: model.trainingDatasetId._id,
            name: model.trainingDatasetId.name,
            version: model.trainingDatasetId.version
          } : null,
          createdAt: model.createdAt,
          isReadyForDeployment: model.isReadyForDeployment()
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error getting fine-tuning job status:', error);
    return messageResponse(SERVER_ERROR, "Failed to get job status", false, 500, { error: error.message }, res);
  }
};

const listModels = async (req, res) => {
  try {
    const {
      status = 'active',
      deploymentStatus,
      limit = 10,
      page = 1,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

    // Build query
    const query = {};
    if (status !== 'all') {
      query.status = status;
    }
    if (deploymentStatus) {
      query.deploymentStatus = deploymentStatus;
    }

    const models = await FineTunedModel.find(query)
      .populate('trainingDatasetId')
      .sort(sort)
      .limit(parseInt(limit))
      .skip(skip);

    const total = await FineTunedModel.countDocuments(query);

    return messageResponse(
      null,
      "Models retrieved successfully",
      true,
      200,
      {
        models: models.map(model => ({
          id: model._id,
          modelName: model.modelName,
          openAIModelId: model.openAIModelId,
          baseModel: model.baseModel,
          version: model.version,
          description: model.description,
          status: model.status,
          deploymentStatus: model.deploymentStatus,
          deployedAt: model.deployedAt,
          trainingResults: model.trainingResults,
          performance: model.performance,
          trainingDataset: model.trainingDatasetId ? {
            id: model.trainingDatasetId._id,
            name: model.trainingDatasetId.name,
            version: model.trainingDatasetId.version
          } : null,
          createdAt: model.createdAt,
          isReadyForDeployment: model.isReadyForDeployment(),
          successRate: model.successRate
        })),
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / parseInt(limit))
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error listing models:', error);
    return messageResponse(SERVER_ERROR, "Failed to list models", false, 500, { error: error.message }, res);
  }
};

const getModel = async (req, res) => {
  try {
    const { id } = req.params;

    const model = await FineTunedModel.findById(id).populate('trainingDatasetId');
    if (!model) {
      return messageResponse(INVALID_MISSING, "Model not found", false, 404, null, res);
    }

    return messageResponse(
      null,
      "Model retrieved successfully",
      true,
      200,
      {
        model: {
          id: model._id,
          modelName: model.modelName,
          openAIModelId: model.openAIModelId,
          openAIJobId: model.openAIJobId,
          baseModel: model.baseModel,
          version: model.version,
          description: model.description,
          notes: model.notes,
          status: model.status,
          deploymentStatus: model.deploymentStatus,
          deployedAt: model.deployedAt,
          deployedBy: model.deployedBy,
          trainingConfig: model.trainingConfig,
          trainingResults: model.trainingResults,
          performance: model.performance,
          trainingDataset: model.trainingDatasetId ? {
            id: model.trainingDatasetId._id,
            name: model.trainingDatasetId.name,
            version: model.trainingDatasetId.version,
            description: model.trainingDatasetId.description,
            statistics: model.trainingDatasetId.statistics
          } : null,
          createdAt: model.createdAt,
          updatedAt: model.updatedAt,
          isReadyForDeployment: model.isReadyForDeployment(),
          successRate: model.successRate,
          trainingDuration: model.trainingDuration
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error getting model:', error);
    return messageResponse(SERVER_ERROR, "Failed to get model", false, 500, { error: error.message }, res);
  }
};

const deployModel = async (req, res) => {
  try {
    const { id } = req.params;
    const { _id: userId } = req.user;

    console.log('🚀 Deploying model via AI API...');

    // Find the model
    const model = await FineTunedModel.findById(id).populate('trainingDatasetId');
    if (!model) {
      return messageResponse(NOT_FOUND, "Model not found", false, 404, null, res);
    }

    if (!model.isReadyForDeployment()) {
      return messageResponse(
        CUSTOM,
        "Model is not ready for deployment. Training must be completed successfully.",
        false,
        400,
        null,
        res
      );
    }

    // Deploy the model (this will undeploy all others automatically)
    const deployedModel = await FineTunedModel.deployModel(id, userId);

    console.log('✅ Model deployed successfully via AI API');

    return messageResponse(
      null,
      "Model deployed successfully",
      true,
      200,
      {
        deployed: true,
        model: {
          id: deployedModel._id,
          modelName: deployedModel.modelName,
          openAIModelId: deployedModel.openAIModelId,
          version: deployedModel.version,
          deployedAt: deployedModel.deployedAt,
          deployedBy: deployedModel.deployedBy
        },
        trainingDataset: {
          id: deployedModel.trainingDatasetId._id,
          name: deployedModel.trainingDatasetId.name,
          version: deployedModel.trainingDatasetId.version
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error deploying model:', error);
    return messageResponse(SERVER_ERROR, "Model deployment failed", false, 500, { error: error.message }, res);
  }
};

const getDeployedModel = async (req, res) => {
  try {
    const deployedModel = await FineTunedModel.findDeployed();

    if (!deployedModel) {
      return messageResponse(
        null,
        "No model is currently deployed",
        true,
        200,
        {
          deployed: false,
          model: null,
          fallbackModel: 'gpt-4o-mini'
        },
        res
      );
    }

    return messageResponse(
      null,
      "Deployed model retrieved successfully",
      true,
      200,
      {
        deployed: true,
        model: {
          id: deployedModel._id,
          modelName: deployedModel.modelName,
          openAIModelId: deployedModel.openAIModelId,
          baseModel: deployedModel.baseModel,
          version: deployedModel.version,
          deployedAt: deployedModel.deployedAt,
          deployedBy: deployedModel.deployedBy,
          performance: deployedModel.performance,
          trainingDataset: deployedModel.trainingDatasetId ? {
            id: deployedModel.trainingDatasetId._id,
            name: deployedModel.trainingDatasetId.name,
            version: deployedModel.trainingDatasetId.version
          } : null
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error getting deployed model:', error);
    return messageResponse(SERVER_ERROR, "Failed to get deployed model", false, 500, { error: error.message }, res);
  }
};

const updateModel = async (req, res) => {
  try {
    const { id } = req.params;
    const { modelName, description, notes, version } = req.body;

    const model = await FineTunedModel.findById(id);
    if (!model) {
      return messageResponse(INVALID_MISSING, "Model not found", false, 404, null, res);
    }

    // Update allowed fields
    if (modelName !== undefined) model.modelName = modelName;
    if (description !== undefined) model.description = description;
    if (notes !== undefined) model.notes = notes;
    if (version !== undefined) model.version = version;

    model.updatedAt = new Date();
    await model.save();

    console.log(`📝 Model updated: ${model.modelName}`);

    return messageResponse(
      null,
      "Model updated successfully",
      true,
      200,
      {
        updated: true,
        model: {
          id: model._id,
          modelName: model.modelName,
          description: model.description,
          notes: model.notes,
          version: model.version,
          updatedAt: model.updatedAt
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error updating model:', error);
    return messageResponse(SERVER_ERROR, "Failed to update model", false, 500, { error: error.message }, res);
  }
};

const testModel = async (req, res) => {
  try {
    const { id } = req.params;
    const { testMessage = "Hello, can you tell me about your programs?", options = {} } = req.body;

    const model = await FineTunedModel.findById(id).populate('trainingDatasetId');
    if (!model) {
      return messageResponse(NOT_FOUND, "Model", false, 404, null, res);
    }

    if (!model.isReadyForDeployment()) {
      return messageResponse(
        CUSTOM,
        "Model is not ready for testing. Training must be completed successfully.",
        false,
        400,
        null,
        res
      );
    }

    console.log(`🧪 Testing model: ${model.modelName}`);

    try {
      const { openAIFineTuningService } = getServices();
      const startTime = Date.now();

      // Test the model with OpenAI
      const testResponse = await openAIFineTuningService.testModel(
        model.openAIModelId,
        testMessage,
        {
          maxTokens: options.maxTokens || 200,
          temperature: options.temperature || 0.7
        }
      );

      const responseTime = Date.now() - startTime;

      // Update model performance if test was successful
      if (testResponse && testResponse.choices && testResponse.choices.length > 0) {
        try {
          await model.updatePerformance(responseTime, true, testResponse.usage?.total_tokens || 0);
        } catch (perfError) {
          console.warn('⚠️ Failed to update model performance during test:', perfError.message);
        }
      }

      console.log('✅ Model test completed successfully');

      return messageResponse(
        null,
        "Model test completed successfully",
        true,
        200,
        {
          testPassed: true,
          model: {
            id: model._id,
            modelName: model.modelName,
            openAIModelId: model.openAIModelId,
            trainingDataset: model.trainingDatasetId ? {
              name: model.trainingDatasetId.name,
              version: model.trainingDatasetId.version
            } : null
          },
          test: {
            testMessage: testMessage,
            response: testResponse.choices[0].message.content,
            responseTime: responseTime,
            usage: testResponse.usage,
            timestamp: new Date()
          }
        },
        res
      );

    } catch (testError) {
      console.error('❌ Model test failed:', testError);

      return messageResponse(
        SERVER_ERROR,
        "Model test failed",
        false,
        500,
        {
          testPassed: false,
          model: {
            id: model._id,
            modelName: model.modelName,
            openAIModelId: model.openAIModelId
          },
          error: testError.message,
          timestamp: new Date()
        },
        res
      );
    }

  } catch (error) {
    console.error('❌ Error testing model:', error);
    return messageResponse(SERVER_ERROR, "Failed to test model", false, 500, { error: error.message }, res);
  }
};

const getSystemOverview = async (req, res) => {
  try {
    console.log('📊 Generating system overview...');

    // Get dataset statistics
    const totalDatasets = await TrainingDataset.countDocuments();
    const activeDatasets = await TrainingDataset.countDocuments({ status: 'active' });
    const archivedDatasets = await TrainingDataset.countDocuments({ status: 'archived' });

    // Get model statistics
    const totalModels = await FineTunedModel.countDocuments();
    const activeModels = await FineTunedModel.countDocuments({ status: 'active' });
    const deployedModel = await FineTunedModel.findDeployed();
    const succeededModels = await FineTunedModel.countDocuments({
      'trainingResults.status': 'succeeded'
    });
    const failedModels = await FineTunedModel.countDocuments({
      'trainingResults.status': 'failed'
    });
    const runningJobs = await FineTunedModel.countDocuments({
      'trainingResults.status': { $in: ['validating_files', 'running'] }
    });

    // Get recent activity
    const recentDatasets = await TrainingDataset.find({ status: 'active' })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('name version createdAt statistics.qualityScore');

    const recentModels = await FineTunedModel.find({ status: 'active' })
      .populate('trainingDatasetId', 'name version')
      .sort({ createdAt: -1 })
      .limit(5)
      .select('modelName trainingResults.status deploymentStatus createdAt');

    // Calculate total performance metrics
    const allModels = await FineTunedModel.find({ status: 'active' });
    const totalRequests = allModels.reduce((sum, model) => sum + (model.performance.totalRequests || 0), 0);
    const totalSuccessfulRequests = allModels.reduce((sum, model) => sum + (model.performance.successfulRequests || 0), 0);
    const averageSuccessRate = totalRequests > 0 ? (totalSuccessfulRequests / totalRequests) : 0;

    return messageResponse(
      null,
      "System overview retrieved successfully",
      true,
      200,
      {
        overview: {
          datasets: {
            total: totalDatasets,
            active: activeDatasets,
            archived: archivedDatasets
          },
          models: {
            total: totalModels,
            active: activeModels,
            succeeded: succeededModels,
            failed: failedModels,
            deployed: deployedModel ? 1 : 0
          },
          training: {
            runningJobs: runningJobs,
            successRate: succeededModels / Math.max(totalModels, 1)
          },
          performance: {
            totalRequests: totalRequests,
            successfulRequests: totalSuccessfulRequests,
            averageSuccessRate: averageSuccessRate
          },
          deployment: deployedModel ? {
            modelId: deployedModel._id,
            modelName: deployedModel.modelName,
            deployedAt: deployedModel.deployedAt,
            performance: deployedModel.performance
          } : null
        },
        recentActivity: {
          datasets: recentDatasets.map(dataset => ({
            id: dataset._id,
            name: dataset.name,
            version: dataset.version,
            qualityScore: dataset.statistics?.qualityScore,
            createdAt: dataset.createdAt
          })),
          models: recentModels.map(model => ({
            id: model._id,
            modelName: model.modelName,
            status: model.trainingResults.status,
            deploymentStatus: model.deploymentStatus,
            trainingDataset: model.trainingDatasetId ? {
              name: model.trainingDatasetId.name,
              version: model.trainingDatasetId.version
            } : null,
            createdAt: model.createdAt
          }))
        },
        timestamp: new Date()
      },
      res
    );

  } catch (error) {
    console.error('❌ Error getting system overview:', error);
    return messageResponse(SERVER_ERROR, "Failed to get system overview", false, 500, { error: error.message }, res);
  }
};

/**
 * MODEL CONFIGURATION MANAGEMENT
 */

/**
 * Get model configuration
 * GET /api/ai/models/:id/configuration
 */
const getModelConfiguration = async (req, res) => {
  try {
    const { id } = req.params;

    const model = await FineTunedModel.findById(id);
    if (!model) {
      return messageResponse(NOT_FOUND, "Model", false, 404, null, res);
    }

    const effectiveConfig = model.getEffectiveConfiguration();

    return messageResponse(
      null,
      "Model configuration retrieved successfully",
      true,
      200,
      {
        model: {
          id: model._id,
          modelName: model.modelName,
          deploymentStatus: model.deploymentStatus
        },
        configuration: effectiveConfig,
      },
      res
    );

  } catch (error) {
    console.error('❌ Error getting model configuration:', error);
    return messageResponse(SERVER_ERROR, "Failed to get model configuration", false, 500, { error: error.message }, res);
  }
};

/**
 * Update model configuration
 * PUT /api/ai/models/:id/configuration
 */
const updateModelConfiguration = async (req, res) => {
  try {
    const { id } = req.params;
    const configUpdate = req.body;

    const model = await FineTunedModel.findById(id);
    if (!model) {
      return messageResponse(INVALID_MISSING, "Model not found", false, 404, null, res);
    }

    // Validate configuration
    const { modelConfigurationService } = getServices();
    const validation = modelConfigurationService.validateConfiguration(configUpdate);

    if (!validation.valid) {
      return messageResponse(
        INVALID_MISSING,
        "Invalid configuration",
        false,
        400,
        {
          errors: validation.errors,
          warnings: validation.warnings
        },
        res
      );
    }

    // Update configuration
    await model.updateConfiguration(configUpdate);

    console.log(`🎛️ Model configuration updated: ${model.modelName}`);

    return messageResponse(
      null,
      "Model configuration updated successfully",
      true,
      200,
      {
        updated: true,
        model: {
          id: model._id,
          modelName: model.modelName
        },
        configuration: model.getEffectiveConfiguration(),
        validation: {
          warnings: validation.warnings
        }
      },
      res
    );

  } catch (error) {
    console.error('❌ Error updating model configuration:', error);
    return messageResponse(SERVER_ERROR, "Failed to update model configuration", false, 500, { error: error.message }, res);
  }
};

module.exports = {
  // Training Data Management
  generateTrainingData,
  listTrainingDatasets,
  getTrainingDataset,
  downloadDatasetFile,
  previewDataset,

  // Fine-Tuning Job Management
  createFineTuningJob,
  listFineTuningJobs,
  getFineTuningJobStatus,

  // Model Management
  listModels,
  getModel,
  deployModel,
  getDeployedModel,
  updateModel,
  testModel,

  // Model Configuration Management
  getModelConfiguration,
  updateModelConfiguration,

  // Analytics
  getSystemOverview,
};
