const mongoose = require('mongoose');

// Import existing models (no changes to existing system)
const { ChatSession } = require('../models/chatbotModels');
const { ChatMessage } = require('../models/chatbotModels');
const { ChatViolation } = require('../models/chatbotModels');
const { College } = require('../models/college');

// Import new services
const OpenAIFineTuningService = require('./openAIFineTuningService');
const FineTunedModel = require('../models/fineTunedModel');
const ModelConfigurationService = require('./modelConfigurationService');
const { cacheService, CACHE_KEYS, CACHE_TTL } = require('./cacheService');
const { performanceMonitor } = require('./performanceMonitorService');

/**
 * Enhanced Message Processing Service
 * Handles message processing with fine-tuned models
 * Integrates with existing chatbot infrastructure without breaking changes
 */
class MessageProcessingService {
  constructor() {
    this.fineTuningService = new OpenAIFineTuningService();
    this.configurationService = new ModelConfigurationService();
    this.defaultModel = 'gpt-4o-mini'; // Fallback model
  }

  /**
   * Process incoming user message
   * @param {string} sessionId - Chat session ID
   * @param {string} message - User message
   * @param {Object} options - Processing options
   * @returns {Promise<Object>} - Processing result
   */
  async processMessage(sessionId, message, options = {}) {
    const {
      includeContext = true,
      maxTokens = 500,
      temperature = 0.7
    } = options;

    console.log(`💬 Processing message for session: ${sessionId}`);
    console.log(`📝 Message: "${message}"`);

    // Start performance monitoring
    const endTiming = performanceMonitor.startTiming('response');

    try {
      // Get session information
      const session = await this.getSessionInfo(sessionId);
      if (!session) {
        throw new Error('Session not found');
      }

      // Get college information for context (with caching)
      const college = await cacheService.wrap(
        CACHE_KEYS.COLLEGE(session.collegeId),
        async () => {
          const collegeData = await College.findById(session.collegeId).lean();
          return collegeData;
        },
        CACHE_TTL.COLLEGE
      );

      if (!college) {
        throw new Error('College not found');
      }

      // Build conversation context
      const conversationContext = includeContext 
        ? await this.buildConversationContext(sessionId)
        : [];

      // Create system prompt with college context
      const systemPrompt = this.buildSystemPrompt(college);

      // Build messages for fine-tuned model
      const messages = [
        { role: 'system', content: systemPrompt },
        ...conversationContext,
        { role: 'user', content: message }
      ];

      // Get response from fine-tuned model
      const response = await this.getModelResponse(messages, {
        maxTokens,
        temperature,
        college
      });

      // Save user message
      await this.saveMessage(sessionId, {
        messageType: 'user',
        content: message,
        responseType: null
      });

      // Save bot response
      await this.saveMessage(sessionId, {
        messageType: 'bot',
        content: response.content,
        responseType: response.type,
        modelUsed: response.modelUsed,
        tokenUsage: response.usage
      });

      // Update session message count
      await this.updateSessionStats(sessionId);

      console.log(`✅ Message processed successfully`);
      console.log(`🤖 Response type: ${response.type}`);
      console.log(`📊 Tokens used: ${response.usage?.total_tokens || 'N/A'}`);

      // End performance monitoring
      const responseTime = endTiming({
        success: true,
        modelUsed: response.modelUsed,
        responseType: response.type
      });

      return {
        success: true,
        response: response.content,
        responseType: response.type,
        modelUsed: response.modelUsed,
        usage: response.usage,
        sessionId: sessionId,
        responseTime: responseTime
      };

    } catch (error) {
      console.error('❌ Message processing failed:', error);

      // End performance monitoring for error case
      const responseTime = endTiming({
        success: false,
        error: error.message
      });

      // Save error message
      await this.saveMessage(sessionId, {
        messageType: 'bot',
        content: 'I apologize, but I encountered an error processing your message. Please try again.',
        responseType: 'error',
        errorDetails: error.message
      });

      return {
        success: false,
        error: error.message,
        response: 'I apologize, but I encountered an error processing your message. Please try again.',
        responseType: 'error',
        sessionId: sessionId,
        responseTime: responseTime
      };
    }
  }

  /**
   * Get session information (with caching)
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object>} - Session data
   */
  async getSessionInfo(sessionId) {
    try {
      return await cacheService.wrap(
        CACHE_KEYS.SESSION(sessionId),
        async () => {
          const session = await ChatSession.findById(sessionId).lean();
          return session;
        },
        CACHE_TTL.SESSION
      );
    } catch (error) {
      console.error('❌ Error getting session info:', error);
      return null;
    }
  }

  /**
   * Build conversation context from recent messages (with caching)
   * @param {string} sessionId - Session ID
   * @returns {Promise<Array>} - Context messages
   */
  async buildConversationContext(sessionId) {
    try {
      return await cacheService.wrap(
        CACHE_KEYS.CONVERSATION_CONTEXT(sessionId),
        async () => {
          const recentMessages = await ChatMessage.find({
            sessionId: new mongoose.Types.ObjectId(sessionId),
            messageType: { $in: ['user', 'bot'] }
          })
          .sort({ createdAt: -1 })
          .limit(10) // Use fixed limit for better caching
          .lean();

          // Reverse to get chronological order
          const contextMessages = recentMessages.reverse().map(msg => ({
            role: msg.messageType === 'user' ? 'user' : 'assistant',
            content: msg.content
          }));

          console.log(`📚 Built context with ${contextMessages.length} messages`);
          return contextMessages;
        },
        CACHE_TTL.CONVERSATION_CONTEXT
      );
    } catch (error) {
      console.error('❌ Error building context:', error);
      return [];
    }
  }

  /**
   * Build system prompt with college context
   * @param {Object} college - College information
   * @returns {string} - System prompt
   */
  buildSystemPrompt(college) {
    const basePrompt = `You are a helpful educational assistant for ${college.name}.`;
    
    const contextPrompt = `
You provide information about:
- ${college.name}'s courses, programs, and academic offerings
- Campus locations and facilities
- Admission requirements and application processes
- Career guidance and educational pathways
- Skills development and professional growth
- Industry sectors and career opportunities

Always focus on providing accurate, helpful information related to ${college.name} and educational guidance.
Be friendly, professional, and supportive in your responses.
If you don't have specific information, acknowledge this and suggest contacting ${college.name} directly.
`;

    return basePrompt + contextPrompt;
  }

  /**
   * Get response from fine-tuned model
   * @param {Array} messages - Conversation messages
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - Model response
   */
  async getModelResponse(messages, options = {}) {
    const { college } = options;
    const startTime = Date.now();

    try {
      // Try to get deployed model from cache first, then database
      const deployedModelData = await cacheService.wrap(
        CACHE_KEYS.DEPLOYED_MODEL,
        async () => {
          const model = await FineTunedModel.findDeployed();
          if (!model) return null;

          // Pre-compute configuration to avoid repeated calls
          return {
            openAIModelId: model.openAIModelId,
            _id: model._id,
            modelName: model.modelName,
            configuration: model.getEffectiveConfiguration()
          };
        },
        CACHE_TTL.DEPLOYED_MODEL
      );

      let modelToUse = deployedModelData?.openAIModelId || this.defaultModel;
      let responseType = deployedModelData ? 'fine-tuned' : 'fallback';

      // Get effective configuration (pre-computed or default)
      let effectiveConfig;
      if (deployedModelData) {
        effectiveConfig = deployedModelData.configuration;
        console.log('🎛️ Using cached model configuration:', effectiveConfig.responseStyle);
      } else {
        effectiveConfig = this.configurationService.getDefaultConfiguration();
        console.log('🎛️ Using default fallback configuration');
      }

      // Override with any options provided (for backward compatibility)
      if (options.maxTokens !== undefined) effectiveConfig.maxTokens = options.maxTokens;
      if (options.temperature !== undefined) effectiveConfig.temperature = options.temperature;

      console.log(`🤖 Using model: ${modelToUse} (${responseType})`);
      console.log(`🎛️ Configuration: temp=${effectiveConfig.temperature}, maxTokens=${effectiveConfig.maxTokens}, style=${effectiveConfig.responseStyle}`);

      // Prepare system message with configuration
      const systemMessage = this.configurationService.getSystemMessage(effectiveConfig, { college });
      console.log("comepleted system message");

      // Prepare messages with system prompt and context limit
      const messagesWithSystem = [
        { role: 'system', content: systemMessage },
        ...messages.slice(-effectiveConfig.maxContextMessages) // Limit context based on config
      ];

      // Make API call with retry logic
      let response = null;
      let attempts = 0;

      while (attempts < effectiveConfig.maxRetries && !response) {
        try {
          response = await this.fineTuningService.openai.chat.completions.create({
            model: modelToUse,
            messages: messagesWithSystem,
            max_tokens: effectiveConfig.maxTokens,
            temperature: effectiveConfig.temperature,
            top_p: effectiveConfig.topP,
            presence_penalty: effectiveConfig.presencePenalty,
            frequency_penalty: effectiveConfig.frequencyPenalty
          });
          console.log("got response form opneAI")
          break;
        } catch (apiError) {
          attempts++;
          console.warn(`⚠️ API attempt ${attempts} failed:`, apiError.message);
          
          // If fine-tuned model fails, fallback to default
          if (responseType === 'fine-tuned' && attempts === 1) {
            modelToUse = this.defaultModel;
            responseType = 'fallback';
            console.log(`🔄 Falling back to default model: ${modelToUse}`);
          }
          
          if (attempts >= effectiveConfig.maxRetries) {
            throw apiError;
          }
          
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempts));
        }
      }

      if (!response || !response.choices || response.choices.length === 0) {
        throw new Error('No response from model');
      }

      const content = response.choices[0].message.content;
      const usage = response.usage;

      // Update model performance metrics if using deployed model
      if (deployedModelData && responseType === 'fine-tuned') {
        try {
          const responseTime = Date.now() - startTime;
          // Update performance asynchronously to avoid blocking response
          setImmediate(async () => {
            try {
              const model = await FineTunedModel.findById(deployedModelData._id);
              if (model) {
                await model.updatePerformance(
                  responseTime,
                  true, // success
                  usage?.total_tokens || 0
                );
                // Invalidate cache after performance update
                cacheService.delete(CACHE_KEYS.DEPLOYED_MODEL);
              }
            } catch (perfError) {
              console.warn('⚠️ Failed to update model performance:', perfError.message);
            }
          });
        } catch (perfError) {
          console.warn('⚠️ Failed to schedule performance update:', perfError.message);
        }
      }

      return {
        content: content,
        type: responseType,
        modelUsed: modelToUse,
        usage: usage
      };

    } catch (error) {
      console.error('❌ Model response error:', error);
      
      // Return fallback response
      return {
        content: `I apologize, but I'm having trouble processing your request right now. Please contact ${college?.name || 'the college'} directly for assistance, or try again in a moment.`,
        type: 'error',
        modelUsed: 'fallback',
        usage: null
      };
    }
  }

  /**
   * Save message to database
   * @param {string} sessionId - Session ID
   * @param {Object} messageData - Message data
   * @returns {Promise<Object>} - Saved message
   */
  async saveMessage(sessionId, messageData) {
    try {
      const message = new ChatMessage({
        sessionId: new mongoose.Types.ObjectId(sessionId),
        messageType: messageData.messageType,
        content: messageData.content,
        responseType: messageData.responseType,
        metadata: {
          modelUsed: messageData.modelUsed,
          tokenUsage: messageData.tokenUsage,
          errorDetails: messageData.errorDetails
        },
        createdAt: new Date()
      });

      const savedMessage = await message.save();

      // Invalidate conversation context cache when new messages are added
      cacheService.delete(CACHE_KEYS.CONVERSATION_CONTEXT(sessionId));

      return savedMessage;

    } catch (error) {
      console.error('❌ Error saving message:', error);
      throw error;
    }
  }

  /**
   * Update session statistics
   * @param {string} sessionId - Session ID
   * @returns {Promise<void>}
   */
  async updateSessionStats(sessionId) {
    try {
      await ChatSession.findByIdAndUpdate(sessionId, {
        $inc: { messageCount: 1 },
        lastActivity: new Date()
      });
    } catch (error) {
      console.error('❌ Error updating session stats:', error);
    }
  }

  /**
   * Get conversation history
   * @param {string} sessionId - Session ID
   * @param {number} limit - Message limit
   * @returns {Promise<Array>} - Message history
   */
  async getConversationHistory(sessionId, limit = 50) {
    try {
      const messages = await ChatMessage.find({
        sessionId: new mongoose.Types.ObjectId(sessionId)
      })
      .sort({ createdAt: 1 })
      .limit(limit)
      .lean();

      return messages.map(msg => ({
        id: msg._id,
        type: msg.messageType,
        content: msg.content,
        responseType: msg.responseType,
        timestamp: msg.createdAt,
        metadata: msg.metadata
      }));

    } catch (error) {
      console.error('❌ Error getting conversation history:', error);
      return [];
    }
  }
}

module.exports = MessageProcessingService;
