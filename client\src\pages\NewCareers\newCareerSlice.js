import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { decodeBase64ToBlob } from 'src/utils';
import { get } from 'lodash';
import axiosInstance from '../../utils/axiosInstance';

// const { default: courses } = require("src/_mock/courses");

export const postCareer = createAsyncThunk('newcareers/addCareer', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "careers/add",
            method: "POST",
            data
        })
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})
export const importCareer = createAsyncThunk('newcareers/importCareer', async (data, { rejectWithValue }) => {
    const bodyFormData = new FormData();
    bodyFormData.append('file', data);
    try {
        const response = await axiosInstance({
            url: "careers/import",
            method: "POST",
            data: bodyFormData,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        })
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response.data)
    }
})
export const importCareersMeta = createAsyncThunk('newcareers/importCareersMeta', async (data, { rejectWithValue }) => {
    const bodyFormData = new FormData();
    bodyFormData.append('file', data?.selectedMetaFile);
    bodyFormData.append('regionId', data?.regionId);
    try {
        const response = await axiosInstance({
            url: "careers/importMetadata",
            method: "POST",
            data: bodyFormData,
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        })
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response.data)
    }
})
export const exportCareer = createAsyncThunk('newcareers/exportCareer', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "careers/export",
            method: "GET",
            responseType: 'blob'
        })
        if (get(response, 'status') === 200 || response?.status === '200') {
            // Create a download link
            const url = URL.createObjectURL(response?.data);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'career_export.xlsx';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        data?.notify();
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response.data)
    }
})
export const exportMetaFile = createAsyncThunk('newcareers/exportMetaFile', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: `careers/exportMetadata?regionId=${data?.regionId}`,
            method: "GET",
            responseType: 'blob'
        })
        if (get(response, 'status') === 200 || response?.status === '200') {
            // Create a download link
            const url = URL.createObjectURL(response?.data);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'career_metadata_export.xlsx';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }
        data?.notify();
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response.data)
    }
})
export const removeCareer = createAsyncThunk('newcareers/removeCareer', async ({ id, type }, { rejectWithValue }) => {
    const data = {
        id
    }
    try {
        const response = await axiosInstance({
            url: "careers/remove",
            method: "DELETE",
            data
        })
        response.data.id = id && id
        response.data.type = type && type
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})
export const updateCareer = createAsyncThunk('newcareers/updateCareer', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "careers/update",
            method: "PUT",
            data
        })
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})

export const getNestedCareers = createAsyncThunk('newcareers/getNestedCareers', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "/careers/v2/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const getCareersByType = createAsyncThunk('newcareers/getCareersByType', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: `/careers/v2/getCareersByType?type=${data}`,
            method: "GET",
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

const newCareerSlice = createSlice({
    name: 'careers',
    initialState: {
        newCareers: [],
        importLoading: false,
        exportLoading: false,
        exportMetaLoading: false,
    },
    reducers: {
        addCareer: (state, action) => {
            state.careers.push(action.payload)
        },
        updateCareers: (state, action) => {

        },
        deleteCareer: (state, action) => {
            state.newCareers = state.newCareers.filter(career => career.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getNestedCareers.pending]: (state) => {
            state.status = "pending"
        },
        [getNestedCareers.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data;
            state.newCareers = data
        },
        [getNestedCareers.rejected]: (state, action) => {
            state.status = "failed"
            state.error = action.payload
        },
        [importCareer.pending]: (state) => {
            state.importLoading = true;
        },
        [importCareer.fulfilled]: (state, action) => {
            state.importLoading = false;
        },
        [importCareer.rejected]: (state, action) => {
            state.importLoading = false;
        },
        [importCareersMeta.pending]: (state) => {
            state.importLoading = true;
        },
        [importCareersMeta.fulfilled]: (state, action) => {
            state.importLoading = false;
        },
        [importCareersMeta.rejected]: (state, action) => {
            state.importLoading = false;
        },
        [exportCareer.pending]: (state) => {
            state.exportLoading = true;
        },
        [exportCareer.fulfilled]: (state, action) => {
            state.exportLoading = false;
        },
        [exportCareer.rejected]: (state, action) => {
            state.exportLoading = false;
        },
        [exportMetaFile.pending]: (state) => {
            state.exportMetaLoading = true;
        },
        [exportMetaFile.fulfilled]: (state, action) => {
            state.exportMetaLoading = false;
        },
        [exportMetaFile.rejected]: (state, action) => {
            state.exportMetaLoading = false;
        },
        [removeCareer.fulfilled]: (state, action) => {
            const { id, type } = action.payload;
            
            if (type === 'unit_group') {
                // Remove from top level
                state.newCareers = state.newCareers.filter(unit => unit._id !== id);
            } else if (type === 'broad_career') {
                // Remove from each unit's `careers` array
                state.newCareers = state.newCareers.map(unit => ({
                    ...unit,
                    careers: (unit.careers || []).filter(broad => broad._id !== id)
                }));
            } else if (type === 'specialised_role') {
                // Remove from each broad's `specialisedRoles` array inside each unit
                state.newCareers = state.newCareers.map(unit => ({
                    ...unit,
                    careers: (unit.careers || []).map(broad => ({
                        ...broad,
                        specialisedRoles: (broad.specialisedRoles || []).filter(specialised => specialised._id !== id)
                    }))
                }));
            }
        }
    }
})
export const { addCareer, updateCareers, deleteCareer } = newCareerSlice.actions;
export default newCareerSlice.reducer