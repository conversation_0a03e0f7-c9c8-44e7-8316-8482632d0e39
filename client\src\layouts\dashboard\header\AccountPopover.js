import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router';
import Cookies from 'universal-cookie';
// @mui
import PersonIcon from '@mui/icons-material/Person';
import { Avatar, Box, Divider, IconButton, MenuItem, Button, Popover, Stack, Typography } from '@mui/material';
import { alpha } from '@mui/material/styles';
import axiosInstance from 'src/utils/axiosInstance';
// mocks_
import { useDispatch, useSelector } from 'react-redux';
import { Icon } from '@iconify/react';
import { logoutUser } from '../../../pages/Auth/loginSlice';
import { APP_ROUTER_BASE_URL } from '../../../utils';
import { getColleges } from '../../../pages/Colleges/collegesSlice';
import { addSelectedCollege, addUserPermissions, removeSelectedCollege, removeUserPermissions } from '../../../Redux/selectedCollegeSlice';
import useAuth from '../../../hooks/useAuth';

// ----------------------------------------------------------------------

export default function AccountPopover() {
  const navigate = useNavigate()
  const [open, setOpen] = useState(null);
  const [Colleges, setColleges] = useState([])
  const [selectedCollege, setSelectedCollege] = useState(null);
  const dispatch = useDispatch()
  const collegeStates = useSelector(state => state.colleges)
  const selectedCollegeStates = useSelector(state => state.selectedCollege)
  const { colleges, status, error } = collegeStates
  const { selectedCollege: SelectedCollege, currentUserPermissions } = selectedCollegeStates
  const handleOpen = (event) => {
    setOpen(event.currentTarget);
  };
  const {user} = useAuth()
  useEffect(() => {
    if(user){
      if(user?.role === 1|| user?.role === 2 || user?.role === 3){
        dispatch(getColleges())
      }
    }
  }, [])
  const handleRemoveSelectedCollege = () => {
    setSelectedCollege(null)
    dispatch(removeSelectedCollege())
    localStorage.removeItem('selectedCollege')
    handleClose()
  }
  // console.log('filteredCollege',filteredCollege);
  useEffect(() => {
    const filteredCollege = JSON.parse(localStorage.getItem('selectedCollege'));
    if (filteredCollege) {
      setSelectedCollege(filteredCollege);
      dispatch(addSelectedCollege(filteredCollege))
    }
  }, []);
  const handleCollegeChange = (college) => {
    localStorage.setItem('selectedCollege', JSON.stringify(college));
    setSelectedCollege(college)
    dispatch(addSelectedCollege(college))
    handleClose()
  };

  const setCollegePermissions = async ()=>{
   try{
    const response = await axiosInstance({
      url: "/colleges/permissions",
      method: "GET",
      params: {
        collegeId: selectedCollege?._id,
      },
    })
    localStorage.setItem('currentUserPermissions', JSON.stringify(response?.data?.data))
    dispatch(addUserPermissions(response?.data?.data))
   }catch(error){
    console.log('error', error);
   }
  }
  useEffect(() => {
    if(user?.role === 2){
      if(selectedCollege){
        setCollegePermissions()
      }else{
        localStorage.removeItem('currentUserPermissions')
        dispatch(removeUserPermissions())
      }
    }
  }, [selectedCollege])
  

  const handleClose = () => {
    setOpen(null);
  }

  useEffect(() => {
    if (!!colleges) {
      setColleges([...colleges])
    }
  }, [colleges])
  const handleLogout = () => {
    // dispatch(logoutUser())
    navigate(`${APP_ROUTER_BASE_URL}login`)
    const cookies = new Cookies()
    // cookies.remove("token", { path: `${APP_ROUTER_BASE_URL}` })
    cookies.remove("token", { path: '/' })
    handleClose()
  }
  return (
    <>
      <Button
        onClick={handleOpen}
        sx={{
          px: 2,
          ...(open && {
            '&:before': {
              zIndex: 1,
              content: "''",
              width: '100%',
              height: '100%',
              borderRadius: '8px',
              position: 'absolute',
              bgcolor: (theme) => alpha(theme.palette.grey[600], 0.1),
            },
          }),
        }}
      >
       {/* { `${(selectedCollege && selectedCollege?.name || "Select College")} ${<Icon icon="eva:arrow-down-fill" />}` } */}
       {(selectedCollege && selectedCollege?.name || "Select College")}
        <Icon style={{marginLeft:6}} width='16' icon={open ? "eva:arrow-right-fill" : "eva:arrow-down-fill"} />
        {/* <Icon style={{marginLeft:6}} width='16' icon="flat-color-icons:clear-filters" /> */}
      </Button>
      {/* <IconButton
        onClick={handleOpen}
        sx={{
          p: 0,
          ...(open && {
            '&:before': {
              zIndex: 1,
              content: "''",
              width: '100%',
              height: '100%',
              borderRadius: '50%',
              position: 'absolute',
              bgcolor: (theme) => alpha(theme.palette.grey[900], 0.8),
            },
          }),
        }}
      >
        <Avatar >
          <PersonIcon fontSize='large' />
        </Avatar>
      </IconButton> */}

      <Popover
        open={Boolean(open)}
        anchorEl={open}
        onClose={handleClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
        PaperProps={{
          sx: {
            p: 0,
            mt: 1.5,
            ml: 0.75,
            minWidth: 180,
            '& .MuiMenuItem-root': {
              typography: 'body2',
              borderRadius: 0.75,
            },
          },
        }}
      >
        <Box sx={{ my: 1.5, px: 2.5 }}>
          <Typography variant="subtitle2" noWrap>
            {"Select College"}
          </Typography>
        </Box>

        <Divider sx={{ borderStyle: 'dashed' }} />

        <Stack sx={{ p: 1 }}>
          {Colleges.map((college) => (
            <MenuItem key={college.name} onClick={() => handleCollegeChange(college)}>
              {college.name}
            </MenuItem>
          ))}
        </Stack>

        {selectedCollege &&
        <Divider sx={{ borderStyle: 'dashed' }} />}

        {/* <MenuItem onClick={() => {
          navigate("/dashboard/app")
          localStorage.setItem("Role", 1)
          handleClose()
        }} sx={{ m: 1 }}>
          Super Admin
        </MenuItem>
        <MenuItem onClick={() => {
          localStorage.setItem("Role", 2)
          navigate("/dashboard/careers")
          handleClose()
        }} sx={{ m: 1 }}>
          College Admin
        </MenuItem> */}
        {selectedCollege &&
         <MenuItem onClick={handleRemoveSelectedCollege} sx={{ m: 1, color:'red' }}>
          Remove College
        </MenuItem> }
      </Popover>
    </>
  );
}
