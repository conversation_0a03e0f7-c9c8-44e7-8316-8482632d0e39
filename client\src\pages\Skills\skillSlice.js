import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance';

// const { default: courses } = require("src/_mock/courses");
export const getSkills = createAsyncThunk('skills/getSkills', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "lmiSkillsAbilities/get",
            method: "GET",
            params : {
                category : "skill"
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const getSkillsById = createAsyncThunk('skills/getSkillsById', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "lmiSkillsAbilities/getByID",
            method: "GET",
            params : {
                id : data.id
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const getSubRadarCat = createAsyncThunk('radar-category/getSubRadarCat', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "radar-category/getSubRadarCat",
            method: "GET",
            params : {
                id : data.id
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const updateSkill = createAsyncThunk('skills/update', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "lmiSkillsAbilities/update",
            method: "PUT",
            data
        })
        return response.data
    } catch (error) {
        return rejectWithValue(error)
    }
})

const skillSlice = createSlice({
    name:'skills',
    initialState:{
        skills : [],
        skillId : {},
        subRadarCat:{},
        status: 'idle', // 'idle' | 'pending' | 'succeeded' | 'failed',
        error: null,
    },
    reducers:{
        // updateSkills : (state, action) =>{},
    },
    extraReducers: {
        [getSkills.pending]: (state) => {
            state.status = "pending"
        },
        [getSkills.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data;
            state.skills = data
        },
        [getSkills.rejected]: (state, action) => {
            state.status = "failed"
            state.error = action.payload
        },
        [getSkillsById.pending]: (state) => {
            // state.status = "pending"
        },
        [getSkillsById.fulfilled]: (state, action) => {
            // state.status = "succeeded"
            const data = action.payload?.data;
            state.skillId = data
        },
        [getSkillsById.rejected]: (state, action) => {
            // state.status = "failed"
            state.error = action.payload
        },
        [updateSkill.pending]: (state) => {
            // state.status = "pending"
        },
        [updateSkill.fulfilled]: (state, action) => {
            // state.status = "succeeded"
            // const data = action.payload?.data;
            // state.skillId = data
        },
        [updateSkill.rejected]: (state, action) => {
            // state.status = "failed"
            state.error = action.payload
        },
        [getSubRadarCat.pending]: (state) => {
            // state.status = "pending"
        },
        [getSubRadarCat.fulfilled]: (state, action) => {
            // state.status = "succeeded"
            const data = action.payload?.data;
            state.subRadarCat = data
        },
        [getSubRadarCat.rejected]: (state, action) => {
            // state.status = "failed"
            state.error = action.payload
        },
    }
})
export const {updateSkills} = skillSlice.actions;
export default skillSlice.reducer