import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
    Box,
    Button,
    Card,
    Container,
    FormControl,
    Grid,
    Stack,
    Typography
} from '@mui/material';
// @mui
import { useFormik } from 'formik';
import { LoadingButton } from '@mui/lab';
import useLoading from '../../hooks/useLoading';
import SelectComponent from '../../components/SelectComponent';
import { addRadarSubCategoryValidationSchema } from '../../utils/validationSchemas';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { CancelButton } from '../../utils/cssStyles';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import { getRadarSubCategories, postRadarSubCategory, updateRadarSubCategory, removeRadarSubCategory} from './RadarSubCategorySlice';
import { getRadarCategories } from '../RadarCategory/RadarCategorySlice';

// ----------------------------------------------------------------------
export const RADAR_CATEGORY_TABLE_HEAD = [
    { id: 'name', label: 'Radar Sub-Category', alignRight: false },
    { id: 'radarCategory', label: 'Radar Category', alignRight: false },
    { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderRadarSubCategoryCells = ['name', 'radarCategoryName']

const RadarSubCategory = () => {
    const dispatch = useDispatch()
    const RadarSubCategoryState = useSelector(state => state.radarSubCategories)
    const RadarCategoryState = useSelector(state => state.radarCategories.radarCategories)
    const [RadarCategory, setRadarCategory] = useState([]);
    const [RadarSubCategory, setRadarSubCategory] = useState([]);
    const [loading, setLoading] = useState(false)
    const [editRadarCategoryDetails, setEditRadarSubCategoryDetails] = useState('');
    const { radarSubCategories, status, error } = RadarSubCategoryState;
    const dataLoading = useLoading(status)

    const addRadarSubCategoryformik = useFormik({
        initialValues: {
            title: '',
            radarCategory: ''
        },
        onSubmit: (values, {resetForm}) => {
            setLoading(true)
            let radarCategoryName = '';
            for(let i=0; i < RadarCategory.length; i+=1){
                if(values?.radarCategory === RadarCategory[i]?._id){
                    radarCategoryName = RadarCategory[i].name;
                }
            }
            let data = {
                name: values.title,
                radarCategoryId: values.radarCategory,
                radarCategoryName
            }
            if(editRadarCategoryDetails){
                data = {
                    ...data,
                    id: editRadarCategoryDetails._id ? editRadarCategoryDetails._id : editRadarCategoryDetails.id
            }}
            dispatch(editRadarCategoryDetails? updateRadarSubCategory(data): postRadarSubCategory(data)).then(res => {
                if (res?.payload?.success || res?.payload?.data?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: `Succesfully ${editRadarCategoryDetails ? 'Updated' : 'Added'} RadarCategory`
                    }))
                }
                const error = res?.payload?.response?.data
                if (error?.success === false) {
                    console.log(error?.msg)
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error?.msg || "Something went wrong!"
                    }))
                }
            }).finally(() => {
                setLoading(false)
                setEditRadarSubCategoryDetails(''); 
            })
            resetForm({values: ''})
        },
        validationSchema: addRadarSubCategoryValidationSchema
    })

    const handleFilterSearch = (event) => {
        const filteredRadarSubCategory = RadarSubCategory.filter(category => category.name?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            category.redarCategory?.toLowerCase().includes(event.target.value.toLowerCase()))
        return filteredRadarSubCategory
    };

    const handleDeleteRedarSubSector = (item, handleOpenBackdrop, handleCloseBackdrop) => {
        handleOpenBackdrop();
        const data = {
            id: item.id ? item.id : item._id
        }
        dispatch(removeRadarSubCategory(data)).then(res => {
            if (res?.payload?.success) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Radar Sub-Category Deleted Succesfully"
                }))
            } 
            const error = res?.payload?.response?.data
            if(error.success === false) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: error?.message || "Something went wrong"
                }))
                console.log('Something went wrong!!', res)
            }
        }).finally(()=>{
            handleCloseBackdrop();
        })
    }

    const editRadarSubCategory = (item) => {
        setEditRadarSubCategoryDetails(item);
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }

    const handleCancel = () => {
        setEditRadarSubCategoryDetails('');
        addRadarSubCategoryformik.setValues({
            title: ''
        })
    }
    useEffect(()=>{
        dispatch(getRadarSubCategories());
        dispatch(getRadarCategories());
    }, [])
    useEffect(() => {
        if(RadarCategoryState){
            setRadarCategory([...RadarCategoryState])
        }
    }, [RadarCategoryState])

    useEffect(() => {
        if(radarSubCategories){
            setRadarSubCategory([...radarSubCategories])
        }
    }, [radarSubCategories])

    useEffect(() => {
        addRadarSubCategoryformik.setValues({
            ...addRadarSubCategoryformik.values,
            title: editRadarCategoryDetails?.name,
            radarCategory: editRadarCategoryDetails?.radarCategoryId
        })
    }, [editRadarCategoryDetails])

    return (
        <>
            <Helmet>
                <title> Radar Sub-Categories | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Grid container justifyContent={'space-between'}>
                    <Grid item lg={7}>
                        <Typography variant="h4" gutterBottom mb={3}>
                            Radar Sub-Categories
                        </Typography>
                        <DataTable
                            deleteDescription={"Are you sure want to delete this redar sub-category ?"}
                            deleteTitle={"Delete Redar Sub-Category ?"}
                            loading={dataLoading}
                            TableHead={RADAR_CATEGORY_TABLE_HEAD}
                            TableData={RadarSubCategory}
                            filterSearch={handleFilterSearch}
                            searchLable={"Search..."}
                            handleEdit={editRadarSubCategory}
                            renderCells={renderRadarSubCategoryCells}
                            handleDelete={handleDeleteRedarSubSector}
                            pagination
                        />
                    </Grid>
                    <Grid item lg={4}>
                        <Typography variant="h4" gutterBottom mb={3}>
                            {editRadarCategoryDetails ? 'Edit Radar Sub-Category' : 'Add Radar Sub-Categories'}
                        </Typography>
                        <Card>
                            <Box sx={{ p: 4 }}>
                                <form onSubmit={addRadarSubCategoryformik.handleSubmit}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='title'
                                        label="Title"
                                        onBlur={addRadarSubCategoryformik.handleBlur}
                                        value={addRadarSubCategoryformik.values.title}
                                        onChange={addRadarSubCategoryformik.handleChange}
                                        error={addRadarSubCategoryformik.touched.title && Boolean(addRadarSubCategoryformik.errors.title)}
                                        helperText={addRadarSubCategoryformik.touched.title && addRadarSubCategoryformik.errors.title}
                                    />
                                    <FormControl sx={{ marginTop: '20px', width: '100%' }}>
                                        <SelectComponent
                                            menuName={"name"}
                                            menuValue={"_id"}
                                            labelId="radar-label"
                                            label="Radar Category *"
                                            inputLabel="Radar Category"
                                            disableNone
                                            menuItems={RadarCategory}
                                            sx={{ width: '100%' }}
                                            name='radarCategory'
                                            onBlur={addRadarSubCategoryformik.handleBlur}
                                            defaultValue={addRadarSubCategoryformik.values?.radarCategory}
                                            value={addRadarSubCategoryformik.values?.radarCategory}
                                            onChange={addRadarSubCategoryformik.handleChange}
                                            labelColor={addRadarSubCategoryformik.touched.radarCategory && addRadarSubCategoryformik.errors.radarCategory && 'error'}
                                            labelError={addRadarSubCategoryformik.touched.radarCategory && addRadarSubCategoryformik.errors.radarCategory}
                                            error={addRadarSubCategoryformik.touched.radarCategory && Boolean(addRadarSubCategoryformik.errors.radarCategory)}
                                            helperText={addRadarSubCategoryformik.touched.radarCategory && addRadarSubCategoryformik.errors.radarCategory}
                                        />
                                    </FormControl>
                                    <Stack direction="row" justifyContent="flex-end" >
                                        {editRadarCategoryDetails ?
                                            <Button
                                                type='button'
                                                variant='contained'
                                                sx={CancelButton}
                                                color='error'
                                                onClick={() => handleCancel()}
                                            >
                                                Cancel
                                            </Button> : ''
                                        }

                                        <LoadingButton
                                            loading={loading}
                                            type='submit'
                                            variant='contained'
                                            sx={{ width: '10%', m: 1, mt: 2 }}
                                        >
                                            {editRadarCategoryDetails ? 'Save' : 'Add'}
                                        </LoadingButton>
                                    </Stack>
                                </form>
                            </Box>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
        </>
    )
}

export default RadarSubCategory






