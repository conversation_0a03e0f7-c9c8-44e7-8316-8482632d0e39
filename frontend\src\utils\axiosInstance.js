import axios from "axios";
import Cookies from "js-cookie";
import { Api_Base_URL } from "src/config-global";

const axiosInstance = axios.create({
  baseURL: "http://localhost:3000/api/",
  // baseURL: '/api/',
  headers: {
    //  Authorization: `<Auth Token>`,
    "Content-Type": "application/json",
    // timeout : 1000,
  },
});

axiosInstance.interceptors.request.use(
  (config) => {
    const token = Cookies.get("feToken");

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)

);

export default axiosInstance;