import { Button, <PERSON>po<PERSON>, FormHelperText, Tooltip, tooltipClasses, styled } from "@mui/material";
import { isEmpty } from "lodash";
import { useState } from "react";
import { useDispatch } from "react-redux";
import TextFIeldComponent from "./TextField/TextFIeldComponent";
import Iconify from "./Iconify/Iconify";
import { setSnackbar } from "../Redux/snackbarSlice";
import { copiedFunction } from "../utils";

const CustomTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} classes={{ popper: className }} />
))({
    [`& .${tooltipClasses.tooltip}`]: {
        maxWidth: 400,
        fontSize: '0.7rem',
    },
});

const UrlComponent = ({ url, onInputChange, error, formik , isRegion}) => {
    const [editPremalink, setEditPermalink] = useState(false);
    const changePermalink = (event) => {
        event.stopPropagation()
        setEditPermalink(!editPremalink);
    };


    const dispatch = useDispatch()

    const handleCopyToClipboard = () => {
        const value =  isRegion ? `${window.location.origin}/region/${url}` : `${window.location.origin}/college/${url}`
        copiedFunction(value, dispatch)
    }
    return (
        <>
            {/* {loading ? <Loading /> : null} */}
            {/* {!isEmpty(url) ? ( */}
            <CustomTooltip
                title={(editPremalink) ? "" : "Copy to Clipboard"}
                placement="top"
                PopperProps={{
                    modifiers: [
                        {
                            name: "offset",
                            options: {
                                offset: [10, -10],
                            },
                        },
                    ],
                }}
            >
                <Typography
                    onClick={!editPremalink && handleCopyToClipboard}
                    sx={!editPremalink ? {
                        my: 0,
                        '&:hover': {
                            // backgroundColor:'#25422508',
                            color: (theme) => theme.palette.info.main,
                            "& .slugg": {
                                color: (theme) => theme.palette.info.main,
                            }
                        },
                        display: 'inline-block',
                        cursor: 'pointer'
                    } : {
                        display: 'inline-block',
                    }}
                    color='InactiveCaptionText'
                >
                    {/* <Typography
                    color='ActiveCaption'
                    variant='inherit'
                    component='strong'
                    mr={1}
                >
                    Slug: 
                </Typography> */}
                    {
                        window.location.origin}/{isRegion ? 'region/' : 'college/'}{editPremalink === false &&
                            <Typography
                                color='ActiveCaption'
                                component='span'
                                className="slugg"
                                sx={{ fontWeight: 600 }}
                            >
                                {url}
                            </Typography>
                    }
                    {editPremalink === true && (
                        <input
                            style={{
                                padding: 3,
                                paddingInline: 8,
                                fontSize: 'inherit',
                                font: 'inherit',
                                marginLeft: 8,
                            }}
                            id="url"
                            name="slug"
                            // value={formik.values.slug}
                            value={formik.values.slug}
                            onInput={formik.handleChange}
                            onBlur={formik.handleBlur}
                        // value={url}
                        // onChange={(e) => {
                        //     // if (!isEmpty(e.target.value)) {
                        //     onInputChange(e.target.value);
                        //     // }
                        // }}
                        />
                    )}
                </Typography>
            </CustomTooltip>
                    <Button
                        color="primary"
                        onClick={changePermalink}
                        sx={{ ml: "8px" }}
                    >
                        {
                            editPremalink ?
                                <Iconify icon={'el:ok'} sx={{ mr: 1 }} />
                                :
                                <Iconify icon={'eva:edit-fill'} sx={{ mr: 1 }} />
                        }
                    </Button>
            {(error && formik.touched.slug) ? <FormHelperText
                sx={{ color: error && 'error.main' }}
            >
                {error}
            </FormHelperText> :
                null}

            {/* // ) : null} */}
        </>
    );
}

export default UrlComponent

