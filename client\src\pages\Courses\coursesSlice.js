import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import courses from '../../_mock/careers';
import axiosInstance from '../../utils/axiosInstance';

// const { default: courses } = require("src/_mock/courses");

export const postCourse = createAsyncThunk('courses/addCourse', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "courses/add",
            method: "POST",
            data
        })
        // response.data.id = data.id
        console.log("res ==> ",response);
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const editCourse = createAsyncThunk('courses/editCourse', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "courses/update",
            method: "PUT",
            data
        })
        // response.data.id = data.id
        console.log("res ==> ",response);
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeCourse = createAsyncThunk('courses/removeCourse', async (id, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "courses/remove",
            method: "DELETE",
            data:{
                id
            }
        })
        // response.data.id = data.id
        response.data.id = id;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const postExportCourse = createAsyncThunk('courses/export', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "courses/export",
            method: "POST",
            data
        })
        // response.data.id = data.id
        console.log("res ==> ",response);
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const getCourses = createAsyncThunk('courses/getCourses', async (college, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "courses/get",
            method: "GET",
            params: {
                collegeId:college?._id
            }
        })
        // response.data.id = data.id
        // console.log("res ==> ",response);
        return response.data.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

const coursesSlice = createSlice({
    name:'courses',
    initialState:{
        courses:[],
        status: 'idel',
        error: null
    },
    reducers:{
        addCourse : (state, action) =>{

        },
        updateCourse : (state, action) =>{

        },
        deleteCourse : (state, action) =>{

        },
    },
    extraReducers: {
        [getCourses.pending]: (state) => {
            state.status = "pending"
        },
        [getCourses.fulfilled]: (state, action) => {
            state.status = "succeeded"
            state.courses = action.payload
        },
        [getCourses.rejected]: (state, action) => {
            if(action.payload.response.data.data){
                state.courses = action.payload.response.data.data
            }
            state.status = "rejected"
            state.error = action.payload
        },
        [removeCourse.fulfilled]: (state, action) => {
            state.courses = state.courses.filter(course => course._id !== action.payload?.id)
        },

    }
})
export const {addCourse, updateCourse, deleteCourse} = coursesSlice.actions;
export default coursesSlice.reducer