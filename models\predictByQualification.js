const mongoose = require("mongoose");

const PredictByQualificationSchema = new mongoose.Schema({
  code: Number,
  name: String,
  employment: { type: Number },
  note: String,
  careerId: { type: mongoose.Schema.Types.ObjectId, ref: "career" },

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.PredictByQualificationSchema = PredictByQualificationSchema;

class PredictByQualification extends mongoose.Model {
  
}

mongoose.model(PredictByQualification, PredictByQualificationSchema, "predictByQualification");

module.exports.PredictByQualification = PredictByQualification;