const mongoose = require("mongoose");

const SheetCareerSchema = new mongoose.Schema({
  name: String,
  onetcode:String,
  sector:String,
  subsector:String
});

module.exports.SheetCareerSchema = SheetCareerSchema;

class SheetCareer extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await SheetCareer.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await SheetCareer.create(entry);
    }
  }
}

mongoose.model(<PERSON><PERSON><PERSON><PERSON><PERSON>, SheetCareerSchema, "sheetCareers");

module.exports.SheetCareer = SheetCareer;

