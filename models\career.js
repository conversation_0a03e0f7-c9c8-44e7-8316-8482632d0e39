const mongoose = require("mongoose");
const { Sector, SectorSchema } = require("./sector");
const { Subsector, SubsectorSchema } = require("./subsector");

const careerTypes = {
  UNIT_GROUP: "unit_group",
  BROAD_CAREER: "broad_career",
  SPECIALISED_ROLE: "specialised_role",
}
module.exports.careerTypes = careerTypes;

const CareerSchema = new mongoose.Schema({
  onetCode: String,
  title: String,
  socCode: String,
  description: String,
  videoUrl: String,
  tasks: String,
  type: { type: String, enum: Object.values(careerTypes), default: careerTypes.BROAD_CAREER },
  unitGroupId: { type: mongoose.Schema.Types.ObjectId, ref: "career" }, // used only for type "broad_career"
  broadCareerIds: [{ type: mongoose.Schema.Types.ObjectId, ref: "career" }], // used only for type "specialised_role"
  isFuture: { type: Boolean, default: false },
  isEmerging: { type: Boolean, default: false },
  jobZone: Number,
  sectors: { type: [Object], default: [] },
  sectorIds: { type: [mongoose.Schema.Types.ObjectId], default: [], ref: mongoose.model(Sector, SectorSchema) },
  subsectorIds: { type: [mongoose.Schema.Types.ObjectId], default: [], ref: mongoose.model(Subsector, SubsectorSchema) },
  onetCodeForSkillAbility: String,
  interests: { type: [Object], default: [] },

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.CareerSchema = CareerSchema;

class Career extends mongoose.Model {
  static async createDefaultEntries() {
    for (const entry of defaultEntries) {
      const count = await Career.count({ _id: entry._id });
      if (count) {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await Career.create(entry);
    }
  }
}

mongoose.model(Career, CareerSchema, "careers");
module.exports.Career = Career;
