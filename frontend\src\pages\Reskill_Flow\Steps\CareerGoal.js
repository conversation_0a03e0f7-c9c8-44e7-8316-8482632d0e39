import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Button, Container, Typography, Select, FormControl, MenuItem, InputLabel } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import StepperComponent from 'src/components/stepper/Stepper';
import { MultiSelectComponent } from 'src/components/hook-form';
import { ReskillSteps } from 'src/assets/data/Dummy';

export default function ReskillCareerGoal() {
    const navigate = useNavigate();
    const CareerGoals = [
        {label: 'Accountants & Auditors', value: 1},
        {label: 'Actors', value: 2},
        {label: 'Barbers', value: 3},
        {label: 'Biologists', value: 4},
        {label: 'Carpenters', value: 5},
        {label: 'Chemist', value: 6},
    ];
    const [currentRole, setCurrentRole] = useState([]);
    const [showError,setShowError] = useState(false);

    const handleChange = (event) => {
      const {
        target: { value },
      } = event;
      setCurrentRole(
        typeof value === 'string' ? value.split(',') : value,
      );
    };

    const handleClick = () => {
      if(currentRole && currentRole.length > 0){
        navigate('/reskill/skilldar');
      }
      else{
        setShowError(true);
      }
    }
    useEffect(()=>{
      if(currentRole && currentRole.length > 0){
        setShowError(false);
      }
    }, [currentRole])
  return (
    <>
      <Helmet>
        <title>Reskill - Current Career</title>
      </Helmet>

      <Box className='page-content-wrapper'>
        <Container>
          <Box className='content'>
            <Typography variant='h2' color='primary.black' className='page-head'>Reskill</Typography>
            <StepperComponent steps={ReskillSteps.CareerGoals} activeStep={0}/>
            <Box className='content-wrapper career'>
              <Typography variant='h2' color='primary.black' >What is your career history?</Typography>
              <Typography variant='body1' color='primary.light'>Select the closest matches from our database, starting with your current or most recent role, and add as many of your previous roles as you can.</Typography>
              <MultiSelectComponent
              name="currentCareer" 
              placeholder="Start typing to find a job title..."
              defaultValue='none'
              value={currentRole} handleChange={handleChange}
              showError={showError} errorMessage='Please select atleast one career to identify your skills'
              >
                {CareerGoals.map((career) => (
                  <MenuItem key={career.value} value={career.label}>
                    {career.label}
                  </MenuItem>
                ))}
              </MultiSelectComponent>
              
            </Box>
            <Box className="btn-wrapper">
              <Button className='btn noUppercase' sx={{backgroundColor: '#7040f1 !important'}} onClick={handleClick}><Typography color='primary.white'>I'm all done - Identify my skills</Typography></Button>
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
}