const mongoose = require('mongoose');
const { User, UserSchema } = require('./user');

/**
 * Fine-Tuned Model Schema
 * Stores metadata for fine-tuned AI models and deployment information
 */
const fineTunedModelSchema = new mongoose.Schema({
  // Basic Information
  modelName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  
  // OpenAI Model Information
  openAIModelId: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  
  openAIJobId: {
    type: String,
    required: true,
    trim: true
  },
  
  baseModel: {
    type: String,
    required: true,
    default: 'gpt-4o-mini'
  },
  
  // Training References
  trainingDatasetId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'TrainingDataset',
    required: true
  },
  
  // Training Configuration
  trainingConfig: {
    suffix: {
      type: String,
      required: true,
      trim: true
    },
    epochs: {
      type: Number,
      required: true,
      min: 1,
      max: 10
    },
    hyperparameters: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },
  
  // Training Results
  trainingResults: {
    status: {
      type: String,
      enum: ['validating_files', 'running', 'succeeded', 'failed', 'cancelled'],
      required: true,
      default: 'validating_files'
    },
    trainingTokens: {
      type: Number,
      min: 0
    },
    validationLoss: {
      type: Number,
      min: 0
    },
    trainingLoss: {
      type: Number,
      min: 0
    },
    completedAt: {
      type: Date
    },
    errorMessage: {
      type: String,
      trim: true
    }
  },
  
  // Deployment Status
  deploymentStatus: {
    type: String,
    enum: ['deployed', 'undeployed'],
    default: 'undeployed',
    required: true
  },
  
  deployedAt: {
    type: Date
  },
  
  deployedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: mongoose.model(User, UserSchema)
  },
  
  // Performance Tracking
  performance: {
    totalRequests: {
      type: Number,
      default: 0,
      min: 0
    },
    successfulRequests: {
      type: Number,
      default: 0,
      min: 0
    },
    failedRequests: {
      type: Number,
      default: 0,
      min: 0
    },
    averageResponseTime: {
      type: Number,
      min: 0
    },
    totalTokensUsed: {
      type: Number,
      default: 0,
      min: 0
    },
    lastUsed: {
      type: Date
    }
  },
  
  // Model Configuration
  configuration: {
    // Response Generation Settings
    maxTokens: {
      type: Number,
      default: 500,
      min: 1,
      max: 4000
    },
    temperature: {
      type: Number,
      default: 0.7,
      min: 0,
      max: 2
    },
    topP: {
      type: Number,
      default: 1,
      min: 0,
      max: 1
    },
    frequencyPenalty: {
      type: Number,
      default: 0,
      min: -2,
      max: 2
    },
    presencePenalty: {
      type: Number,
      default: 0,
      min: -2,
      max: 2
    },

    // System Behavior Settings
    systemPrompt: {
      type: String,
      trim: true,
      maxlength: 2000,
      default: "You are a helpful assistant for college information and guidance."
    },
    maxRetries: {
      type: Number,
      default: 3,
      min: 1,
      max: 10
    },
    timeoutMs: {
      type: Number,
      default: 30000,
      min: 5000,
      max: 120000
    },

    // Content Moderation Settings
    moderationEnabled: {
      type: Boolean,
      default: true
    },
    moderationStrict: {
      type: Boolean,
      default: false
    },

    // Response Formatting
    includeContext: {
      type: Boolean,
      default: true
    },
    maxContextMessages: {
      type: Number,
      default: 10,
      min: 1,
      max: 50
    },

    // Custom Settings
    customInstructions: {
      type: String,
      trim: true,
      maxlength: 1000
    },
    responseStyle: {
      type: String,
      enum: ['professional', 'friendly', 'concise', 'detailed', 'custom'],
      default: 'professional'
    }
  },

  // Model Metadata
  version: {
    type: String,
    trim: true,
    maxlength: 50
  },

  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },

  notes: {
    type: String,
    trim: true,
    maxlength: 2000
  },
  
  // Status and Lifecycle
  status: {
    type: String,
    enum: ['active', 'archived', 'failed', 'deprecated'],
    default: 'active'
  },
}, { timestamps: true });

// Indexes for performance
fineTunedModelSchema.index({ deploymentStatus: 1 });
fineTunedModelSchema.index({ status: 1, createdAt: -1 });
// fineTunedModelSchema.index({ openAIModelId: 1 });
fineTunedModelSchema.index({ openAIJobId: 1 });
fineTunedModelSchema.index({ trainingDatasetId: 1 });
fineTunedModelSchema.index({ 'trainingResults.status': 1 });

// Compound index for finding deployed models
fineTunedModelSchema.index({ deploymentStatus: 1, status: 1 });

// Update the updatedAt field before saving
fineTunedModelSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for success rate
fineTunedModelSchema.virtual('successRate').get(function() {
  if (this.performance.totalRequests === 0) return 0;
  return this.performance.successfulRequests / this.performance.totalRequests;
});

// Virtual for training duration (if completed)
fineTunedModelSchema.virtual('trainingDuration').get(function() {
  if (!this.trainingResults.completedAt) return null;
  return this.trainingResults.completedAt - this.createdAt;
});

// Instance method to check if model is ready for deployment
fineTunedModelSchema.methods.isReadyForDeployment = function() {
  return this.status === 'active' && 
         this.trainingResults.status === 'succeeded' &&
         this.openAIModelId ? true : false;
};

// Instance method to update performance metrics
fineTunedModelSchema.methods.updatePerformance = function(responseTime, success = true, tokensUsed = 0) {
  this.performance.totalRequests += 1;
  this.performance.lastUsed = new Date();
  this.performance.totalTokensUsed += tokensUsed;

  if (success) {
    this.performance.successfulRequests += 1;
  } else {
    this.performance.failedRequests += 1;
  }

  // Update average response time
  if (this.performance.averageResponseTime) {
    this.performance.averageResponseTime =
      (this.performance.averageResponseTime + responseTime) / 2;
  } else {
    this.performance.averageResponseTime = responseTime;
  }

  return this.save();
};

// Instance method to get effective configuration (with fallbacks)
fineTunedModelSchema.methods.getEffectiveConfiguration = function() {
  const defaultConfig = {
    maxTokens: 500,
    temperature: 0.7,
    topP: 1,
    frequencyPenalty: 0,
    presencePenalty: 0,
    systemPrompt: "You are a helpful assistant for college information and guidance.",
    maxRetries: 3,
    timeoutMs: 30000,
    moderationEnabled: true,
    moderationStrict: false,
    includeContext: true,
    maxContextMessages: 10,
    customInstructions: "",
    responseStyle: "professional"
  };

  // Merge model configuration with defaults
  return {
    ...defaultConfig,
    ...this.configuration
  };
};

// Instance method to update configuration
fineTunedModelSchema.methods.updateConfiguration = function(newConfig) {
  // Validate and merge configuration
  const allowedFields = [
    'maxTokens', 'temperature', 'topP', 'frequencyPenalty', 'presencePenalty',
    'systemPrompt', 'maxRetries', 'timeoutMs', 'moderationEnabled',
    'moderationStrict', 'includeContext', 'maxContextMessages',
    'customInstructions', 'responseStyle'
  ];

  allowedFields.forEach(field => {
    if (newConfig[field] !== undefined) {
      this.configuration[field] = newConfig[field];
    }
  });

  this.updatedAt = new Date();
  return this.save();
};

// Instance method to reset configuration to defaults
fineTunedModelSchema.methods.resetConfiguration = function() {
  this.configuration = {};
  this.updatedAt = new Date();
  return this.save();
};

// Static method to find deployed model
fineTunedModelSchema.statics.findDeployed = function() {
  return this.findOne({ 
    deploymentStatus: 'deployed', 
    status: 'active' 
  }).populate('trainingDatasetId');
};

// Static method to find all active models
fineTunedModelSchema.statics.findActive = function() {
  return this.find({ status: 'active' })
    .populate('trainingDatasetId')
    .sort({ createdAt: -1 });
};

// Static method to find models by training status
fineTunedModelSchema.statics.findByTrainingStatus = function(status) {
  return this.find({ 
    'trainingResults.status': status,
    status: 'active'
  }).populate('trainingDatasetId');
};

// Static method to deploy a model (ensures only one deployed at a time)
fineTunedModelSchema.statics.deployModel = async function(modelId, deployedBy = 'system') {
  const session = await mongoose.startSession();
  
  try {
    await session.withTransaction(async () => {
      // Undeploy all currently deployed models
      await this.updateMany(
        { deploymentStatus: 'deployed' },
        { 
          deploymentStatus: 'undeployed',
          updatedAt: new Date()
        },
        { session }
      );
      
      // Deploy the selected model
      const model = await this.findByIdAndUpdate(
        modelId,
        {
          deploymentStatus: 'deployed',
          deployedAt: new Date(),
          deployedBy: deployedBy,
          updatedAt: new Date()
        },
        { session, new: true }
      ).populate('trainingDatasetId');
      
      if (!model) {
        throw new Error('Model not found');
      }
      
      if (!model.isReadyForDeployment()) {
        throw new Error('Model is not ready for deployment');
      }
      
      return model;
    });
    
    return await this.findDeployed();
    
  } finally {
    await session.endSession();
  }
};

const FineTunedModel = mongoose.model('FineTunedModel', fineTunedModelSchema);

module.exports = FineTunedModel;
