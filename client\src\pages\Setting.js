import { LoadingButton } from '@mui/lab';
import React , { useEffect, useState } from 'react'
import { Box, Button, Card, Container, FormLabel, Grid, InputAdornment, LinearProgress, Stack, TextField, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { get } from 'lodash';
import TextFIeldComponent from 'src/components/TextField/TextFIeldComponent';
import { settingValidation } from 'src/utils/validationSchemas';
import { useDispatch } from 'react-redux';
import axiosInstance from 'src/utils/axiosInstance';
import { setSnackbar } from '../Redux/snackbarSlice';

const Setting = () => {
    const [loading, setLoading] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const dispatch = useDispatch();

    const style = {
        p: 4,
    };

    useEffect(() => {
        const getSetting = async (id) => {
            setLoading(true)
            try {
                const response = await axiosInstance({
                    url: "setting/get",
                    method: "GET",
                })
                const settingDetails = response.data?.data;
                formik.setValues({
                    ...formik.values,
                    skillpercentage: settingDetails.skillMatchingPercentage,
                    skillstomatch: settingDetails.noOfSkillsToMatch,
                })
            } catch (error) {
                const errorMessage = error?.response?.data?.msg
                console.log("error get colleges", errorMessage)
            } finally {
                setLoading(false)
            }
        }
        getSetting()
    }, [])

    const updateSetting = async (data) => {
        setLoading(true)
        try {
            const response = await axiosInstance({
                url: "setting/update",
                method: "PUT",
                data : {
                    skillMatchingPercentage: data.skillpercentage,
                    noOfSkillsToMatch: data.skillstomatch
                }
            })
            if(response?.status === 200){
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Updated succesfully"
                }))
            }
        } catch (error) {
            const errorMessage = error?.response?.data?.msg
            dispatch(setSnackbar({
                snackbarOpen: true,
                snackbarType: 'error',
                snackbarMessage: "Something went wrong"
            }))
        } finally {
            setLoading(false)
        }
    }


    const formik = useFormik({
        initialValues: {
            skillpercentage: '5',
            skillstomatch: '5',
        },
        validationSchema: settingValidation,
        onSubmit: (values) => {
            updateSetting(values)
        },
    })
    return (
        <Container maxWidth="lg">
            <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                Setting
            </Typography>
            {loading ? 
                 <LinearProgress />
            :
                <Grid container wrap='wrap' width={'100%'} gap={2}>
                    <Grid xs={12} lg={11.8}>
                        <Card>
                            <Box 
                            sx={style}
                            >
                                <form
                                 onSubmit={formik.handleSubmit}
                                >
                                    <Grid container gap={2}   >
                                        <Grid item xs={12} md={3.8}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='skillpercentage'
                                                type={'number'}
                                                label="Skill matching percentage"
                                                value={formik.values.skillpercentage}
                                                InputProps={{
                                                    endAdornment: <InputAdornment position="start">%</InputAdornment>,
                                                }}
                                                onChange={(e)=> formik.setValues({
                                                    ...formik.values,
                                                    skillpercentage: e.target.value
                                                })}
                                                InputLabelProps={{
                                                    shrink: true,
                                                }}
                                                error={formik.touched.skillpercentage && Boolean(formik.errors.skillpercentage)}
                                                helperText={formik.touched.skillpercentage && formik.errors.skillpercentage}
                                                onBlur={formik.handleBlur}
                                            />
                                        </Grid>

                                        <Grid item xs={12} md={3.8}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='skillstomatch'
                                                type={'number'}
                                                label="No. of Skills to match"
                                                value={formik.values.skillstomatch}
                                                onChange={(e)=> formik.setValues({
                                                    ...formik.values,
                                                    skillstomatch: e.target.value
                                                })}
                                                InputLabelProps={{
                                                    shrink: true,
                                                }}
                                                error={formik.touched.skillstomatch && Boolean(formik.errors.skillstomatch)}
                                                helperText={formik.touched.skillstomatch && formik.errors.skillstomatch}
                                                onBlur={formik.handleBlur}
                                            />
                                        </Grid>
                                    </Grid>
                                    <Stack direction="row" justifyContent="flex-end" >
                                        <LoadingButton
                                            loading={isLoading}
                                            type='submit'
                                            variant='contained'
                                            sx={{ width: '10%', m: 1, mt: 2 }}
                                        >
                                            Save
                                        </LoadingButton>
                                    </Stack>
                                </form>
                            </Box>
                        </Card>
                    </Grid>
                </Grid>
            }

        </Container>
    )
}

export default Setting
