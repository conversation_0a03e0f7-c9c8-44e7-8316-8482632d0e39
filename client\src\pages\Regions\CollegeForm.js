import { Box, Button, Fade, FormControl, Grid, Stack } from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch } from 'react-redux';
import SelectComponent from '../../components/SelectComponent';
import TextFieldComponent from '../../components/TextField/TextFIeldComponent';
import { collegeValidationSchema } from '../../utils/validationSchemas';
import { postRegions, updateRegion } from './regionsSlice';
 
const CollegeForm = ({ Groups, setOpenModel, openModel, editingRegion = null }) => {
  const dispatch = useDispatch();
  const isEdit = Boolean(editingRegion);
 
  const formik = useFormik({
    initialValues: {
      name: editingRegion?.name || '',
      groupName: editingRegion?.collegeGroupId || '',
      collegeEmail: editingRegion?.email || '',
      primaryColor: editingRegion?.primaryColor || '#7040f1',
      secondaryColor: editingRegion?.secondaryColor || '#fd1f97',
      thirdColor: editingRegion?.thirdColor || '#ffa236',
    },
    onSubmit: async (values) => {
      const data = {
        name: values.name,
        collegeGroupId: values.groupName,
        email: values.collegeEmail,
        primaryColor: values.primaryColor,
        secondaryColor: values.secondaryColor,
        thirdColor: values.thirdColor,
      };
 
      if (isEdit && editingRegion?.id) {
        await dispatch(updateRegion({ id: editingRegion.id, ...data }));
      } else {
        await dispatch(postRegions(data));
      }
 
      setOpenModel(false);
    },
    validationSchema: collegeValidationSchema,
    enableReinitialize: true,
  });
 
  return (
<Fade in={openModel}>
<Box py={2}>
<form onSubmit={formik.handleSubmit}>
<Grid container gap={2}>
<Grid item xs={12} md={5.8}>
<FormControl fullWidth>
<SelectComponent
                  menuName="Name"
                  menuValue="id"
                  menuItems={Groups}
                  name="groupName"
                  label="Group *"
                  inputLabel="Group"
                  onBlur={formik.handleBlur}
                  onChange={formik.handleChange}
                  value={formik.values.groupName}
                  error={formik.touched.groupName && Boolean(formik.errors.groupName)}
                  helperText={formik.touched.groupName && formik.errors.groupName}
                />
</FormControl>
</Grid>
 
            <Grid item xs={12} md={5.8}>
<TextFieldComponent
                fullWidth
                name="name"
                label="Region Name"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.name}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
              />
</Grid>
 
            <Grid item xs={12} md={5.8}>
<TextFieldComponent
                fullWidth
                name="collegeEmail"
                label="Email"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.collegeEmail}
                error={formik.touched.collegeEmail && Boolean(formik.errors.collegeEmail)}
                helperText={formik.touched.collegeEmail && formik.errors.collegeEmail}
              />
</Grid>
 
            <Grid item xs={12} md={3.8}>
<TextFieldComponent
                fullWidth
                name="primaryColor"
                label="Primary Color"
                type="color"
                onChange={formik.handleChange}
                value={formik.values.primaryColor}
              />
</Grid>
 
            <Grid item xs={12} md={3.8}>
<TextFieldComponent
                fullWidth
                name="secondaryColor"
                label="Secondary Color"
                type="color"
                onChange={formik.handleChange}
                value={formik.values.secondaryColor}
              />
</Grid>
 
            <Grid item xs={12} md={3.8}>
<TextFieldComponent
                fullWidth
                name="thirdColor"
                label="Third Color"
                type="color"
                onChange={formik.handleChange}
                value={formik.values.thirdColor}
              />
</Grid>
</Grid>
 
          <Stack direction="row" justifyContent="flex-end">
<Button type="submit" variant="contained" sx={{ width: '10%', m: 1, mt: 2 }}>
              {isEdit ? 'Update' : 'Add'}
</Button>
</Stack>
</form>
</Box>
</Fade>
  );
};
 
export default CollegeForm;