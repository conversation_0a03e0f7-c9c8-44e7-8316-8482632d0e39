import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { get } from 'lodash';
import { Api_Base_URL } from 'src/config-global';
import axiosInstance from 'src/utils/axiosInstance';

export const CareersApi = createApi({
    reducerPath: 'CareersApi',
    baseQuery: fetchBaseQuery({ baseUrl: Api_Base_URL }),
    endpoints: (builder) => ({
        getCareers: builder.mutation({
            query: data => ({
                url: '/getCareers',
                method: 'POST',
                body: data
            }),
            transformResponse: (response, meta, arg) => response.data,
            transformErrorResponse: (response, meta, arg) => response,
        }),
        getNestedCareers: builder.mutation({
            query: data => ({
                url: '/getNestedCareers',
                method: 'POST',
                body: data
            }),
            transformResponse: (response, meta, arg) => response.data,
            transformErrorResponse: (response, meta, arg) => response,
        }),
        getCareerGoals: builder.mutation({
            query: data => ({
                url: '/getGoalCareers',
                method: 'POST',
                body: data
            }),
            transformResponse: (response, meta, arg) => response.data,
            transformErrorResponse: (response, meta, arg) => response,
        }),
    }),
})

export const getRegionBySlug = createAsyncThunk("regions/getRegionBySlug", async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: `frontend/getRegionBySlug?slug=${data}`,
            method: "GET",
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get regions", error)
        return rejectWithValue(error)
    }
}
)
export const getWidgetCareers = createAsyncThunk("regions/getWidgetCareers", async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: `frontend/getWidgetCareers?url=${data}`,
            method: "POST",
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get regions", error)
        return rejectWithValue(error)
    }
}
)
export const getColleges = createAsyncThunk("regions/getColleges", async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: 'frontend/getColleges',
            method: "POST",
            data
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get regions", error)
        return rejectWithValue(error)
    }
}
)
export const getCareerHistory = createAsyncThunk("careers/getCareerHistory", async (data, { rejectWithValue }) => {
    const baseUrl = data?.isRegion
        ? `frontend/getCareerHistory?regionId=${data?.regionId}`
        : `frontend/getCareerHistory?collegeId=${data?.collegeId}`;
    try {
        const response = await axiosInstance({
            url: baseUrl,
            method: "GET",
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get regions", error)
        return rejectWithValue(error)
    }
}
)
export const userLogin = createAsyncThunk("auth/userLogin", async (credentials, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "users/authenticate",
            method: "POST",
            data: credentials
        })
        // localdata set
        return response
    } catch (error) {
        console.log("error login", error)
        return rejectWithValue(error)
    }
}
)
export const userRegister = createAsyncThunk("auth/userRegister", async (credentials, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "users/register",
            method: "POST",
            data: credentials
        })
        // localdata set
        return response
    } catch (error) {
        console.log("error login", error)
        return rejectWithValue(error)
    }
}
)
export const getRegionInfo = createAsyncThunk("regions/getRegionInfo", async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: `frontend/getRegionalInfo`,
            method: "POST",
            data
        })
        // localdata set
        return response.data
    } catch (error) {
        console.log("error get regions", error)
        return rejectWithValue(error)
    }
}
)

export const initChatbotSession = async (collegeId, userAgent) => {
  try {
    const response = await axiosInstance.post('/chatbot/session/init', {
      collegeId,
      userAgent,
    });
    return response.data; // contains sessionId etc.
  } catch (error) {
    console.error('Error initializing chatbot session:', error);
    throw error;
  }
};

export const sendChatbotMessage = async (sessionId, message, options = {}) => {
  try {
    const response = await axiosInstance.post('/chatbot/message', {
      sessionId,
      message,
      options,
    });
    return response.data; // contains chatbot response
  } catch (error) {
    console.error('Error sending chatbot message:', error);
    throw error;
  }
};

const CareerHistorySlice = createSlice({
    name: 'CareerHistory',
    initialState: {
        Careers: {},
        CareerHistory: [],
        CareerGoal: null,
        regions: [],
        loading: false,
        isCollegeLoading: false,
    },
    reducers: {
        addCareerHistory: (state, action) => {
            state.CareerHistory = action.payload
        },
        addCareerGoal: (state, action) => {
            state.CareerGoal = action.payload
        },
    },
    extraReducers: {
        [getRegionBySlug.fulfilled]: (state, action) => {
            state.regions = action.payload?.data
            // console.log(state.regions)
        },
        [userLogin.pending]: (state) => {
            state.loading = true
            state.error = null
        },
        [userLogin.fulfilled]: (state, action) => {
            state.loading = false
            state.success = true
            state.userInfo = action.payload.data
            // cookies.set("token", action.payload.data.token, { path: `${APP_ROUTER_BASE_URL}`, maxAge: 31536000  })
        },
        [userLogin.rejected]: (state, action) => {
            state.loading = false
            state.success = false
            state.error = get(action.payload, 'response.data')
        },
        [getColleges.pending]: (state) => {
            state.isCollegeLoading = true
        },
        [getColleges.fulfilled]: (state, action) => {
            state.isCollegeLoading = false
        },
        [getColleges.rejected]: (state, action) => {
            state.isCollegeLoading = false
        },
    }
})
export const { useGetCareersMutation, useGetCareerGoalsMutation, useGetNestedCareersMutation } = CareersApi;
export const { addCareerHistory, addCareerGoal } = CareerHistorySlice.actions;
export default CareerHistorySlice.reducer;