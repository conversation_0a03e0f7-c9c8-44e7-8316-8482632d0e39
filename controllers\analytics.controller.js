const mongoose = require("mongoose");
const {Analytics, eventValues, fromValues} = require("../models/analytics");
const {Career} = require("../models/career");
const {Course} = require("../models/course");
const { College } = require("../models/college");
const { messageResponse, isEmpty } = require("../helpers/commonHelper");
const { INVALID_MISSING, ADD_SUCCESS } = require("../config/messages");
const _ = require('lodash');
const { Campus } = require("../models/campus");

const UNIQUE_EVENTS = [
  "LANDING_PAGE",
  "GET_STARTED",
  "RESKILL_CLICK",
  "UPSKILL_CLICK",
]

const addEventLog = (fakeReq, res) => {
  let { IP, collegeId, event, from, data } = fakeReq;

  let showMessage = (name) => {
    return res
      ? messageResponse(INVALID_MISSING, name, false, 400, null, res)
      : { msg: INVALID_MISSING, name };
  }

  if (isEmpty(IP)) {
    return showMessage("IP Address");
  }
  if (isEmpty(collegeId) || !mongoose.isValidObjectId(collegeId)) {
    return showMessage("College ID");
  }
  if (isEmpty(event) || !eventValues.includes(event)) {
    return showMessage("Event");
  }

  switch (event) {
    case "CAREER_HISTORY":
      if (!data?.careerIds?.length) return showMessage("Current Career ID");
      break;
    case "CAREER_GOAL":
      if (!data?.careerGoal) return showMessage("Career Goal ID");
      break;
    case "CAREER_VIEW":
      if (!data?.selectedCareerId) return showMessage("Selected Career ID");
      break;
    case "CAREER_COMPARISON":
      if (!data?.careerComparisonId) return showMessage("Career Comparison ID");
      break;
    case "CAREER_RECOMMEND_IDS":
      if (!data?.careerRecommedIds) return showMessage("Career Recommed ID");
      break;
    case "SKILL_REPORT":
      if (!from || !fromValues.includes(from)) return showMessage("From");
      break;
    case "COURSE_DETAILS":
      if (!data?.selectedCourseId) return showMessage("Selected Course ID");
      break;
    case "COURSE_ENQUIRY":
      if (!data?.selectedCourseId) return showMessage("Selected Course ID");
      break;
    case "COURSE_APPLY":
      if (!data?.selectedCourseId) return showMessage("Selected Course ID");
      break;
  }

  Analytics.create(fakeReq);
  if(res)
    return messageResponse(ADD_SUCCESS, "Analytic Record", true, 200, null, res)
};

const prepareEventLog = (fakeData, res) => {
  const { IP, collegeId, event, from, data } = fakeData;
  const fakeReq = {
    IP,
    collegeId,
    event,
    from,
  };
  if (data) fakeReq["data"] = data;
  const error = addEventLog(fakeReq);
  if (error)
    console.log(`Event ${event} not created - ${error.msg}, ${error.msg}`)
    // return messageResponse(error.msg, error.name, false, 400, null, res);
};

const fetchAnalytics = async(req) => {
  try {
    // console.log("feching data")
    const allCourses = await Course.find().select({title: 1})
    const allCareers = await Career.find().select({title: 1})
    let { startDate, endDate, collegeId } = req.body
    let collegeIds = [], eventLogs = [];
    if(!req.user.collegeIds.length){
      if(req.user.collegeGroupIds.length){
        let colleges = await College.find({collegeGroupId: {$in: req.user.collegeGroupIds}}, {_id: 1})
        colleges.map(college => {
          collegeIds.push(college._id)
        })
      } else if (req.user.campusIds.length) {
        let campuses = await Campus.find({_id: {$in: req.user.campusIds}}, {collegeId: 1})
        campuses.map(campus => {
          collegeIds.push(campus.collegeId)
        })
      }
    } else {
      collegeIds = req.user.collegeIds
    }
    // console.log(collegeIds)
    if(collegeId){
      eventLogs = await Analytics.find({collegeId: new mongoose.Types.ObjectId(collegeId)})
    } else if(collegeIds.length) {
      eventLogs = await Analytics.find({collegeId: {$in: collegeIds}})
    } else {
      eventLogs = await Analytics.find()
    }
    // console.log("feched data", eventLogs.length)
    // console.log("feched data")
    const careersData = [], coursesData = [], pageLeaveData = {}, deviceData = {}
    startDate = new Date(startDate).getTime()
    endDate = new Date(endDate).getTime()+86400000 //add 24 hours 
    let groupedData = [];

    const insertData = (dateRange, obj) => {
      const foundObj = dateRange.length ? dateRange.find(item => item.IP === obj.IP && item.collegeId.toString() === obj.collegeId.toString()) : false
      if(foundObj){
        foundObj.count++
      } else {
        dateRange.push(obj)
      }
    }

    const populateData = (eventGroup, from) => {
      eventGroup.totalCount++
      eventGroup.from[from] = eventGroup.from[from] ?? 0
      eventGroup.from[from]++
    }

    const prepareData = (dataArray) => { 
      const data = dataArray.reduce((group, eventLog) => {
        const {event, collegeId: eventCollegeID, from, IP, date, data} = eventLog
        group[event] = group[event] ?? {totalCount: 0, from: {}, unique: {totalCount: 0, from: {}}, withinDateRange: [], outsideDateRange: []}

        let obj = {
          IP,
          from,
          collegeId: eventCollegeID,
          count: 1
        }        
        
        if(date.getTime() >= startDate && date.getTime() <= endDate){
          if(data){
            const {selectedCourseId, selectedCareerId, careerIds, careerComparisonId, careerRecommedIds, careerGoal} = data
            if(selectedCourseId){
              let courseTitle = allCourses.find(course => course._id.toString() === selectedCourseId.toString())
              if(courseTitle){
                let course = coursesData.find(course => course.id.toString() === selectedCourseId.toString())
                if(!course) {
                  course = {
                    id: selectedCourseId,
                    title: courseTitle.title,
                    details: 0,
                    apply: 0,
                    enquiry: 0,
                  }
                  coursesData.push(course)
                }
                if(event === "COURSE_DETAILS") course.details++
                if(event === "COURSE_APPLY") course.apply++
                if(event === "COURSE_ENQUIRY") course.enquiry++
              }
            } else {
              let allCareerIds = []
              if(careerIds && careerIds.length) allCareerIds = careerIds 
              if(careerRecommedIds && careerRecommedIds.length) allCareerIds = careerRecommedIds 
              if(careerComparisonId) allCareerIds = [careerComparisonId] 
              if(careerGoal) allCareerIds = [careerGoal] 
              if(selectedCareerId) allCareerIds = [selectedCareerId] 
  
              allCareerIds.map(async(careerId) => {
                let careerTitle = allCareers.find(career => career._id.toString() === careerId.toString())
                if(careerTitle){
                  let career = careersData.find(career => career.id.toString() === careerId.toString())
                  if(!career) {
                    career = {
                      id: careerId,
                      title: careerTitle.title,
                      current: 0,
                      details: 0,
                      history: 0,
                      goal: 0,
                      compared: 0,
                      recommended: 0,
                    }
                    careersData.push(career)
                  }
                  if(event === "CAREER_VIEW") career.details++
                  if(event === "CAREER_HISTORY") career.history++
                  if(event === "CAREER_HISTORY" && (careerIds && careerIds.length && careerIds[0].toString() === careerId.toString())) career.current++
                  if(event === "CAREER_GOAL") career.goal++
                  if(event === "CAREER_COMPARISON") career.compared++
                  if(event === "CAREER_RECOMMEND_IDS") career.recommended++           
                }
              })            
            }
          }
  
          if(event === "PAGE_LEAVE"){
            if(!pageLeaveData[from]){
              pageLeaveData[from] = 0
            }
            pageLeaveData[from]++
          }
  
          if(event === "DEVICE_INFO"){
            if(!deviceData[from]){
              deviceData[from] = 0
            }
            deviceData[from]++
          }
          
          populateData(group[event], from)
          insertData(group[event].withinDateRange, obj)
        } else {
          insertData(group[event].outsideDateRange, obj)
        }
          
        return group
      }, {})
      return data
    }    

    const calcUniqueness = (groupedData) => {
      for([key, value] of Object.entries(groupedData))
      {
        value.withinDateRange.map(wEntry => {
          const {IP, collegeId: eventCollegeID, from} = wEntry
          // if(wEntry.count === 1)
          {
            const foundEntry = value.outsideDateRange.find(oEntry => oEntry.IP === IP && oEntry.collegeId.toString() === eventCollegeID.toString())
            if(!foundEntry && UNIQUE_EVENTS.includes(key)) {
              populateData(value.unique, from)
            }
          }
        })
      }
    }

    const assignCount = (event, query = {}) => {
      const {isUnique, from} = query
      let count;
      if(isUnique && from){
        count = _.get(groupedData, [event, 'unique', 'from', from], 0)
      } else if(isUnique){
        count = _.get(groupedData, [event, 'unique', 'totalCount'], 0)
      } else if(from) {
        count = _.get(groupedData, [event, 'from', from], 0) 
      } else {
        count = _.get(groupedData, [event, 'totalCount'], 0)
      }
      return count
    }
    // console.log("preparing adta")
    groupedData = prepareData(eventLogs)
    // console.log("prepared adta")
    // console.log("uniqe adta")
    calcUniqueness(groupedData)
    // console.log("uniqued adta")
    // console.log("reurn response")

    return {
      totalLanding: assignCount("LANDING_PAGE"),
      uniqueLanding: assignCount("LANDING_PAGE", {isUnique: true}),
      totalVisitors: assignCount("GET_STARTED"),
      uniqueVisitors: assignCount("GET_STARTED", {isUnique: true}),
      reskillVisitors: assignCount("RESKILL_CLICK"),
      uniqueReskillVisitors: assignCount("RESKILL_CLICK", {isUnique: true}),
      upskillVisitors: assignCount("UPSKILL_CLICK"),
      uniqueUpskillVisitors: assignCount("UPSKILL_CLICK", {isUnique: true}),
      radarsGenerated: assignCount("RADARS_COUNT"),
      skillReports: assignCount("SKILL_REPORT"),
      skillReportsFromCareersPage: assignCount("SKILL_REPORT", {from: 'careers-page'}),
      skillReportsFromCareersResultPage: assignCount("SKILL_REPORT", {from: 'careers-result-page'}),
      skillReportsFromComparisonPage: assignCount("SKILL_REPORT", {from: 'comparison'}),
      courseDetails: assignCount("COURSE_DETAILS"),
      courseEnquiry: assignCount("COURSE_ENQUIRY"),
      courseApply: assignCount("COURSE_APPLY"),
      careerList: assignCount("LIST_GRAPH_CLICK"),
      careerGraph: assignCount("SCATTER_GRAPH_CLICK"),
      // groupedData,
      data: {
        coursesData,
        careersData,
        pageLeaveData,
        deviceData
      }
    }
  } catch (error) {
    console.log(error)
  }
}

const addLogs = (req, res) => {
  addEventLog(req.body, res);
};

module.exports = {
  addEventLog,
  prepareEventLog,
  addLogs,
  fetchAnalytics,
};
