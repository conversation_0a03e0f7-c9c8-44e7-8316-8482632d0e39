import { Typography, Box } from '@mui/material';
import PropTypes from 'prop-types';

const ThinkSkillsHeader = ({ fontColor }) => (
  <Box width='100% !important' mb={4} textAlign="center">
    <Typography variant='h3' sx={{ fontFamily: '"Merriweather", serif',  fontWeight: 900, fontSize: '20px', display: 'inline', color: fontColor || 'unset' }}>
      Your Think Skills results
    </Typography>
    {/* <Typography component="span" sx={{ fontSize: '20px !important', fontWeight: 700, display: 'inline', color: fontColor || 'unset' }}>
      Your THINK
    </Typography>
    {/* <Typography component="span" sx={{ fontSize: '20px !important', fontWeight: 700, display: 'inline', color: fontColor || 'unset' }}>
      Your THINK
    </Typography>
    <Typography component="span" sx={{ fontSize: '20px !important', fontWeight: 300, display: 'inline', color: fontColor || 'unset' }}>
      SKILLS
    </Typography>
    <Typography component="span" sx={{ fontSize: '20px !important', fontWeight: 700, display: 'inline', color: fontColor || 'unset' }}>
      {' '}results
    </Typography> */}
  </Box>
);

ThinkSkillsHeader.propTypes = {
  fontColor: PropTypes.string,
};

export default ThinkSkillsHeader;