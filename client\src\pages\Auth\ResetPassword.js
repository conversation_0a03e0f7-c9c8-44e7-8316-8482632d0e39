import { Helmet } from 'react-helmet-async';
import { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
// @mui
import { LoadingButton } from '@mui/lab';
import { Container, Typography, Alert, Checkbox, FormHelperText, IconButton, InputAdornment, Link, Stack, TextField, Box } from '@mui/material';
// components
import { styled } from '@mui/material/styles';
// hooks
import { useFormik } from 'formik';
import { useLocation, useNavigate } from 'react-router-dom';
import Iconify from '../../components/Iconify';
import useResponsive from '../../hooks/useResponsive';
// components
// sections
import LoginForm from './LoginForm';
import { setSnackbar } from '../../Redux/snackbarSlice';
import { APP_ROUTER_BASE_URL } from '../../utils';
import HorizonLogo from '../../assets/images/thinkskill.png';


// ----------------------------------------------------------------------

const StyledRoot = styled('div')(({ theme }) => ({
  [theme.breakpoints.up('md')]: {
    display: 'flex',
  },
}));

const StyledSection = styled('div')(({ theme }) => ({
  width: '100%',
  maxWidth: 480,
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  boxShadow: theme.customShadows.card,
  backgroundColor: theme.palette.background.default,
}));

const StyledContent = styled('div')(({ theme }) => ({
  maxWidth: 480,
  margin: 'auto',
  minHeight: '100vh',
  display: 'flex',
  justifyContent: 'center',
  flexDirection: 'column',
  padding: theme.spacing(12, 0),
}));

// ----------------------------------------------------------------------

export default function ResetPassword() {
  const navigate = useNavigate()
  const dispatch = useDispatch()
  const mdUp = useResponsive('up', 'md');
  const location = useLocation();
  const from = location.state?.from?.pathname || "/";
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  // const loading = useSelector(state => state.auth.loading)
  const [loading, setLoading] = useState(false)
  const error = useSelector(state => state.auth.error)
  const formik = useFormik({
    initialValues: {
      password: "",
      confirmpassword:""
    },
    onSubmit: (values) => {
      setLoading(true)
      setTimeout(() => {
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'success',
          snackbarMessage: "Password Reset link has been sent to your email",
          snackbarPosition: {
            vertical: 'top',
            horizontal: 'right'
          }
        }))
        setLoading(false)
      }, 1000);
    },
  })


  return (
    <>
      <Helmet>
        <title> ResetPassword | ThinkSkill </title>
      </Helmet>

      <StyledRoot>
        {/* <Logo
          sx={{
            position: 'fixed',
            top: { xs: 16, sm: 24, md: 40 },
            left: { xs: 16, sm: 24, md: 40 },
          }}
        /> */}
        <Container maxWidth="sm">
          <StyledContent>
          <Box
              sx={{ maxWidth: 200, cursor: 'pointer', mb: 8, alignSelf: 'center' }}
              onClick={() => navigate(`${APP_ROUTER_BASE_URL}`)}
            >
              <img src={HorizonLogo} alt="logo" />
            </Box>
            <Typography variant="h4" gutterBottom sx={{ mb: 3 }}>
              Change Your Password
            </Typography>

            <form onSubmit={formik.handleSubmit} >
              <Stack spacing={3}>
                {error &&
                  <Alert severity='error'>
                    {error?.message || "something went wrong"}
                  </Alert>}

                <TextField
                  name="password"
                  label="Password"
                  value={formik.values.password}
                  type={showPassword ? 'text' : 'password'}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.password && Boolean(formik.errors.password)}
                  helperText={formik.touched.password && formik.errors.password}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={() => {
                          setShowPassword(!showPassword)
                        }} edge="end">
                          <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
                <TextField
                  name="confirmpassword"
                  label="Confirm Password"
                  value={formik.values.confirmpassword}
                  type={showConfirmPassword ? 'text' : 'password'}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.confirmpassword && Boolean(formik.errors.confirmpassword)}
                  helperText={formik.touched.confirmpassword && formik.errors.confirmpassword}
                  InputLabelProps={{
                    shrink: true,
                  }}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton onClick={() => {
                          setShowConfirmPassword(!showConfirmPassword)
                        }} edge="end">
                          <Iconify icon={showConfirmPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                        </IconButton>
                      </InputAdornment>
                    ),
                  }}
                />
              </Stack>
              <FormHelperText sx={{ color: 'error.main', my: 2 }}>{error && error.response.data.msg}</FormHelperText>

              <LoadingButton
                loading={loading}
                fullWidth
                size="large"
                type="submit"
                variant="contained"
                sx={{ mt: 2 }}
              >
                Reset Password
              </LoadingButton>
            </form>

            {/* <Typography variant="body2" sx={{ mb: 5 }}> */}
            {/* Don’t have an account? {''}
              <Link variant="subtitle2">Get started</Link> */}
            {/* </Typography> */}

            {/* <Stack direction="row" spacing={2}>
              <Button fullWidth size="large" color="inherit" variant="outlined">
                <Iconify icon="eva:google-fill" color="#DF3E30" width={22} height={22} />
              </Button>

              <Button fullWidth size="large" color="inherit" variant="outlined">
                <Iconify icon="eva:facebook-fill" color="#1877F2" width={22} height={22} />
              </Button>

              <Button fullWidth size="large" color="inherit" variant="outlined">
                <Iconify icon="eva:twitter-fill" color="#1C9CEA" width={22} height={22} />
              </Button>
            </Stack> */}
            {/* 
            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                OR
              </Typography>
            </Divider> */}
          </StyledContent>
        </Container>
      </StyledRoot>
    </>
  );
}
