const mongoose = require("mongoose");

const ChatMessageSchema = new mongoose.Schema({
  sessionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "ChatSession",
    required: true,
    index: true
  },
  messageType: {
    type: String,
    enum: ['user', 'bot', 'system'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  moderationFlags: {
    type: [String],
    default: [],
    enum: ['profanity', 'irrelevant', 'off-topic', 'spam']
  },
  responseType: {
    type: String,
    enum: ['fine-tuned', 'fallback', 'violation', 'error'],
    default: null
  },
  vectorSearchResults: {
    type: Object,
    default: null
  },
  responseTime: {
    type: Number, // milliseconds
    default: null
  },
  tokenUsage: {
    promptTokens: { type: Number, default: 0 },
    completionTokens: { type: Number, default: 0 },
    totalTokens: { type: Number, default: 0 }
  },
  sources: {
    type: [String], // Array of content IDs that were used
    default: []
  },
  metadata: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
ChatMessageSchema.index({ sessionId: 1, createdAt: 1 });
ChatMessageSchema.index({ messageType: 1, createdAt: -1 });
ChatMessageSchema.index({ moderationFlags: 1 });

// Static methods
ChatMessageSchema.statics.getSessionHistory = function(sessionId, limit = null) {
  const query = this.find({ sessionId }).sort({ createdAt: 1 });
  return limit ? query.limit(limit) : query;
};

ChatMessageSchema.statics.getViolationMessages = function(sessionId) {
  return this.find({ 
    sessionId, 
    moderationFlags: { $exists: true, $not: { $size: 0 } }
  });
};

ChatMessageSchema.statics.getTokenUsageBySession = function(sessionId) {
  return this.aggregate([
    { $match: { sessionId } },
    {
      $group: {
        _id: "$sessionId",
        totalPromptTokens: { $sum: "$tokenUsage.promptTokens" },
        totalCompletionTokens: { $sum: "$tokenUsage.completionTokens" },
        totalTokens: { $sum: "$tokenUsage.totalTokens" },
        messageCount: { $sum: 1 }
      }
    }
  ]);
};

class ChatMessage extends mongoose.Model {}

mongoose.model(ChatMessage, ChatMessageSchema, "chatMessages");

module.exports.ChatMessage = ChatMessage;
module.exports.ChatMessageSchema = ChatMessageSchema;
