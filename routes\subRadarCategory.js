const express = require("express");
const router = express.Router();
const radarSubCategory = require('../controllers/subRadarCategory.controller')
const SuperUserGuard = require('../guards/super-user.guard')


router.post("/add", SuperUserGuard, radarSubCategory.addEntry);

router.get("/get", radarSubCategory.getEntry);

router.get("/getById", radarSubCategory.getByID);

router.delete("/remove", SuperUserGuard, radarSubCategory.removeEntry);

router.put("/update", SuperUserGuard, radarSubCategory.updateEntry);

module.exports = router;