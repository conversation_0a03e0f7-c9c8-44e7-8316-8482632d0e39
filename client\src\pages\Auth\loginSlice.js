import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { get } from 'lodash';
import Cookies from 'universal-cookie';
import axiosInstance from '../../utils/axiosInstance';

const cookies = new Cookies();
export const userLogin = createAsyncThunk("auth/userLogin", async (credentials, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "users/authenticate",
            method: "POST",
            data: credentials
        })
        // localdata set
        return response
    } catch (error) {
        console.log("error login", error)
        return rejectWithValue(error)
    }
}
)

const initialState = {
    loading: false,
    userInfo: {}, // for user object
    userToken: null, // for storing the JWT
    error: null,
    success: false, // for monitoring the registration process.
}

const authSlice = createSlice({
    name: 'auth',
    initialState,
    reducers: {
        logoutUser: (state, action) => {
            state.success = false
        }
    },
    extraReducers: {
        [userLogin.pending]: (state) => {
            state.loading = true
            state.error = null
        },
        [userLogin.fulfilled]: (state, action) => {
            state.loading = false
            state.success = true
            state.userInfo = action.payload.data
            // cookies.set("token", action.payload.data.token, { path: `${APP_ROUTER_BASE_URL}`, maxAge: 31536000  })
            cookies.set("token", action.payload.data.token, { path: '/', maxAge: 31536000  })

        },
        [userLogin.rejected]: (state, action) => {
            state.loading = false
            state.success = false
            state.error = get(action.payload,'response.data')
        },
    },
})

export default authSlice.reducer
export const { logoutUser } = authSlice.actions