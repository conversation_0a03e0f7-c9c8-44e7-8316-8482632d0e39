const { default: mongoose, ConnectionStates } = require("mongoose");
const { College } = require("../models/college");
const { Career } = require("../models/career");
const { CareerHistory } = require("../models/careerHistory");
const { RadarCategory } = require("../models/radarCategory");
const settingSchema = require("../models/settingSchema");
const commonHelper = require("../helpers/commonHelper");
const { messageResponse, generateCode, isEmpty } = require("../helpers/commonHelper");
const emailTemplate = require("../helpers/emailTemplate");
const { prepareEventLog } = require("../controllers/analytics.controller");
const keys = require("../config/keys");
const mailchimpClient = require("@mailchimp/mailchimp_transactional")(
  keys.SMTP_PWD
);
const { SERVER_ERROR, REQUIRED, NOT_FOUND, INVALID_MISSING, DATA_NOT_FOUND,
  SEND_EMAIL_SUCCESS, 
  ADD_SUCCESS} = require("../config/messages");
const { Region } = require("../models/region");
const { getNestedCareersAggregation } = require("../aggregations/frontend");
const { toObjectId } = require("../helpers/misc");
const { Course } = require("../models/course");

const colors = [
  "#F5AE1B",
  "#F04E66",
  "#25B681",
  "#6859A6",
  "#3585C7",
  "#D1227C",
  "#8EB34C",
  "#00969E",
  "#204497",
  "#968358",
];

const reSkillsAndUpSkillscolors = [
  "#F5AE1B",
  "#F04E66",
  "#25B681",
  "#6859A6",
  "#3585C7",
  "#D1227C",
  "#8EB34C",
  "#00969E",
  "#204497",
  "#968358",

  "#F5AE1B",
  "#F04E66",
  "#25B681",
  "#6859A6",
  "#3585C7",
  "#D1227C",
  "#8EB34C",
  "#00969E",
  "#204497",
  "#968358", 

  "#F5AE1B",
  "#F04E66",
  "#25B681",
  "#6859A6",
  "#3585C7",
  "#D1227C",
  "#8EB34C",
  "#00969E",
  "#204497",
  "#968358",

  "#F5AE1B",
  "#F04E66",
  "#25B681",
  "#6859A6",
  "#3585C7",
  "#D1227C",
  "#8EB34C",
  "#00969E",
  "#204497",
  "#968358",

  "#F5AE1B",
  "#F04E66",
  "#25B681",
  "#6859A6",
  "#3585C7",
  "#D1227C",
  "#8EB34C",
  "#00969E",
  "#204497",
  "#968358",
]

// Common controllers for both flows
const getColleges = async (req, res) => {
  try {
    const { regionId, collegeId } = req.body;

    if (!regionId && !collegeId) {
      return messageResponse(REQUIRED, "Region or College", false, 400, null, res);
    }

    const validateId = (id, type) => {
      if (id && !mongoose.isValidObjectId(id)) {
        return messageResponse(INVALID_MISSING, type, false, 400, null, res);
      }
      return false;
    };

    const idValidation = validateId(regionId, "Region") || validateId(collegeId, "College");
    if (idValidation) return idValidation;

    // Common pipeline stages
    const commonLookupStages = [
      {
        $lookup: {
          from: "collegeGroups", 
          localField: "collegeGroupId",
          foreignField: "_id",
          as: "collegeGroup"
        }
      },
      {
        $addFields: {
          collegeGroupName: { $arrayElemAt: ["$collegeGroup.name", 0] }
        }
      },
      {
        $project: {
          _id: 1,
          name: 1,
          slug: 1,
          collegeGroupId: 1,
          collegeGroupName: 1,
          primaryColor: 1,
          secondaryColor: 1,
          thirdColor: 1,
          address1: 1,
          city: 1,
          state: 1,
          zip: 1,
          contactNumber: 1,
          website: 1,
          email: 1,
          image: 1,
          logo: 1,
          description: 1,
        }
      }
    ];

    let result;
    if (regionId) {
      result = await Region.aggregate([
        { $match: { _id: new mongoose.Types.ObjectId(regionId) } },
        {
          $lookup: {
            from: "collegeGroups",
            localField: "collegeGroupIds", 
            foreignField: "_id",
            as: "collegeGroups"
          }
        },
        {
          $lookup: {
            from: "colleges",
            let: { groupIds: "$collegeGroups._id" },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ["$collegeGroupId", "$$groupIds"] }
                }
              },
              ...commonLookupStages
            ],
            as: "colleges"
          }
        }
      ]);

      if (!result.length) {
        return messageResponse(NOT_FOUND, "Region", false, 400, null, res);
      }
      return messageResponse(null, "", true, 200, { colleges: result[0].colleges }, res);

    } else {
      result = await College.aggregate([
        { $match: { _id: new mongoose.Types.ObjectId(collegeId) } },
        {
          $lookup: {
            from: "regions",
            localField: "collegeGroupId",
            foreignField: "collegeGroupIds", 
            as: "region"
          }
        },
        {
          $lookup: {
            from: "colleges",
            let: { groupIds: "$region.collegeGroupIds" },
            pipeline: [
              {
                $match: {
                  $expr: { $in: ["$collegeGroupId", { $arrayElemAt: ["$$groupIds", 0] }] }
                }
              },
              ...commonLookupStages
            ],
            as: "colleges"
          }
        }
      ]);

      if (!result.length) {
        return messageResponse(NOT_FOUND, "College", false, 400, null, res);
      }
      return messageResponse(null, "", true, 200, { colleges: result[0].colleges }, res);
    }

  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const getRegionalInfo = async (req, res) => {
  try {
    const { regionId, collegeId } = req.body;

    if (!regionId && !collegeId) {
      return messageResponse(REQUIRED, "Region or College", false, 400, null, res);
    }

    const validateId = (id, type) => {
      if (id && !mongoose.isValidObjectId(id)) {
        return messageResponse(INVALID_MISSING, type, false, 400, null, res); 
      }
      return false;
    };

    if (validateId(regionId, "Region") || validateId(collegeId, "College")) {
      return;
    }

    // Common pipeline stages
    const lookupStages = [
      {
        $lookup: {
          from: "campuses",
          localField: "colleges._id", 
          foreignField: "collegeId",
          as: "campuses"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "campuses._id",
          foreignField: "campusId", 
          as: "courses"
        }
      },
      {
        $lookup: {
          from: "sectors",
          localField: "courses.sectorIds",
          foreignField: "_id",
          as: "sectors"  
        }
      },
      {
        $lookup: {
          from: "subsectors", 
          localField: "courses.subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      }
    ];

    // Project stage
    const projectStage = {
      $project: {
        name: 1, 
        slug: 1,
        bannerImages: 1,
        description: 1,
        primaryColor: 1,
        secondaryColor: 1,
        "sectors._id": 1,
        "sectors.name": 1,
        "subsectors._id": 1,
        "subsectors.name": 1
      }
    };

    let result;
    if (regionId) {
      result = await Region.aggregate([
        { $match: { _id: new mongoose.Types.ObjectId(regionId) } },
        {
          $lookup: {
            from: "collegeGroups",
            localField: "collegeGroupIds",
            foreignField: "_id",
            as: "collegeGroups"
          }
        },
        {
          $lookup: {
            from: "colleges",
            localField: "collegeGroups._id",
            foreignField: "collegeGroupId",
            as: "colleges"
          }
        },
        ...lookupStages,
        projectStage
      ]);

      if (!result.length) {
        return messageResponse(NOT_FOUND, "Region", false, 400, null, res);
      }

    } else {
      result = await College.aggregate([
        { $match: { _id: new mongoose.Types.ObjectId(collegeId) } },
        {
          $lookup: {
            from: "regions",
            localField: "collegeGroupId",
            foreignField: "collegeGroupIds",
            as: "region"
          }
        },
        {
          $lookup: {
            from: "colleges", 
            localField: "region.collegeGroupIds",
            foreignField: "collegeGroupId",
            as: "colleges"
          }
        },
        // ...lookupStages,
        {
          $project: {
            ...projectStage.$project,
            _id: {$first: "$region._id"},
            name: {$first: "$region.name"},
            slug: {$first: "$region.slug"},
            bannerImages: {$first: "$region.bannerImages"},
            description: {$first: "$region.description"},
            primaryColor: {$first: "$region.primaryColor"},
            secondaryColor: {$first: "$region.secondaryColor"}
          }
        }
      ]);

      if (!result.length) {
        return messageResponse(NOT_FOUND, "College", false, 400, null, res);
      }
    }

    return messageResponse(null, "", true, 200, result[0], res);

  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const preparePolarRadar = async (data, currentCareerIds, particularCareerId = "", radarCategories, isCurrentRoleReq = false, from = 'none') => {
  if (!radarCategories) {
    radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});
  }
  const initialRadarValues = [];
  radarCategories.forEach(radar => {
    initialRadarValues.push({ name: radar.name, total: 0, count: 0, skillsAbilities: [] });
  });

  if (!particularCareerId || (currentCareerIds.length === 1 && currentCareerIds.includes(particularCareerId.toString()))) {
    particularCareerId = "";
  }

  let currentJobSkillsAbilities = [], currentJobTitle, currentJobRadarValues, currentRoleTitle, currentRoleRadarValues, 
    careerGoalTitle, careerGoalRadarValues;
  data.forEach(career => {
    let skills = [], abilities = [];
    // creating deep copy of firstRadarValues
    radarValues = JSON.parse(JSON.stringify(initialRadarValues));
    if (career.lmiSkillAbilityLevels && career.lmiSkillAbilityLevels.length > 0) {
      career.lmiSkillAbilityLevels.forEach(skillabilitylevels => {
        const skillAbility = career.skillsAbilities.find(skillAbility => skillAbility._id.toString() == skillabilitylevels.lmiSkillAbilityId.toString());
        let radarCategory;
        if (skillAbility.radarCategoryId) {
          radarCategory = radarCategories.find(rc => rc._id.toString() == skillAbility.radarCategoryId.toString());
        }
        if (skillAbility && radarCategory) {
          const foundInCurrentCareers = currentCareerIds.find(currentCareer => currentCareer.toString() == career._id.toString());
          if (career._id.toString() == particularCareerId.toString() || 
            (isCurrentRoleReq && career._id.toString() == currentCareerIds[0].toString())) {
            let foundRadarValue = radarValues.find(rv => rv.name == radarCategory.name);
            if (!foundRadarValue) {
              foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
              radarValues.push(foundRadarValue);
            }
            foundRadarValue.total += skillabilitylevels.level;
            foundRadarValue.count++;
            foundRadarValue.skillsAbilities.push({ name: skillAbility.lmiName, level: skillabilitylevels.level });
          }
          // else {
          
          if (foundInCurrentCareers) {
            const foundCJSkillAbility = currentJobSkillsAbilities.find(cjskillAbility => cjskillAbility._id.toString() == skillAbility._id.toString());
            if (foundCJSkillAbility) {
              if (skillabilitylevels.level > foundCJSkillAbility.level) {
                foundCJSkillAbility.level = skillabilitylevels.level;
              }
            }
            else {
              currentJobSkillsAbilities.push({ _id: skillAbility._id, id: skillAbility.lmiId, name: skillAbility.lmiName, level: skillabilitylevels.level, radarCategory: radarCategory.name });
            }
          }
        }
      });

    }
    career.skills = skills;
    career.abilities = abilities;
    if (career._id.toString() == particularCareerId.toString()) {
      careerGoalTitle = career.title;
      careerGoalRadarValues = radarValues;
    }
    else if (isCurrentRoleReq && career._id.toString() == currentCareerIds[0].toString()) {
      currentRoleTitle = career.title;
      currentRoleRadarValues = radarValues;
    }
    else if (career._id.toString() == currentCareerIds[0].toString()) {
      currentJobTitle = career.title;
    }
  });

  // creating deep copy of initialRadarValues
  currentJobRadarValues = JSON.parse(JSON.stringify(initialRadarValues));
  currentJobSkillsAbilities.forEach(skillAbility => {
    let foundRadarValue = currentJobRadarValues.find(rv => rv.name == skillAbility.radarCategory);
    if (!foundRadarValue) {
      foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
      currentJobRadarValues.push(foundRadarValue);
    }
    foundRadarValue.total += skillAbility.level;
    foundRadarValue.count++;
    foundRadarValue.skillsAbilities.push({ name: skillAbility.name, level: skillAbility.level });
  })

  let categories = [];
  let series = [];
  let currentJobRadarData = [];
  currentJobRadarValues.forEach(rv => {
    categories.push(rv.name);
    currentJobRadarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
  });

  series.push(...currentJobRadarData)

  if (isCurrentRoleReq && currentRoleRadarValues && currentRoleRadarValues.length) {
    let currentRoleRadarData = [];
    currentRoleRadarValues.forEach(rv => {
      currentRoleRadarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
    });
    series.push(...currentRoleRadarData)
  }

  if (careerGoalRadarValues && careerGoalRadarValues.length) {
    let careerGoalRadarData = [];
    careerGoalRadarValues.forEach(rv => {
      careerGoalRadarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
    });
    series.push(...careerGoalRadarData)
  }

  const polarChartData = {
    series,
    labels: categories,
  }
  return { polarChartData, currentJobRadarValues, careerGoalRadarValues }

}

const prepareUpskillRadar = async (data, currentCareerIds, particularCareerId = "", radarCategories, isCurrentRoleReq = false, from = 'none') => {
  if (!radarCategories) {
    radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});
  }
  const initialRadarValues = [];
  radarCategories.forEach(radar => {
    initialRadarValues.push({ name: radar.name, total: 0, count: 0, skillsAbilities: [] });
  });

  if (!particularCareerId || (currentCareerIds.length === 1 && currentCareerIds.includes(particularCareerId.toString()))) {
    particularCareerId = "";
  }

  let currentJobSkillsAbilities = [], currentJobTitle, currentJobRadarValues, currentRoleTitle, currentRoleRadarValues, 
    careerGoalTitle, careerGoalRadarValues;
  data.forEach(career => {
    let skills = [], abilities = [];
    // creating deep copy of firstRadarValues
    radarValues = JSON.parse(JSON.stringify(initialRadarValues));
    if (career.lmiSkillAbilityLevels && career.lmiSkillAbilityLevels.length > 0) {
      career.lmiSkillAbilityLevels.forEach(skillabilitylevels => {
        const skillAbility = career.skillsAbilities.find(skillAbility => skillAbility._id.toString() == skillabilitylevels.lmiSkillAbilityId.toString());
        let radarCategory;
        if (skillAbility.radarCategoryId) {
          radarCategory = radarCategories.find(rc => rc._id.toString() == skillAbility.radarCategoryId.toString());
        }
        if (skillAbility && radarCategory) {
          const foundInCurrentCareers = currentCareerIds.find(currentCareer => currentCareer.toString() == career._id.toString());
          if (career._id.toString() == particularCareerId.toString() || 
            (isCurrentRoleReq && career._id.toString() == currentCareerIds[0].toString())) {
            let foundRadarValue = radarValues.find(rv => rv.name == radarCategory.name);
            if (!foundRadarValue) {
              foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
              radarValues.push(foundRadarValue);
            }
            foundRadarValue.total += skillabilitylevels.level;
            foundRadarValue.count++;
            foundRadarValue.skillsAbilities.push({ name: skillAbility.lmiName, level: skillabilitylevels.level });
          }
          // else {
          
          if (foundInCurrentCareers) {
            const foundCJSkillAbility = currentJobSkillsAbilities.find(cjskillAbility => cjskillAbility._id.toString() == skillAbility._id.toString());
            if (foundCJSkillAbility) {
              if (skillabilitylevels.level > foundCJSkillAbility.level) {
                foundCJSkillAbility.level = skillabilitylevels.level;
              }
            }
            else {
              currentJobSkillsAbilities.push({ _id: skillAbility._id, id: skillAbility.lmiId, name: skillAbility.lmiName, level: skillabilitylevels.level, radarCategory: radarCategory.name });
            }
          }
        }
      });

    }
    career.skills = skills;
    career.abilities = abilities;
    if (career._id.toString() == particularCareerId.toString()) {
      careerGoalTitle = career.title;
      careerGoalRadarValues = radarValues;
    }
    else if (isCurrentRoleReq && career._id.toString() == currentCareerIds[0].toString()) {
      currentRoleTitle = career.title;
      currentRoleRadarValues = radarValues;
    }
    else if (career._id.toString() == currentCareerIds[0].toString()) {
      currentJobTitle = career.title;
    }
  });

  // creating deep copy of initialRadarValues
  currentJobRadarValues = JSON.parse(JSON.stringify(initialRadarValues));
  currentJobSkillsAbilities.forEach(skillAbility => {
    let foundRadarValue = currentJobRadarValues.find(rv => rv.name == skillAbility.radarCategory);
    if (!foundRadarValue) {
      foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
      currentJobRadarValues.push(foundRadarValue);
    }
    foundRadarValue.total += skillAbility.level;
    foundRadarValue.count++;
    foundRadarValue.skillsAbilities.push({ name: skillAbility.name, level: skillAbility.level });
  })

  let categories = [];
  let series = [];
  let currentJobRadarData = [];
  currentJobRadarValues.forEach(rv => {
    categories.push(rv.name);
    currentJobRadarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
  });
  // if(currentCareerIds.length === 1 && from === 'skilldar'){
  //   // console.log("skilldar")
  // } else {
    // series.push({ name:"Your Skills", data: currentJobRadarData, color: "#F9B5D7" })
  // }

  series.push({ name:"Your Skills", data: currentJobRadarData, color: "#F9B5D7" })

  if (isCurrentRoleReq && currentRoleRadarValues && currentRoleRadarValues.length) {
    let currentRoleRadarData = [];
    currentRoleRadarValues.forEach(rv => {
      currentRoleRadarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
    });
    series.push({ 
      name: currentRoleTitle, 
      data:currentRoleRadarData, 
      color: from === "skilldar" && currentCareerIds.length === 1 ? 
              "#F9B5D7" : 
              colors[Math.floor(Math.random() * colors.length)]
    })
  }

  if (careerGoalRadarValues && careerGoalRadarValues.length) {
    let careerGoalRadarData = [];
    careerGoalRadarValues.forEach(rv => {
      careerGoalRadarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
    });
    series.push({ name: careerGoalTitle, data: careerGoalRadarData, color: colors[Math.floor(Math.random() * colors.length)] })
  }

  const skilldarChartData = {
    series:[...series],
    options: {
      xaxis: {
        categories: categories,
      },
    }
  }
  return { skilldarChartData, currentJobRadarValues, careerGoalRadarValues }

}

const getCollegeBySlug = async(req, res) => {
  try {
    const {slug} = req.query 
    if (!slug) {
      return messageResponse(REQUIRED, "Slug", false, 400, null, res)
    }

    const pipeline = [
      {
        $match: {
          slug
        }
      },
      {
        $lookup: {
          from: "regions",
          localField: "collegeGroupId",
          foreignField: "collegeGroupIds",
          as: "region"
        }
      },
      {
        $addFields: {
          region: { $arrayElemAt: ["$region", 0] }
        }
      },
    ]
    const college = await College.aggregate(pipeline);

    if (!college.length) return messageResponse(NOT_FOUND, "College", false, 404, null, res)

    return messageResponse(null, "", true, 200, college[0], res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getRegionBySlug = async(req, res) => {
  try {
    const {slug} = req.query 
    if (!slug) {
      return messageResponse(REQUIRED, "Slug", false, 400, null, res)
    }

    const region = await Region.find({ slug });

    if (!region.length) return messageResponse(NOT_FOUND, "Region", false, 404, null, res)

    return messageResponse(null, "", true, 200, region[0], res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCareers = async(req, res) => {
  try {
    const {IP, collegeId} = req.body
    let careers = await Career.find({},
      { onetCode: 1, title: 1, socCode: 1, description: 1, addedBy: 1 }).sort({ title: 1 });

    if (!careers.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res)
    
    // add event for get started
    const fakeData = {
      IP,
      collegeId,
      event: "GET_STARTED",
      from: "",
    }
    prepareEventLog(fakeData, res)

    return messageResponse(null, "", true, 200, careers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getNestedCareers = async (req, res) => {
  try {
    const {IP, collegeId, searchTerm} = req.body
    const pipeline = getNestedCareersAggregation(searchTerm);
    const careers = await Career.aggregate(pipeline);
    if (!careers.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res)

    // add event for get started
    const fakeData = {
      IP,
      collegeId,
      event: "GET_STARTED",
      from: "",
    }
    prepareEventLog(fakeData, res)

    return messageResponse(null, "", true, 200, careers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getSkilldarChartData = async(req, res, isReturn = true) => {
  try {
    const { IP, collegeId, regionId, careerIds, careerGoal } = req.body
    const { _id: userId } = req.user || {}
    if (!careerIds || !careerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    if (careerGoal && !mongoose.isValidObjectId(careerGoal)) {
      return messageResponse(REQUIRED, "Career Goal", false, 400, null, isReturn ? res : null);
    }
    // log event
    if(IP && collegeId) {
      if (careerGoal) {
        const fakeData = {
          IP,
          collegeId,
          event: "CAREER_GOAL",
          from: "",
          data: {
            careerGoal
          }
        }
        await prepareEventLog(fakeData, res)
      }
      const fakeData2 = {
        IP,
        collegeId,
        event: "RADARS_COUNT",
        from: "",
      }
      await prepareEventLog(fakeData2, res)
      const fakeData3 = {
        IP,
        collegeId,
        event: "CAREER_HISTORY",
        from: "",
        data: {
          careerIds
        }
      }
      await prepareEventLog(fakeData3, res)
    }

    let careerHistoryObj = {
      currentCareerIds: careerIds,
    }
    if(userId) {
      careerHistoryObj["userId"] = userId
    }
    if(careerGoal) {
      careerHistoryObj["careerGoal"] = careerGoal
    }
    if(collegeId) {
      await CareerHistory.findOneAndUpdate(
        { userId, collegeId: toObjectId(collegeId) },
        { 
          $set: {
            ...careerHistoryObj,
            collegeId
          }
        },
        { upsert: true }
      )
    }
    if(regionId) {
      await CareerHistory.findOneAndUpdate(
        { userId, regionId: toObjectId(regionId) },
        { 
          $set: {
            ...careerHistoryObj,
            regionId
          }
        },
        { upsert: true }
      )
    }

    let careerObjectIds = [];
    careerIds.forEach(career => {
      careerObjectIds.push(new mongoose.Types.ObjectId(career));
    });
    if (careerGoal) {
      careerObjectIds.push(new mongoose.Types.ObjectId(careerGoal));
    }

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },
      {
        $project: {
          "onetCode": 1,
          "title": 1,
          "socCode": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    const radarData = await prepareUpskillRadar(aggrResult, careerIds, "", null, false, "skilldar");
    // const polarRadarData = await preparePolarRadar(aggrResult, careerIds, "", null, false, "skilldar");

    return messageResponse(null, "", true, 200, radarData.skilldarChartData, isReturn ? res : null);
    // return messageResponse(null, "", true, 200, polarRadarData.polarChartData, isReturn ? res : null);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

const getTransferDetail = (jobZone) => {
  let randomTransferWindowMonths, transferWindow, transferWindowMin, transferWindowMax;
  if (jobZone == 1) {
    // Returns a random integer from 1 to 3:
    randomTransferWindowMonths = Math.floor(Math.random() * 3) + 1;
    transferWindow = "up to 3 months";
    transferWindowMin = 0;
    transferWindowMax = 0.25;
  }
  else if (jobZone == 2) {
    // Returns a random integer from 4 to 12:
    randomTransferWindowMonths = Math.floor(Math.random() * 9) + 4;
    transferWindow = "up to 1 year";
    transferWindowMin = 0;
    transferWindowMax = 1;
  }
  else if (jobZone == 3) {
    // Returns a random integer from 13 to 24:
    randomTransferWindowMonths = Math.floor(Math.random() * 12) + 13;
    transferWindow = "1 - 2 years";
    transferWindowMin = 1;
    transferWindowMax = 2;
  }
  else if (jobZone == 4) {
    // Returns a random integer from 25 to 48:
    randomTransferWindowMonths = Math.floor(Math.random() * 24) + 25;
    transferWindow = "2 - 4 years";
    transferWindowMin = 2;
    transferWindowMax = 4;
  }
  else if (jobZone == 5) {
    // Returns a random integer from 49 to 60:
    randomTransferWindowMonths = Math.floor(Math.random() * 12) + 49;
    transferWindow = "4+ years";
    transferWindowMin = 4;
    transferWindowMax = 5;
  }

  return { randomTransferWindowMonths, transferWindow, transferWindowMin, transferWindowMax };
}

const getGoalCareers = async(req, res) => {
  try {
    const { careerIds } = req.body
    if (!careerIds || !careerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, res);
    }

    let careerObjectIds = [];
    careerIds.forEach(career => {
      careerObjectIds.push(new mongoose.Types.ObjectId(career));
    });

    const pipeline = [
      // { $match: { _id: { $in: careerObjectIds } } },
      { $match: { _id: { $eq: careerObjectIds[0] } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectorsData"
        }
      },
      {
        $lookup: {
          from: "careers",
          localField: "sectorIds",
          foreignField: "sectorIds",
          as: "goalCareers"
        }
      },
      {
        $project: {
          "goalCareers._id": 1,
          "goalCareers.title": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    let goalCareers = [];
    if (aggrResult && aggrResult.length && aggrResult[0].goalCareers && aggrResult[0].goalCareers.length) {
      aggrResult[0].goalCareers.forEach(career => {
        const foundCareer = careerObjectIds.find(careerObjectId => careerObjectId.toString() == career._id.toString());
        // do not want to omit career history career in career goal list for now
        //if (!foundCareer) {
          goalCareers.push(career);
        //};
      });
    }
    goalCareers.sort((career1, career2) => {
      return career1.title.localeCompare(career2.title)
    })
    return messageResponse(null, "", true, 200, goalCareers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCareerHistory = async(req, res) => {
  try {
    const { collegeId, regionId } = req.query
    const { _id: userId } = req.user || {}

    if(!userId) {
      return messageResponse(NOT_FOUND, "UserId/CollegeId", false, 400, null, res)
    }

    if(!collegeId && !regionId) {
      return messageResponse(REQUIRED, "CollegeId or RegionId", false, 400, null, res)
    }
    
    const findQuery = { userId }
    if(collegeId) {
      findQuery["collegeId"] = toObjectId(collegeId)
    }
    if(regionId) {
      findQuery["regionId"] = toObjectId(regionId)
    }
    const careerHistory = await CareerHistory.findOne(findQuery).populate([
      {path: "currentCareerIds", select: "_id title"},
      {path: "compareCareerIds", select: "_id title"},
      {path: "careerGoal", select: "_id title"},
    ])
    if(!careerHistory) {
      return messageResponse(NOT_FOUND, "History", false, 400, null, res)
    }
    return messageResponse(null, "", true, 200, careerHistory, res)

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const sendEmail = async(req, res) => {
  try {
    const {firstName, lastName, email, collegeId, pdfB64} = req.body;
    const college = await College.findById(collegeId, { name: 1, email:1 });

    const userMessage = {
      subject: emailTemplate.skillReportUser.emailSubject, 
      html: emailTemplate.skillReportUser.htmlTemplate({firstName, lastName, college}),
      from_email: keys.NOREPLY_EMAIL,
      to:[ { email: email } ],
      attachments: [{type:"application/pdf", name:"Skills Report.pdf", content:pdfB64}]
    }
    const userEmailResponse = await mailchimpClient.messages.send({ message: userMessage });

    const collegeMessage = {
      subject: emailTemplate.skillReportCollege.emailSubject, 
      html: emailTemplate.skillReportCollege.htmlTemplate({email, firstName, lastName}),
      from_email: keys.NOREPLY_EMAIL,
      to: [ { email: college.email } ],
      attachments: [{type:"application/pdf", name:"Skills Report.pdf", content:pdfB64}]
    }
    const collegeEmailResponse = await mailchimpClient.messages.send({ message: collegeMessage });

    if(collegeEmailResponse.status == 'error' || userEmailResponse.status == "error") {
      return messageResponse(SERVER_ERROR, "", false, 500, "Something went wrong", res);
    }
    else {
      return messageResponse(SEND_EMAIL_SUCCESS, "", true, 200, null, res);
    }

  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const getCourseCount = (courses, subsectorIds, campuses) => {
  // console.log(courses, subsectorIds)
  let count = 0
  subsectorIds.forEach(subsector => {
    courses.forEach(course => {
      const foundCampus = campuses.find(campus => campus._id.toString() == course.campusId.toString());
      const foundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == subsector._id.toString());
      if (foundCampus && foundSubsector) {
        count++;
      }
    });
  });
  // courses.map(course => {
  //   let courseSubsectorIds = course.subsectorIds.map(subsectorId => subsectorId.toString())
  //   subsectorIds.map(subsectorId => {
  //     if(courseSubsectorIds.includes(subsectorId.toString())){
  //       count++
  //     }
  //   })
  // })
  return count
}

const getCourseCountRegion = (courses, subsectorIds, colleges) => {
  // console.log(courses, subsectorIds)
  let count = 0
  subsectorIds.forEach(subsector => {
    courses.forEach(course => {
      const foundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == subsector._id.toString());
      colleges.map(college => {
        const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
        if (foundCampus && foundSubsector) {
          count++;
        }
      })
    });
  });
  // courses.map(course => {
  //   let courseSubsectorIds = course.subsectorIds.map(subsectorId => subsectorId.toString())
  //   subsectorIds.map(subsectorId => {
  //     if(courseSubsectorIds.includes(subsectorId.toString())){
  //       count++
  //     }
  //   })
  // })
  return count
}

const getWidgetCareers = async (req, res, isReturn = true) => {
  try {
    const { url: courseURL } = req.query;

    if (!courseURL) {
      return messageResponse(REQUIRED, "Course URL", false, 400, null, isReturn ? res : null);
    }

    // Step 1: Fetch course with campus and college
    const course = await Course.findOne({pageURL: courseURL}).populate([
      { path: "campusId", select: "name collegeId" }
    ]);

    if (!course || !course.campusId || !course.campusId.collegeId) {
      return messageResponse(NOT_FOUND, "Course", false, 400, null, isReturn ? res : null);
    }

    const courseCampusId = course.campusId._id;
    const collegeId = course.campusId.collegeId;
    const sectorIds = course.sectorIds || [];
    const subsectorIds = course.subsectorIds || [];

    const college = await College.aggregate([
      { $match: { _id: new mongoose.Types.ObjectId(collegeId) } },
      {
        $lookup: {
          from: "regions",
          localField: "collegeGroupId",
          foreignField: "collegeGroupIds",
          as: "region"
        }
      },
    ])
    const regionId = college[0].region[0]._id;

    // Step 2: Fetch careers related to sectors or subsectors
    const careers = await Career.aggregate([
      {
        $match: {
          sectorIds: { $in: sectorIds },
          subsectorIds: { $in: subsectorIds }
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectorIds",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $project: {
          title: 1,
          jobZone: 1,
          interests: 1,
          subsectors: { _id: 1, name: 1, priority: 1 },
          careerSalary: { $first: "$careerSalary" },
          courses: {
            _id: 1,
            title: 1,
            campusId: 1,
            subsectorIds: 1
          }
        }
      }
    ]);

    // Step 3: Filter careers by courses available at the same college
    const filteredCareers = careers.map(career => {
      const validCourses = career.courses.filter(course =>
        course.campusId.toString() === courseCampusId.toString()
      );

      if (!validCourses.length) return null;

      const transferDetail = getTransferDetail(career.jobZone);

      return {
        id: career._id,
        name: career.title,
        transferWindowMin: transferDetail.transferWindowMin,
        transferWindowMax: transferDetail.transferWindowMax,
        transferWindow: transferDetail.transferWindow,
        compare: false,
        salary: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
        subsectors: career.subsectors,
        interests: career.interests,
        coursesAvailable: validCourses.length,
        careerType: "NORMAL",
      };
    }).filter(Boolean);

    if (!filteredCareers.length) {
      return messageResponse(DATA_NOT_FOUND, "Careers", false, 404, null, isReturn ? res : null);
    }

    // Step 4: Group by subsector
    const groupedBySubsector = {};

    filteredCareers.forEach(career => {
      career.subsectors.forEach(subsector => {
        const key = subsector._id.toString();
        if (!groupedBySubsector[key]) {
          groupedBySubsector[key] = {
            name: subsector.name,
            priority: subsector.priority || false,
            color: reSkillsAndUpSkillscolors[Object.keys(groupedBySubsector).length % reSkillsAndUpSkillscolors.length],
            careers: []
          };
        }
        groupedBySubsector[key].careers.push(career);
      });
    });

    // Step 5: Sort careers and subsectors
    Object.values(groupedBySubsector).forEach(group => {
      group.careers.sort((a, b) => a.name.localeCompare(b.name));
    });

    const sortedSubsectors = Object.values(groupedBySubsector).sort((a, b) => {
      if (a.priority === b.priority) {
        return b.careers.length - a.careers.length;
      }
      return a.priority ? -1 : 1;
    });

    const result = {
      sectorName: "",
      sectorOrSubsector: sortedSubsectors
    };

    return messageResponse(null, "", true, 200, result, isReturn ? res : null);

  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null);
  }
};


// controllers for college based career flow
const getSkillsReportData = async (req, res) => {
  try {
    const {IP, careerRegionId, collegeId, currentCareerIds, careerGoal, compareCareerIds, callFrom} = req.body
    if (!collegeId || !mongoose.isValidObjectId(collegeId)) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, res);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Current Career IDs", false, 400, null, res);
    }

    if (careerGoal) {
      if (!mongoose.isValidObjectId(careerGoal)) {
        return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, res);
      }
    }

    // if (!compareCareerIds || !compareCareerIds.length) {
    //   return messageResponse(REQUIRED, "Compare Career IDs", false, 400, null, res);
    // }

    // log event
    const fakeData = {
      IP,
      collegeId,
      event: "SKILL_REPORT",
      from: callFrom,
    }
    await prepareEventLog(fakeData, res)

    // let careerObjectIds = [new mongoose.Types.ObjectId(currentCareerIds[0])];
    let careerObjectIds = [];
    currentCareerIds.forEach(career => {
      if (mongoose.isValidObjectId(career)) {
        careerObjectIds.push(new mongoose.Types.ObjectId(career));
      }
    });
    if (careerGoal) {
      if (careerObjectIds.includes(careerGoal)) {
        careerGoal = "";
      }
      else {
        careerObjectIds.push(new mongoose.Types.ObjectId(careerGoal));
      }
    }
    if(compareCareerIds && compareCareerIds.length){
      compareCareerIds.forEach(career => {
        if (mongoose.isValidObjectId(career)) {
          if (!careerObjectIds.includes(career)) {
            careerObjectIds.push(new mongoose.Types.ObjectId(career));
          }
        }
      });
    }

    const collegePipeline = [
      { $match: { _id: new mongoose.Types.ObjectId(collegeId) } },
      {
        $lookup: {
          from: 'campuses',
          localField: '_id',
          foreignField: 'collegeId',
          as: 'campuses'
        }
      },
      {
        $project: {
          "campuses._id": 1
        }
      }
    ]
    let college = await College.aggregate(collegePipeline)
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, res);
    }
    college = college[0];

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectorIds",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerHours",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerHours"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerGrowth",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerGrowth"
        }
      },
      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobzone"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },

      {
        $project: {
          "title": 1,
          "description": 1,
          "tasks": 1,
          "jobZone": 1,
          "onetCode": 1,
          "socCode": 1,
          "sectorIds": 1,
          "subsectorIds": 1,

          "courses.code": 1,
          "courses.level": 1,
          "courses.duration": 1,
          "courses.title": 1,
          "courses.description": 1,
          "courses.pageURL": 1,
          "courses.applyURL": 1,
          "courses.enquiryURL": 1,
          "courses.campusId": 1,

          careerHours: { $first: "$careerHours" },
          careerSalary: { $first: "$careerSalary" },
          careerGrowth: { $first: "$careerGrowth" },

          "jobzone.job_training": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, res);
    }

    // return messageResponse(null, "", true, 200, aggrResult, res);

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});

    let currentRoleData, careerGoalData, yourSkills;
    let compareCareersData = [], colorIndex = colors.length - 1;
    // aggrResult.forEach((career) => {
    for (let career of aggrResult) {
      if (currentCareerIds.includes(career._id.toString())) {
        if (currentCareerIds[0].toString() != career._id.toString()) {
          continue;
        }
      }

      let noOfCourse = 0, firstCourse, courses = [];
      career.courses.forEach(course => {
        const foundCampusInCourse = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
        if (foundCampusInCourse) {
          if (!firstCourse) {
            firstCourse = course;
          }
          noOfCourse++;
          courses.push({
            code: course.code,
            title: course.title,
            level: course.level,
            duration: course.duration || "",
          })
        }
      });

      let overallGrowth = "N/A";
      if (career.careerGrowth) {
        let predictByYear = career.careerGrowth.values;
        const sortedYears = Object.keys(predictByYear)
          .map(year => ({
            year,
            value: predictByYear[year]
          }))
          .sort((a, b) => Number(a.year) - Number(b.year));
        
        overallGrowth = (sortedYears[sortedYears.length - 1].value > sortedYears[0].value ? "Growth" : "Decline");
      }

      const { _id, title, jobZone } = career;

      let transferDetail = getTransferDetail(jobZone);

      const careerData = {
        id: _id,
        title,
        color: colors[colorIndex--],
        transferWindow: transferDetail.transferWindow,
        noOfCourse,
        courses,
        estimateHours: career.careerHours?.medianValue || career.careerHours?.meanValue || "N/A",
        estimatePayWeek: career.careerSalary?.medianValue / 52 || career.careerSalary?.meanValue / 52 || "N/A",
        estimatePayYear: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
        overallGrowth,
        levelRange: firstCourse ? firstCourse.level : '',
      }
      // prepare skilldar for your skills
      let particularCareerId = career._id;
      if (currentCareerIds[0].toString() == career._id.toString()) {
        const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, "", radarCategories);
        careerData.skilldarChartData = radarData.skilldarChartData
        careerData.radarDetail = radarData.currentJobRadarValues;
        careerData.careerType = "YOUR_SKILLS";
        yourSkills = careerData;
        yourSkills = JSON.parse(JSON.stringify(yourSkills))
      }
      // prepare skilldar for current and selected career
      const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, particularCareerId, radarCategories);

      careerData.skilldarChartData = radarData.skilldarChartData
      if (currentCareerIds[0].toString() == career._id.toString()) {
        careerData.radarDetail = radarData.currentJobRadarValues;
        careerData.careerType = "CURRENT_ROLE";
        currentRoleData = careerData;
      }
      else if (careerGoal && careerGoal.toString() == career._id.toString()) {
        careerData.radarDetail = radarData.currentJobRadarValues;
        careerData.careerType = "CAREER_GOAL";
        careerGoalData = careerData;
      }
      else {
        careerData.careerType = "COMPARE_CAREER";
        compareCareersData.push(careerData);
      }
    };

    if (careerGoalData) {
      compareCareersData.unshift(careerGoalData);
    }
    compareCareersData.unshift(currentRoleData);

    const {firstName, lastName, email} = req.body
    let returnObject = {
      reportForVisitorName: firstName + " " + lastName,
      firstName,
      lastName,
      email,
      currentRole: currentRoleData,
      careerGoal: careerGoalData,
      careers: compareCareersData,
      yourSkills
    };

    // return messageResponse(null, "", true, 200, currentCareerData, res)
    return messageResponse(null, "", true, 200, returnObject, res);

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCompareCareersData = async(req, res) => {
  try {
    const {collegeId, careerRegionId, currentCareerIds, careerGoal, compareCareerIds} = req.body
    const { _id: userId } = req.user || {}
    if (!collegeId || !mongoose.isValidObjectId(collegeId)) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, res);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Current Career IDs", false, 400, null, res);
    }

    if (careerGoal) {
      if (!mongoose.isValidObjectId(careerGoal)) {
        return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, res);
      }
    }

    // if (!compareCareerIds || !compareCareerIds.length) {
    //   return messageResponse(REQUIRED, "Compare Career IDs", false, 400, null, res);
    // }

    // let careerObjectIds = [new mongoose.Types.ObjectId(currentCareerIds[0])];
    let careerObjectIds = [];
    currentCareerIds.forEach(career => {
      if (mongoose.isValidObjectId(career)) {
        careerObjectIds.push(new mongoose.Types.ObjectId(career));
      }
    });
    if (careerGoal) {
      if (careerObjectIds.includes(careerGoal)) {
        careerGoal = "";
      }
      else {
        careerObjectIds.push(new mongoose.Types.ObjectId(careerGoal));
      }
    }
    if(compareCareerIds && compareCareerIds.length){
      if(userId) {
        await CareerHistory.findOneAndUpdate(
          { userId, collegeId: toObjectId(collegeId) },
          { 
            $set: {
              compareCareerIds,
            }
          }
        )
      }
      compareCareerIds.forEach(career => {
        if (mongoose.isValidObjectId(career)) {
          if (!careerObjectIds.includes(career)) {
            careerObjectIds.push(new mongoose.Types.ObjectId(career));
          }
        }
      });
    }

    const collegePipeline = [
      { $match: { _id: new mongoose.Types.ObjectId(collegeId) } },
      {
        $lookup: {
          from: 'campuses',
          localField: '_id',
          foreignField: 'collegeId',
          as: 'campuses'
        }
      },
      {
        $project: {
          "campuses._id": 1
        }
      }
    ]
    let college = await College.aggregate(collegePipeline)
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, res);
    }
    college = college[0];

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectorIds",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerHours",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerHours"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobzone"
        }
      },

      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },

      {
        $project: {
          "title": 1,
          "description": 1,
          "tasks": 1,
          "jobZone": 1,
          "onetCode": 1,
          "socCode": 1,
          "sectorIds": 1,
          "subsectorIds": 1,

          "courses.code": 1,
          "courses.level": 1,
          "courses.duration": 1,
          "courses.title": 1,
          "courses.description": 1,
          "courses.pageURL": 1,
          "courses.applyURL": 1,
          "courses.enquiryURL": 1,
          "courses.campusId": 1,
          "courses.subsectorIds": 1,

          careerHours: { $first: "$careerHours" },
          careerSalary: { $first: "$careerSalary" },

          "jobzone.job_training": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, res);
    }

    // return messageResponse(null, "", true, 200, aggrResult, res);

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});

    let currentRoleData, careerGoalData;
    let compareCareersData = [], colorIndex = colors.length - 1;
    // aggrResult.forEach((career) => {
    for (let career of aggrResult) {
      if (currentCareerIds.includes(career._id.toString())) {
        if (currentCareerIds[0].toString() != career._id.toString()) {
          continue;
        }
      }

      let noOfCourse = 0, firstCourse;
      career.courses.forEach(course => {
        const foundCampusInCourse = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
        if (foundCampusInCourse) {
          if (!firstCourse) {
            firstCourse = course;
          }
          noOfCourse++;
        }
      });

      let particularCareerId = career._id;
      // prepare skilldar for current and selected career
      const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, particularCareerId, radarCategories);

      const { _id, title, jobZone } = career;
      let transferDetail = getTransferDetail(jobZone);
      const careerData = {
        id: _id,
        title,
        color: colors[colorIndex--],
        transferWindow: transferDetail.transferWindow,
        noOfCourse,
        estimateHours: career.careerHours?.medianValue || career.careerHours?.meanValue || "N/A",
        estimatePayWeek: career.careerSalary?.medianValue / 52 || career.careerSalary?.meanValue / 52 || "N/A",
        estimatePayYear: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
        overallGrowth: 'Growth',
        regionalGrowth: 'Growth',
        levelRange: firstCourse ? firstCourse.level : '',
        courseDuration: firstCourse ? firstCourse.duration ? firstCourse.duration : '' : '',
        skilldarChartData: radarData.skilldarChartData,
      }

      if (currentCareerIds[0].toString() == career._id.toString()) {
        careerData.careerType = "CURRENT_ROLE"
        currentRoleData = careerData;
      }
      else if (careerGoal && careerGoal.toString() == career._id.toString()) {
        careerData.careerType = "CAREER_GOAL"
        careerGoalData = careerData;
      }
      else {
        careerData.careerType = "COMPARE_CAREER"
        compareCareersData.push(careerData);
      }
    };

    if (careerGoalData) {
      compareCareersData.unshift(careerGoalData);
    }
    compareCareersData.unshift(currentRoleData);

    // return messageResponse(null, "", true, 200, currentCareerData, res)
    return messageResponse(null, "", true, 200, compareCareersData, res);

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCareersNCoursesDetails = async(req, res) => {
  try {
    const { IP, collegeId, careerRegionId, selectedCareerId, currentCareerIds } = req.body

    if (!collegeId || !mongoose.isValidObjectId(collegeId)) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, res);
    }

    if (!selectedCareerId || !currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, res);
    }
    // log event
    const fakeData = {
      IP,
      collegeId,
      event: "CAREER_VIEW",
      from: "",
      data: {
        selectedCareerId
      }
    }
    await prepareEventLog(fakeData, res)

    const collegePipeline = [
      { $match: { _id: new mongoose.Types.ObjectId(collegeId) } },
      {
        $lookup: {
          from: 'campuses',
          localField: '_id',
          foreignField: 'collegeId',
          as: 'campuses'
        }
      },
      {
        $project: {
          "campuses._id": 1,
          "campuses.name": 1
        }
      }
    ]
    let college = await College.aggregate(collegePipeline)
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, res);
    }
    college = college[0];

    // let careerObjectIds = [new mongoose.Types.ObjectId(currentCareerIds[0])];
    let careerObjectIds = [];
    currentCareerIds.forEach(career => {
      careerObjectIds.push(new mongoose.Types.ObjectId(career));
    });
    if (!currentCareerIds.includes(selectedCareerId)) {
      careerObjectIds.push(new mongoose.Types.ObjectId(selectedCareerId));
    }

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "selectedCareerSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobzone"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerWorktype",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerWorktype"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerQualification",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerQualification"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerGrowth",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerGrowth"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerHours",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerHours"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },

      {
        $project: {
          "title": 1,
          "description": 1,
          "videoUrl": 1,
          "tasks": 1,
          "jobZone": 1,
          "onetCode": 1,
          "socCode": 1,

          "sectors._id": 1,
          "sectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,

          "selectedCareerSubsectors._id": 1,
          "selectedCareerSubsectors.name": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.level": 1,
          "courses.duration": 1,
          "courses.title": 1,
          "courses.description": 1,
          "courses.pageURL": 1,
          "courses.applyURL": 1,
          "courses.enquiryURL": 1,
          "courses.campusId": 1,
          "courses.subsectorIds": 1,

          "careerHours": {$first: "$careerHours"},
          "careerSalary": {$first: "$careerSalary"},
          "careerWorktype": {$first: "$careerWorktype"},
          "careerQualification": {$first: "$careerQualification"},
          "careerGrowth": {$first: "$careerGrowth"},

          "jobzone.job_training": 1,
          "jobzone.job_training_custom": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, res);
    }

    // return messageResponse(null, "", true, 200, aggrResult, res);

    const selectedCareer = aggrResult.find(career => career._id.toString() === selectedCareerId.toString());
    const { _id, title, description, videoUrl, tasks, courses,
      jobzone, careerHours, careerSalary } = selectedCareer
    let transferDetail = getTransferDetail(selectedCareer.jobZone);
    const numFormat = Intl.NumberFormat('en-US')
    let careerNCourseDetails = {
      id: _id,
      title,
      description,
      videoUrl,
      tasks: tasks.split('\n'),
      averageHours: `${careerHours?.medianValue || careerHours?.meanValue || "N/A"}`,
      averageSalary: {
        weekly: `${careerSalary?.medianValue / 52 || careerSalary?.meanValue / 52 || "N/A"}`,
        yearly: `${careerSalary?.medianValue || careerSalary?.meanValue || "N/A"}`,
      },
      transferWindow: {
        duration: transferDetail.transferWindow,
        description: jobzone[0].job_training_custom,
      }
    }

    let coursesAsPerCollege = []
    selectedCareer.selectedCareerSubsectors.forEach(subsector => {
      selectedCareer.courses.forEach(course => {
        const foundSubsector = course.subsectorIds.find(subsectorId => subsectorId.toString() === subsector._id.toString())
        const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
        if (foundSubsector && foundCampus) {
          course.campusName = foundCampus.name
          coursesAsPerCollege.push(course);
        }
      });
    })
    coursesAsPerCollege = coursesAsPerCollege.reduce((groups, course) => {
      const {
        _id,
        code,
        title,
        description,
        level,
        duration,
        campusId,
        pageURL,
        applyURL,
        enquiryURL,
        campusName,
      } = course
      let foundCourse = groups.find(
        group => 
          group.code === code && 
          group.title === title && 
          group.description === description && 
          group.level === level && 
          group.duration === duration || ""
      )
      if(!foundCourse){
        foundCourse = {
          id: _id,
          code,
          title,
          description,
          level,
          duration: duration || "",
          campus: [],
        }
        groups.push(foundCourse)
      }
      foundCourse.campus.push({
        id: campusId,
        name: campusName,
        pageURL,
        applyURL,
        enquiryURL,
      })
      return groups
    }, [])
    coursesAsPerCollege.sort((course1, course2) => {
      return course1.title.localeCompare(course2.title)
    })
    careerNCourseDetails.courses = coursesAsPerCollege;
    // prepare growthData
    const growthData = {}
    // prepare overallGrowth data
    const predictByYear = selectedCareer.careerGrowth?.values || { "2025": 0, "2026": 0, "2027": 0, "2028": 0, "2029": 0, "2030": 0, "2031": 0, "2032": 0, "2033": 0, "2034": 0, "2035": 0 };
    const sortedYears = Object.keys(predictByYear)
      .map(year => ({
        year,
        value: predictByYear[year]
      }))
      .sort((a, b) => Number(a.year) - Number(b.year));

    const startYear = sortedYears[0];
    const endYear = sortedYears[sortedYears.length - 1];

    let overallGrowthDetails = () => {
      const overallGrowthNumber = endYear?.value - startYear?.value;
      const overallGrowthPercentage = overallGrowthNumber
        ? Math.round(((overallGrowthNumber / startYear?.value) * 100) * 100) / 100
        : 0;

      return {
        overallGrowthNumber: numFormat.format(overallGrowthNumber || 0),
        overallGrowthPercentage
      };
    };

    growthData.overallGrowth = {
      duration: `${startYear?.year}-${endYear?.year}`,
      overallGrowthNumber: overallGrowthDetails().overallGrowthNumber,
      overallGrowthPercentage: overallGrowthDetails().overallGrowthPercentage,
    };

    // prepare QualificationGrowth data
    const predictByQualification = selectedCareer.careerQualification?.values || { "0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0 }
    const qualificationChartData = {
      series: [{
        name: 'Qualification Growth',
        data: Object.values(predictByQualification)
      }],
      xaxis: {
        categories: Object.keys(predictByQualification)
      }
    }
    growthData.QualificationGrowth = {
      year: endYear?.year,
      chartData: qualificationChartData
    }

    // prepare workTypeGrowth data
    growthData.workTypeGrowth = {
      year: endYear?.year,
      fullTime: numFormat.format(selectedCareer.careerWorktype?.FTValue) || 0,
      partTime: numFormat.format(selectedCareer.careerWorktype?.PTValue) || 0,
      selfEmployeed: numFormat.format(selectedCareer.careerWorktype?.SEValue) || 0,
    }
    careerNCourseDetails.growthData = growthData

    // prepare skilldar for current and selected career
    const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, selectedCareerId)
    careerNCourseDetails.skilldarChartData = radarData.skilldarChartData

    // return messageResponse(null, "", true, 200, currentCareerData, res)
    return messageResponse(null, "", true, 200, careerNCourseDetails, res)

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCareersNUpskillTimeNew = async (req, res, isReturn = true) => {
  try {
    const {IP, collegeId, careerRegionId} = req.body
    const { _id: userId } = req.user || {}
    if (!req.body.collegeId) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!mongoose.isValidObjectId(req.body.collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!req.body.currentCareerIds || !req.body.currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    if (req.body.careerGoal && !mongoose.isValidObjectId(req.body.careerGoal)) {
      return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, isReturn ? res : null);
    }

    if (userId) {
      await CareerHistory.findOneAndUpdate(
        { userId, collegeId: toObjectId(collegeId) },
        { 
          $set: {
            skillType: "upskill"
          }
        }
      )
    }


    const collegePipeline = [
      { $match: { _id: { $eq: new mongoose.Types.ObjectId(req.body.collegeId) } } },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
    ]
    let college = await College.aggregate(collegePipeline);
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, isReturn ? res : null);
    }
    college = college[0];

    // const colors = [
    //   '#24D92B',
    //   '#F916E4',
    //   '#17BDFA',
    //   '#FF4D1B',
    //   '#24265A',
    //   '#F6C017',
    //   '#C74710',
    //   '#9BA6EF',
    //   '#033201',
    //   '#1B47DE',
    //   '#F5A864',
    //   '#2DE8B4',
    //   '#D2626F',
    //   '#08D2C8',
    //   '#EE8851'
    // ];

    let careerObjectIds = [new mongoose.Types.ObjectId(req.body.currentCareerIds[0])];

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "currentRoleSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "careers",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "careers"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $project: {
          "title": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "jobZone": 1,

          "sectors._id": 1,
          "sectors.name": 1,
          "sectors.priority": 1,

          "currentRoleSubsectors._id": 1,
          "currentRoleSubsectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,
          "subsectors.priority": 1,

          "careers._id": 1,
          "careers.title": 1,
          "careers.subsectorIds": 1,
          "careers.jobZone": 1,
          "careers.interests": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.title": 1,
          "courses.subsectorIds": 1,
          "courses.campusId": 1,
          "courses.duration": 1,

          careerSalary: { $first: "$careerSalary" },
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    const currentRole = aggrResult[0];

    let coursesAsPerCollege = [];
    currentRole.courses.forEach(course => {
      const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
      if (foundCampus) {
        coursesAsPerCollege.push(course);
      }
    });
    currentRole.courses = coursesAsPerCollege;

    // return messageResponse(null, "", true, 200, currentRole, isReturn ? res : null);

    let sectorOrSubsector = [], colorIndex = 0;
    let returnResult = { sectorName: currentRole.sectors[0].name };

    currentRole.subsectors.forEach(subsector => {
      let sectorOrSubsectorObj = {
        name: subsector.name,
        color: reSkillsAndUpSkillscolors[colorIndex++],
        priority: subsector.priority || false,
        careers: []
      }

      currentRole.careers.forEach(career => {
        const foundSubsector = career.subsectorIds.find(careerSubsector => careerSubsector.toString() == subsector._id.toString());
        if (foundSubsector) {
          let transferDetail = getTransferDetail(career.jobZone), noOfCourse = 0, careerType = "";
          let careerObj = {
            id: career._id,
            name: career.title,
            transferWindowMin: transferDetail.transferWindowMin,
            transferWindowMax: transferDetail.transferWindowMax,
            transferWindow: transferDetail.transferWindow,
            compare: false,
            interests: career.interests,
            salary: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
          }

          currentRole.courses.forEach(course => {
            const courseFoundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == foundSubsector.toString())
            if(courseFoundSubsector){
              noOfCourse++
            }
          })

          if(career._id.toString() === req.body.currentCareerIds[0].toString()){
            careerType = "CURRENT_ROLE"
          } else if(req.body.careerGoal && (career._id.toString() === req.body.careerGoal.toString())) {
            careerType = "CAREER_GOAL"
          } else {
            careerType = "NORMAL"
          }
          careerObj.coursesAvailable = noOfCourse
          careerObj.careerType = careerType
          if(careerObj.coursesAvailable > 0) {
            sectorOrSubsectorObj.careers.push(careerObj)
          }
        }
      });
      sectorOrSubsectorObj.careers.sort((career1, career2) => {
        return career1.name.localeCompare(career2.name)
      })
      if(sectorOrSubsectorObj.careers.length) {
        sectorOrSubsector.push(sectorOrSubsectorObj)
      }
    });
   sectorOrSubsector.sort((a, b) => {
     // Priority subsectors come first
     if (a.priority === b.priority) {
       // If both are same priority, sort by number of careers (desc)
       return b.careers.length - a.careers.length;
     }
     return a.priority ? -1 : 1; // true comes before false
   });


    returnResult.sectorOrSubsector = sectorOrSubsector;
    return messageResponse(null, "", true, 200, returnResult, isReturn ? res : null)

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

const getCareersNReskillTimeNew = async (req, res, isReturn = true) => {
  try {
    const { collegeId, currentCareerIds, careerRegionId } = req.body;
    const { _id: userId } = req.user || {}
    if (!collegeId) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!mongoose.isValidObjectId(collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    if (userId) {
      await CareerHistory.findOneAndUpdate(
        { userId, collegeId: toObjectId(collegeId) },
        { 
          $set: {
            skillType: "reskill"
          }
        }
      )
    }

    const collegePipeline = [
      { $match: { _id: { $eq: new mongoose.Types.ObjectId(collegeId) } } },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
    ]
    let college = await College.aggregate(collegePipeline);
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, isReturn ? res : null);
    }
    college = college[0];

    const bubbleSize = 25;

    // const colors = [
    //   '#24D92B',
    //   '#F916E4',
    //   '#17BDFA',
    //   '#FF4D1B',
    //   '#24265A',
    //   '#F6C017',
    //   '#C74710',
    //   '#9BA6EF',
    //   '#033201',
    //   '#1B47DE',
    //   '#F5A864',
    //   '#2DE8B4',
    //   '#D2626F',
    //   '#08D2C8',
    //   '#EE8851'
    // ];

    const pipeline = [
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "currentRoleSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "currentRoleSubsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },
      {
        $project: {
          "_id": 1,
          "title": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "jobZone": 1,

          "sectors._id": 1,
          "sectors.name": 1,
          "sectors.priority": 1,

          "currentRoleSubsectors._id": 1,
          "currentRoleSubsectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,
          "subsectors.priority": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.title": 1,
          "courses.sectorIds": 1,
          "courses.subsectorIds": 1,
          "courses.campusId": 1,
          "courses.duration": 1,

          careerSalary: { $first: "$careerSalary" },

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    // return messageResponse(null, "", true, 200, aggrResult, isReturn ? res : null);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});
    const initialRadarValues = [];
    radarCategories.forEach(radar => {
      initialRadarValues.push({ name: radar.name, total: 0, count: 0, skillsAbilities: [] });
    });

    const currentCareers = aggrResult.filter(career => currentCareerIds.includes(career._id.toString()));

    // return messageResponse(null, "", true, 200, currentCareers, isReturn ? res : null);

    // prepare skilldar for current and selected career
    const currentRoleRadarData = await prepareUpskillRadar(currentCareers, currentCareerIds, null, radarCategories);

    let skillMatchingPercentage;
    let noOfSkillsToMatch;
    const getSetting = await settingSchema.findOne({})
    skillMatchingPercentage = getSetting && getSetting.skillMatchingPercentage ? getSetting.skillMatchingPercentage : 5
    noOfSkillsToMatch =  getSetting && getSetting.noOfSkillsToMatch ? getSetting.noOfSkillsToMatch : 5;
   // console.log(skillMatchingPercentage,noOfSkillsToMatch);

    let newRadarData = [];
    currentRoleRadarData.skilldarChartData.series[0].data.forEach(value => {
      const maxRadarValueAsPerAlgorithm = value + (value * skillMatchingPercentage / 100);
      const minRadarValueAsPerAlgorithm = value - (value * skillMatchingPercentage / 100);
      const radarValues = { value, minRadarValueAsPerAlgorithm, maxRadarValueAsPerAlgorithm };
      newRadarData.push(radarValues);
    })
    currentRoleRadarData.radarData = newRadarData;

    // return messageResponse(null, "", true, 200, aggrResult, isReturn ? res : null);

    let colorIndex = 0, noOfCourse = 0;
    let returnResult = { sectorName: "", sectorOrSubsector: [] };
    // for current role

    let currentRole = aggrResult.find(career => career._id.toString() == currentCareerIds[0].toString());
    let transferDetail = getTransferDetail(currentRole.jobZone);
    currentRole.sectors.forEach(sector => {
      let sectorOrSubsectorObj = {
        name: sector.name,
        color: reSkillsAndUpSkillscolors[colorIndex++],
        priority: sector.priority || false,
        careers: []
      }
      let careerObj = {
        id: currentRole._id,
        name: currentRole.title,
        transferWindowMin: transferDetail.transferWindowMin,
        transferWindowMax: transferDetail.transferWindowMax,
        transferWindow: transferDetail.transferWindow,
        coursesAvailable: getCourseCount(currentRole.courses, currentRole.currentRoleSubsectors, college.campuses),
        compare: false,
        salary: currentRole.careerSalary?.medianValue || currentRole.careerSalary?.meanValue || "N/A",
      }
      careerObj.careerType = "CURRENT_ROLE"
      if(careerObj.coursesAvailable > 0){
        sectorOrSubsectorObj.careers.push(careerObj)
      }
      returnResult.sectorOrSubsector.push(sectorOrSubsectorObj)
    });
    //

    aggrResult.forEach(career => {

      // if career is in current careers then no need to do anything in loop
      if (currentCareerIds.includes(career._id.toString())) {
        // console.log('career is skipped due to current career', career._id, career.title);
        return;
      }

      let noOfCourse = 0;
      // currentRole.currentRoleSubsectors.forEach(subsector => {
      //   currentRole.courses.forEach(course => {
      //     const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
      //     const foundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == subsector._id.toString());
      //     if (foundCampus && foundSubsector) {
      //       noOfCourse++;
      //     }
      //   });
      // });

      noOfCourse = getCourseCount(career.courses, career.currentRoleSubsectors, college.campuses);

      // if career has no belonging courses then no need to do anything in loop
      if (!noOfCourse) {
        // console.log('career is skipped due to course not available', career._id, career.title);
        return;
      }

      let skills = [], abilities = [];
      // creating deep copy of firstRadarValues
      radarValues = JSON.parse(JSON.stringify(initialRadarValues));
      if (career.lmiSkillAbilityLevels && career.lmiSkillAbilityLevels.length > 0) {
        career.lmiSkillAbilityLevels.forEach(skillabilitylevels => {
          const skillAbility = career.skillsAbilities.find(skillAbility => skillAbility._id.toString() == skillabilitylevels.lmiSkillAbilityId.toString());
          let radarCategory;
          if (skillAbility.radarCategoryId) {
            radarCategory = radarCategories.find(rc => rc._id.toString() == skillAbility.radarCategoryId.toString());
          }
          if (skillAbility && radarCategory) {
            let foundRadarValue = radarValues.find(rv => rv.name == radarCategory.name);
            if (!foundRadarValue) {
              foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
              radarValues.push(foundRadarValue);
            }
            foundRadarValue.total += skillabilitylevels.level;
            foundRadarValue.count++;
            foundRadarValue.skillsAbilities.push({ name: skillAbility.lmiName, level: skillabilitylevels.level });
          }
        });
      }
      career.skills = skills;
      career.abilities = abilities;

      let radarData = [];
      for (let rv of radarValues) {
        radarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
      }
      career.radarData = radarData;

      let noOfMatchedSkills = 0;
      career.radarData.forEach((radar, index) => {
        if (radar >= currentRoleRadarData.radarData[index].minRadarValueAsPerAlgorithm && radar <= currentRoleRadarData.radarData[index].maxRadarValueAsPerAlgorithm) {
          noOfMatchedSkills++;
        }
      });
      
      if (noOfMatchedSkills >= noOfSkillsToMatch) {
        // console.log('career is selected', career._id, career.title);

        // console.log("sector---", returnResult)
        career.sectors.forEach(sector => {
          let foundSector = returnResult.sectorOrSubsector.find(result => result.name === sector.name)
          if(!foundSector){
            foundSector = {name: sector.name, color: reSkillsAndUpSkillscolors[colorIndex++], priority : sector.priority, careers: []}
            returnResult.sectorOrSubsector.push(foundSector)
          }
          let foundCareer = foundSector.careers.find(c => c.id.toString() === career._id.toString())
          if(!foundCareer){
            let transferDetail = getTransferDetail(career.jobZone);
            foundCareer = {
              id: career._id,
              name: career.title,
              transferWindowMin: transferDetail.transferWindowMin,
              transferWindowMax: transferDetail.transferWindowMax,
              transferWindow: transferDetail.transferWindow,
              compare: false,
              coursesAvailable: noOfCourse,
              careerType: "NORMAL",
            }
            if(foundCareer.coursesAvailable > 0){
              foundSector.careers.push(foundCareer)
            }
          }
          foundSector.careers.sort((career1, career2) => {
            return career1.name.localeCompare(career2.name)
          })
        });
      }
    });

    returnResult.sectorOrSubsector.sort((sectorOrSubsector1, sectorOrSubsector2) => {

      if( sectorOrSubsector1.priority === sectorOrSubsector2.priority) {
        // If both are same priority, sort by number of careers (desc)
        return sectorOrSubsector2.careers.length - sectorOrSubsector1.careers.length;
      }

      return sectorOrSubsector1.priority ? -1 : 1; // true comes before false
    })

    return messageResponse(null, "", true, 200, returnResult, isReturn ? res : null);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

// controllers for region based career flow
const getSkillsReportDataForRegion = async (req, res) => {
  try {
    const {IP, collegeId, currentCareerIds, careerGoal, compareCareerIds, callFrom, regionId} = req.body
    if (!regionId || !mongoose.isValidObjectId(regionId)) {
      return messageResponse(REQUIRED, "Region ID", false, 400, null, res);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Current Career IDs", false, 400, null, res);
    }

    if (careerGoal) {
      if (!mongoose.isValidObjectId(careerGoal)) {
        return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, res);
      }
    }

    // if (!compareCareerIds || !compareCareerIds.length) {
    //   return messageResponse(REQUIRED, "Compare Career IDs", false, 400, null, res);
    // }

    // log event
    // const fakeData = {
    //   IP,
    //   collegeId,
    //   event: "SKILL_REPORT",
    //   from: callFrom,
    // }
    // await prepareEventLog(fakeData, res)

    // let careerObjectIds = [new mongoose.Types.ObjectId(currentCareerIds[0])];
    let careerObjectIds = [];
    currentCareerIds.forEach(career => {
      if (mongoose.isValidObjectId(career)) {
        careerObjectIds.push(new mongoose.Types.ObjectId(career));
      }
    });
    if (careerGoal) {
      if (careerObjectIds.includes(careerGoal)) {
        careerGoal = "";
      }
      else {
        careerObjectIds.push(new mongoose.Types.ObjectId(careerGoal));
      }
    }
    if(compareCareerIds && compareCareerIds.length){
      compareCareerIds.forEach(career => {
        if (mongoose.isValidObjectId(career)) {
          if (!careerObjectIds.includes(career)) {
            careerObjectIds.push(new mongoose.Types.ObjectId(career));
          }
        }
      });
    }

    const regionData = await Region.findById(regionId).select("collegeGroupIds")
    const collegeGroupIds = regionData.collegeGroupIds

    const collegePipeline = [
      { $match: { collegeGroupId: { $in: collegeGroupIds } } },
      {
        $lookup: {
          from: 'campuses',
          localField: '_id',
          foreignField: 'collegeId',
          as: 'campuses'
        }
      },
      {
        $project: {
          "campuses._id": 1
        }
      }
    ]
    let colleges = await College.aggregate(collegePipeline)
    if (!colleges || !colleges.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, res);
    }

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectorIds",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },

      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobzone"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerHours",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerHours"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(regionId)
          },
          from: "careerGrowth",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerGrowth"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },

      {
        $project: {
          "title": 1,
          "description": 1,
          "tasks": 1,
          "jobZone": 1,
          "onetCode": 1,
          "socCode": 1,
          "sectorIds": 1,
          "subsectorIds": 1,

          "courses.code": 1,
          "courses.level": 1,
          "courses.duration": 1,
          "courses.title": 1,
          "courses.description": 1,
          "courses.pageURL": 1,
          "courses.applyURL": 1,
          "courses.enquiryURL": 1,
          "courses.campusId": 1,

          careerHours: { $first: "$careerHours" },
          careerSalary: { $first: "$careerSalary" },
          careerGrowth: { $first: "$careerGrowth" },

          "jobzone.job_training": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, res);
    }

    // return messageResponse(null, "", true, 200, aggrResult, res);

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});

    let currentRoleData, careerGoalData, yourSkills;
    let compareCareersData = [], colorIndex = colors.length - 1;
    // aggrResult.forEach((career) => {
    for (let career of aggrResult) {
      if (currentCareerIds.includes(career._id.toString())) {
        if (currentCareerIds[0].toString() != career._id.toString()) {
          continue;
        }
      }

      let noOfCourse = 0, firstCourse, courses = [];
      career.courses.forEach(course => {
        colleges.map(college => {
          const foundCampusInCourse = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
          if (foundCampusInCourse) {
            if (!firstCourse) {
              firstCourse = course;
            }
            noOfCourse++;
            courses.push({
              code: course.code,
              title: course.title,
              level: course.level,
              duration: course.duration || "",
            })
          }
        })
      });

      let overallGrowth = "N/A";
      if(career.careerGrowth) {
        let predictByYear = career.careerGrowth.values;
        const sortedYears = Object.keys(predictByYear)
          .map(year => ({
            year,
            value: predictByYear[year]
          }))
          .sort((a, b) => Number(a.year) - Number(b.year));
        
        overallGrowth = (sortedYears[sortedYears.length - 1].value > sortedYears[0].value ? "Growth" : "Decline");
      }

      const { _id, title, jobZone } = career;

      let transferDetail = getTransferDetail(jobZone);

      const careerData = {
        id: _id,
        title,
        color: colors[colorIndex--],
        transferWindow: transferDetail.transferWindow,
        noOfCourse,
        courses,
        estimateHours: career.careerHours?.medianValue || career.careerHours?.meanValue || "N/A",
        estimatePayWeek: career.careerSalary?.medianValue / 52 || career.careerSalary?.meanValue / 52 || "N/A",
        estimatePayYear: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
        overallGrowth,
        levelRange: firstCourse ? firstCourse.level : '',
      }
      // prepare skilldar for your skills
      let particularCareerId = career._id;
      if (currentCareerIds[0].toString() == career._id.toString()) {
        const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, "", radarCategories);
        careerData.skilldarChartData = radarData.skilldarChartData
        careerData.radarDetail = radarData.currentJobRadarValues;
        careerData.careerType = "YOUR_SKILLS";
        yourSkills = careerData;
        yourSkills = JSON.parse(JSON.stringify(yourSkills))
      }
      // prepare skilldar for current and selected career
      const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, particularCareerId, radarCategories);

      careerData.skilldarChartData = radarData.skilldarChartData
      if (currentCareerIds[0].toString() == career._id.toString()) {
        careerData.radarDetail = radarData.currentJobRadarValues;
        careerData.careerType = "CURRENT_ROLE";
        currentRoleData = careerData;
      }
      else if (careerGoal && careerGoal.toString() == career._id.toString()) {
        careerData.radarDetail = radarData.currentJobRadarValues;
        careerData.careerType = "CAREER_GOAL";
        careerGoalData = careerData;
      }
      else {
        careerData.careerType = "COMPARE_CAREER";
        compareCareersData.push(careerData);
      }
    };

    if (careerGoalData) {
      compareCareersData.unshift(careerGoalData);
    }
    compareCareersData.unshift(currentRoleData);

    const {firstName, lastName, email} = req.body
    let returnObject = {
      reportForVisitorName: firstName + " " + lastName,
      firstName,
      lastName,
      email,
      currentRole: currentRoleData,
      careerGoal: careerGoalData,
      careers: compareCareersData,
      yourSkills
    };

    // return messageResponse(null, "", true, 200, currentCareerData, res)
    return messageResponse(null, "", true, 200, returnObject, res);

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCompareCareersDataForRegion = async(req, res) => {
  try {
    const {collegeId, currentCareerIds, careerGoal, compareCareerIds, regionId} = req.body
    const { _id: userId } = req.user || {}
    if (!regionId || !mongoose.isValidObjectId(regionId)) {
      return messageResponse(REQUIRED, "Region ID", false, 400, null, res);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Current Career IDs", false, 400, null, res);
    }

    if (careerGoal) {
      if (!mongoose.isValidObjectId(careerGoal)) {
        return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, res);
      }
    }

    // if (!compareCareerIds || !compareCareerIds.length) {
    //   return messageResponse(REQUIRED, "Compare Career IDs", false, 400, null, res);
    // }

    // let careerObjectIds = [new mongoose.Types.ObjectId(currentCareerIds[0])];
    let careerObjectIds = [];
    currentCareerIds.forEach(career => {
      if (mongoose.isValidObjectId(career)) {
        careerObjectIds.push(new mongoose.Types.ObjectId(career));
      }
    });
    if (careerGoal) {
      if (careerObjectIds.includes(careerGoal)) {
        careerGoal = "";
      }
      else {
        careerObjectIds.push(new mongoose.Types.ObjectId(careerGoal));
      }
    }
    if(compareCareerIds && compareCareerIds.length){
      if(userId) {
        await CareerHistory.findOneAndUpdate(
          { userId, regionId: toObjectId(regionId) },
          { 
            $set: {
              compareCareerIds
            }
          }
        )
      }
      compareCareerIds.forEach(career => {
        if (mongoose.isValidObjectId(career)) {
          if (!careerObjectIds.includes(career)) {
            careerObjectIds.push(new mongoose.Types.ObjectId(career));
          }
        }
      });
    }

    const regionData = await Region.findById(regionId).select("collegeGroupIds")
    const collegeGroupIds = regionData.collegeGroupIds

    const collegePipeline = [
      { $match: { collegeGroupId: { $in: collegeGroupIds } } },
      {
        $lookup: {
          from: 'campuses',
          localField: '_id',
          foreignField: 'collegeId',
          as: 'campuses'
        }
      },
      {
        $project: {
          "campuses._id": 1
        }
      }
    ]
    let colleges = await College.aggregate(collegePipeline)
    if (!colleges || !colleges.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, res);
    }

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectorIds",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
{
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerHours",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerHours"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobzone"
        }
      },

      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },

      {
        $project: {
          "title": 1,
          "description": 1,
          "tasks": 1,
          "jobZone": 1,
          "onetCode": 1,
          "socCode": 1,
          "sectorIds": 1,
          "subsectorIds": 1,

          "courses.code": 1,
          "courses.level": 1,
          "courses.duration": 1,
          "courses.title": 1,
          "courses.description": 1,
          "courses.pageURL": 1,
          "courses.applyURL": 1,
          "courses.enquiryURL": 1,
          "courses.campusId": 1,
          "courses.subsectorIds": 1,

          careerHours: { $first: "$careerHours" },
          careerSalary: { $first: "$careerSalary" },

          "jobzone.job_training": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, res);
    }

    // return messageResponse(null, "", true, 200, aggrResult, res);

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});

    let currentRoleData, careerGoalData;
    let compareCareersData = [], colorIndex = colors.length - 1;
    // aggrResult.forEach((career) => {
    for (let career of aggrResult) {
      if (currentCareerIds.includes(career._id.toString())) {
        if (currentCareerIds[0].toString() != career._id.toString()) {
          continue;
        }
      }

      let noOfCourse = 0, firstCourse;
      career.courses.forEach(course => {
        colleges.map(college => {
          const foundCampusInCourse = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
          if (foundCampusInCourse) {
            if (!firstCourse) {
              firstCourse = course;
            }
            noOfCourse++;
          }
        })
      });

      let particularCareerId = career._id;
      // prepare skilldar for current and selected career
      const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, particularCareerId, radarCategories);

      const { _id, title, jobZone } = career;
      let transferDetail = getTransferDetail(jobZone);
      const careerData = {
        id: _id,
        title,
        color: colors[colorIndex--],
        transferWindow: transferDetail.transferWindow,
        noOfCourse,
        estimateHours: career.careerHours?.medianValue || career.careerHours?.meanValue || "N/A",
        estimatePayWeek: career.careerSalary?.medianValue / 52 || career.careerSalary?.meanValue / 52 || "N/A",
        estimatePayYear: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
        overallGrowth: 'Growth',
        regionalGrowth: 'Growth',
        levelRange: firstCourse ? firstCourse.level : '',
        courseDuration: firstCourse ? firstCourse.duration ? firstCourse.duration : '' : '',
        skilldarChartData: radarData.skilldarChartData,
      }

      if (currentCareerIds[0].toString() == career._id.toString()) {
        careerData.careerType = "CURRENT_ROLE"
        currentRoleData = careerData;
      }
      else if (careerGoal && careerGoal.toString() == career._id.toString()) {
        careerData.careerType = "CAREER_GOAL"
        careerGoalData = careerData;
      }
      else {
        careerData.careerType = "COMPARE_CAREER"
        compareCareersData.push(careerData);
      }
    };

    if (careerGoalData) {
      compareCareersData.unshift(careerGoalData);
    }
    compareCareersData.unshift(currentRoleData);

    // return messageResponse(null, "", true, 200, currentCareerData, res)
    return messageResponse(null, "", true, 200, compareCareersData, res);

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCareersNCoursesDetailsForRegion = async(req, res) => {
  try {
    const { IP, collegeId, careerRegionId, selectedCareerId, currentCareerIds, regionId } = req.body

    if (!regionId || !mongoose.isValidObjectId(regionId)) {
      return messageResponse(REQUIRED, "Region ID", false, 400, null, res);
    }

    if (!selectedCareerId || !currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, res);
    }
    // log event
    // const fakeData = {
    //   IP,
    //   collegeId,
    //   event: "CAREER_VIEW",
    //   from: "",
    //   data: {
    //     selectedCareerId
    //   }
    // }
    // await prepareEventLog(fakeData, res)

    const regionData = await Region.findById(regionId).select("collegeGroupIds")
    const collegeGroupIds = regionData.collegeGroupIds

    const collegePipeline = [
      { $match: { collegeGroupId: { $in: collegeGroupIds } } },
      {
        $lookup: {
          from: 'campuses',
          localField: '_id',
          foreignField: 'collegeId',
          as: 'campuses'
        }
      },
       {
        $project: {
          "campuses._id": 1,
          "campuses.name": 1,
          name: 1
        }
      }
    ]
    let colleges = await College.aggregate(collegePipeline)
    if (!colleges || !colleges.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, res);
    }

    // let careerObjectIds = [new mongoose.Types.ObjectId(currentCareerIds[0])];
    let careerObjectIds = [];
    currentCareerIds.forEach(career => {
      careerObjectIds.push(new mongoose.Types.ObjectId(career));
    });
    if (!currentCareerIds.includes(selectedCareerId)) {
      careerObjectIds.push(new mongoose.Types.ObjectId(selectedCareerId));
    }

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "selectedCareerSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },

      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobzone"
        }
      },

      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerWorktype",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerWorktype"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerQualification",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerQualification"
        }
      },
      {
        $lookup: {
          let: {
            socCode: { $substr: ["$socCode", 0, 2] },
            regionId: toObjectId(careerRegionId)
          },
          from: "careerGrowth",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [{ $substr: ["$socCode", 0, 2] }, "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerGrowth"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerHours",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerHours"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(careerRegionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },

      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },

      {
        $project: {
          "title": 1,
          "description": 1,
          "videoUrl": 1,
          "tasks": 1,
          "jobZone": 1,
          "onetCode": 1,
          "socCode": 1,

          "sectors._id": 1,
          "sectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,

          "selectedCareerSubsectors._id": 1,
          "selectedCareerSubsectors.name": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.level": 1,
          "courses.duration": 1,
          "courses.title": 1,
          "courses.description": 1,
          "courses.pageURL": 1,
          "courses.applyURL": 1,
          "courses.enquiryURL": 1,
          "courses.campusId": 1,
          "courses.subsectorIds": 1,

          "jobzone.job_training": 1,
          "jobzone.job_training_custom": 1,

          "careerHours": {$first: "$careerHours"},
          "careerSalary": {$first: "$careerSalary"},
          "careerWorktype": {$first: "$careerWorktype"},
          "careerQualification": {$first: "$careerQualification"},
          "careerGrowth": {$first: "$careerGrowth"},

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, res);
    }

    // return messageResponse(null, "", true, 200, aggrResult, res);

    const selectedCareer = aggrResult.find(career => career._id.toString() === selectedCareerId.toString());
    const { _id, title, description, videoUrl, tasks, courses,
      jobzone, careerHours, careerSalary } = selectedCareer
    let transferDetail = getTransferDetail(selectedCareer.jobZone);
    const numFormat = Intl.NumberFormat('en-US')
    let careerNCourseDetails = {
      id: _id,
      title,
      description,
      videoUrl,
      tasks: tasks.split('\n'),
      averageHours: `${careerHours?.medianValue || careerHours?.meanValue || "N/A"}`,
      averageSalary: {
        weekly: `${careerSalary?.medianValue / 52 || careerSalary?.meanValue / 52 || "N/A"}`,
        yearly: `${careerSalary?.medianValue || careerSalary?.meanValue || "N/A"}`,
      },
      transferWindow: {
        duration: transferDetail.transferWindow,
        description: jobzone[0].job_training_custom,
      }
    }

    let coursesAsPerCollege = []
    selectedCareer.selectedCareerSubsectors.forEach(subsector => {
      selectedCareer.courses.forEach(course => {
        const foundSubsector = course.subsectorIds.find(subsectorId => subsectorId.toString() === subsector._id.toString())
        colleges.map(college => {
          const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
          if (foundSubsector && foundCampus) {
            course.campusName = foundCampus.name
            course.collegeName = college.name
            coursesAsPerCollege.push(course);
          }
        })
      });
    })
    coursesAsPerCollege = coursesAsPerCollege.reduce((groups, course) => {
      const {
        _id,
        code,
        title,
        description,
        level,
        duration,
        campusId,
        pageURL,
        applyURL,
        enquiryURL,
        campusName,
        collegeName,
      } = course
      let foundCourse = groups.find(
        group => 
          group.code === code && 
          group.title === title && 
          group.description === description && 
          group.level === level && 
          group.duration === duration || ""
      )
      if(!foundCourse){
        foundCourse = {
          id: _id,
          code,
          title,
          description,
          level,
          duration: duration || "",
          campus: [],
        }
        groups.push(foundCourse)
      }
      foundCourse.campus.push({
        id: campusId,
        name: campusName,
        college: collegeName,
        pageURL,
        applyURL,
        enquiryURL,
      })
      return groups
    }, [])
    coursesAsPerCollege.sort((course1, course2) => {
      return course1.title.localeCompare(course2.title)
    })
    careerNCourseDetails.courses = coursesAsPerCollege;
    // prepare growthData
    const growthData = {}
    // prepare overallGrowth data
    const predictByYear = selectedCareer.careerGrowth?.values || {  "2025": 0, "2026": 0, "2027": 0, "2028": 0, "2029": 0, "2030": 0, "2031": 0, "2032": 0, "2033": 0, "2034": 0, "2035": 0 };
    const sortedYears = Object.keys(predictByYear)
      .map(year => ({
        year,
        value: predictByYear[year]
      }))
      .sort((a, b) => Number(a.year) - Number(b.year));

    const startYear = sortedYears[0];
    const endYear = sortedYears[sortedYears.length - 1];

    let overallGrowthDetails = () => {
      const overallGrowthNumber = endYear?.value - startYear?.value;
      const overallGrowthPercentage = overallGrowthNumber
        ? Math.round(((overallGrowthNumber / startYear?.value) * 100) * 100) / 100
        : 0;

      return {
        overallGrowthNumber: numFormat.format(overallGrowthNumber || 0),
        overallGrowthPercentage
      };
    };

    growthData.overallGrowth = {
      duration: `${startYear?.year}-${endYear?.year}`,
      overallGrowthNumber: overallGrowthDetails().overallGrowthNumber,
      overallGrowthPercentage: overallGrowthDetails().overallGrowthPercentage,
    };

    // prepare QualificationGrowth data
    const predictByQualification = selectedCareer.careerQualification?.values || { "0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0 }
    const qualificationChartData = {
      series: [{
        name: 'Qualification Growth',
        data: Object.values(predictByQualification)
      }],
      xaxis: {
        categories: Object.keys(predictByQualification)
      }
    }
    growthData.QualificationGrowth = {
      year: endYear?.year,
      chartData: qualificationChartData
    }

    // prepare workTypeGrowth data
    growthData.workTypeGrowth = {
      year: endYear?.year,
      fullTime: numFormat.format(selectedCareer.careerWorktype?.FTValue) || 0,
      partTime: numFormat.format(selectedCareer.careerWorktype?.PTValue) || 0,
      selfEmployeed: numFormat.format(selectedCareer.careerWorktype?.SEValue) || 0,
    }
    careerNCourseDetails.growthData = growthData

    // prepare skilldar for current and selected career
    const radarData = await prepareUpskillRadar(aggrResult, currentCareerIds, selectedCareerId)
    careerNCourseDetails.skilldarChartData = radarData.skilldarChartData

    // return messageResponse(null, "", true, 200, currentCareerData, res)
    return messageResponse(null, "", true, 200, careerNCourseDetails, res)

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const getCareersNUpskillTimeForRegion = async (req, res, isReturn = true) => {
  try {
    const {IP, collegeId, regionId} = req.body
    const { _id: userId } = req.user || {}
    if (!req.body.regionId) {
      return messageResponse(REQUIRED, "Region ID", false, 400, null, isReturn ? res : null);
    }

    if (!mongoose.isValidObjectId(req.body.regionId)) {
      return messageResponse(INVALID_MISSING, "Region ID", false, 400, null, isReturn ? res : null);
    }

    if (!req.body.currentCareerIds || !req.body.currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    if (req.body.careerGoal && !mongoose.isValidObjectId(req.body.careerGoal)) {
      return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, isReturn ? res : null);
    }

    if (userId) {
      await CareerHistory.findOneAndUpdate(
        { userId, regionId: toObjectId(regionId) },
        { 
          $set: {
            skillType: "upskill"
          }
        }
      )
    }

    const regionData = await Region.findById(regionId).select("collegeGroupIds")
    const collegeGroupIds = regionData.collegeGroupIds


    const collegePipeline = [
      { $match: { collegeGroupId: { $in: collegeGroupIds } } },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
      {
        $project: { campuses: 1, name: 1, slug: 1 }
      }
    ]
    const colleges = await College.aggregate(collegePipeline);
    if (!colleges || !colleges.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, isReturn ? res : null);
    }

    // const colors = [
    //   '#24D92B',
    //   '#F916E4',
    //   '#17BDFA',
    //   '#FF4D1B',
    //   '#24265A',
    //   '#F6C017',
    //   '#C74710',
    //   '#9BA6EF',
    //   '#033201',
    //   '#1B47DE',
    //   '#F5A864',
    //   '#2DE8B4',
    //   '#D2626F',
    //   '#08D2C8',
    //   '#EE8851'
    // ];

    let careerObjectIds = [new mongoose.Types.ObjectId(req.body.currentCareerIds[0])];

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "currentRoleSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "careers",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "careers"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $project: {
          "title": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "jobZone": 1,

          "sectors._id": 1,
          "sectors.name": 1,
          "sectors.priority": 1,

          "currentRoleSubsectors._id": 1,
          "currentRoleSubsectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,
          "subsectors.priority": 1,

          "careers._id": 1,
          "careers.title": 1,
          "careers.subsectorIds": 1,
          "careers.jobZone": 1,
          "careers.interests": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.title": 1,
          "courses.subsectorIds": 1,
          "courses.campusId": 1,
          "courses.duration": 1,

          careerSalary: { $first: "$careerSalary" },
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    const currentRole = aggrResult[0];

    let coursesAsPerCollege = [];
    currentRole.courses.forEach(course => {
      colleges.map(college => {
        const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString())
        if (foundCampus) {
          coursesAsPerCollege.push({...course, campusName: foundCampus.name, collegeName: college.name, collegeId: college._id});
        }
      })
    });
    currentRole.courses = coursesAsPerCollege;

    // return messageResponse(null, "", true, 200, currentRole, isReturn ? res : null);

    let sectorOrSubsector = [], colorIndex = 0;
    let returnResult = { sectorName: currentRole.sectors[0].name };

    currentRole.subsectors.forEach(subsector => {
      let sectorOrSubsectorObj = {
        name: subsector.name,
        color: reSkillsAndUpSkillscolors[colorIndex++],
        priority: subsector.priority,
        careers: []
      }

      currentRole.careers.forEach(career => {
        const foundSubsector = career.subsectorIds.find(careerSubsector => careerSubsector.toString() == subsector._id.toString());
        if (foundSubsector) {
          let transferDetail = getTransferDetail(career.jobZone), noOfCourse = 0, careerType = "";
          let careerObj = {
            id: career._id,
            name: career.title,
            transferWindowMin: transferDetail.transferWindowMin,
            transferWindowMax: transferDetail.transferWindowMax,
            transferWindow: transferDetail.transferWindow,
            compare: false,
            interests: career.interests,
            salary: career.careerSalary?.medianValue || career.careerSalary?.meanValue || "N/A",
          }

          currentRole.courses.forEach(course => {
            const courseFoundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == foundSubsector.toString())
            if(courseFoundSubsector){
              noOfCourse++
            }
          })

          if(career._id.toString() === req.body.currentCareerIds[0].toString()){
            careerType = "CURRENT_ROLE"
          } else if(req.body.careerGoal && (career._id.toString() === req.body.careerGoal.toString())) {
            careerType = "CAREER_GOAL"
          } else {
            careerType = "NORMAL"
          }
          careerObj.coursesAvailable = noOfCourse
          careerObj.careerType = careerType
          if(careerObj.coursesAvailable > 0) {
            sectorOrSubsectorObj.careers.push(careerObj)
          }
        }
      });
      sectorOrSubsectorObj.careers.sort((career1, career2) => {
        return career1.name.localeCompare(career2.name)
      })
      if(sectorOrSubsectorObj.careers.length) {
        sectorOrSubsector.push(sectorOrSubsectorObj)
      }
    });
   sectorOrSubsector.sort((a, b) => {
     // Priority subsectors come first
     if (a.priority === b.priority) {
       // If both are same priority, sort by number of careers (desc)
       return b.careers.length - a.careers.length;
     }
     return a.priority ? -1 : 1; // true comes before false
   });

    returnResult.sectorOrSubsector = sectorOrSubsector;
    return messageResponse(null, "", true, 200, returnResult, isReturn ? res : null)

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

const getCareersNReskillTimeForRegion = async (req, res, isReturn = true) => {
  try {
    const { collegeId, currentCareerIds, regionId } = req.body;
    const { _id: userId } = req.user || {}
    if (!regionId) {
      return messageResponse(REQUIRED, "Region ID", false, 400, null, isReturn ? res : null);
    }

    if (!mongoose.isValidObjectId(regionId)) {
      return messageResponse(INVALID_MISSING, "Region ID", false, 400, null, isReturn ? res : null);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    if (userId) {
      await CareerHistory.findOneAndUpdate(
        { userId, regionId: toObjectId(regionId) },
        { 
          $set: {
            skillType: "reskill"
          }
        }
      )
    }

    const regionData = await Region.findById(regionId).select("collegeGroupIds")
    const collegeGroupIds = regionData.collegeGroupIds


    const collegePipeline = [
      { $match: { collegeGroupId: { $in: collegeGroupIds } } },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
    ]
    let colleges = await College.aggregate(collegePipeline);
    if (!colleges || !colleges.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, isReturn ? res : null);
    }

    const bubbleSize = 25;

    // const colors = [
    //   '#24D92B',
    //   '#F916E4',
    //   '#17BDFA',
    //   '#FF4D1B',
    //   '#24265A',
    //   '#F6C017',
    //   '#C74710',
    //   '#9BA6EF',
    //   '#033201',
    //   '#1B47DE',
    //   '#F5A864',
    //   '#2DE8B4',
    //   '#D2626F',
    //   '#08D2C8',
    //   '#EE8851'
    // ];

    const pipeline = [
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "currentRoleSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "currentRoleSubsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          let: {
            socCode: "$socCode",
            regionId: toObjectId(regionId)
          },
          from: "careerSalary",
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$socCode", "$$socCode"] },
                    { $eq: ["$regionID", "$$regionId"] }
                  ]
                }
              }
            }
          ],
          as: "careerSalary"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },
      {
        $project: {
          "_id": 1,
          "title": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "jobZone": 1,

          "sectors._id": 1,
          "sectors.name": 1,
          "sectors.priority": 1,

          "currentRoleSubsectors._id": 1,
          "currentRoleSubsectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,
          "subsectors.priority": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.title": 1,
          "courses.sectorIds": 1,
          "courses.subsectorIds": 1,
          "courses.campusId": 1,
          "courses.duration": 1,

          careerSalary: { $first: "$careerSalary" },

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    // return messageResponse(null, "", true, 200, aggrResult, isReturn ? res : null);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});
    const initialRadarValues = [];
    radarCategories.forEach(radar => {
      initialRadarValues.push({ name: radar.name, total: 0, count: 0, skillsAbilities: [] });
    });

    const currentCareers = aggrResult.filter(career => currentCareerIds.includes(career._id.toString()));

    // return messageResponse(null, "", true, 200, currentCareers, isReturn ? res : null);

    // prepare skilldar for current and selected career
    const currentRoleRadarData = await prepareUpskillRadar(currentCareers, currentCareerIds, null, radarCategories);

    let skillMatchingPercentage;
    let noOfSkillsToMatch;
    const getSetting = await settingSchema.findOne({})
    skillMatchingPercentage = getSetting && getSetting.skillMatchingPercentage ? getSetting.skillMatchingPercentage : 5
    noOfSkillsToMatch =  getSetting && getSetting.noOfSkillsToMatch ? getSetting.noOfSkillsToMatch : 5;
   // console.log(skillMatchingPercentage,noOfSkillsToMatch);

    let newRadarData = [];
    currentRoleRadarData.skilldarChartData.series[0].data.forEach(value => {
      const maxRadarValueAsPerAlgorithm = value + (value * skillMatchingPercentage / 100);
      const minRadarValueAsPerAlgorithm = value - (value * skillMatchingPercentage / 100);
      const radarValues = { value, minRadarValueAsPerAlgorithm, maxRadarValueAsPerAlgorithm };
      newRadarData.push(radarValues);
    })
    currentRoleRadarData.radarData = newRadarData;

    // return messageResponse(null, "", true, 200, aggrResult, isReturn ? res : null);

    let colorIndex = 0, noOfCourse = 0;
    let returnResult = { sectorName: "", sectorOrSubsector: [] };
    // for current role

    let currentRole = aggrResult.find(career => career._id.toString() == currentCareerIds[0].toString());
    let transferDetail = getTransferDetail(currentRole.jobZone);
    currentRole.sectors.forEach(sector => {
      let sectorOrSubsectorObj = {
        name: sector.name,
        color: reSkillsAndUpSkillscolors[colorIndex++],
        priority: sector.priority,
        careers: []
      }
      let careerObj = {
        id: currentRole._id,
        name: currentRole.title,
        transferWindowMin: transferDetail.transferWindowMin,
        transferWindowMax: transferDetail.transferWindowMax,
        transferWindow: transferDetail.transferWindow,
        coursesAvailable: getCourseCountRegion(currentRole.courses, currentRole.currentRoleSubsectors, colleges),
        compare: false,
        salary: currentRole.careerSalary?.medianValue || currentRole.careerSalary?.meanValue || "N/A",
      }
      careerObj.careerType = "CURRENT_ROLE"
      if(careerObj.coursesAvailable > 0){
        sectorOrSubsectorObj.careers.push(careerObj)
      }
      returnResult.sectorOrSubsector.push(sectorOrSubsectorObj)
    });
    //

    aggrResult.forEach(career => {

      // if career is in current careers then no need to do anything in loop
      if (currentCareerIds.includes(career._id.toString())) {
        // console.log('career is skipped due to current career', career._id, career.title);
        return;
      }

      let noOfCourse = 0;
      // currentRole.currentRoleSubsectors.forEach(subsector => {
      //   currentRole.courses.forEach(course => {
      //     const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
      //     const foundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == subsector._id.toString());
      //     if (foundCampus && foundSubsector) {
      //       noOfCourse++;
      //     }
      //   });
      // });

      noOfCourse = getCourseCountRegion(career.courses, career.currentRoleSubsectors, colleges);

      // if career has no belonging courses then no need to do anything in loop
      if (!noOfCourse) {
        // console.log('career is skipped due to course not available', career._id, career.title);
        return;
      }

      let skills = [], abilities = [];
      // creating deep copy of firstRadarValues
      radarValues = JSON.parse(JSON.stringify(initialRadarValues));
      if (career.lmiSkillAbilityLevels && career.lmiSkillAbilityLevels.length > 0) {
        career.lmiSkillAbilityLevels.forEach(skillabilitylevels => {
          const skillAbility = career.skillsAbilities.find(skillAbility => skillAbility._id.toString() == skillabilitylevels.lmiSkillAbilityId.toString());
          let radarCategory;
          if (skillAbility.radarCategoryId) {
            radarCategory = radarCategories.find(rc => rc._id.toString() == skillAbility.radarCategoryId.toString());
          }
          if (skillAbility && radarCategory) {
            let foundRadarValue = radarValues.find(rv => rv.name == radarCategory.name);
            if (!foundRadarValue) {
              foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
              radarValues.push(foundRadarValue);
            }
            foundRadarValue.total += skillabilitylevels.level;
            foundRadarValue.count++;
            foundRadarValue.skillsAbilities.push({ name: skillAbility.lmiName, level: skillabilitylevels.level });
          }
        });
      }
      career.skills = skills;
      career.abilities = abilities;

      let radarData = [];
      for (let rv of radarValues) {
        radarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
      }
      career.radarData = radarData;

      let noOfMatchedSkills = 0;
      career.radarData.forEach((radar, index) => {
        if (radar >= currentRoleRadarData.radarData[index].minRadarValueAsPerAlgorithm && radar <= currentRoleRadarData.radarData[index].maxRadarValueAsPerAlgorithm) {
          noOfMatchedSkills++;
        }
      });
      
      if (noOfMatchedSkills >= noOfSkillsToMatch) {
        // console.log('career is selected', career._id, career.title);

        // console.log("sector---", returnResult)
        career.sectors.forEach(sector => {
          let foundSector = returnResult.sectorOrSubsector.find(result => result.name === sector.name)
          if(!foundSector){
            foundSector = {name: sector.name, color: reSkillsAndUpSkillscolors[colorIndex++], priority: sector.priority, careers: []}
            returnResult.sectorOrSubsector.push(foundSector)
          }
          let foundCareer = foundSector.careers.find(c => c.id.toString() === career._id.toString())
          if(!foundCareer){
            let transferDetail = getTransferDetail(career.jobZone);
            foundCareer = {
              id: career._id,
              name: career.title,
              transferWindowMin: transferDetail.transferWindowMin,
              transferWindowMax: transferDetail.transferWindowMax,
              transferWindow: transferDetail.transferWindow,
              compare: false,
              coursesAvailable: noOfCourse,
              careerType: "NORMAL",
            }
            if(foundCareer.coursesAvailable > 0){
              foundSector.careers.push(foundCareer)
            }
          }
          foundSector.careers.sort((career1, career2) => {
            return career1.name.localeCompare(career2.name)
          })
        });
      }
    });

    returnResult.sectorOrSubsector.sort((a, b) => {
     // Priority subsectors come first
     if (a.priority === b.priority) {
       // If both are same priority, sort by number of careers (desc)
       return b.careers.length - a.careers.length;
     }
     return a.priority ? -1 : 1; // true comes before false
   });

    return messageResponse(null, "", true, 200, returnResult, isReturn ? res : null);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

// Controller for open/public routes
const getCareersNUpskillTimeInterests = async (req, res, isReturn = true) => {
  try {
    const {IP, collegeId} = req.body
    if (!req.body.collegeId) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!mongoose.isValidObjectId(req.body.collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!req.body.currentCareerIds || !req.body.currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    if (req.body.careerGoal && !mongoose.isValidObjectId(req.body.careerGoal)) {
      return messageResponse(INVALID_MISSING, "Career Goal", false, 400, null, isReturn ? res : null);
    }

    const collegePipeline = [
      { $match: { _id: { $eq: new mongoose.Types.ObjectId(req.body.collegeId) } } },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
    ]
    let college = await College.aggregate(collegePipeline);
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, isReturn ? res : null);
    }
    college = college[0];

    // const colors = [
    //   '#24D92B',
    //   '#F916E4',
    //   '#17BDFA',
    //   '#FF4D1B',
    //   '#24265A',
    //   '#F6C017',
    //   '#C74710',
    //   '#9BA6EF',
    //   '#033201',
    //   '#1B47DE',
    //   '#F5A864',
    //   '#2DE8B4',
    //   '#D2626F',
    //   '#08D2C8',
    //   '#EE8851'
    // ];

    let careerObjectIds = [new mongoose.Types.ObjectId(req.body.currentCareerIds[0])];

    const pipeline = [
      { $match: { _id: { $in: careerObjectIds } } },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "currentRoleSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "careers",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "careers"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "subsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $project: {
          "title": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "jobZone": 1,
          "estimatePay": 1,

          "sectors._id": 1,
          "sectors.name": 1,

          "currentRoleSubsectors._id": 1,
          "currentRoleSubsectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,

          "careers._id": 1,
          "careers.title": 1,
          "careers.subsectorIds": 1,
          "careers.jobZone": 1,
          "careers.estimatePay": 1,
          "careers.interests": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.title": 1,
          "courses.subsectorIds": 1,
          "courses.campusId": 1,
          "courses.duration": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    const currentRole = aggrResult[0];

    let coursesAsPerCollege = [];
    currentRole.courses.forEach(course => {
      const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
      if (foundCampus) {
        coursesAsPerCollege.push(course);
      }
    });
    currentRole.courses = coursesAsPerCollege;

    colorIndex = 0;
    const interestWiseCareers = {}

    for(const career of currentRole.careers) {
      if(!career.interests || !career.interests.length) continue

      const interestName = career.interests[0].name
      if(!interestWiseCareers[interestName]) {
        interestWiseCareers[interestName] = { 
          sectorName: currentRole.sectors[0].name,
          sectorOrSubsector: []
        };
      }

      currentRole.subsectors.forEach(subsector => {
        let sectorOrSubsectorObj = interestWiseCareers[interestName].sectorOrSubsector.find(sectorOrSubsector => sectorOrSubsector.name === subsector.name)

        if(!sectorOrSubsectorObj) {
          sectorOrSubsectorObj = {
            name: subsector.name,
            color: reSkillsAndUpSkillscolors[colorIndex++],
            careers: []
          }
          interestWiseCareers[interestName].sectorOrSubsector.push(sectorOrSubsectorObj)
        }

        const foundSubsector = career.subsectorIds.find(careerSubsector => careerSubsector.toString() == subsector._id.toString());
        if (foundSubsector) {
          let transferDetail = getTransferDetail(career.jobZone), noOfCourse = 0, careerType = "";
          let careerObj = {
            id: career._id,
            name: career.title,
            salary: career.estimatePay * 52,
            transferWindowMin: transferDetail.transferWindowMin,
            transferWindowMax: transferDetail.transferWindowMax,
            transferWindow: transferDetail.transferWindow,
            compare: false,
            interests: career.interests,
          }
  
          currentRole.courses.forEach(course => {
            const courseFoundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == foundSubsector.toString())
            if(courseFoundSubsector){
              noOfCourse++
            }
          })
  
          if(career._id.toString() === req.body.currentCareerIds[0].toString()){
            careerType = "CURRENT_ROLE"
          } else if(req.body.careerGoal && (career._id.toString() === req.body.careerGoal.toString())) {
            careerType = "CAREER_GOAL"
          } else {
            careerType = "NORMAL"
          }
          careerObj.coursesAvailable = noOfCourse
          careerObj.careerType = careerType
          if(careerObj.coursesAvailable > 0) {
            sectorOrSubsectorObj.careers.push(careerObj)
          }
        }  
        sectorOrSubsectorObj.careers.sort((career1, career2) => {
          return career1.name.localeCompare(career2.name)
        })
      });

      interestWiseCareers[interestName].sectorOrSubsector.sort((sectorOrSubsector1, sectorOrSubsector2) => {
        return sectorOrSubsector2.careers.length - sectorOrSubsector1.careers.length
      })
    };

    const returnResult = Object.keys(interestWiseCareers).map(interestKey => {
      return {
        interest: interestKey,
        careers: {
          sectorName: interestWiseCareers[interestKey].sectorName,
          sectorOrSubsector: interestWiseCareers[interestKey].sectorOrSubsector.filter(sec => sec.careers.length)
        }
      }
    })

    return messageResponse(null, "", true, 200, returnResult, isReturn ? res : null)

  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

const getCareersNReskillTimeInterests = async (req, res, isReturn = true) => {
  try {
    const { collegeId, currentCareerIds } = req.body;

    if (!collegeId) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!mongoose.isValidObjectId(collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, isReturn ? res : null);
    }

    if (!currentCareerIds || !currentCareerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, isReturn ? res : null);
    }

    const collegePipeline = [
      { $match: { _id: { $eq: new mongoose.Types.ObjectId(collegeId) } } },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
    ]
    let college = await College.aggregate(collegePipeline);
    if (!college || !college.length) {
      return messageResponse(INVALID_MISSING, "College", false, 400, null, isReturn ? res : null);
    }
    college = college[0];

    const bubbleSize = 25;

    // const colors = [
    //   '#24D92B',
    //   '#F916E4',
    //   '#17BDFA',
    //   '#FF4D1B',
    //   '#24265A',
    //   '#F6C017',
    //   '#C74710',
    //   '#9BA6EF',
    //   '#033201',
    //   '#1B47DE',
    //   '#F5A864',
    //   '#2DE8B4',
    //   '#D2626F',
    //   '#08D2C8',
    //   '#EE8851'
    // ];

    const pipeline = [
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "currentRoleSubsectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors._id",
          foreignField: "sectorId",
          as: "subsectors"
        }
      },
      {
        $lookup: {
          from: "courses",
          localField: "currentRoleSubsectors._id",
          foreignField: "subsectorIds",
          as: "courses"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },
      {
        $project: {
          "_id": 1,
          "title": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "jobZone": 1,
          "estimatePay": 1,
          "interests": 1,

          "sectors._id": 1,
          "sectors.name": 1,

          "currentRoleSubsectors._id": 1,
          "currentRoleSubsectors.name": 1,

          "subsectors._id": 1,
          "subsectors.name": 1,

          "courses._id": 1,
          "courses.code": 1,
          "courses.title": 1,
          "courses.sectorIds": 1,
          "courses.subsectorIds": 1,
          "courses.campusId": 1,
          "courses.duration": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,
          "skillsAbilities.radarCategoryId": 1,
          "skillsAbilities.radarSubcategoryId": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    // return messageResponse(null, "", true, 200, aggrResult, isReturn ? res : null);

    if (!aggrResult || !aggrResult.length) {
      return messageResponse(DATA_NOT_FOUND, "", false, 400, null, isReturn ? res : null);
    }

    let radarCategories = await RadarCategory.find({}, { name: 1 }).sort({priority: 1});
    const initialRadarValues = [];
    radarCategories.forEach(radar => {
      initialRadarValues.push({ name: radar.name, total: 0, count: 0, skillsAbilities: [] });
    });

    const currentCareers = aggrResult.filter(career => currentCareerIds.includes(career._id.toString()));

    // return messageResponse(null, "", true, 200, currentCareers, isReturn ? res : null);

    // prepare skilldar for current and selected career
    const currentRoleRadarData = await prepareUpskillRadar(currentCareers, currentCareerIds, null, radarCategories);

    let skillMatchingPercentage;
    let noOfSkillsToMatch;
    const getSetting = await settingSchema.findOne({})
    skillMatchingPercentage = getSetting && getSetting.skillMatchingPercentage ? getSetting.skillMatchingPercentage : 5
    noOfSkillsToMatch =  getSetting && getSetting.noOfSkillsToMatch ? getSetting.noOfSkillsToMatch : 5;
   // console.log(skillMatchingPercentage,noOfSkillsToMatch);

    let newRadarData = [];
    currentRoleRadarData.skilldarChartData.series[0].data.forEach(value => {
      const maxRadarValueAsPerAlgorithm = value + (value * skillMatchingPercentage / 100);
      const minRadarValueAsPerAlgorithm = value - (value * skillMatchingPercentage / 100);
      const radarValues = { value, minRadarValueAsPerAlgorithm, maxRadarValueAsPerAlgorithm };
      newRadarData.push(radarValues);
    })
    currentRoleRadarData.radarData = newRadarData;

    // return messageResponse(null, "", true, 200, aggrResult, isReturn ? res : null);

    let colorIndex = 0, noOfCourse = 0;
    let returnResult = { sectorName: "", sectorOrSubsector: [] };
    // for current role

    let currentRole = aggrResult.find(career => career._id.toString() == currentCareerIds[0].toString());
    let transferDetail = getTransferDetail(currentRole.jobZone);
    currentRole.sectors.forEach(sector => {
      let sectorOrSubsectorObj = {
        name: sector.name,
        color: reSkillsAndUpSkillscolors[colorIndex++],
        careers: []
      }
      let careerObj = {
        id: currentRole._id,
        name: currentRole.title,
        salary: currentRole.estimatePay * 52,
        transferWindowMin: transferDetail.transferWindowMin,
        transferWindowMax: transferDetail.transferWindowMax,
        transferWindow: transferDetail.transferWindow,
        coursesAvailable: getCourseCount(currentRole.courses, currentRole.currentRoleSubsectors, college.campuses),
        compare: false,
        interests: currentRole.interests
      }
      careerObj.careerType = "CURRENT_ROLE"
      if(careerObj.coursesAvailable > 0){
        sectorOrSubsectorObj.careers.push(careerObj)
      }
      returnResult.sectorOrSubsector.push(sectorOrSubsectorObj)
    });
    //

    aggrResult.forEach(career => {

      // if career is in current careers then no need to do anything in loop
      if (currentCareerIds.includes(career._id.toString())) {
        // console.log('career is skipped due to current career', career._id, career.title);
        return;
      }

      let noOfCourse = 0;
      // currentRole.currentRoleSubsectors.forEach(subsector => {
      //   currentRole.courses.forEach(course => {
      //     const foundCampus = college.campuses.find(campus => campus._id.toString() == course.campusId.toString());
      //     const foundSubsector = course.subsectorIds.find(courseSubsector => courseSubsector.toString() == subsector._id.toString());
      //     if (foundCampus && foundSubsector) {
      //       noOfCourse++;
      //     }
      //   });
      // });

      noOfCourse = getCourseCount(career.courses, career.currentRoleSubsectors, college.campuses);

      // if career has no belonging courses then no need to do anything in loop
      if (!noOfCourse) {
        // console.log('career is skipped due to course not available', career._id, career.title);
        return;
      }

      let skills = [], abilities = [];
      // creating deep copy of firstRadarValues
      radarValues = JSON.parse(JSON.stringify(initialRadarValues));
      if (career.lmiSkillAbilityLevels && career.lmiSkillAbilityLevels.length > 0) {
        career.lmiSkillAbilityLevels.forEach(skillabilitylevels => {
          const skillAbility = career.skillsAbilities.find(skillAbility => skillAbility._id.toString() == skillabilitylevels.lmiSkillAbilityId.toString());
          let radarCategory;
          if (skillAbility.radarCategoryId) {
            radarCategory = radarCategories.find(rc => rc._id.toString() == skillAbility.radarCategoryId.toString());
          }
          if (skillAbility && radarCategory) {
            let foundRadarValue = radarValues.find(rv => rv.name == radarCategory.name);
            if (!foundRadarValue) {
              foundRadarValue = { name: skillAbility.radarCategory, total: 0, count: 0, skillsAbilities: [] };
              radarValues.push(foundRadarValue);
            }
            foundRadarValue.total += skillabilitylevels.level;
            foundRadarValue.count++;
            foundRadarValue.skillsAbilities.push({ name: skillAbility.lmiName, level: skillabilitylevels.level });
          }
        });
      }
      career.skills = skills;
      career.abilities = abilities;

      let radarData = [];
      for (let rv of radarValues) {
        radarData.push(rv.total ? Math.round(rv.total / (rv.count * 7) * 100) : 0);
      }
      career.radarData = radarData;

      let noOfMatchedSkills = 0;
      career.radarData.forEach((radar, index) => {
        if (radar >= currentRoleRadarData.radarData[index].minRadarValueAsPerAlgorithm && radar <= currentRoleRadarData.radarData[index].maxRadarValueAsPerAlgorithm) {
          noOfMatchedSkills++;
        }
      });
      
      if (noOfMatchedSkills >= noOfSkillsToMatch) {
        // console.log('career is selected', career._id, career.title);

        // console.log("sector---", returnResult)
        career.sectors.forEach(sector => {
          let foundSector = returnResult.sectorOrSubsector.find(result => result.name === sector.name)
          if(!foundSector){
            foundSector = {name: sector.name, color: reSkillsAndUpSkillscolors[colorIndex++], careers: []}
            returnResult.sectorOrSubsector.push(foundSector)
          }
          let foundCareer = foundSector.careers.find(c => c.id.toString() === career._id.toString())
          if(!foundCareer){
            let transferDetail = getTransferDetail(career.jobZone);
            foundCareer = {
              id: career._id,
              name: career.title,
              salary: career.estimatePay * 52,
              transferWindowMin: transferDetail.transferWindowMin,
              transferWindowMax: transferDetail.transferWindowMax,
              transferWindow: transferDetail.transferWindow,
              compare: false,
              coursesAvailable: noOfCourse,
              careerType: "NORMAL",
            }
            if(foundCareer.coursesAvailable > 0){
              foundSector.careers.push(foundCareer)
            }
          }
          foundSector.careers.sort((career1, career2) => {
            return career1.name.localeCompare(career2.name)
          })
        });
      }
    });

    returnResult.sectorOrSubsector.sort((sectorOrSubsector1, sectorOrSubsector2) => {
      return sectorOrSubsector2.careers.length - sectorOrSubsector1.careers.length
    })

    return messageResponse(null, "", true, 200, returnResult, isReturn ? res : null);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

const getSkilldarAndRecommendedCareers = async (req, res) => {
  try {
    const { collegeId, careerIds, type } = req.body
    if(!collegeId || !mongoose.isValidObjectId(collegeId)) {
      return messageResponse(REQUIRED, "College ID", false, 400, null, res)
    }
    if (!careerIds || !careerIds.length) {
      return messageResponse(REQUIRED, "Career IDs", false, 400, null, res);
    }

    const fakeReq = {
      body: req.body
    }
    const fakeReq2 = {
      body: {...req.body, currentCareerIds: careerIds}
    }
    const skilldarData = await getSkilldarChartData(fakeReq, res, false);
    const recommendedCareers = await getCareersNUpskillTimeInterests(fakeReq2, res, false)
    
    // const recommendedCareers = !type || type === "upskill" ?
    //   // await getCareersNUpskillTimeNew(fakeReq2, res, false) :
    //   await getCareersNUpskillTimeInterests(fakeReq2, res, false) :
    //   await getCareersNReskillTimeInterests(fakeReq2, res, false)
    
    const response = {
      skilldarChartData: skilldarData.data,
      careers: recommendedCareers.data
    }

    return messageResponse(null, "", true, 200, response, res);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, isReturn ? res : null)
  }
}

module.exports = {
  getColleges,
  getRegionalInfo,
  getCollegeBySlug,
  getRegionBySlug,
  getCareerHistory,
  getCareers,
  getNestedCareers,
  getSkilldarChartData,
  getGoalCareers,
  sendEmail,
  getWidgetCareers,
  
  getSkillsReportData,
  getCompareCareersData,
  getCareersNCoursesDetails,
  getCareersNUpskillTimeNew,
  getCareersNReskillTimeNew,

  getSkillsReportDataForRegion,
  getCompareCareersDataForRegion,
  getCareersNCoursesDetailsForRegion,
  getCareersNUpskillTimeForRegion,
  getCareersNReskillTimeForRegion,

  getSkilldarAndRecommendedCareers
}