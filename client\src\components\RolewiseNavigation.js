import { Navigate, useLocation } from "react-router-dom"
import { APP_ROUTER_BASE_URL } from "../utils";

const RolewiseNavigation = ({ role }) => {
    const location = useLocation();
    let navigateUrl
    switch (role) {
        case "1":
            navigateUrl = "dashboard/app";
            break;
        case "2":
            navigateUrl = "dashboard/collegegroups";
            break;
        case "3":
            navigateUrl = "dashboard/colleges";
            break;
        case "4":
            navigateUrl = "dashboard/campuses";
            break;
        default:
            navigateUrl = "login";
            break;
    }
    return (
        <Navigate to={`${APP_ROUTER_BASE_URL}${navigateUrl}`} state={{ from: location }} replace />
    )
}

export default RolewiseNavigation