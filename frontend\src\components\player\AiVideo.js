import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types';
import { Box, Tooltip, Typography, tooltipClasses, alpha, Backdrop } from '@mui/material';
import PlayArrowRoundedIcon from '@mui/icons-material/PlayArrowRounded';
import { styled } from '@mui/system';
import PlayerDialog from './PlayerDialog';
import videoBackground from '../../assets/images/background-video.png'
import laicie from '../../assets/images/laicie-image.png'
import svg from '../../assets/images/speech-bubble.svg'



const AiVideo = ({ url, position, disableClick, closeLabel, secondaryColor }) => {
    
    const [openVideo, setOpenVideo] = useState(false);
    const handleOpenVideo = () => {
        if (closeLabel) {
            closeLabel()
            setOpenVideo(true);
        }else{
            setOpenVideo(true);
        }
    };

    const handleCloseVideo = () => {
        setOpenVideo(false);
    };
    return (
            <Box sx={{ position: 'relative', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Box
                    className='laicie-background'
                    sx={{
                        // p: 8,
                        // width: 490,
                        height: 279,
                        // backgroundImage: `url(${videoBackground})`,
                        backgroundColor: 'white',
                        backgroundRepeat: 'no-repeat',
                        backgroundSize: 'cover',
                        // position: position !== 'default' ? 'absolute' : 'relative',
                        // bottom: position !== 'default' ? 76 : '',
                        // right: position !== 'default' ? 300 : '',
                        position: 'relative',
                        border: '2px solid #aeaeae',
                        overflow: 'hidden',
                        borderRadius: '8px',
                    }}
                >
                    <img
                        alt='playback'
                        src={laicie}
                    />


                </Box>
                <Box
                    onClick={handleOpenVideo}
                    sx={{
                        width: 64,
                        height: 64,
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        backgroundColor: secondaryColor || 'secondary.main',
                        position: 'absolute',
                        bottom: '-8%',
                        // left: '34%',

                        borderRadius: '50%',
                        boxShadow: "0px 0px 12px rgba(0,0,0,0.4)",
                        cursor: 'pointer',
                        transform: 'translateX(-50%)',
                        left: '50%'
                    }}
                >
                    {/* background: linear-gradient(180deg, rgb(255 255 255 / 83%) 0%, rgb(0 164 219 / 51%) 60%, rgb(0 164 219) 100%) */}
                    <Box
                        sx={{
                            position: 'absolute',
                            top: '6%',
                            left: '16%',
                            width: '70%',
                            height: '60%',
                            borderRadius: '50%',
                            background: (theme) => `linear-gradient(180deg, rgb(255 255 255 / 83%) 0%, ${secondaryColor || theme.palette.secondary.main} 60%, ${secondaryColor || theme.palette.secondary.main} 100%)`,
                            opacity: 0.8
                        }}
                    />
                    <PlayArrowRoundedIcon sx={{ color: 'white', zIndex: 9999, fontSize: '36px' }} />

                </Box>
                <PlayerDialog open={openVideo} onClose={handleCloseVideo} videoPath={url} />
            </Box>
    )
}

export default AiVideo

AiVideo.propTypes = {
    url: PropTypes.string,
    position: PropTypes.string,
    disableClick: PropTypes.bool,
    closeLabel: PropTypes.func,
    secondaryColor : PropTypes.string
}