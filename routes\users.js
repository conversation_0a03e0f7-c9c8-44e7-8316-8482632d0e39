const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const usersController = require('../controllers/users.controller')
const SuperUserGuard = require("../guards/super-user.guard");
const CollegeAdminGuard = require("../guards/collegeAdmin.guard");
const CampusAdminGuard = require("../guards/campusAdmin.guard");


router.post("/register", usersController.register);

router.post("/add", CollegeAdminGuard, usersController.add);

router.get("/get", CollegeAdminGuard, usersController.get);
// router.get("/get", SuperUserGuard, usersController.get);

router.get("/getById", CampusAdminGuard, usersController.getByID);

router.get("/dashboard", CollegeAdminGuard, usersController.dashboard);

router.delete("/remove", CollegeAdminGuard, usersController.remove);

router.put("/update", CampusAdminGuard, usersController.update);

router.post("/authenticate", usersController.authenticate);

router.get("/getAvailableUsers", usersController.getAvailableUsers);

router.post("/resetPassword", usersController.resetPassword);

router.post("/forgotPassword", usersController.forgotPassword);

module.exports = router;