const mongoose = require('mongoose');
require('dotenv').config();

// Import Phase 4 helpers and services
const DataExtractor = require('../helpers/dataExtractor');
const TrainingDataGenerator = require('../helpers/trainingDataGenerator');
const DataValidator = require('../helpers/dataValidator');
const FineTuningDataService = require('../services/fineTuningDataService');

/**
 * Test script for Phase 4 Data Pipeline
 * Tests the complete data pipeline from extraction to validation
 */
const testDataPipeline = async () => {
  console.log('🧪 Testing Phase 4 Data Pipeline');
  console.log('=================================');

  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
    console.log('✅ Connected to MongoDB');

    // Test 1: Data Extractor
    console.log('\n🔍 Testing Data Extractor...');
    await testDataExtractor();

    // Test 2: Training Data Generator
    console.log('\n🎯 Testing Training Data Generator...');
    await testTrainingDataGenerator();

    // Test 3: Data Validator
    console.log('\n🔍 Testing Data Validator...');
    await testDataValidator();

    // Test 4: Enhanced Fine-Tuning Data Service
    console.log('\n🚀 Testing Enhanced Fine-Tuning Data Service...');
    await testEnhancedFineTuningService();

    // Test 5: Complete Pipeline Integration
    console.log('\n🔗 Testing Complete Pipeline Integration...');
    await testPipelineIntegration();

    console.log('\n🎉 All Phase 4 data pipeline tests passed successfully!');
    console.log('✅ Data pipeline is ready for production use');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
};

/**
 * Test Data Extractor
 */
const testDataExtractor = async () => {
  try {
    const extractor = new DataExtractor();

    // Test comprehensive data extraction
    const extractedData = await extractor.extractAllData({
      includeInactive: true,
      populateRelationships: true,
      maxRecordsPerType: 5, // Small sample for testing
      includeMetadata: true
    });

    console.log('✅ Data extraction completed');
    console.log(`📊 Extracted data types: ${Object.keys(extractedData).filter(k => k !== 'metadata').length}`);
    console.log(`📊 Total records: ${extractedData.metadata.statistics.totalRecords}`);
    console.log(`⏱️ Extraction time: ${extractedData.metadata.statistics.extractionTime}ms`);

    // Test college-specific extraction
    if (extractedData.colleges && extractedData.colleges.length > 0) {
      const testCollegeId = extractedData.colleges[0]._id;
      const collegeData = await extractor.extractCollegeSpecificData(testCollegeId);
      
      console.log('✅ College-specific extraction completed');
      console.log(`📊 College: ${collegeData.college.name}`);
      console.log(`📊 Campuses: ${collegeData.campuses.length}, Courses: ${collegeData.courses.length}`);
    }

    console.log('✅ Data Extractor: All tests passed');

  } catch (error) {
    console.error('❌ Data Extractor test failed:', error.message);
    throw error;
  }
};

/**
 * Test Training Data Generator
 */
const testTrainingDataGenerator = async () => {
  try {
    const generator = new TrainingDataGenerator();
    const extractor = new DataExtractor();

    // Get sample data
    const extractedData = await extractor.extractAllData({
      maxRecordsPerType: 3, // Very small sample
      includeMetadata: true
    });

    // Generate training data
    const trainingExamples = await generator.generateTrainingData(extractedData, {
      examplesPerRecord: 2,
      includeMultiTurn: false, // Disable for testing
      includeVariations: false, // Disable for testing
      responseStyle: 'helpful',
      maxExamplesTotal: 50
    });

    console.log('✅ Training data generation completed');
    console.log(`📊 Generated examples: ${trainingExamples.length}`);

    // Validate structure of generated examples
    if (trainingExamples.length > 0) {
      const firstExample = trainingExamples[0];
      if (firstExample.messages && Array.isArray(firstExample.messages)) {
        console.log('✅ Training examples have correct structure');
        console.log(`📝 Sample: ${firstExample.messages.length} messages`);
        console.log(`📝 Roles: ${firstExample.messages.map(m => m.role).join(', ')}`);
      } else {
        throw new Error('Invalid training example structure');
      }
    }

    console.log('✅ Training Data Generator: All tests passed');

  } catch (error) {
    console.error('❌ Training Data Generator test failed:', error.message);
    throw error;
  }
};

/**
 * Test Data Validator
 */
const testDataValidator = async () => {
  try {
    const validator = new DataValidator();

    // Create sample training data for validation
    const sampleData = [
      {
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'What courses do you offer?' },
          { role: 'assistant', content: 'We offer various courses including computer science, business, and engineering programs.' }
        ]
      },
      {
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Tell me about admissions.' },
          { role: 'assistant', content: 'Our admissions process is straightforward. You can apply online through our website.' }
        ]
      },
      // Add a duplicate for testing
      {
        messages: [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'What courses do you offer?' },
          { role: 'assistant', content: 'We offer various courses including computer science, business, and engineering programs.' }
        ]
      },
      // Add an invalid example for testing
      {
        messages: [
          { role: 'user', content: 'Invalid example with missing assistant response' }
        ]
      }
    ];

    // Validate the sample data
    const validationResults = await validator.validateTrainingData(sampleData, {
      strictMode: false,
      checkDuplicates: true,
      validateContent: true,
      generateReport: true
    });

    console.log('✅ Data validation completed');
    console.log(`📊 Total examples: ${validationResults.totalExamples}`);
    console.log(`📊 Valid examples: ${validationResults.validExamples}`);
    console.log(`📊 Invalid examples: ${validationResults.invalidExamples}`);
    console.log(`📊 Duplicates: ${validationResults.duplicates}`);
    console.log(`📊 Errors: ${validationResults.errors.length}`);
    console.log(`📊 Warnings: ${validationResults.warnings.length}`);
    console.log(`📊 Quality score: ${validationResults.report?.qualityScore?.toFixed(2) || 'N/A'}`);

    // Validate that we detected issues
    if (validationResults.duplicates > 0) {
      console.log('✅ Duplicate detection working');
    }
    if (validationResults.invalidExamples > 0) {
      console.log('✅ Invalid example detection working');
    }

    console.log('✅ Data Validator: All tests passed');

  } catch (error) {
    console.error('❌ Data Validator test failed:', error.message);
    throw error;
  }
};

/**
 * Test Enhanced Fine-Tuning Data Service
 */
const testEnhancedFineTuningService = async () => {
  try {
    const service = new FineTuningDataService();

    // Test enhanced data generation
    const result = await service.generateEnhancedTrainingData({
      maxExamplesPerType: 3, // Small sample for testing
      validationSplit: 0.2,
      validateData: true,
      examplesPerRecord: 2
    });

    console.log('✅ Enhanced data generation completed');
    console.log(`📊 Training examples: ${result.trainingData.length}`);
    console.log(`📊 Validation examples: ${result.validationData.length}`);
    console.log(`📊 Total examples: ${result.statistics.totalExamples}`);

    if (result.validationResults) {
      console.log(`📊 Data quality score: ${result.validationResults.report?.qualityScore?.toFixed(2) || 'N/A'}`);
      console.log(`📊 Validation errors: ${result.validationResults.errors.length}`);
      console.log(`📊 Validation warnings: ${result.validationResults.warnings.length}`);
    }

    // Test validation of existing data
    if (result.trainingData.length > 0) {
      const validationResults = await service.validateTrainingData(result.trainingData.slice(0, 5));
      console.log('✅ Training data validation completed');
      console.log(`📊 Validated ${validationResults.totalExamples} examples`);
    }

    console.log('✅ Enhanced Fine-Tuning Data Service: All tests passed');

  } catch (error) {
    console.error('❌ Enhanced Fine-Tuning Data Service test failed:', error.message);
    throw error;
  }
};

/**
 * Test Complete Pipeline Integration
 */
const testPipelineIntegration = async () => {
  try {
    console.log('Testing complete data pipeline flow...');

    // Step 1: Extract data
    const extractor = new DataExtractor();
    const extractedData = await extractor.extractAllData({
      maxRecordsPerType: 2,
      includeMetadata: true
    });

    // Step 2: Generate training data
    const generator = new TrainingDataGenerator();
    const trainingExamples = await generator.generateTrainingData(extractedData, {
      examplesPerRecord: 1,
      maxExamplesTotal: 20
    });

    // Step 3: Validate data
    const validator = new DataValidator();
    const validationResults = await validator.validateTrainingData(trainingExamples, {
      generateReport: true
    });

    // Step 4: Export to JSONL
    const service = new FineTuningDataService();
    const jsonlData = service.exportToJSONL(trainingExamples.slice(0, 3));

    console.log('✅ Complete pipeline integration test passed');
    console.log(`📊 Pipeline processed ${extractedData.metadata.statistics.totalRecords} records`);
    console.log(`📊 Generated ${trainingExamples.length} training examples`);
    console.log(`📊 Validation quality score: ${validationResults.report?.qualityScore?.toFixed(2) || 'N/A'}`);
    console.log(`📊 JSONL export: ${jsonlData.split('\n').length} lines`);

    console.log('✅ Pipeline Integration: All tests passed');

  } catch (error) {
    console.error('❌ Pipeline Integration test failed:', error.message);
    throw error;
  }
};

// Run test if called directly
if (require.main === module) {
  testDataPipeline();
}

module.exports = testDataPipeline;
