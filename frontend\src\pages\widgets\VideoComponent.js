import { Box } from '@mui/material';
import PropTypes from 'prop-types';
import React from 'react';

const VideoComponent = ({ url }) => (
  <Box
    sx={{
      width: '100%',
      height: 650,
      p: 0,
      m: 0,
      display: 'flex',
      flexDirection: 'column',
      flex: 1,
      minHeight: 0,
      overflow: 'hidden',
    }}
  >
    <iframe
      src={url}
      width="100%"
      height="100%"
      frameBorder="0"
      allow="autoplay; fullscreen; picture-in-picture"
      allowFullScreen
      title="Immersive Video"
      style={{
        borderRadius: 8,
        width: '100%',
        height: '100%',
        display: 'block',
        margin: 0,
        padding: 0,
      }}
    />
  </Box>
);

export default VideoComponent;

VideoComponent.propTypes = {
  url: PropTypes.string,
};
