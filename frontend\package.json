{"name": "@zone/mui-kit-react", "author": "Minimals", "version": "2.0.0", "description": "Cra & JavaScript", "private": true, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx .", "lint:fix": "eslint --fix --ext .js,.jsx .", "prettier": "prettier --write 'src/**/*.{js,jsx}'", "clear-all": "rm -rf build node_modules", "re-start": "rm -rf build node_modules && yarn install && yarn start", "re-build": "rm -rf build node_modules && yarn install && yarn build"}, "eslintConfig": {"extends": ["react-app"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@emotion/cache": "^11.10.5", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@hookform/resolvers": "^3.0.0", "@iconify/react": "^4.1.0", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.124", "@mui/material": "^5.11.15", "@mui/system": "^5.11.15", "@mui/x-date-pickers": "^6.0.3", "@progress/kendo-drawing": "^1.17.5", "@progress/kendo-licensing": "^1.3.0", "@progress/kendo-react-pdf": "^5.13.1", "@reduxjs/toolkit": "^1.9.5", "apexcharts": "^3.40.0", "axios": "^1.4.0", "date-fns": "^2.29.3", "framer-motion": "^10.9.2", "google-map-react": "^2.2.0", "html2pdf.js": "^0.9.0", "js-cookie": "^3.0.5", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lodash.merge": "^4.6.2", "nprogress": "^0.2.0", "numeral": "^2.0.6", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-countup": "^6.4.2", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.43.8", "react-lazy-load-image-component": "^1.5.6", "react-markdown": "^10.1.0", "react-player": "^2.12.0", "react-redux": "^8.0.5", "react-router": "^6.9.0", "react-router-dom": "^6.9.0", "react-scripts": "^5.0.1", "react-select": "^5.7.3", "react-slick": "^0.29.0", "serve": "^14.2.0", "simplebar-react": "^3.2.3", "slick-carousel": "^1.8.1", "stylis": "^4.1.1", "stylis-plugin-rtl": "^2.0.2", "universal-cookie": "^8.0.1", "web-vitals": "^3.3.0", "yet-another-react-lightbox": "^3.2.0", "yup": "^1.0.2"}, "devDependencies": {"@babel/core": "^7.21.3", "@babel/eslint-parser": "^7.21.3", "eslint": "^8.37.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.8.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.7", "typescript": "^4.9.5"}, "overrides": {"nth-check": "2.1.1"}}