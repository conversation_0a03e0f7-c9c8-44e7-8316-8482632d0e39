.report-container h1, .report-container h2, .report-container h3,.report-container h4{
    color: #2f2f2f;
    font-family: <PERSON>,sans-serif;
    margin: 0;
    text-align: left;
    font-weight: 700 !important;
}
.report-container h1{
    font-size: 24px;
}
.report-container h2{
    font-size: 16px;
    font-weight: 700;
}
.report-container h3{
    font-size: 12px;
}
.report-container h4{
    font-size: 11px;
    line-height: 1.3;
}
.report-container p, .report-container span, .report-container b{
    color: #2f2f2f;
    font-weight: 300 !important;
    margin: 0;
    font-size: 8px;
    line-height: 1.6;
    text-align: left;
    font-family: Public sans ,sans-serif;
    display: block;
}
.report-container b{
    font-weight: 700 !important;
}
.report-container{
    width: 880px;
    margin: 0 auto;
    padding: 0px 20px;
}
/* .report-container .section{
    margin-bottom: 50px;
} */
.report-container .full-height{
    height: 990px;
    position: relative;
}
.report-container .flex{
    display: flex;
}
.report-container .align-center{
    align-items: center;
}
.report-container .justify-center{
    justify-content: center;
}
/* .report-container .justify-between{
    justify-content: space-between;
} */
.report-container .report-head{
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    height: calc(100vh - 220px);
}
.report-container .report-head h1{
    text-align: center;
}
.report-container .report-head img{
    margin: 30px auto;
    margin-top: 20px;
    width: 220px;
}
.report-container .report-head p{
    font-size: 14px;
    text-align: center;
}
.report-container .report-head p b{
    display: inline-block;
    text-transform: uppercase;
    width: fit-content;
    font-size: 13px;
}
.report-container .section .content p{
    margin: 20px 0;
}
.report-container .chart-wrapper img{
    width: 70% !important;
    height: 290px !important;
}
.report-container .chart-wrapper .units-wrapper{
    margin-left: 20px; 
    max-width: 360px;
    padding-left: 10px;
    position: unset;
}
.report-container .chart-wrapper .units-wrapper h3{
    margin-bottom: 16px;
    text-align: left;
}
.report-container .chart-wrapper .units-wrapper .skill-levels{
    margin-top: 20px;
}
.report-container .chart-wrapper .units-wrapper .skill-levels b{
    color: #1f1f1f;
}
.report-container .chart-wrapper .units-wrapper .skill-levels .skill{
    display: flex; 
    align-items: center; 
    margin-bottom: 12px;
}
.report-container .chart-wrapper .units-wrapper .skill-levels span{
    border: 2px solid #FD4645; 
    display: block;
    margin-right: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}
.report-container .skills-wrappper{
    display: grid;
    grid-template-columns: repeat(4,1fr);
    grid-gap: 12px;
    margin: 30px 0;
}
.report-container .skills-wrappper h4{
    margin-bottom: 12px;
    text-align: left;
    min-height: 30px;
}
.report-container .skills-wrappper p,.report-container .skills-wrappper span{
    max-width: 94px;
}
.career-matches .career-info-wrapper{
    display: flex;
    /* border-top: 1px solid #ccc; */
    width: 100%;
    align-items: center;
    justify-content: space-between;
    padding: 20px 0;
}
.career-matches .career-info-wrapper .career-info{
    margin-right: 40px;
}
.report-container .career-matches .career-info-wrapper{
    display: grid;
    grid-template-columns: repeat(2,1fr);
    grid-column-gap: 60px;
    /* border-top: 1px solid #ccc; */
    padding: 20px 0;
}
.report-container .code{
    padding: 20px 0;
    text-align: center;
    border-bottom: 1px solid #ccc;
}
.report-container .code p, .report-container .code h3{
    text-align: center;
}
.report-container .code b{
    background: #000c3b;
    color: #fff;
    margin: 0 auto;
    margin-top: 12px;
    width: fit-content;
    padding: 5px 28px;
}
.report-container .code h2, .report-container .code p, .report-container .code span{
    text-align: center !important;
}
.report-container .code p{
    margin: 0px;
}
.report-container .code span{
    background-color: #000c3b;
    /* border: 1px solid #7040f1; */
    color: white !important;
    font-weight: 700 !important;
    width: fit-content;
    padding: 2px 15px;
    font-size: 14px;
    /* border-radius: 8px; */
    margin: 10px auto;
}
.report-container .footer img{
    width: 220px;
}
.report-container .footer a{
    font-size: 20px !important;
    color: #1f1f1f !important;
    font-weight: 700 !important;
}
.report-container .footer{
    height: calc(100% - 240px);
}
.report-container .skilldarChart{
    overflow: hidden !important;
    border: none;
    scrollbar-width: none !important;
}
.report-container .skilldarChart .apexcharts-canvas{
    overflow: hidden !important;
    border: none;
    scrollbar-width: none !important;
}
.report-container .skilldarChart .apexcharts-canvas foreignObject{
    overflow: hidden !important;
    border: none;
    scrollbar-width: none !important;
}
.report-container .skilldarChart .apexcharts-canvas g{
    overflow: hidden !important;
    border: none;
    scrollbar-width: none !important;
}
/* .report-container .skilldarChart g {
    display: none !important;
    opacity: 0;
} */
.apexcharts-xcrosshairs, .apexcharts-ycrosshairs{
    display: none !important;
    opacity: 0;
}
.report-container .skilldarChart .apexcharts-canvas svg{
    overflow: hidden !important;
    border: none;
    scrollbar-width: none !important;
}

@media print {
    h1,h2,h3,h4{
        color: #1f1f1f;
        font-family: Barlow,sans-serif !important;
        margin: 0 !important;
        font-weight: 800 !important;
        text-align: left;
    }
    h1{
        font-size: 28px !important;
    }
    h2{
        font-size: 16px !important;
    }
    h3{
        font-size: 16px !important;
    }
    h4{
        font-size: 14px !important;
    }
    p, span, b{
        color: #637480 !important;
        font-weight: 400 !important;
        margin: 0 !important;
        font-size: 10px !important;
        line-height: 1.8 !important;
        text-align: left;
    }
    b{
        font-weight: 600 !important;
    }
    .apexcharts-xcrosshairs, .apexcharts-ycrosshairs{
        display: none !important;
        opacity: 0;
    }
    /* .report-container .skilldarChart g line{
        display: none !important;
        opacity: 0;
    } */
    .full-height{
        height: 990px;
    }
    .report-container{
        width: 880px;
        margin: 0 auto !important;
        padding: 40px 30px!important;
    }
    .section{
        margin-bottom: 50px !important;
    }
    .flex{
        display: flex;
    }
    .align-center{
        align-items: center;
    }
    .justify-center{
        justify-content: center;
    }
    .justify-between{
        justify-content: space-between;
    }
    .report-head{
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        height: calc(100vh - 220px);
    }
    .report-head h1{
        text-align: center;
    }
    .report-head img{
        margin: 30px auto;
        margin-top: 20px;
        width: 220px;
    }
    .report-head p{
        font-size: 14px;
        text-align: center;
    }
    .report-head p b{
        display: inline-block;
        text-transform: uppercase;
        width: fit-content;
    }
    .section .content p{
        margin: 20px 0;
    }
    .chart-wrapper img{
        width: 70% !important;
        height: 290px !important;
    }
    .chart-wrapper .units-wrapper{
        margin-left: 20px; 
        max-width: 360px;
        padding-left: 10px;
    }
    .chart-wrapper .units-wrapper h3{
        margin-bottom: 16px;
        text-align: left;
    }
    .chart-wrapper .units-wrapper .skill-levels{
        margin-top: 20px;
    }
    .chart-wrapper .units-wrapper .skill-levels b{
        color: #1f1f1f;
    }
    .chart-wrapper .units-wrapper .skill-levels .skill{
        display: flex; 
        align-items: center; 
        margin-bottom: 12px;
    }

    .section.skilldar-current-job .chart-wrapper .units-wrapper .skill{
        color: white !important;
    }
    
    /* .section.skilldar-current-job .chart-wrapper {
        border: 1px solid rgb(0, 0, 152);
        border-radius: 20px;
        overflow: hidden;
    } */

    .report-chart-skilldar .apexcharts-legend .apexcharts-legend-text{
        font-size: 8px !important;
        text-align: left !important;
        font-family: Public Sans,sans-serif !important;
        padding-left: 24px;
        line-height: 10px;
        /* width: calc(100% - 14px); */
        font-weight: 500 !important;
        /* color: #008ff5 !important; */
    }
    
    .chart-wrapper .units-wrapper .skill-levels span{
        border: 2px solid #FD4645; 
        display: block;
        margin-right: 20px;
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }
    .skills-wrappper{
        display: grid;
        grid-template-columns: repeat(4,1fr);
        grid-gap: 20px;
        margin-top: 60px;
        margin-bottom: 40px;
    }
    .skills-wrappper h4{
        margin-bottom: 12px;
        text-align: left;
    }
    .career-matches .career-info-wrapper{
        display: flex;
        /* border-top: 1px solid #ccc; */
        width: 100%;
        align-items: center;
        justify-content: space-between;
        padding: 20px 0;
    }
    .career-matches .career-info-wrapper .career-info{
        margin-right: 40px;
    }
    /* .code{
        padding: 20px 0;
        border-top: 1px solid #1f1f1f;
        border-bottom: 1px solid #1f1f1f;
    } */
    .code h2, .code p, .code span{
        text-align: center !important;
    }
    .code p{
        margin: 20px 0;
    }
    .code span{
        background-color: #F0ECFE;
        border: 1px solid #7040f1;
        color: #7040f1 !important;
        font-weight: 800 !important;
        width: fit-content;
        padding: 10px 30px;
        font-size: 14px;
        border-radius: 8px;
        margin: 0 auto;
    }
    .footer{
        height: calc(100% - 240px);
    }
    .footer img{
        width: 220px;
    }
    .footer a{
        font-size: 20px !important;
        color: #1f1f1f !important;
        font-weight: 700 !important;
    }
}

 /* ===============================  */
.chart-units-wrapper{
    width: 40%;
    padding: 20px;
    border-left: 1px solid #ccc;
    display: flex;
    align-items: center;
}
.chart-units-wrapper .skill-levels{
    margin-top: 20px;

}
.chart-units-wrapper .skill{
    display: flex;
    margin-top: 5px;
}
.chart-units-wrapper .skill span{
    border: 2px solid #FD4645; 
    margin-right: 20px;
    width: 12px;
    height: 12px;
    border-radius: 50%; 
}

.report-chart-skilldar .report-careerGoal span{
    /* border: 2px solid #FD4645;  */
    display: block;
    margin-right: 10px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

