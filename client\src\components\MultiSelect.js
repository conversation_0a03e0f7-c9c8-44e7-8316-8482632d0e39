import { Autocomplete, MenuItem, OutlinedInput, Select, TextField, useTheme } from '@mui/material';

function getStyles(name, personName, theme) {
    return {
        fontWeight:
            personName.indexOf(name) === -1
                ? theme.typography.fontWeightRegular
                : theme.typography.fontWeightMedium,
    };
}

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
    PaperProps: {
        style: {
            maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
            width: 250,
        },
    },
};

export const MultiSelect = (props) => {
    // const { value, onChange, placeHolder, options, ...rest } = props
    const { placeHolder, label, ...rest } = props
    const theme = useTheme();
    return (
        // <Select
        //     {...rest}
        //     multiple
        //     displayEmpty
        //     value={value}
        //     onChange={onChange}
        //     input={<OutlinedInput />}
        //     renderValue={(selected) => {
        //         if (selected.length === 0) {
        //             return <em>{placeHolder}</em>;
        //         }

        //         return selected.join(', ');
        //     }}
        //     MenuProps={MenuProps}
        //     inputProps={{ 'aria-label': 'Without label' }}
        // >
        //     <MenuItem disabled value="">
        //         <em>{placeHolder}</em>
        //     </MenuItem>
        //     {options.map((option) => (
        //         <MenuItem
        //             key={option}
        //             value={option}
        //             style={getStyles(option, value, theme)}
        //         >
        //             {option}
        //         </MenuItem>
        //     ))}
        // </Select>


        <Autocomplete
            {...rest}
            multiple
            id="multiple-limit-tags"
            getOptionLabel={(option) => option}
            renderInput={(params) => (
                <TextField {...params} label={label} placeholder={placeHolder} />
            )}
        />

    )
}
