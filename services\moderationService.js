const mongoose = require('mongoose');

// Simple profanity filter implementation (avoiding ES module issues)
class SimpleProfanityFilter {
  constructor() {
    this.badWords = [
      'damn', 'hell', 'shit', 'fuck', 'bitch', 'ass', 'bastard', 'crap',
      'piss', 'dick', 'cock', 'pussy', 'whore', 'slut', 'fag', 'nigger',
      'retard', 'gay', 'lesbian', 'homo', 'queer', 'tranny', 'dyke'
    ];
  }

  isProfane(text) {
    const lowerText = text.toLowerCase();
    return this.badWords.some(word => lowerText.split(" ").includes(word));
  }

  clean(text) {
    let cleanText = text;
    this.badWords.forEach(word => {
      const regex = new RegExp(word, 'gi');
      cleanText = cleanText.replace(regex, '*'.repeat(word.length));
    });
    return cleanText;
  }
}

// Import existing models (no changes to existing system)
const { ChatSession } = require('../models/chatbotModels');
const { ChatMessage } = require('../models/chatbotModels');
const { ChatViolation } = require('../models/chatbotModels');

/**
 * Content Moderation Service
 * Handles content filtering, violation tracking, and session management
 * Integrates with existing chatbot infrastructure without breaking changes
 */
class ModerationService {
  constructor() {
    this.profanityFilter = new SimpleProfanityFilter();
    this.maxViolationsPerSession = 3;
    this.violationCooldownHours = 24;
    
    // Educational content keywords (allowed)
    this.educationalKeywords = [
      'course', 'program', 'degree', 'certificate', 'education', 'learning',
      'career', 'job', 'skill', 'training', 'qualification', 'study',
      'college', 'university', 'campus', 'admission', 'enrollment',
      'tuition', 'scholarship', 'financial aid', 'academic', 'curriculum'
    ];

    // Off-topic keywords (flagged)
    this.offTopicKeywords = [
      'politics', 'religion', 'dating', 'gambling', 'drugs', 'alcohol',
      'violence', 'illegal', 'hack', 'cheat', 'fraud', 'scam'
    ];

    // Spam patterns
    this.spamPatterns = [
      /(.)\1{4,}/g, // Repeated characters (aaaaa)
      /[A-Z]{5,}/g, // Excessive caps
      /\b(\w+)\s+\1\b/gi, // Repeated words
      /[!?]{3,}/g, // Excessive punctuation
    ];
  }

  /**
   * Moderate user message before processing
   * @param {string} sessionId - Chat session ID
   * @param {string} message - User message to moderate
   * @param {Object} options - Moderation options
   * @returns {Promise<Object>} - Moderation result
   */
  async moderateMessage(sessionId, message, options = {}) {
    const {
      strictMode = false,
      logViolations = true
    } = options;

    console.log(`🛡️ Moderating message for session: ${sessionId}`);
    console.log(`📝 Message length: ${message.length} characters`);

    try {
      // Check if session is flagged/blocked
      const sessionStatus = await this.checkSessionStatus(sessionId);
      if (sessionStatus.blocked) {
        return {
          allowed: false,
          reason: 'session_blocked',
          message: 'Your session has been temporarily blocked due to policy violations. Please try again later.',
          violations: sessionStatus.violations,
          blockExpiry: sessionStatus.blockExpiry
        };
      }

      // Perform content moderation checks
      const moderationResults = await this.performModerationChecks(message, strictMode);
      
      if (moderationResults.violations.length > 0) {
        // Log violation if enabled
        if (logViolations) {
          await this.logViolation(sessionId, message, moderationResults.violations);
        }

        // Check if this violation should block the session
        const shouldBlock = await this.shouldBlockSession(sessionId);
        if (shouldBlock) {
          await this.blockSession(sessionId);
        }

        return {
          allowed: false,
          reason: 'content_violation',
          message: this.getViolationMessage(moderationResults.violations),
          violations: moderationResults.violations,
          blocked: shouldBlock
        };
      }

      console.log('✅ Message passed moderation');
      return {
        allowed: true,
        reason: 'approved',
        message: null,
        violations: []
      };

    } catch (error) {
      console.error('❌ Moderation error:', error);
      
      // In case of error, allow message but log the issue
      return {
        allowed: true,
        reason: 'moderation_error',
        message: null,
        violations: [],
        error: error.message
      };
    }
  }

  /**
   * Perform comprehensive moderation checks
   * @param {string} message - Message to check
   * @param {boolean} strictMode - Enable strict moderation
   * @returns {Promise<Object>} - Moderation results
   */
  async performModerationChecks(message, strictMode = false) {
    const violations = [];
    const cleanMessage = message.toLowerCase().trim();

    // 1. Profanity check
    if (this.profanityFilter.isProfane(message)) {
      violations.push({
        type: 'profanity',
        severity: 'high',
        description: 'Message contains inappropriate language'
      });
    }

    // 2. Off-topic content check
    const offTopicScore = this.checkOffTopicContent(cleanMessage);
    if (offTopicScore > (strictMode ? 0.3 : 0.5)) {
      violations.push({
        type: 'off-topic',
        severity: 'medium',
        description: 'Message appears to be off-topic for educational context',
        score: offTopicScore
      });
    }

    // 3. Spam detection
    const spamScore = this.checkSpamPatterns(message);
    if (spamScore > (strictMode ? 0.4 : 0.6)) {
      violations.push({
        type: 'spam',
        severity: 'medium',
        description: 'Message appears to be spam or low-quality content',
        score: spamScore
      });
    }

    // 4. Length checks
    if (message.length > 2000) {
      violations.push({
        type: 'excessive_length',
        severity: 'low',
        description: 'Message is too long'
      });
    }

    if (message.trim().length < 2) {
      violations.push({
        type: 'too_short',
        severity: 'low',
        description: 'Message is too short to be meaningful'
      });
    }

    // 5. Repetitive content check
    if (this.isRepetitiveContent(message)) {
      violations.push({
        type: 'repetitive',
        severity: 'medium',
        description: 'Message contains repetitive content'
      });
    }

    return {
      violations: violations,
      totalScore: violations.reduce((sum, v) => sum + this.getSeverityScore(v.severity), 0)
    };
  }

  /**
   * Check for off-topic content
   * @param {string} message - Message to check
   * @returns {number} - Off-topic score (0-1)
   */
  checkOffTopicContent(message) {
    let offTopicCount = 0;
    let educationalCount = 0;

    // Count off-topic keywords
    this.offTopicKeywords.forEach(keyword => {
      if (message.includes(keyword)) {
        offTopicCount++;
      }
    });

    // Count educational keywords
    this.educationalKeywords.forEach(keyword => {
      if (message.includes(keyword)) {
        educationalCount++;
      }
    });

    // Calculate score
    const totalWords = message.split(/\s+/).length;
    const offTopicRatio = offTopicCount / Math.max(totalWords, 1);
    const educationalRatio = educationalCount / Math.max(totalWords, 1);

    // Higher off-topic ratio and lower educational ratio = higher score
    return Math.min(1, offTopicRatio * 2 - educationalRatio);
  }

  /**
   * Check for spam patterns
   * @param {string} message - Message to check
   * @returns {number} - Spam score (0-1)
   */
  checkSpamPatterns(message) {
    let spamScore = 0;

    this.spamPatterns.forEach(pattern => {
      const matches = message.match(pattern);
      if (matches) {
        spamScore += matches.length * 0.2;
      }
    });

    return Math.min(1, spamScore);
  }

  /**
   * Check if content is repetitive
   * @param {string} message - Message to check
   * @returns {boolean} - Is repetitive
   */
  isRepetitiveContent(message) {
    const words = message.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    
    // If less than 50% unique words, consider repetitive
    return uniqueWords.size / words.length < 0.5 && words.length > 10;
  }

  /**
   * Get severity score for violation
   * @param {string} severity - Violation severity
   * @returns {number} - Numeric score
   */
  getSeverityScore(severity) {
    const scores = {
      'low': 0.2,
      'medium': 0.5,
      'high': 1.0
    };
    return scores[severity] || 0.2;
  }

  /**
   * Check session status and violation history
   * @param {string} sessionId - Session ID
   * @returns {Promise<Object>} - Session status
   */
  async checkSessionStatus(sessionId) {
    try {
      const session = await ChatSession.findById(sessionId).lean();
      if (!session) {
        return { blocked: false, violations: 0 };
      }

      // Check if session is flagged
      if (session.status === 'flagged') {
        // Check if cooldown period has passed
        const lastViolation = await ChatViolation.findOne({
          sessionId: new mongoose.Types.ObjectId(sessionId)
        }).sort({ createdAt: -1 }).lean();

        if (lastViolation) {
          const cooldownEnd = new Date(lastViolation.createdAt);
          cooldownEnd.setHours(cooldownEnd.getHours() + this.violationCooldownHours);
          
          if (new Date() < cooldownEnd) {
            return {
              blocked: true,
              violations: session.violationCount,
              blockExpiry: cooldownEnd
            };
          } else {
            // Cooldown expired, unblock session
            await ChatSession.findByIdAndUpdate(sessionId, {
              status: 'active',
              violationCount: 0
            });
          }
        }
      }

      return {
        blocked: false,
        violations: session.violationCount || 0
      };

    } catch (error) {
      console.error('❌ Error checking session status:', error);
      return { blocked: false, violations: 0 };
    }
  }

  /**
   * Log violation to database
   * @param {string} sessionId - Session ID
   * @param {string} message - Violating message
   * @param {Array} violations - Violation details
   * @returns {Promise<void>}
   */
  async logViolation(sessionId, message, violations) {
    try {
      const violation = new ChatViolation({
        sessionId: new mongoose.Types.ObjectId(sessionId),
        message: message,
        violationType: violations.map(v => v.type),
        severity: Math.max(...violations.map(v => this.getSeverityScore(v.severity))),
        details: violations,
        createdAt: new Date()
      });

      await violation.save();

      // Update session violation count
      await ChatSession.findByIdAndUpdate(sessionId, {
        $inc: { violationCount: 1 }
      });

      console.log(`📝 Logged violation for session ${sessionId}`);

    } catch (error) {
      console.error('❌ Error logging violation:', error);
    }
  }

  /**
   * Check if session should be blocked
   * @param {string} sessionId - Session ID
   * @returns {Promise<boolean>} - Should block
   */
  async shouldBlockSession(sessionId) {
    try {
      const session = await ChatSession.findById(sessionId).lean();
      return session && session.violationCount >= this.maxViolationsPerSession;
    } catch (error) {
      console.error('❌ Error checking block status:', error);
      return false;
    }
  }

  /**
   * Block session due to violations
   * @param {string} sessionId - Session ID
   * @returns {Promise<void>}
   */
  async blockSession(sessionId) {
    try {
      await ChatSession.findByIdAndUpdate(sessionId, {
        status: 'flagged'
      });

      console.log(`🚫 Blocked session ${sessionId} due to violations`);

    } catch (error) {
      console.error('❌ Error blocking session:', error);
    }
  }

  /**
   * Get appropriate violation message
   * @param {Array} violations - Violation details
   * @returns {string} - User-friendly message
   */
  getViolationMessage(violations) {
    const highSeverity = violations.some(v => v.severity === 'high');
    const types = violations.map(v => v.type);

    if (highSeverity || types.includes('profanity')) {
      return 'Your message contains inappropriate content. Please keep the conversation educational and respectful.';
    }

    if (types.includes('off-topic')) {
      return 'Please keep your questions related to education, courses, careers, and academic guidance.';
    }

    if (types.includes('spam')) {
      return 'Please avoid repetitive or spam-like messages. Ask clear, specific questions about your educational needs.';
    }

    return 'Your message doesn\'t meet our community guidelines. Please rephrase your question in a clear, educational context.';
  }

  /**
   * Get session violation history
   * @param {string} sessionId - Session ID
   * @returns {Promise<Array>} - Violation history
   */
  async getViolationHistory(sessionId) {
    try {
      const violations = await ChatViolation.find({
        sessionId: new mongoose.Types.ObjectId(sessionId)
      }).sort({ createdAt: -1 }).lean();

      return violations.map(v => ({
        id: v._id,
        types: v.violationType,
        severity: v.severity,
        message: v.message.substring(0, 100) + '...',
        timestamp: v.createdAt,
        details: v.details
      }));

    } catch (error) {
      console.error('❌ Error getting violation history:', error);
      return [];
    }
  }
}

module.exports = ModerationService;
