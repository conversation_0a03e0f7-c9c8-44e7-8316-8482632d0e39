import Cookies from "universal-cookie"
import { removeUserPermissions } from "src/Redux/selectedCollegeSlice"
import { setSnackbar } from "../Redux/snackbarSlice"

export const APP_ROUTER_BASE_URL = '/admin/'

export const allowedRoles = (role, accessRoles) => {
    if (accessRoles.includes(role)) {
        return true
    }
    return false
}

export const logoutUser = () => {
    window.location = `${APP_ROUTER_BASE_URL}login`;
    const cookies = new Cookies()
    cookies.remove("token", { path: '/' })
    localStorage.removeItem('selectedCollege')
    localStorage.removeItem('currentUserPermissions')
}
export const copiedFunction = (value, dispatch) => {
    if(value){
      const copy = function (e) {
        e.clipboardData.setData("text/plain", value);
        e.preventDefault();

        document.removeEventListener("copy", copy,true);
    }
      document.addEventListener("copy", copy,true);
      document.execCommand("copy");
      dispatch(setSnackbar({
        snackbarOpen: true,
        snackbarType: 'success',
        snackbarMessage: "Copied to Clipboard"
      }))
    }else {
        dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: "Unable to Copy"
          }))
    }
  }
export const isEmpty = (value) =>
  value === undefined ||
  value === null ||
  (typeof value === "object" && Object.keys(value).length === 0) ||
  (typeof value === "string" && value.trim().length === 0);
export const decodeBase64ToBlob = (base64String) => {
  // Decode base64 to binary string manually
  const binary = Uint8Array.from(
    atob(base64String),
    char => char.charCodeAt(0)
  );

  return new Blob([binary], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });
};

export const getUserRole = (role) => {
    let userRole
    if(role === 1){
        userRole = "Super Admin"
    }else if(role === 2){
        userRole = "Group Admin"
    }else if(role === 3){
        userRole = "College Admin"
    }else if(role === 4){
        userRole = "Campus Admin"
    }else{
        userRole = "User"
    }
    return userRole
}