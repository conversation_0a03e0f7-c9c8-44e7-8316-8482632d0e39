import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
  Container,
  Grid,
  Typography
} from '@mui/material';
import { createAsyncThunk } from '@reduxjs/toolkit';
// @mui
import { useFormik } from 'formik';
import axiosInstance from '../../utils/axiosInstance';
import useLoading from '../../hooks/useLoading';
import { skillAbilitiesRadarCategoryValidationSchema, skillAbilitiesSubRadarCategoryValidationSchema } from '../../utils/validationSchemas';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import { getSkills, getSkillsById, getSubRadarCat, updateSkill } from './skillSlice';
import SkillsForm from './SkillsForm';
// import { getSectors, postSector, removeSector, updateSector } from './SectorSlice';

// ----------------------------------------------------------------------
export const SKILLS_TABLE_HEAD = [
  { id: 'lmiName', label: 'Name', alignRight: false },
  { id: 'radarCategory', label: 'Radar Category', alignRight: false },
  { id: 'radarSubcategory', label: 'Radar Sub Category', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderRadarCategoryCells = ['lmiName','radarCategory', 'radarSubcategory' ]

const Skills = () => {
  const dispatch = useDispatch()
  const skillsData = useSelector(state => state.skills.skills)
  const { status } = useSelector(state => state.skills)
  const skillId = useSelector(state => state.skills.skillId)
  const subRadarCategoryId = useSelector(state => state.skills.subRadarCat)
  // const [loading, setLoading] = useState(false)
  const [openModel, setOpenModel] = useState(false);
  const [skills, setSkills] = useState([])
  const [skillIdDetails, setSkillIdDetails] = useState({})
  const [detailsData, setDetailsData] = useState({})
  const loading = useLoading(status)
  const [radarDetails, setRadarDetails] = useState([])
  const [subRadarDetails, setSubRadarDetails] = useState([])
  const [updateSubRadarDetails, setUpdateSubRadarDetails] = useState([])

  const getRadarDetails = createAsyncThunk('radar-category/get', async (data, { rejectWithValue }) => {
    try {
      const response = await axiosInstance({
        url: "radar-category/get",
        method: "GET",
      })
      setRadarDetails(response.data.data)
      return response.data;
    } catch (error) {
      console.log("err", error);
      return rejectWithValue(error)
    }
  })
  const getSubRadarDetails = createAsyncThunk('sub-radar-category/get', async (data, { rejectWithValue }) => {
    try {
      const response = await axiosInstance({
        url: "sub-radar-category/get",
        method: "GET",
      })
      setSubRadarDetails(response.data.data)
      return response.data;
    } catch (error) {
      console.log("err", error);
      return rejectWithValue(error)
    }
  })

  const formik = useFormik({
    initialValues: {
      id: '',
      lmiId: '',
      lmiName: '',
      radarCategory: '',
      radarSubCategory: '',
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      // setLoading(true)
      const skill = {
        id: values?.id,
        radarCategoryId: values?.radarCategory,
        radarSubcategoryId: values?.radarSubCategory
      }
      dispatch(updateSkill(skill))
        .then(response => {
          if (response?.payload?.success) {
            dispatch(getSkills());
            dispatch(setSnackbar({
              snackbarOpen: true,
              snackbarType: 'success',
              snackbarMessage: "Succesfully updated skills"
            }))
          } else {
            // error occoured
            const errorMessage = response?.payload?.response?.data?.msg
            dispatch(setSnackbar({
              snackbarOpen: true,
              snackbarType: 'error',
              snackbarMessage: errorMessage || "Something went wrong!"
            }))
          }
        }).finally(() => {
          // setLoading(false)
        })
      handleClose()
    },
    validationSchema: updateSubRadarDetails && updateSubRadarDetails.length ? skillAbilitiesRadarCategoryValidationSchema : skillAbilitiesSubRadarCategoryValidationSchema

  })

  useEffect(() => {
    dispatch(getSkills());
    dispatch(getRadarDetails())
    dispatch(getSubRadarDetails())
  }, [])

  useEffect(() => {
    setSkills(skillsData)
  }, [skillsData])

  const handleFilterSearch = (event) => {
    const filteredSkills = skills.filter(skill => skill.lmiName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
    skill.radarCategory?.toLowerCase().includes(event.target.value.toLowerCase()) ||
    skill.radarSubcategory?.toLowerCase().includes(event.target.value.toLowerCase())
    )
    return filteredSkills
  };

  const editSkills = (skillID) => {
    dispatch(getSkillsById({ id: skillID?._id }))
    setOpenModel(true)
  }

  useEffect(() => {
    const radarCId = formik.values.radarCategory;
    if(radarCId){
      dispatch(getSubRadarCat({id:radarCId}))
    }
    const updateRadarId = []
    if (radarCId) {
      const id = subRadarDetails && subRadarDetails.map((item, id) => {
        if (radarCId === item.radarCategoryId) {
          updateRadarId.push(item)
        }
        return item
      })
    }
    setUpdateSubRadarDetails(updateRadarId)
  }, [formik.values.radarCategory])

  useEffect(()=>{
    if(subRadarCategoryId && subRadarCategoryId.radarSubcategories?.length){
      setUpdateSubRadarDetails(subRadarCategoryId.radarSubcategories)
    }
  },[subRadarCategoryId])

  useEffect(() => {
    setSkillIdDetails(skillId)
  }, [skillId])


  useEffect(() => {
    const data = {
      id: skillIdDetails && skillIdDetails?._id,
      lmiId: skillIdDetails?.lmiId,
      lmiName: skillIdDetails?.lmiName,
      radarCategory: skillIdDetails?.radarCategoryId && skillIdDetails.radarCategoryId,
      radarSubCategory: skillIdDetails?.radarSubcategoryId && skillIdDetails.radarSubcategoryId,
    }
    setDetailsData(data)
  }, [skillIdDetails])


  useEffect(() => {
    if (detailsData.id !== undefined)
      formik.setValues(detailsData)
  }, [detailsData])

  const handleClose = () => {
    setOpenModel(false)
    formik.resetForm()
  };

  return (
    <>
      <Helmet>
        <title> Skills | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        <PopUp
          open={openModel}
          onClose={handleClose}
          title={"Edit Skills"}
          maxWidth="md"
        >
          <SkillsForm
            formik={formik && formik}
            Groups={radarDetails}
            Groups1={updateSubRadarDetails || subRadarDetails}
            setOpenModel={setOpenModel}
            openModel={openModel}
          />
        </PopUp>
        <Grid container justifyContent={'space-between'}>
          <Grid item lg={12}>
            <Typography variant="h4" gutterBottom mb={3}>
              Skills
            </Typography>
            <DataTable
              loading={loading}
              TableHead={SKILLS_TABLE_HEAD}
              TableData={skills}
              filterSearch={handleFilterSearch}
              searchLable={"Search..."}
              handleEdit={editSkills}
              renderCells={renderRadarCategoryCells}
              pagination
              disableDelete={"true"}
            />
          </Grid>
        </Grid>
      </Container>
    </>
  )
}

export default Skills






