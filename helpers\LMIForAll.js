const axios = require('axios');

class LMIForAll {
  static abc;

  async getSocCodes(onetCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/o-net/onet2soc?onetCodes=' + onetCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getSocDetails(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/soc/code/' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getEstimatePay(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/ashe/estimatePay?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getEstimateHours(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/ashe/estimateHours?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getPredictByQualification(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/wf/predict/breakdown/qualification?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getPredictByQualification(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/wf/predict/breakdown/qualification?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getPredictByRegion(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/wf/predict/breakdown/region?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getPredictByYear(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/wf/predict?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getPredictByWorktype(socCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/wf/predict/breakdown/status?soc=' + socCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getSkills(oNetCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/o-net/skills/' + oNetCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getAbilities(oNetCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/o-net/abilities/' + oNetCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getInterests(oNetCode) {
    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: 'http://api.lmiforall.org.uk/api/v1/o-net/interests/' + oNetCode,
      headers: {
        'Accept': 'application/json'
      }
    };

    const response = await axios.request(config);
    return response;
  }
}

module.exports = LMIForAll;