const mongoose = require('mongoose');
const { User, UserSchema } = require('./user');

/**
 * Training Dataset Schema
 * Stores metadata for training data files and generation information
 */
const trainingDatasetSchema = new mongoose.Schema({
  // Basic Information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  
  version: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Generation Metadata
  generatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: mongoose.model(User, UserSchema),
  },
  
  generatedAt: {
    type: Date,
    required: true,
    default: Date.now
  },
  
  generationConfig: {
    maxExamplesPerType: {
      type: Number,
      required: true
    },
    validationSplit: {
      type: Number,
      required: true,
      min: 0,
      max: 1
    },
    examplesPerRecord: {
      type: Number,
      required: true,
      min: 1
    },
    validateData: {
      type: Boolean,
      required: true,
      default: true
    }
  },
  
  // File Storage Information
  files: {
    trainingFile: {
      filename: {
        type: String,
        required: true
      },
      path: {
        type: String,
        required: true
      },
      size: {
        type: Number,
        required: true
      },
      examples: {
        type: Number,
        required: true
      },
      checksum: {
        type: String,
        required: true
      }
    },
    
    validationFile: {
      filename: {
        type: String,
        required: true
      },
      path: {
        type: String,
        required: true
      },
      size: {
        type: Number,
        required: true
      },
      examples: {
        type: Number,
        required: true
      },
      checksum: {
        type: String,
        required: true
      }
    },
    
    statisticsFile: {
      filename: String,
      path: String,
      size: Number
    },
    
    reportFile: {
      filename: String,
      path: String,
      size: Number
    }
  },
  
  // Quality Metrics
  statistics: {
    trainingExamples: {
      type: Number,
      required: true
    },
    validationExamples: {
      type: Number,
      required: true
    },
    totalRecordsProcessed: {
      type: Number,
      required: true
    },
    qualityScore: {
      type: Number,
      min: 0,
      max: 1
    },
    contentBreakdown: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    }
  },
  
  validation: {
    warnings: {
      type: Number,
      default: 0
    },
    errors: {
      type: Number,
      default: 0
    },
    warningRate: {
      type: Number,
      min: 0,
      max: 1
    },
    qualityScore: {
      type: Number,
      min: 0,
      max: 1
    }
  },
  
  // Status and Organization
  status: {
    type: String,
    enum: ['active', 'archived', 'failed'],
    default: 'active'
  },
}, { timestamps: true });

// Indexes for performance
trainingDatasetSchema.index({ status: 1, createdAt: -1 });
trainingDatasetSchema.index({ version: 1 });
trainingDatasetSchema.index({ 'statistics.qualityScore': -1 });

// Update the updatedAt field before saving
trainingDatasetSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

// Virtual for total examples
trainingDatasetSchema.virtual('totalExamples').get(function() {
  return this.statistics.trainingExamples + this.statistics.validationExamples;
});

// Virtual for file count
trainingDatasetSchema.virtual('fileCount').get(function() {
  let count = 0;
  if (this.files.trainingFile) count++;
  if (this.files.validationFile) count++;
  if (this.files.statisticsFile) count++;
  if (this.files.reportFile) count++;
  return count;
});

// Instance method to check if dataset is ready for training
trainingDatasetSchema.methods.isReadyForTraining = function() {
  return this.status === 'active' && 
         this.files.trainingFile && 
         this.files.validationFile &&
         this.statistics.trainingExamples > 0;
};

// Static method to find active datasets
trainingDatasetSchema.statics.findActive = function() {
  return this.find({ status: 'active' }).sort({ createdAt: -1 });
};

// Static method to find by quality score
trainingDatasetSchema.statics.findByQuality = function(minScore = 0.7) {
  return this.find({ 
    status: 'active',
    'statistics.qualityScore': { $gte: minScore }
  }).sort({ 'statistics.qualityScore': -1 });
};

const TrainingDataset = mongoose.model('TrainingDataset', trainingDatasetSchema);

module.exports = TrainingDataset;
