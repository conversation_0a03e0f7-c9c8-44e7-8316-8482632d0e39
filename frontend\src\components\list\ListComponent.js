import { Box, Divider, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import PropTypes from 'prop-types';
import AccountBalanceWalletOutlinedIcon from '@mui/icons-material/AccountBalanceWalletOutlined';
import UpdateOutlinedIcon from '@mui/icons-material/UpdateOutlined';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import RemoveCircleOutlineOutlinedIcon from '@mui/icons-material/RemoveCircleOutlineOutlined';
import AddCircleOutlineOutlinedIcon from '@mui/icons-material/AddCircleOutlineOutlined';
import { currencyFormat } from 'src/utils'
import { width } from '@mui/system';

const ListComponent = ({ data, addToCompare, compareCareersState, showCareerDetailsPopup, sortCareerBy }) => {
    const [sortableData, setSortableData] = useState(data)
    // highestSalary , mostCourses, shortestTransfer
    useEffect(() => {
        let newData =[]
         switch(sortCareerBy?.value) {
            case "highestSalary":
               newData = [...data]?.sort((a, b) => (a?.salary > b?.salary) ? -1 : 1)
                setSortableData(newData)
              break;
            case "mostCourses":
              newData = [...data]?.sort((a,b)=> (a?.coursesAvailable > b?.coursesAvailable) ? -1 : 1)
              setSortableData(newData)
              break;
            case "shortestTransfer":
              newData = [...data]?.sort((a,b)=> ((a.transferWindowMin + a.transferWindowMax ) < b.transferWindowMin + b.transferWindowMax) ? -1 : 1)
                setSortableData(newData)
              break;
            default:
                setSortableData(data)
          }
    }, [sortCareerBy, data])
    
    return (
        <Box className='list-view-wrapper'>
            {data?.length > 0 ? <>
                {sortableData?.map(career =>
                    <Box className='list-item'>
                        <Box sx={{ width: '32%' }} >
                            <Stack sx={{ ml: 2, py: 2 }} direction='column'>
                                <Typography variant='h6' color='primary.dark'>
                                    {career?.name}
                                </Typography>
                                <Stack
                                    direction='row'
                                    alignItems='center'
                                    gap={1}
                                    sx={{mt: '4px'}}
                                >
                                    <span className='color-dot' style={{ background: career?.color }} />
                                    <Typography color='primary.dark' sx={{fontStyle: 'italic'}} variant='subtitle2'>
                                        {career?.sectorName}
                                    </Typography>
                                </Stack>
                            </Stack>
                        </Box>
                        <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />

                        <Box
                            flex={1}
                            className="list-content"
                        >
                            <Stack direction='row' gap={3} justifyContent='space-between'>
                                <Stack direction='row' alignItems="center" gap={1}>
                                    <AccountBalanceWalletOutlinedIcon sx={{ fontSize: '18px', color: '#000c3b' }} />
                                    <Typography color='primary.dark' variant='medium' component='small'>
                                        {currencyFormat(career?.salary)}
                                    </Typography>
                                </Stack>

                                <Stack direction='row' alignItems="center" gap={1}>
                                    <UpdateOutlinedIcon sx={{ fontSize: '18px', color: '#000c3b' }} />
                                    <Typography color='primary.dark' variant='medium' >
                                        {career?.transferWindow}
                                    </Typography>
                                </Stack>
                                <Stack direction='row' alignItems="center" gap={1}>
                                    <SchoolOutlinedIcon sx={{ fontSize: '18px', color: '#000c3b' }} />
                                    <Typography color='primary.dark' variant='medium' >
                                        {career?.coursesAvailable}
                                    </Typography>
                                </Stack>
                            </Stack>
                        </Box>
                        {(career?.careerType !== 'CAREER_GOAL' && career?.careerType !== 'CURRENT_ROLE') ? <>
                            <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
                            <Box display='flex'
                                sx={{
                                    px: 1,
                                    pr: 2,
                                    fontWeight: 200,
                                    cursor: 'pointer',
                                    width: '14%',
                                    justifyContent: 'center !important',
                                    height: '100%'

                                }}
                                className="list-content"
                                onClick={() => addToCompare(career?.id)}
                                alignItems='center'>

                                {compareCareersState.find(currentCareer => currentCareer === career.id) ?
                                    // (<RemoveCircleOutlineOutlinedIcon sx={{  fontSize:'20px', color:'black', mx:1 }} /> ):
                                    <Stack direction='row' alignItems='center'>
                                        <RemoveCircleOutlineOutlinedIcon sx={{ mx: 1, fontSize: '20px', color: 'black' }} />
                                        <Typography sx={{ color: '#000c3b' }}>Remove</Typography>
                                    </Stack> :
                                    <Stack direction='row' alignItems='center'>
                                        <AddCircleOutlineOutlinedIcon sx={{ fontSize: '20px', mx: 1, color: 'black' }} />
                                        <Typography sx={{ color: '#000c3b' }}>Compare</Typography>
                                    </Stack>
                                }
                            </Box>
                            <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
                        </> : <>
                            <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
                            <Box display='flex'
                                sx={{
                                    px: 1,
                                    pr: 1,
                                    fontWeight: 200,
                                    cursor: 'pointer',
                                    width: '14%',
                                    justifyContent: 'center !important'

                                }}
                                alignItems='center'
                                className="list-content">

                                <Stack direction='row' alignItems='center'>
                                    {/* <RemoveCircleOutlineOutlinedIcon sx={{ mx: 1 }} /> */}
                                    {career?.careerType === 'CURRENT_ROLE' ? 
                                    <Typography sx={{ color: '#000c3b', fontSize: '0.8rem !important' }}>Current Role</Typography> :
                                    <Typography sx={{ color: '#000c3b', fontSize: '0.8rem !important' }}>Career Goal</Typography>
                                    }
                                </Stack>
                            </Box>
                            <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
                        </>
                        }
                        <Box className="list-content" onClick={() => showCareerDetailsPopup(career.id)} sx={{ color: '#000c3b', display: 'flex', alignItems: 'center', cursor: 'pointer', height: '100%' }}>
                            <ChevronRightIcon sx={{ mr: 1 }} fontSize='medium' />
                        </Box>
                    </Box>
                )
                }
            </> :
                <Box sx={{
                    display: 'flex', justifyContent: 'center', textAlign: 'center', alignItems: 'center', height: '100%'
                }}>
                    <Typography>
                        No Careers Found
                    </Typography>
                </Box>
            }
        </Box>
    )
}

export default ListComponent

ListComponent.propTypes = {
    data: PropTypes.array,
    addToCompare: PropTypes.func,
    showCareerDetailsPopup: PropTypes.func,
    compareCareersState: PropTypes.array,
    sortCareerBy: PropTypes.string,
}