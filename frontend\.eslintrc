{"root": true, "env": {"browser": true, "es2021": true}, "plugins": ["prettier"], "extends": ["airbnb", "airbnb/hooks", "prettier"], "parser": "@babel/eslint-parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "requireConfigFile": false, "ecmaFeatures": {"experimentalObjectRestSpread": true, "impliedStrict": true}, "babelOptions": {"presets": ["@babel/preset-react"]}}, "rules": {"no-alert": 0, "camelcase": 0, "no-console": 0, "no-param-reassign": 0, "naming-convention": 0, "default-param-last": 0, "no-underscore-dangle": 0, "no-use-before-define": 0, "no-restricted-exports": 0, "react/no-children-prop": 0, "react/forbid-prop-types": 0, "react/react-in-jsx-scope": 0, "jsx-a11y/anchor-is-valid": 0, "react/no-array-index-key": 0, "no-promise-executor-return": 0, "react/require-default-props": 0, "react/jsx-filename-extension": 0, "react/jsx-props-no-spreading": 0, "react/no-unescaped-entities": "off", "import/prefer-default-export": 0, "react/function-component-definition": 0, "jsx-a11y/label-has-associated-control": ["error", {"required": {"some": ["nesting", "id"]}}], "jsx-a11y/label-has-for": ["error", {"required": {"some": ["nesting", "id"]}}], "import/no-unresolved": [2, {"ignore": ["src"]}], "react/jsx-no-useless-fragment": [1, {"allowExpressions": true}], "prefer-destructuring": [1, {"object": true, "array": false}], "react/no-unstable-nested-components": [1, {"allowAsProps": true}], "no-unused-vars": [1, {"args": "none"}], "react/jsx-no-duplicate-props": [1, {"ignoreCase": false}]}}