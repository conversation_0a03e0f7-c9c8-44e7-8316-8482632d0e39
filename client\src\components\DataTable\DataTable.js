import { filter } from 'lodash';
import React, { useEffect, useState } from 'react';
// components
import {
    Backdrop,
    Box,
    Button,
    Card,
    CircularProgress,
    LinearProgress,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TablePagination,
    TableRow,
    Typography,
    styled
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { UserListHead, UserListToolbar } from '../../sections/@dashboard/user';
import ConfirmDialog from '../ConfirmDialog';
import Iconify from '../Iconify';
import Scrollbar from '../Scrollbar';
// @mui

// ----------------------------------------------------------------------
function descendingComparator(a, b, orderBy) {
    if (b[orderBy] < a[orderBy]) {
        return -1;
    }
    if (b[orderBy] > a[orderBy]) {
        return 1;
    }
    return 0;
}

function getComparator(order, orderBy) {
    return order === 'desc'
        ? (a, b) => descendingComparator(a, b, orderBy)
        : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
    const stabilizedThis = array.map((el, index) => [el, index]);
    stabilizedThis.sort((a, b) => {
        const order = comparator(a[0], b[0]);
        if (order !== 0) return order;
        return a[1] - b[1];
    });
    // if (query) {
    //     return filter(array, (_user) => {
    //         console.log("querry",query)
    //        return  _user.name.toLowerCase().indexOf(query.toLowerCase()) !== -1
    //     });
    // }
    return stabilizedThis.map((el) => el[0]);
}

const DataTable = ({ importText, handleImportClick, importcourse, disableSearch, exportCourse, exportData, exportCoursesFunc, csvDownloadRef, heading, disableActions, disableDelete, Users, Colleges, setUsers, selectedColleges, selectedGroups, setSelectedColleges, setSelectedGroups, loading, handleEdit, deleteDescription, deleteTitle, handleFilter, TableHead, TableData, filterSearch, buttonText, buttonHandler, renderCells, handleDelete, filter, pagination, rowsPerPageProp }) => {
    const [page, setPage] = useState(0);
    const [order, setOrder] = useState('asc');
    const [selected, setSelected] = useState([]);
    const [orderBy, setOrderBy] = useState('name');
    const [filterName, setFilterName] = useState('');
    const [rowsPerPage, setRowsPerPage] = useState(5);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [open, setOpen] = useState(false);
    const [selectedField, setSelectedField] = useState()
    const [openBackdrop, setOpenBackdrop] = useState(false);
    const handleCloseBackdrop = () => {
        setOpenBackdrop(false);
    };
    const handleOpenBackdrop = () => {
        setOpenBackdrop(true);
    };

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };
    const handleChangeRowsPerPage = (event) => {
        setPage(0);
        setRowsPerPage(parseInt(event.target.value, 10));
    };
    useEffect(() => {
        if (TableData && TableData.length) {
            const filterUsers = applySortFilter(TableData, getComparator(order, orderBy), filterName);
            setFilteredUsers(filterUsers)
        }
    }, [TableData, orderBy, order])
    useEffect(() => {
        setPage(0)
    }, [selectedColleges, selectedGroups])

    useEffect(() => {
        if (rowsPerPageProp) {
            setRowsPerPage(rowsPerPageProp);
        }
    }, [rowsPerPageProp]);

    const handleRequestSort = (event, property) => {
        const isAsc = orderBy === property && order === 'asc';
        setOrder(isAsc ? 'desc' : 'asc');
        setOrderBy(property);
    };

    const handleFilterByName = (event) => {
        setPage(0);
        setFilterName(event.target.value);
        if(filterSearch){
            setFilteredUsers(() => filterSearch(event))
        }
    };

    const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - TableData.length) : 0;

    const isNotFound = !filteredUsers.length && !!filterName;
    const closeDialog = () => {
        setOpen(false)
    }
    const openDialog = (Data) => {
        setSelectedField(Data)
        setOpen(true)
    }
    const handleDeleteField = (selectedField) => {
        setFilterName("")
        // setSelectedColleges([])
        // setSelectedGroups([])
        handleDelete(selectedField, handleOpenBackdrop, handleCloseBackdrop)
        setOpen(false)
    }
    return (
        <>
            <Backdrop
                sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
                open={openBackdrop}
                onClick={handleOpenBackdrop}
            >
                <CircularProgress color="inherit" />
            </Backdrop>
            <Card>
                {
                    !heading && disableSearch ?
                    null
                    :
                    <UserListToolbar
                    selectedColleges={selectedColleges}
                    selectedGroups={selectedGroups}
                    handleFilter={handleFilter}
                    buttonHandler={buttonHandler}
                    numSelected={selected.length}
                    filterName={filterName}
                    onFilterName={handleFilterByName}
                    searchLable={"Search..."}
                    disableSearch={disableSearch}
                    buttonText={buttonText}
                    filter={filter}
                    Colleges={Colleges}
                    setFilteredUsers={setFilteredUsers}
                    TableData={TableData}
                    setFilterName={setFilterName}
                    setSelectedColleges={setSelectedColleges}
                    setSelectedGroups={setSelectedGroups}
                    heading={heading}
                    importcourse={importcourse}
                    exportCourse={exportCourse}
                    importText={importText} 
                    handleImportClick={handleImportClick}
                    // exportData={exportData}
                    // exportCoursesFunc={exportCoursesFunc}
                />
                }
                <Scrollbar>
                    {!loading ? <TableContainer >
                        {TableData?.length > 0 ?
                            <> <ConfirmDialog
                                title={deleteTitle || "Delete this Field?"}
                                open={open}
                                setOpen={setOpen}
                                // handleClose={closeDialog}
                                selectedField={selectedField}
                                onConfirm={handleDeleteField}
                            >
                                <Typography variant='body1'>
                                    {deleteDescription || "Are You sure"}
                                </Typography>
                            </ConfirmDialog>
                                {loading ? null : <Table>
                                    <UserListHead
                                        noCheckBox
                                        order={order}
                                        orderBy={orderBy}
                                        headLabel={TableHead}
                                        rowCount={TableData.length}
                                        numSelected={selected.length}
                                        onRequestSort={handleRequestSort}
                                    />
                                    {pagination ?
                                        <TableBody>
                                            {filteredUsers.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((Data, index) => {
                                                const selectedUser = selected.indexOf(Data.name) !== -1;
                                                return (<>
                                                    {/* <ConfirmDialog
                                                        title={deleteTitle || "Delete this Field?"}
                                                        open={open}
                                                        setOpen={setOpen}
                                                        // handleClose={closeDialog}
                                                        selectedField={selectedField}
                                                        onConfirm={handleDeleteField}
                                                    >
                                                        <Typography variant='body1'>
                                                            {deleteDescription || "Are You sure"}
                                                        </Typography>
                                                    </ConfirmDialog> */}
                                                    <TableRow
                                                        hover
                                                        key={Data?.id || index}
                                                        tabIndex={-1}
                                                        role="checkbox"
                                                        selected={selectedUser}
                                                    >
                                                        {renderCells.map((cell, index) => (
                                                            <TableCell
                                                                key={index}
                                                                component="th"
                                                                scope="row"
                                                                padding="normal"
                                                            >
                                                                {typeof cell === "function" ? <>
                                                                    {cell(Data)}
                                                                </> :
                                                                    <Typography variant="p" >
                                                                        {typeof cell === "string" ? Data[cell] || (Data[cell] === 0 ? Data[cell] : "-" ) : `${Data[cell[0]] || ""} ${Data[cell[1]] || ""}`}
                                                                    </Typography>}
                                                            </TableCell>
                                                        ))}
                                                        {disableActions ? null : <TableCell align="right" >
                                                            <Button
                                                                onClick={() => {
                                                                    handleEdit(Data)
                                                                }}
                                                            >
                                                                <Iconify icon={'eva:edit-fill'} sx={{ mr: 1 }} />
                                                            </Button>

                                                            {Data.email !== '<EMAIL>' && !disableDelete &&
                                                                <Button sx={{ color: 'error.main' }} onClick={() => openDialog(Data)} >
                                                                    <Iconify icon={'eva:trash-2-outline'} sx={{ mr: 1 }} />
                                                                </Button>}
                                                        </TableCell>}
                                                    </TableRow>
                                                </>
                                                );
                                            })
                                            }
                                            {/* {emptyRows > 0 && (
                                                <TableRow style={{ height: 53 * emptyRows }}>
                                                    <TableCell colSpan={6} />
                                                </TableRow>
                                            )} */}
                                        </TableBody>
                                        :
                                        <TableBody>
                                            {filteredUsers.map((Data, index) => {
                                                const selectedUser = selected.indexOf(Data.name) !== -1;
                                                return (<>

                                                    <TableRow
                                                        hover
                                                        key={index}
                                                        tabIndex={-1}
                                                        role="checkbox"
                                                        selected={selectedUser}
                                                    >
                                                        {renderCells.map((cell, index) => (
                                                            <TableCell
                                                                key={index}
                                                                component="th"
                                                                scope="row"
                                                                padding="normal"
                                                            >
                                                                <Typography variant="p" >
                                                                    {typeof cell === "string" ? Data[cell] || (Data[cell] === 0 ? Data[cell] : "-" ): `${Data[cell[0]] || ""} ${Data[cell[1]] || ""}`}
                                                                </Typography>
                                                            </TableCell>
                                                        ))}
                                                        {disableActions ? null :
                                                            <TableCell align="right" >
                                                                <Button
                                                                    onClick={() => {
                                                                        handleEdit(Data)
                                                                    }}
                                                                >
                                                                    <Iconify icon={'eva:edit-fill'} sx={{ mr: 1 }} />
                                                                </Button>

                                                                {Data?.email !== '<EMAIL>' && !disableDelete &&
                                                                    <Button sx={{ color: 'error.main' }} onClick={() => openDialog(Data)} >
                                                                        <Iconify icon={'eva:trash-2-outline'} sx={{ mr: 1 }} />
                                                                    </Button>}
                                                            </TableCell>
                                                        }
                                                    </TableRow>
                                                </>
                                                );
                                            })
                                            }
                                            {emptyRows > 0 && (
                                                <TableRow style={{ height: 53 * emptyRows }}>
                                                    <TableCell colSpan={6} />
                                                </TableRow>
                                            )}
                                        </TableBody>
                                    }

                                    {isNotFound && (
                                        <TableBody>
                                            <TableRow>
                                                <TableCell align="center" colSpan={6} sx={{ py: 3 }}>
                                                    <Paper
                                                        sx={{
                                                            textAlign: 'center',
                                                        }}
                                                    >
                                                        <Typography variant="h6" paragraph>
                                                            Not found
                                                        </Typography>

                                                        <Typography variant="body2">
                                                            No results found for &nbsp;
                                                            <strong>&quot;{filterName}&quot;</strong>.
                                                            <br /> Try checking for typos or using complete words.
                                                        </Typography>
                                                    </Paper>
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    )}
                                </Table>} </> :
                            <Typography
                                textAlign='center'
                                m={2}
                                variant='body1'
                            >
                                No Data Found
                            </Typography>}

                    </TableContainer> :
                        <Box sx={{ width: '100%' }}>
                            <LinearProgress />
                        </Box>}
                </Scrollbar>
                {pagination && TableData?.length > 0 ?
                    <TablePagination
                        rowsPerPageOptions={[5, 10, 25]}
                        component="div"
                        count={filteredUsers.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={handleChangePage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                    />
                    : ''}
            </Card>
        </>
    )
}

export default DataTable;



