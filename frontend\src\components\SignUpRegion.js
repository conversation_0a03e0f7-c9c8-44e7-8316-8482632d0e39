import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { <PERSON>, Button, Container, Stack, Typography } from '@mui/material';
import { get, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { setAlert } from 'src/layouts/main/MainLayoutSlice';
import { getRegionBySlug, userRegister } from 'src/pages/CareerHistory/CareerHistorySlice';
import { base_route_url, EMAIL_REGEXP } from 'src/utils';
import * as Yup from 'yup';
import AuthWrapper from './AuthWrapper';
import FormTextField from './FormTextField';
import TextFIeldComponent from './TextField/TextFieldComponent';

const SignUpRegion = () => {
  const validationSchema = Yup.object().shape({
    firstName: Yup.string()
      .required('First Name is required')
      .min(2, 'First Name must be at least 2 characters'),
    lastName: Yup.string().required('Last Name is required'),

    email: Yup.string().trim().required('Email is required').matches(EMAIL_REGEXP, 'Email format is not valid'),

    postcode: Yup.string()
      .required('Postcode is required')
      .matches(/^[0-9]{5}$/, 'Postcode must be 5 digits'), // customize based on your country's format

    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters')
      .max(16, 'Password must not exceed 16 characters')
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,16}$/,
        'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
      ),
  });
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    getValues,
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      postcode: '',
      password: '',
    },
  });
  const [isPasswordHidden, setIsPasswordHidden] = useState(true);

  const dispatch = useDispatch();
  const regions = useSelector((state) => state.careerHistory);
  const [regionData, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const params = useParams();
  const { cg_name, rg_name } = useParams();
  const handleSignInClicked = () => {
    if (isRegion) {
      navigate(`${base_route_url}region/${params?.rg_name}/sign-in`);
    } else {
      navigate(`${base_route_url}${params?.cg_name}/sign-in`);
    }
  };
  const onSubmit = (values) => {
    setIsLoading(true);
    dispatch(userRegister(values))
      .then((response) => {
        if (get(response, 'payload.data.success')) {
          dispatch(
            setAlert({
              open: true,
              isSuccess: true,
              type: 'success',
              msg: get(response, 'payload.data.message')
                ? get(response, 'payload.data.message')
                : 'User registered successfully',
            })
          );
          handleSignInClicked();
        } else {
          const errorMessage =
            get(response, 'payload.response.data.message') ||
            'Something Went Wrong, Please try again later';

          dispatch(
            setAlert({
              open: true,
              msg: errorMessage,
            })
          );
        }
      })
      .catch((error) => console.log('error----', error))
      .finally(() => setIsLoading(false));
  };
  const navigate = useNavigate();
  const isRegion = !!params?.rg_name;

  useEffect(() => {
    setLoading(true);
    dispatch(getRegionBySlug(rg_name));
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(regionData)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, regionData]);

  const buttonColor = regionData?.button?.bgColor;
  const buttonFontColor = regionData?.button?.color;
  const logo = regionData?.logo || '';
  const partnerLogos = regionData?.partnerLogos || [];
  const primaryColor = regionData?.primaryColor || '';
  const fontColor = regionData?.fontColor || '';
  const secondaryColor = regionData?.secondaryColor || '';
  const bgImage = regionData?.bgImage || '';

  return (
    <>
      <AuthWrapper title="Sign-Up">
        <Box
          sx={{
            backgroundImage: `url(${bgImage})`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundAttachment: 'fixed',
            pb: 5,
            mt: 0,
          }}
          className="page-content-wrapper"
        >
          <Container>
            <Box className="content">
              {/* <ProgressStepper
                steps={['Career History', 'Skills', 'Create Account (optional)', 'Results']}
                activeStep={2} // Change to 1 to highlight "Results"
              /> */}
              <Box mt={0} display="flex" flexDirection="column" alignItems="center">
                <Typography sx={{ mt: 2, fontSize: '28px !important', color: fontColor }}>
                  <b>Create your free THINK</b>SKILLS <b>account</b>
                </Typography>
                <Typography
                  sx={{ mb: 2, fontSize: '14px !important', color: fontColor }}
                  component="p"
                >
                  Access results immediately next time you log in
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    backgroundColor: 'white',
                    py: 4,
                    borderRadius: '8px',
                    width: {
                      xs: '100%', // full width on extra-small screens
                      sm: 500, // 500px on small screens
                      md: 600, // 600px on medium screens
                      lg: 700, // 700px on large screens
                      xl: 800, // 800px on extra-large screens
                    },
                  }}
                >
                  <form onSubmit={handleSubmit(onSubmit)}>
                    <Box className="sign-up-wrapper">
                      <FormTextField
                        title="First Name"
                        isRequired
                        name="name"
                        placeholder="Enter First Name"
                        {...register('firstName')}
                        control={control}
                        className="input-text-field"
                      />
                      <FormTextField
                        title="Last Name"
                        isRequired
                        name="name"
                        placeholder="Enter Last Name"
                        {...register('lastName')}
                        control={control}
                        className="input-text-field"
                      />
                      <FormTextField
                        title="Email Address"
                        isRequired
                        name="email"
                        placeholder="Enter email"
                        {...register('email')}
                        control={control}
                        className="input-text-field"
                      />
                      <FormTextField
                        title="Postcode"
                        isRequired
                        name="postcode"
                        placeholder="Enter Postcode"
                        {...register('postcode')}
                        control={control}
                        className="input-text-field postcode"
                      />

                      <Box className="field-wrapper">
                        <Typography mb={1} variant="subtitle2">
                          Password*
                        </Typography>
                        <Stack direction="row" alignItems="center" spacing={2}>
                          <TextFIeldComponent
                            name="password"
                            placeholder="Enter password"
                            control={control}
                            {...register('password')}
                            className="input-text-field postcode"
                            type={isPasswordHidden ? 'password' : 'text'}
                          />
                          <Button
                            sx={{
                              borderRadius: '8px',
                              color: buttonFontColor || 'white',
                            }}
                            onClick={() => setIsPasswordHidden((prevValue) => !prevValue)}
                          >
                            <Typography mb={1} variant="subtitle2">
                              {!isPasswordHidden ? 'Hide' : 'Show'} Password
                            </Typography>
                          </Button>
                        </Stack>
                        <Typography my={1} variant="caption" textAlign="left">
                          Between 8-16 characters, with at least one uppercase & lowercase letter,
                          one number and one symbol
                        </Typography>
                        <LoadingButton
                          sx={{
                            backgroundColor: (theme) =>
                              `${buttonColor || theme.palette.primary.main} !important`,
                            borderRadius: '8px',
                            color: buttonFontColor || 'white',
                            p: 2,
                            mb: 2,
                          }}
                          loading={isLoading}
                          fullWidth
                          type="submit"
                        >
                          Create Account
                        </LoadingButton>
                        <Button
                          sx={{
                            borderRadius: '8px',
                            p: 2,
                            borderColor: (theme) =>
                              `${buttonColor || theme.palette.primary.main} !important`,
                            color: buttonFontColor || 'white',
                          }}
                          fullWidth
                          variant="outlined"
                          onClick={handleSignInClicked}
                        // onClick={(e) => showCareerDetailsPopup(item.id, e)}
                        >
                          <Stack rowGap={0.2} direction="column">
                            <Typography>Sign in</Typography>
                            <Typography>(I already have an account)</Typography>
                          </Stack>
                        </Button>
                      </Box>
                    </Box>
                  </form>
                </Box>
              </Box>
            </Box>
          </Container>
        </Box>
      </AuthWrapper>
    </>
  );
};

export default SignUpRegion;
