// component
import { APP_ROUTER_BASE_URL } from '../../../utils';
import SvgColor from '../../../components/Svg-color';

// ----------------------------------------------------------------------

export const icon = (name) => <SvgColor src={`/assets/icons/navbar/${name}.svg`} sx={{ width: 1, height: 1 }} />;

const navConfig = [
  {
    title: 'dashboard',
    isSuper:'true',
    roles: ["1", "2", "3", "4"],
    path: `${APP_ROUTER_BASE_URL}dashboard/app`,
    icon: icon('ic_analytics'),
  },
  {
    title: 'users',
    isSuper:'true',
    roles: ["1","2","3"],
    path: `${APP_ROUTER_BASE_URL}dashboard/user`,
    icon: icon('ic_user'),
  },
  {
    title: 'College Groups',
    isSuper:'true',
    roles: ["1","2"],
    path: `${APP_ROUTER_BASE_URL}dashboard/collegegroups`,
    icon: icon('ic_college_group'),
  },
  {
    title: 'Colleges',
    isSuper:'true',
    roles: ["1","2","3"],
    path: `${APP_ROUTER_BASE_URL}dashboard/colleges`,
    icon: icon('ic_college'),
  },
  {
    title: 'Regions',
    isSuper:'true',
    roles: ["1","2","3","4"],
    path: `${APP_ROUTER_BASE_URL}dashboard/regions`,
    icon: icon('ic_sectors'),
  },
  {
    title: 'Campuses',
    isSuper:'true',
    roles: ["1","2","3","4"],
    path: `${APP_ROUTER_BASE_URL}dashboard/campuses`,
    icon: icon('ic_campus'),
  },
  {
    title: 'Careers',
    path: `${APP_ROUTER_BASE_URL}dashboard/careers`,
    icon: icon('ic_career'),
    roles: ["1"],
  },
  {
    title: 'careers (Old)',
    path: `${APP_ROUTER_BASE_URL}dashboard/old-careers`,
    icon: icon('ic_career'),
    roles: ["1"],
  },
  {
    title: 'courses',
    path: `${APP_ROUTER_BASE_URL}dashboard/courses`,
    icon: icon('ic_courses'),
    roles: ["1","2","3","4"],
  },

  {
    title: 'skills',
    path: `${APP_ROUTER_BASE_URL}dashboard/skills`,
    icon: icon('ic_skills'),
    roles: ["1"],
  },
  {
    title: 'abilities',
    path: `${APP_ROUTER_BASE_URL}dashboard/abilities`,
    icon: icon('ic_ability'),
    roles: ["1"],
  },
  {
    title: 'Sectors',
    path: `${APP_ROUTER_BASE_URL}dashboard/sectors`,
    icon: icon('ic_sectors'),
    roles: ["1"],
  },
  // {
  //   title: 'Sub Sectors',
  //   path: `${APP_ROUTER_BASE_URL}dashboard/subsectors`,
  //   icon: icon('ic_subSector'),
  //   roles: ["1"],
  // },
  {
    title: 'Radar Categories',
    path: `${APP_ROUTER_BASE_URL}dashboard/radarcategory`,
    icon: icon('ic_radarCategories'),
    roles: ["1"],
  },
  {
    title: 'Setting',
    path: `${APP_ROUTER_BASE_URL}dashboard/setting`,
    icon: icon('ic_setting'),
    roles: ["1"],
  },
  // {
  //   title: 'Radar Sub Categories',
  //   path: `${APP_ROUTER_BASE_URL}dashboard/radarsubcategory`,
  //   icon: icon('ic_radarSubCategory'),
  //   roles: ["1"],
  // },
];

export default navConfig;
