const { default: mongoose } = require("mongoose");
const { RadarCategory } = require("../models/radarCategory");
const { RadarSubcategory } = require("../models/radarSubcategory");
const { getAddedBy, getEditedBy } = require('../tools/database');
const commonHelper = require("../helpers/commonHelper");
const {messageResponse} = require("../helpers/commonHelper");
const { LMISkillAbility } = require("../models/lmiSkillAbility");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS, REMOVE_ERROR_ENGAGED } = require("../config/messages");

const validate = async (req, action) => {
  try {
    if (action == 'edit' && !mongoose.isValidObjectId(req.body.id)) return messageResponse(REQUIRED, "ID", false, 400, null);

    if (!req.body.name) return messageResponse(INVALID_MISSING, "Name", false, 400, null);

    let query = action == 'edit'
        ? { $and: [{ name: { $eq: req.body.name } }, { _id: { $ne: new mongoose.Types.ObjectId(req.body.id) } }] }
        : { name: req.body.name };

    let existingEntry = await RadarCategory.findOne(query);
    if (existingEntry != null) return messageResponse(DUPLICATE, "", false, 400, null);

    return { success: true };
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addUpdateEntry = async (req, res, action) => {
  try {
    let validateResult = await validate(req, action);

    if (!validateResult.success) {

      let statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];

      return res.status(statusCode).json(validateResult);
    }

    if (action == 'add') {
      let addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      let newRadarCat = await RadarCategory.create(req.body)

      if(!newRadarCat) return messageResponse(ADD_ERROR, "Radar Category", false, 400, null, res)

      res.status(200).json({ success: true, id: newRadarCat._id })
    }
    else {
      req.body.editedBy = getEditedBy(req, 'edit');

      let updateRadarCat = await RadarCategory.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updateRadarCat) return messageResponse(EXIST_PERMISSION, "Radar Category", false, 404, null, res) 

      return messageResponse(UPDATE_SUCCESS, "Radar Category", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const getEntry = async (req, res) => {
  try {
    const pipeline = [
      {
        $lookup: {
          from: "radarSubcategories",
          localField: "_id",
          foreignField: "radarCategoryId",
          as: "radarSubcategories"
        }
      },
      { $sort: {'addedBy.date': -1} }
    ]
    let existingRadarCat = await RadarCategory.aggregate(pipeline);
    if(!existingRadarCat.length) return messageResponse(NOT_FOUND, "Radar Category", false, 404, null, res) 
    return messageResponse(null, "", true, 200, existingRadarCat, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const radarCategory = await RadarCategory.findById(req.query.id)

    if (!radarCategory) return messageResponse(NOT_FOUND, "Radar Category", false, 404, null, res) 

    return messageResponse(null, "", true, 200, radarCategory, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const getSubRadarCat = async (req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res) 
    }

    const pipeline = [
      { $match: { _id: new mongoose.Types.ObjectId(req.query.id) } },
      {
        $lookup: {
          from: "radarSubcategories",
          localField: "_id",
          foreignField: "radarCategoryId",
          as: "radarSubcategories"
        }
      }
    ]

    let radarCategory = await RadarCategory.aggregate(pipeline);

    if (!radarCategory || !radarCategory.length) return messageResponse(EXIST_PERMISSION, "Radar Category", false, 404, null, res);

    return messageResponse(null, "", true, 200, radarCategory[0], res);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const removeEntry = async (req, res)  => {
  try {
    if (!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    let RadarSubCat = await RadarSubcategory.findOne({ radarCategoryId: req.body.id });
    if (RadarSubCat) return messageResponse(REMOVE_ERROR_ENGAGED, "Radar Category", false, 400, null, res);

    let lmiSkillAbility = await LMISkillAbility.findOne({ radarCategoryId: req.body.id });
    if (lmiSkillAbility) return messageResponse(REMOVE_ERROR_ENGAGED, "Radar Category", false, 400, null, res);

    let RadarCat = await RadarCategory.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

    if(!RadarCat) return messageResponse(EXIST_PERMISSION, "Radar Category", false, 404, null, res);

    return messageResponse(REMOVE_SUCCESS, "Radar Category", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const updateEntry = async (req, res, next) => await addUpdateEntry(req, res, 'edit');

const addEntry = async (req, res, next) => await addUpdateEntry(req, res, 'add');

module.exports.addEntry = addEntry;
module.exports.updateEntry = updateEntry;
module.exports.removeEntry = removeEntry;
module.exports.getSubRadarCat = getSubRadarCat;
module.exports.getEntry = getEntry;
module.exports.getByID = getByID;
