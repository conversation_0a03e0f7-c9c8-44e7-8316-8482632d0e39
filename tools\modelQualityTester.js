const mongoose = require('mongoose');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import services for direct testing
const ChatGPTService = require('../services/chatGPTService');
const { College } = require('../models/college');

/**
 * Model Quality Testing Suite
 * Tests AI model response quality, accuracy, and educational relevance
 */
class ModelQualityTester {
  constructor() {
    this.apiBaseURL = process.env.API_BASE_URL || 'http://localhost:3000';
    this.chatGPTService = new ChatGPTService();
    this.testResults = {
      qualityTests: {},
      accuracyTests: {},
      consistencyTests: {},
      educationalRelevanceTests: {},
      startTime: null,
      endTime: null
    };

    // Test scenarios for quality evaluation
    this.testScenarios = this.initializeTestScenarios();
  }

  /**
   * Run complete model quality test suite
   */
  async runModelQualityTests(options = {}) {
    const {
      includeQualityTests = true,
      includeAccuracyTests = true,
      includeConsistencyTests = true,
      includeEducationalTests = true,
      testIterations = 3
    } = options;

    console.log('🧠 Starting Model Quality Testing Suite');
    console.log('=======================================');
    console.log(`📊 Configuration:`);
    console.log(`   Quality Tests: ${includeQualityTests}`);
    console.log(`   Accuracy Tests: ${includeAccuracyTests}`);
    console.log(`   Consistency Tests: ${includeConsistencyTests}`);
    console.log(`   Educational Tests: ${includeEducationalTests}`);
    console.log(`   Test Iterations: ${testIterations}`);

    this.testResults.startTime = new Date();

    try {
      // Connect to database
      await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
      console.log('✅ Connected to MongoDB');

      // Get test college for context
      const testCollege = await College.findOne({}).lean();
      if (!testCollege) {
        throw new Error('No colleges found for testing');
      }

      // Test 1: Response Quality Tests
      if (includeQualityTests) {
        await this.runResponseQualityTests(testCollege, testIterations);
      }

      // Test 2: Accuracy Tests
      if (includeAccuracyTests) {
        await this.runAccuracyTests(testCollege, testIterations);
      }

      // Test 3: Consistency Tests
      if (includeConsistencyTests) {
        await this.runConsistencyTests(testCollege, testIterations);
      }

      // Test 4: Educational Relevance Tests
      if (includeEducationalTests) {
        await this.runEducationalRelevanceTests(testCollege, testIterations);
      }

      // Generate quality report
      await this.generateQualityReport();

      this.testResults.endTime = new Date();
      const duration = this.testResults.endTime - this.testResults.startTime;

      console.log('\n🎉 Model Quality Testing Completed!');
      console.log('===================================');
      console.log(`⏱️ Total Duration: ${Math.round(duration / 1000)}s`);

      return this.testResults;

    } catch (error) {
      console.error('❌ Model quality testing failed:', error);
      throw error;
    } finally {
      await mongoose.disconnect();
      console.log('✅ Disconnected from MongoDB');
    }
  }

  /**
   * Test response quality metrics
   */
  async runResponseQualityTests(testCollege, iterations) {
    console.log('\n📝 Running Response Quality Tests...');
    
    const qualityResults = {};

    for (const scenario of this.testScenarios.quality) {
      console.log(`   Testing: ${scenario.name}`);
      const responses = [];
      const scores = [];

      for (let i = 0; i < iterations; i++) {
        try {
          const messages = [
            { role: 'system', content: `You are a helpful assistant for ${testCollege.name}.` },
            { role: 'user', content: scenario.prompt }
          ];

          const response = await this.chatGPTService.generateResponse(messages, {
            maxTokens: 200,
            temperature: 0.7,
            collegeContext: testCollege
          });

          if (response.content) {
            responses.push(response.content);
            const qualityScore = this.evaluateResponseQuality(response.content, scenario.expectedElements);
            scores.push(qualityScore);
          }

        } catch (error) {
          console.warn(`⚠️ Response generation failed for ${scenario.name}:`, error.message);
        }

        await new Promise(resolve => setTimeout(resolve, 500));
      }

      if (scores.length > 0) {
        qualityResults[scenario.name] = {
          averageScore: Math.round((scores.reduce((a, b) => a + b, 0) / scores.length) * 100) / 100,
          minScore: Math.min(...scores),
          maxScore: Math.max(...scores),
          responses: responses,
          successfulResponses: responses.length,
          totalAttempts: iterations
        };

        console.log(`   ✅ ${scenario.name}: ${qualityResults[scenario.name].averageScore}/5.0 avg quality`);
      } else {
        console.log(`   ❌ ${scenario.name}: No successful responses`);
      }
    }

    this.testResults.qualityTests = qualityResults;
  }

  /**
   * Test response accuracy
   */
  async runAccuracyTests(testCollege, iterations) {
    console.log('\n🎯 Running Accuracy Tests...');
    
    const accuracyResults = {};

    for (const scenario of this.testScenarios.accuracy) {
      console.log(`   Testing: ${scenario.name}`);
      const responses = [];
      const accuracyScores = [];

      for (let i = 0; i < iterations; i++) {
        try {
          const messages = [
            { role: 'system', content: `You are a helpful assistant for ${testCollege.name}. Provide accurate information.` },
            { role: 'user', content: scenario.prompt }
          ];

          const response = await this.chatGPTService.generateResponse(messages, {
            maxTokens: 150,
            temperature: 0.3, // Lower temperature for more consistent responses
            collegeContext: testCollege
          });

          if (response.content) {
            responses.push(response.content);
            const accuracyScore = this.evaluateResponseAccuracy(response.content, scenario.expectedContent);
            accuracyScores.push(accuracyScore);
          }

        } catch (error) {
          console.warn(`⚠️ Response generation failed for ${scenario.name}:`, error.message);
        }

        await new Promise(resolve => setTimeout(resolve, 500));
      }

      if (accuracyScores.length > 0) {
        accuracyResults[scenario.name] = {
          averageAccuracy: Math.round((accuracyScores.reduce((a, b) => a + b, 0) / accuracyScores.length) * 100) / 100,
          minAccuracy: Math.min(...accuracyScores),
          maxAccuracy: Math.max(...accuracyScores),
          responses: responses,
          successfulResponses: responses.length,
          totalAttempts: iterations
        };

        console.log(`   ✅ ${scenario.name}: ${(accuracyResults[scenario.name].averageAccuracy * 100).toFixed(1)}% avg accuracy`);
      } else {
        console.log(`   ❌ ${scenario.name}: No successful responses`);
      }
    }

    this.testResults.accuracyTests = accuracyResults;
  }

  /**
   * Test response consistency
   */
  async runConsistencyTests(testCollege, iterations) {
    console.log('\n🔄 Running Consistency Tests...');
    
    const consistencyResults = {};

    for (const scenario of this.testScenarios.consistency) {
      console.log(`   Testing: ${scenario.name}`);
      const responses = [];

      for (let i = 0; i < iterations; i++) {
        try {
          const messages = [
            { role: 'system', content: `You are a helpful assistant for ${testCollege.name}.` },
            { role: 'user', content: scenario.prompt }
          ];

          const response = await this.chatGPTService.generateResponse(messages, {
            maxTokens: 150,
            temperature: 0.5,
            collegeContext: testCollege
          });

          if (response.content) {
            responses.push(response.content);
          }

        } catch (error) {
          console.warn(`⚠️ Response generation failed for ${scenario.name}:`, error.message);
        }

        await new Promise(resolve => setTimeout(resolve, 500));
      }

      if (responses.length > 1) {
        const consistencyScore = this.evaluateResponseConsistency(responses);
        consistencyResults[scenario.name] = {
          consistencyScore: Math.round(consistencyScore * 100) / 100,
          responses: responses,
          responseCount: responses.length,
          totalAttempts: iterations
        };

        console.log(`   ✅ ${scenario.name}: ${(consistencyResults[scenario.name].consistencyScore * 100).toFixed(1)}% consistency`);
      } else {
        console.log(`   ❌ ${scenario.name}: Insufficient responses for consistency testing`);
      }
    }

    this.testResults.consistencyTests = consistencyResults;
  }

  /**
   * Test educational relevance
   */
  async runEducationalRelevanceTests(testCollege, iterations) {
    console.log('\n🎓 Running Educational Relevance Tests...');
    
    const relevanceResults = {};

    for (const scenario of this.testScenarios.educational) {
      console.log(`   Testing: ${scenario.name}`);
      const responses = [];
      const relevanceScores = [];

      for (let i = 0; i < iterations; i++) {
        try {
          const messages = [
            { role: 'system', content: `You are a helpful educational assistant for ${testCollege.name}.` },
            { role: 'user', content: scenario.prompt }
          ];

          const response = await this.chatGPTService.generateResponse(messages, {
            maxTokens: 200,
            temperature: 0.7,
            collegeContext: testCollege
          });

          if (response.content) {
            responses.push(response.content);
            const relevanceScore = this.evaluateEducationalRelevance(response.content, scenario.educationalKeywords);
            relevanceScores.push(relevanceScore);
          }

        } catch (error) {
          console.warn(`⚠️ Response generation failed for ${scenario.name}:`, error.message);
        }

        await new Promise(resolve => setTimeout(resolve, 500));
      }

      if (relevanceScores.length > 0) {
        relevanceResults[scenario.name] = {
          averageRelevance: Math.round((relevanceScores.reduce((a, b) => a + b, 0) / relevanceScores.length) * 100) / 100,
          minRelevance: Math.min(...relevanceScores),
          maxRelevance: Math.max(...relevanceScores),
          responses: responses,
          successfulResponses: responses.length,
          totalAttempts: iterations
        };

        console.log(`   ✅ ${scenario.name}: ${(relevanceResults[scenario.name].averageRelevance * 100).toFixed(1)}% educational relevance`);
      } else {
        console.log(`   ❌ ${scenario.name}: No successful responses`);
      }
    }

    this.testResults.educationalRelevanceTests = relevanceResults;
  }

  /**
   * Evaluate response quality
   */
  evaluateResponseQuality(response, expectedElements) {
    let score = 0;
    const maxScore = 5;

    // Check length (not too short, not too long)
    if (response.length > 50 && response.length < 500) score += 1;

    // Check for complete sentences
    if (response.includes('.') || response.includes('!') || response.includes('?')) score += 1;

    // Check for expected elements
    if (expectedElements) {
      const foundElements = expectedElements.filter(element => 
        response.toLowerCase().includes(element.toLowerCase())
      );
      score += (foundElements.length / expectedElements.length) * 2;
    }

    // Check for helpfulness indicators
    const helpfulPhrases = ['can help', 'information', 'available', 'contact', 'visit'];
    if (helpfulPhrases.some(phrase => response.toLowerCase().includes(phrase))) score += 1;

    return Math.min(score, maxScore);
  }

  /**
   * Evaluate response accuracy
   */
  evaluateResponseAccuracy(response, expectedContent) {
    if (!expectedContent || expectedContent.length === 0) return 0.5;

    let matches = 0;
    for (const content of expectedContent) {
      if (response.toLowerCase().includes(content.toLowerCase())) {
        matches++;
      }
    }

    return matches / expectedContent.length;
  }

  /**
   * Evaluate response consistency
   */
  evaluateResponseConsistency(responses) {
    if (responses.length < 2) return 0;

    // Simple consistency check based on common words and phrases
    const allWords = responses.map(r => r.toLowerCase().split(/\s+/));
    const commonWords = allWords[0].filter(word => 
      allWords.every(wordList => wordList.includes(word))
    );

    const averageLength = responses.reduce((sum, r) => sum + r.length, 0) / responses.length;
    const lengthVariance = responses.reduce((sum, r) => sum + Math.abs(r.length - averageLength), 0) / responses.length;

    // Score based on common words and length consistency
    const wordConsistency = commonWords.length / Math.max(...allWords.map(w => w.length));
    const lengthConsistency = 1 - (lengthVariance / averageLength);

    return (wordConsistency + lengthConsistency) / 2;
  }

  /**
   * Evaluate educational relevance
   */
  evaluateEducationalRelevance(response, educationalKeywords) {
    if (!educationalKeywords || educationalKeywords.length === 0) return 0.5;

    const lowerResponse = response.toLowerCase();
    let keywordMatches = 0;

    for (const keyword of educationalKeywords) {
      if (lowerResponse.includes(keyword.toLowerCase())) {
        keywordMatches++;
      }
    }

    return keywordMatches / educationalKeywords.length;
  }

  /**
   * Initialize test scenarios
   */
  initializeTestScenarios() {
    return {
      quality: [
        {
          name: 'Course Information Request',
          prompt: 'What computer science courses do you offer?',
          expectedElements: ['computer science', 'courses', 'programs', 'available']
        },
        {
          name: 'Admission Requirements',
          prompt: 'What are the admission requirements?',
          expectedElements: ['admission', 'requirements', 'apply', 'contact']
        },
        {
          name: 'Campus Information',
          prompt: 'Tell me about your campus locations.',
          expectedElements: ['campus', 'location', 'address', 'facilities']
        }
      ],
      accuracy: [
        {
          name: 'Contact Information',
          prompt: 'How can I contact the college?',
          expectedContent: ['contact', 'phone', 'email', 'website']
        },
        {
          name: 'General Information',
          prompt: 'Tell me about this college.',
          expectedContent: ['college', 'education', 'programs', 'students']
        }
      ],
      consistency: [
        {
          name: 'Greeting Response',
          prompt: 'Hello, can you help me?'
        },
        {
          name: 'Course Inquiry',
          prompt: 'What programs do you offer?'
        }
      ],
      educational: [
        {
          name: 'Career Guidance',
          prompt: 'What career opportunities are available after graduation?',
          educationalKeywords: ['career', 'job', 'employment', 'opportunities', 'graduation']
        },
        {
          name: 'Academic Support',
          prompt: 'What academic support services do you provide?',
          educationalKeywords: ['academic', 'support', 'tutoring', 'learning', 'assistance']
        }
      ]
    };
  }

  /**
   * Generate quality report
   */
  async generateQualityReport() {
    console.log('\n📋 Generating Model Quality Report...');
    
    const report = {
      summary: {
        testDate: new Date(),
        totalDuration: this.testResults.endTime - this.testResults.startTime,
        overallQualityScore: this.calculateOverallQualityScore()
      },
      qualityTests: this.testResults.qualityTests,
      accuracyTests: this.testResults.accuracyTests,
      consistencyTests: this.testResults.consistencyTests,
      educationalRelevanceTests: this.testResults.educationalRelevanceTests,
      recommendations: this.generateQualityRecommendations()
    };
    
    // Save report to file
    const reportDir = path.join(__dirname, '../test-reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `model-quality-test-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
    
    console.log(`📋 Model quality report saved: ${reportFile}`);
    return report;
  }

  /**
   * Calculate overall quality score
   */
  calculateOverallQualityScore() {
    const scores = [];
    
    // Quality test scores
    Object.values(this.testResults.qualityTests).forEach(test => {
      if (test.averageScore) scores.push(test.averageScore / 5); // Normalize to 0-1
    });
    
    // Accuracy test scores
    Object.values(this.testResults.accuracyTests).forEach(test => {
      if (test.averageAccuracy) scores.push(test.averageAccuracy);
    });
    
    // Consistency test scores
    Object.values(this.testResults.consistencyTests).forEach(test => {
      if (test.consistencyScore) scores.push(test.consistencyScore);
    });
    
    // Educational relevance scores
    Object.values(this.testResults.educationalRelevanceTests).forEach(test => {
      if (test.averageRelevance) scores.push(test.averageRelevance);
    });
    
    return scores.length > 0 ? scores.reduce((a, b) => a + b, 0) / scores.length : 0;
  }

  /**
   * Generate quality recommendations
   */
  generateQualityRecommendations() {
    const recommendations = [];
    const overallScore = this.calculateOverallQualityScore();
    
    if (overallScore < 0.7) {
      recommendations.push('Overall model quality is below 70% - consider retraining with more data');
    }
    
    // Check specific test results
    Object.entries(this.testResults.qualityTests).forEach(([test, results]) => {
      if (results.averageScore < 3.5) {
        recommendations.push(`${test} quality is low (${results.averageScore}/5.0) - review training data for this scenario`);
      }
    });
    
    Object.entries(this.testResults.accuracyTests).forEach(([test, results]) => {
      if (results.averageAccuracy < 0.6) {
        recommendations.push(`${test} accuracy is low (${(results.averageAccuracy * 100).toFixed(1)}%) - improve training data accuracy`);
      }
    });
    
    if (recommendations.length === 0) {
      recommendations.push('Model quality is within acceptable limits for production use');
    }
    
    return recommendations;
  }
}

// Command line interface
const runModelQualityTests = async () => {
  const args = process.argv.slice(2);
  const tester = new ModelQualityTester();

  const options = {
    testIterations: parseInt(args[0]) || 3,
    includeQualityTests: !args.includes('--skip-quality'),
    includeAccuracyTests: !args.includes('--skip-accuracy'),
    includeConsistencyTests: !args.includes('--skip-consistency'),
    includeEducationalTests: !args.includes('--skip-educational')
  };

  try {
    await tester.runModelQualityTests(options);
  } catch (error) {
    console.error('❌ Model quality testing failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runModelQualityTests();
}

module.exports = ModelQualityTester;
