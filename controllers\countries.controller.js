const { Country } = require("../models/country");
const { getAddedBy, getEditedBy } = require('../tools/database')
const { default: mongoose } = require('mongoose')
const commonHelper = require("../helpers/commonHelper");
const {messageResponse} = require("../helpers/commonHelper");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS } = require("../config/messages");

const validate = async(req, res, action) => {
  try {
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }
    }

    if (!req.body.name) {
      return messageResponse(INVALID_MISSING, "Name", false, 400, null)
    }

    let query = { name: req.body.name };
    if (action == 'edit') {
      query = { $and: [{ name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } }] };
    }

    const existingCountry = await Country.findOne(query);
    if (existingCountry != null) {
      return messageResponse(DUPLICATE, "", false, 400, null);
    }

    return { success: true };
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == 'add') {
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newCountry = Country.create(req.body)

      if(!newCountry) return messageResponse(ADD_ERROR, "Country", false, 400, null, res)

      await updateUserRole(req.body.adminUserId, 3, newCountry._id);
      res.status(200).json({ success: true, id: newCountry._id })
    }
    else {
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedCountry = await Country.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updatedCountry) return messageResponse(EXIST_PERMISSION, "Country", false, 404, null, res) 

      return messageResponse(UPDATE_SUCCESS, "Country", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async(req, res, next) => {
  return await addOrEdit(req, res, 'add')
}
module.exports.add=add;

const get = async(req, res) => {
  try {
    const countries = await Country.find({}, { name:1 } );

    if(!countries.length) return messageResponse(NOT_FOUND, "Countries", false, 404, null, res) 

    return messageResponse(null, "", true, 200, countries, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.get=get;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }
    
    const existingCountry = await Country.findById(req.query.id)

    if (!existingCountry) return messageResponse(NOT_FOUND, "Country", false, 404, null, res) 

    return messageResponse(null, "", true, 200, existingCountry, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID=getByID;

const remove = async(req, res) => {
  try {
    if(!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

  const country = await Country.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

  if(!country) return messageResponse(EXIST_PERMISSION, "Country", false, 404, null, res);

  return messageResponse(REMOVE_SUCCESS, "Country", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove=remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit')
};
module.exports.update=update;