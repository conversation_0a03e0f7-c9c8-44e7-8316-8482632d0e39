const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const subsectorsController = require('../controllers/subsectors.controller')
const SuperUserGuard = require('../guards/super-user.guard')


router.post("/add", SuperUserGuard, subsectorsController.add);

router.get("/get", subsectorsController.get);

router.get("/getById", subsectorsController.getByID);

router.delete("/remove", SuperUserGuard, subsectorsController.remove);

router.put("/update", SuperUserGuard, subsectorsController.update);

module.exports = router;