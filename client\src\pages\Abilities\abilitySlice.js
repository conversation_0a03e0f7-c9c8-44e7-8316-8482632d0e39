import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance';

// const { default: courses } = require("src/_mock/courses");
export const getAbilities = createAsyncThunk('abilities/getAbilities', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "lmiSkillsAbilities/get",
            method: "GET",
            params : {
                category : "ability"
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const getAbilityById = createAsyncThunk('abilities/getSkillsById', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "lmiSkillsAbilities/getByID",
            method: "GET",
            params : {
                id : data.id
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const getSubRadarCat = createAsyncThunk('radar-category/getSubRadarCat', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "radar-category/getSubRadarCat",
            method: "GET",
            params : {
                id : data.id
            }
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})
export const updateAbility = createAsyncThunk('abilities/update', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "lmiSkillsAbilities/update",
            method: "PUT",
            data
        })
        return response.data
    } catch (error) {
        return rejectWithValue(error)
    }
})

const abilitySlice = createSlice({
    name:'abilities',
    initialState:{
        abilities : [],
        abilityId : {},
        subRadarCat:{},
        status: 'idle', // 'idle' | 'pending' | 'succeeded' | 'failed',
        error: null,
    },
    reducers:{
        updateAbilities : (state, action) =>{},
    },
    extraReducers: {
        [getAbilities.pending]: (state) => {
            state.status = "pending"
        },
        [getAbilities.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data;
            state.abilities = data
        },
        [getAbilities.rejected]: (state, action) => {
            state.status = "failed"
            state.error = action.payload
        },
        [getAbilityById.pending]: (state) => {
            // state.status = "pending"
        },
        [getAbilityById.fulfilled]: (state, action) => {
            // state.status = "succeeded"
            const data = action.payload?.data;
            state.abilityId = data
        },
        [getAbilityById.rejected]: (state, action) => {
            // state.status = "failed"
            state.error = action.payload
        },
        [updateAbility.pending]: (state) => {
            // state.status = "pending"
        },
        [updateAbility.fulfilled]: (state, action) => {
            // state.status = "succeeded"
            // const data = action.payload?.data;
            // state.abilityId = data
        },
        [updateAbility.rejected]: (state, action) => {
            // state.status = "failed"
            state.error = action.payload
        },
        [getSubRadarCat.pending]: (state) => {
            // state.status = "pending"
        },
        [getSubRadarCat.fulfilled]: (state, action) => {
            // state.status = "succeeded"
            const data = action.payload?.data;
            state.subRadarCat = data
        },
        [getSubRadarCat.rejected]: (state, action) => {
            // state.status = "failed"
            state.error = action.payload
        },
    }
})
export const { updateAbilities} = abilitySlice.actions;
export default abilitySlice.reducer