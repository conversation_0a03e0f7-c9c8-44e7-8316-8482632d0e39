const { Schema, Model, model } = require("mongoose");

const lmiSkillsSchema = new Schema({
  careerID: {
    type: Schema.Types.ObjectId,
    ref: "careers",
  },
  soc: Number,
  onetcode: String,
  scales: [
    {
      id: String,
      skills: [
        {
          id: String,
          name: String,
          value: Number
        }
      ]
    }
  ]
})

module.exports.lmiSkillsSchema = lmiSkillsSchema;

class LmiSkills extends Model {

}

model(LmiSkills, lmiSkillsSchema, "LmiSkills");

module.exports.LmiSkills = LmiSkills;