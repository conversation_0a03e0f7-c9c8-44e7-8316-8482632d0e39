import PropTypes from 'prop-types';
// import step1Icon from '/assets/Images/step-01.svg';
// import step2Icon from '/assets/Images/step-02.svg';
// import step3Icon from '/assets/Images/step-03.svg';
import { Tabs, Tab, Box } from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import SvgIcons from '../svgIcons';

const CustomStepIcon = ({ active, completed, icon }) => {
  const stepIcons = {
    1: '/assets/Images/step-01.svg',
    2: '/assets/Images/step-02.svg',
    3: '/assets/Images/step-03.svg',
  };
  return (
    <Box
      className="step-icon"
      sx={{ background: (theme) => `${active ? theme.palette.secondary.main : '#f4f4f4'}` }}
    >
      <SvgIcons
        sx={{ color: (theme) => `${!active ? theme.palette.primary.main : '#fff'}` }}
        src={stepIcons[String(icon)]}
      />
    </Box>
  );
};
function StepperComponent({ steps, activeStep, noIcon, onStepChange }) {
  const navigate = useNavigate();
  const location = useLocation();

  // Match current tab by route
  const activeTab = steps?.findIndex((step) => location.pathname.includes(step.link));
  return (
    // <Stepper alternativeLabel activeStep={activeStep} >
    //     {steps?.map((step) => (
    //       <Step key={step.label}>
    //             {step.link?
    //               // <StepLabel StepIconComponent={CustomStepIcon}><Link to={step.link}><Typography variant='bold' color='#ccc' >{step.label}</Typography></Link></StepLabel>
    //               <Link to={step.link}><StepLabel StepIconComponent={CustomStepIcon}><Typography variant='bold2' color='secondary.main' >{step.label}</Typography></StepLabel></Link>
    //               :
    //               <StepLabel StepIconComponent={CustomStepIcon}><Typography variant='bold2' color='primary.main' >{step.label}</Typography></StepLabel>
    //             }
    //       </Step>
    //     ))}
    // </Stepper>
    <Box
      sx={{
        borderBottom: 1,
        borderColor: 'divider',
        bgcolor: '#fff',
        display: 'flex',
        justifyContent: 'center',
      }}
    >
      <Tabs
        value={activeStep}
        onChange={(e, newValue) => navigate(steps[newValue].link)}
        // centered
        // TabIndicatorProps={{ style: { backgroundColor: 'secondary.main' } }} // green indicator
        TabIndicatorProps={{ style: { display: 'none' } }}
        sx={{
          minHeight: '48px',
          gap: 0, // Ensures no space between tabs
          '& .MuiTabs-flexContainer': {
            gap: 0, // Removes spacing between children
          },
          '& .MuiTab-root': {
            textTransform: 'none',
            border: '1px solid #ccc',
            borderRadius: '8px 8px 0 0 !important',
            minHeight: '48px',
            px: 3,
            fontWeight: 500,
            color: '#000',
            bgcolor: '#fff',
            margin: 0, // removes spacing between tabs
            '& + .MuiTab-root': {
              marginLeft: 0, // explicitly prevents MUI's internal spacing
            },
            '&.Mui-selected': {
              bgcolor: '#333',
              color: '#fff',
            },
          },
        }}
      >
        {steps?.map((step, index) => (
          <Tab
            key={index}
            label={step.label}
            iconPosition="start"
            icon={noIcon ? null : <SvgIcons src={`/assets/Images/step-0${index + 1}.svg`} />}
            sx={{
              textTransform: 'none',
              fontWeight: 'bold',
              mr: '0px !important',
              color: activeStep === index ? 'secondary.main' : 'text.secondary',
            }}
            onClick={(curStep) => {
              if(!onStepChange) return;
              if(curStep?.stepIndex){
                onStepChange(curStep.stepIndex);
              }else{
                onStepChange(index);
              }
            }}
          />
        ))}
      </Tabs>
    </Box>
  );
}
StepperComponent.propTypes = {
  steps: PropTypes.array,
  activeStep: PropTypes.number,
  noIcon: PropTypes.bool,
  onStepChange: PropTypes.func,
};
CustomStepIcon.propTypes = {
  active: PropTypes.node,
  completed: PropTypes.node,
  icon: PropTypes.node,
};

export default StepperComponent;
