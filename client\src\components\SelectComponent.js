import { FormHelperText, InputLabel, MenuItem, Select } from '@mui/material'
import React from 'react'

const SelectComponent = (props) => {
    const { error, disableNone,disable, labelColor, labelError, labelStyles, name, labelId, inputLabel, menuItems, menuValues, helperText, menuName, menuValue, ...rest } = props
    return (
        <>
            <InputLabel
                id={labelId}
                color={labelColor}
                sx={{ color: labelError && 'error.main' }}
            >
                {inputLabel}
            </InputLabel>
            <Select
                name={name}
                // label="Group *"
                labelId={labelId}
                error={error}
                disabled={disable && true || false}
                {...rest}
            >
                {disableNone?
                null
                : 
                <MenuItem value="">
                    <em>None</em>
                </MenuItem>
                }
                
                {menuItems?.map((item, index) =>
                    <MenuItem
                        key={index}
                        sx={{ pr: '2', minWidth: 'min-content', whiteSpace: 'normal' }}
                        value={item[menuValue]}
                    >
                        {menuValues ?
                            menuValues.map((value)=>{
                                return (item[value] )
                            })
                            :
                            item[menuName]
                        }
                    </MenuItem>
                )}
            </Select>
            <FormHelperText
                sx={{ color: labelError && 'error.main' }}
            >
                {helperText}
            </FormHelperText>
        </>
    )
}

export default SelectComponent