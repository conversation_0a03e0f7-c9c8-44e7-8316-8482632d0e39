const mongoose = require("mongoose");
const { Sector, SectorSchema } = require("./sector");

const SubsectorSchema = new mongoose.Schema({
  name: String,
  sectorId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(Sector, SectorSchema) },
  sector:String,
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
  priority: { type: Boolean, default: false },
});

module.exports.SubsectorSchema = SubsectorSchema;

class Subsector extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await Subsector.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await Subsector.create(entry);
    }
  }
}

mongoose.model(Subsector, SubsectorSchema, "subsectors");

module.exports.Subsector = Subsector;

