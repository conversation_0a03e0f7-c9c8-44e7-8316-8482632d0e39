import PropTypes from 'prop-types';
// import step1Icon from '/assets/Images/step-01.svg';
// import step2Icon from '/assets/Images/step-02.svg';
// import step3Icon from '/assets/Images/step-03.svg';
import { Box, Tab, Tabs } from '@mui/material';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { getRegionBySlug } from 'src/pages/CareerHistory/CareerHistorySlice';
import SvgIcons from '../svgIcons';

const CustomStepIcon = ({ active, completed, icon }) => {
    const stepIcons = {
        1: '/assets/Images/step-01.svg',
        2: '/assets/Images/step-02.svg',
        3: '/assets/Images/step-03.svg',
    };
    return (
        <Box
            className="step-icon"
            sx={{ background: (theme) => `${active ? theme.palette.secondary.main : '#f4f4f4'}` }}
        >
            <SvgIcons
                sx={{ color: (theme) => `${!active ? theme.palette.primary.main : '#fff'}` }}
                src={stepIcons[String(icon)]}
            />
        </Box>
    );
};
function SecondRegionStepper({ steps, activeStep, noIcon, onStepChange }) {
    const navigate = useNavigate();
    const location = useLocation();
    const { cg_name, rg_name } = useParams();
    const dispatch = useDispatch();
    const params = useParams();
    const isRegion = !!params?.rg_name;

    const regions = useSelector((state) => state.careerHistory);
    const [regionData, setData] = useState({});
    const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);

    useEffect(() => {
        if (rg_name) {
            dispatch(getRegionBySlug(rg_name))
        }
    }, [rg_name, dispatch])
    useEffect(() => {
        if (cg_name) {
            setData(clgDetails?.region);
            return;
        }

        if (regions?.regions) {
            setData(regions?.regions);
        }
    }, [regions?.regions, regionData, cg_name, clgDetails?.region]);
    const buttonColor = regionData?.button?.bgColor || clgDetails?.region?.button?.bgColor || '';
    const isRegionData = !!clgDetails?.region;

    // Match current tab by route
    const activeTab = steps?.findIndex((step) => location.pathname.includes(step.link));
    return (
        // <Stepper alternativeLabel activeStep={activeStep} >
        //     {steps?.map((step) => (
        //       <Step key={step.label}>
        //             {step.link?
        //               // <StepLabel StepIconComponent={CustomStepIcon}><Link to={step.link}><Typography variant='bold' color='#ccc' >{step.label}</Typography></Link></StepLabel>
        //               <Link to={step.link}><StepLabel StepIconComponent={CustomStepIcon}><Typography variant='bold2' color='secondary.main' >{step.label}</Typography></StepLabel></Link>
        //               :
        //               <StepLabel StepIconComponent={CustomStepIcon}><Typography variant='bold2' color='primary.main' >{step.label}</Typography></StepLabel>
        //             }
        //       </Step>
        //     ))}
        // </Stepper>
        <Box
            sx={{
                borderBottom: 1,
                borderColor: 'divider',
                // bgcolor: '#fff',
                display: 'flex',
                justifyContent: 'center',
                width: '100%',
                alignContent: 'center',
            }}
        >
            <Tabs
                value={activeStep}
                onChange={(e, newValue) => navigate(steps[newValue].link)}
                TabIndicatorProps={{ style: { display: 'none' } }}
                variant="fullWidth"
                sx={{
                    width: '100%',
                    display: 'flex', // 👈 enables spacing
                    gap: '4px',      // 👈 space between tabs
                    '& .MuiTab-root': {
                        textTransform: 'none',
                        border: '1px solid #E0E0E0',
                        borderBottom: 'none',
                        borderRadius: '12px 12px 0 0',
                        minHeight: '48px',
                        fontWeight: 600,
                        fontSize: '16px',
                        bgcolor: '#fff',
                        color: '#000',
                        '&.Mui-selected': {
                            bgcolor: buttonColor || '#F2B600',
                            color: '#000',
                            // borderColor: '#E0E0E0',
                        },
                    },
                }}
            >
                {steps?.map((step, index) => (
                    <Tab
                        key={index}
                        label={step.label}
                        iconPosition="start"
                        icon={noIcon ? null : <SvgIcons src={`/assets/Images/step-0${index + 1}.svg`} />}
                        sx={{
                            flex: 1, // 👈 equal width per tab
                            textTransform: 'none',
                            fontWeight: 'bold',
                            mr: '0.2px !important',
                            color: activeStep === index ? 'secondary.main' : 'text.secondary',
                        }}
                        onClick={(curStep) => {
                            if (!onStepChange) return;
                            if (curStep?.stepIndex) {
                                onStepChange(curStep.stepIndex);
                            } else {
                                onStepChange(index);
                            }
                        }}
                    />
                ))}
            </Tabs>
        </Box>
    );
}
SecondRegionStepper.propTypes = {
    steps: PropTypes.array,
    activeStep: PropTypes.number,
    noIcon: PropTypes.bool,
    onStepChange: PropTypes.func,
};
CustomStepIcon.propTypes = {
    active: PropTypes.node,
    completed: PropTypes.node,
    icon: PropTypes.node,
};

export default SecondRegionStepper;
