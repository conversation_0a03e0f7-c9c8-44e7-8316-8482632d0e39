import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
  Container,
  Typography
} from '@mui/material';
// @mui
import { useNavigate } from 'react-router-dom';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import GroupForm from './GroupForm';
import { deleteCollegeGroup, getCollegeGroups } from './collegeGroupsSlice';
import useLoading from '../../hooks/useLoading';
import { APP_ROUTER_BASE_URL, allowedRoles } from '../../utils';
import useAuth from '../../hooks/useAuth';
import { setSnackbar } from '../../Redux/snackbarSlice';

// ----------------------------------------------------------------------
export const GROUP_TABLE_HEAD = [
  { id: 'Name', label: 'Group Name', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];
export const renderGroupCells = ["Name"]
const deleteTitle = "Delete College Group?"
const deleteDescription = "Are you sure you want to delete this College Group"

const CollegeGroups = () => {
  const collegeGroups = useSelector(state => state.collegeGroups.groups)
  const error = useSelector(state => state.collegeGroups.error)
  const status = useSelector(state => state.collegeGroups.status)
  const dispatch = useDispatch()
  const [openModel, setOpenModel] = useState(false);
  const [Groups, setCollegeGroups] = useState([]);
  const loading = useLoading(status)

  useEffect(() => {
    if(!!collegeGroups){
      setCollegeGroups([...collegeGroups])
    }
  }, [collegeGroups])

  const handleFilterSearch = (event) => {
    const filteredGroups = !!Groups ? Groups.filter(group => group.Name.toLowerCase().includes(event.target.value.toLowerCase())) : []
    return filteredGroups
  };
  const navigate = useNavigate()
  // const handleOpen = () => setOpenModel(true);
  const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/collegegroups/add`);

  const handleClose = () => {
    setOpenModel(false)
  };

  const editGroup = (group) => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/collegegroups/edit/${group?.id}`)
  }

  //   const handleDeleteAdmin = (Admin, handleOpenBackdrop, handleCloseBackdrop) => {
  //     const user = { id: Admin._id }
  //     handleOpenBackdrop()
  //     dispatch(removeUser(user)).then(res => {
  //         if (res?.payload?.success) {
  //             handleCloseBackdrop()
  //             dispatch(setSnackbar({
  //                 snackbarOpen: true,
  //                 snackbarType: 'success',
  //                 snackbarMessage: "User Deleted Succesfully"
  //             }))
  //         } else {
  //             dispatch(setSnackbar({
  //                 snackbarOpen: true,
  //                 snackbarType: 'error',
  //                 snackbarMessage: "Something went wrong"
  //             }))
  //             console.log('Something went wrong!!', res)
  //         }
  //     })
  // }

  const handleDeleteGroup = (group, handleOpenBackdrop, handleCloseBackdrop) => {
    try {
      handleOpenBackdrop()
      dispatch(deleteCollegeGroup(group)).then(response => {
        // console.log("res", response)
        if (response?.payload?.data?.success) {
          dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'success',
            snackbarMessage: "College group Deleted Succesfully"
          }))
        } else {
          const errorMessage = response?.payload?.response?.data?.message
          dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: errorMessage || "something went wrong"
          }))
        }
      }).finally(() => {
        handleCloseBackdrop()
      })
    } catch (error) {
      console.log('deleteError', error)
    }

  }

  useEffect(() => {
    // if (status === "idle")
    dispatch(getCollegeGroups())
  }, [dispatch])
  const { role } = useAuth()
  return (
    <>
      <Helmet>
        <title> College Groups | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        <PopUp
          open={openModel}
          onClose={handleClose}
          title={"Add College Group"}
        >
          <GroupForm
            Groups={Groups}
            setOpenModel={setOpenModel}
            openModel={openModel}
          />
        </PopUp>
        {/* <Modal
          open={openModel}
          onClose={handleClose}
          closeAfterTransition
          slots={{ backdrop: Backdrop }}
          slotProps={{
            backdrop: {
              timeout: 300,
            },
          }}
        >
          <GroupForm
            Groups={Groups}
            setOpenModel={setOpenModel}
            openModel={openModel}
          />
        </Modal> */}
        {/* <Container > */}
        <Typography variant="h4" gutterBottom mb={3}>
          College Groups
        </Typography>
        {/* {status === "pending" ? */}
        {/* <p>Loading...</p> : */}
        <DataTable
          loading={loading}
          handleEdit={editGroup}
          deleteTitle={deleteTitle}
          deleteDescription={deleteDescription}
          TableHead={GROUP_TABLE_HEAD}
          TableData={Groups}
          filterSearch={handleFilterSearch}
          buttonText={allowedRoles(role, ['1']) && "New Group"}
          buttonHandler={handleOpen}
          renderCells={renderGroupCells}
          handleDelete={handleDeleteGroup}
          disableDelete = {role !== '1'}
          pagination="true"
          rowsPerPageProp={10}
        />

        {/* } */}
        {/* </Container> */}
      </Container>
    </>
  )
}

export default CollegeGroups






