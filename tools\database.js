const moment = require("moment");

const isDuplicateEntry = async(
  doc,
  model,
  returnDuplicate = false,
  ignoreSelf = false
) => {
  let duplicate;

  const query = {
    $or: [
      { uniqueName: doc.uniqueName, customerID: doc.customerID },
      { uniqueName: doc.uniqueName, defaultEntry: true },
    ],
  };

  if (ignoreSelf && doc._id) {
    query["_id"] = { $ne: doc._id };
  }

  if (returnDuplicate) {
    duplicate = model.findOne(query);
  } else {
    duplicate = model.count(query);
  }

  return duplicate;
}

const getUniqueName = (name) => {
  if (!name) {
    return "";
  }
  return formatStringForComparison(name).replace(/\s+/g, "_");
}

const formatStringForComparison = (string) => {
  if (!string) {
    return;
  }
  return string.toString().toLowerCase().trim();
}

const checkStringContainSpecialCharacter = (str) => {
  if (!str) {
    return;
  }
  return /[`!@#$%^&*()_+/\-=\[\]{};':"\\|,.<>\/?~]/.test(str.toString());
}

const getAddedBy = (req, method) => {
  if (method == "add") {
    return { date: moment.utc(), userId: req.user ? req.user._id : null };
  } else {
    return { date: moment.utc(), userId: req.user ? req.user._id : null };
  }
}

const getEditedBy = (req, method) => {
  if (method == "add") {
    return { date: moment.utc(), userId: req.user ? req.user._id : null };
  } else {
    return { date: moment.utc(), userId: req.user ? req.user._id : null };
  }
}

module.exports = isDuplicateEntry;
module.exports.checkStringContainSpecialCharacter =
  checkStringContainSpecialCharacter;
module.exports.getUniqueName = getUniqueName;
module.exports.formatStringForComparison = formatStringForComparison;
module.exports.getAddedBy = getAddedBy;
module.exports.getEditedBy = getEditedBy;
