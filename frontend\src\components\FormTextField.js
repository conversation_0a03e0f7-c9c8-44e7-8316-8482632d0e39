import { Box, Typography } from '@mui/material';
import PropTypes from 'prop-types';
import TextFIeldComponent from './TextField/TextFieldComponent';

const FormTextField = ({ title, isRequired, ...rest }) => {
  let a;
  return (
    <Box className="field-wrapper">
      <Typography mb={1} variant="subtitle2">
        {title} {isRequired && '*'}
      </Typography>
      <TextFIeldComponent {...rest} />
    </Box>
  );
};

export default FormTextField;
FormTextField.propTypes = {
  title: PropTypes.string.isRequired,
  isRequired: PropTypes.bool,
  children: PropTypes.node.isRequired,
};
