import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from "react-redux";
import { useLocation, useNavigate } from 'react-router-dom';
// @mui
import { styled, alpha } from '@mui/material/styles';
import { Box, Link, Button, Drawer, Typography, Avatar, Stack, Divider, ListItemText, List, Backdrop, CircularProgress } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import { removeUserPermissions } from 'src/Redux/selectedCollegeSlice';
// hooks
import useResponsive from '../../../hooks/useResponsive';
// components
import Logo from '../../../components/Logo';
import Scrollbar from '../../../components/Scrollbar';
import NavSection from '../../../components/Nav-section';
import HorizonLogo from '../../../assets/images/UP_RElogo.png'
import ThinkskillLogo from '../../../assets/images/thinkskill.png';
//
import navConfig from './config';
import { APP_ROUTER_BASE_URL, logoutUser } from '../../../utils';
import { StyledNavItem } from '../../../components/Nav-section/styles';
import SidenavItem from '../../../components/SidenavItem';
import ConfirmDialog from '../../../components/ConfirmDialog';

// ----------------------------------------------------------------------

const NAV_WIDTH = 280;

const StyledAccount = styled('div')(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  padding: theme.spacing(2, 2.5),
  borderRadius: Number(theme.shape.borderRadius) * 1.5,
  // backgroundColor: alpha(theme.palette.grey[500], 0.12),
}));

// ----------------------------------------------------------------------

Nav.propTypes = {
  openNav: PropTypes.bool,
  onCloseNav: PropTypes.func,
};

export default function Nav({ openNav, onCloseNav }) {
  const { pathname } = useLocation();
  const navigate = useNavigate()
  const isDesktop = useResponsive('up', 'lg');
  const dispatch = useDispatch()
  useEffect(() => {
    if (openNav) {
      onCloseNav();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname]);

  const [open, setOpen] = useState(false);

  const renderContent = (
    <Scrollbar
      sx={{
        height: 1,
        '& .simplebar-content': { height: 1, display: 'flex', flexDirection: 'column' },
      }}
    >
      <ConfirmDialog
        title={"Log Out"}
        open={open}
        setOpen={setOpen}
        onConfirm={()=>{
          dispatch(removeUserPermissions()) 
          logoutUser()
        }}
      >
        <Typography variant='body1'>
          {"Are You sure you want to log out ?"}
        </Typography>
      </ConfirmDialog>
      <Box sx={{ mb: 1, mx: 2.5, pt: 3 }}>
        <Link underline="none">
          <StyledAccount>
            {/* <Avatar >
              <PersonIcon fontSize='large' />
            </Avatar> */}

            <Box sx={{ ml: 2 }}>
              {/* <Typography variant="subtitle1" sx={{ color: 'text.primary' }}>
                {"Horizon"}
              </Typography> */}
              <Box
                sx={{ maxWidth: 120, cursor: 'pointer' }}
                onClick={() => navigate(`${APP_ROUTER_BASE_URL}`)}
              >
                <img src={ThinkskillLogo} alt="logo" />
              </Box>

              {/* <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                {account.role}
              </Typography> */}
            </Box>
          </StyledAccount>
        </Link>
      </Box>

      <NavSection data={navConfig} />
      <Box sx={{ flexGrow: 1 }} />
      <Divider sx={{ borderStyle: 'dashed' }} />
      <Stack my={2}>
        {/* <SidenavItem
          title={"Home"}
          iconDetail={"ic_home"}
          link={''}
        /> */}
        <SidenavItem
          link="dashboard/profile"
          title={"Profile"}
          iconDetail={"ic_profile"}
        />
        <SidenavItem
          buttonColor='red'
          title={"Logout"}
          iconDetail={"ic_logout"}
          onClick={() => setOpen(true)}
        />
      </Stack>
    </Scrollbar>
  );

  return (
    <Box
      component="nav"
      sx={{
        flexShrink: { lg: 0 },
        width: { lg: NAV_WIDTH },
      }}
    >
      {isDesktop ? (
        <Drawer
          open
          variant="permanent"
          PaperProps={{
            sx: {
              width: NAV_WIDTH,
              bgcolor: 'background.default',
              borderRightStyle: 'dashed',
            },
          }}
        >
          {renderContent}
        </Drawer>
      ) : (
        <Drawer
          open={openNav}
          onClose={onCloseNav}
          ModalProps={{
            keepMounted: true,
          }}
          PaperProps={{
            sx: { width: NAV_WIDTH },
          }}
        >
          {renderContent}
        </Drawer>
      )}
    </Box>
  );
}
