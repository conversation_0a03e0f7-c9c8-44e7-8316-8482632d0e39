const { default: mongoose } = require("mongoose");
const { User, UserRoles } = require("../models/user");
const { Subsector } = require("../models/subsector");
const { getAddedBy, getEditedBy } = require('../tools/database');
const { Sector } = require("../models/sector");
const commonHelper = require("../helpers/commonHelper");
const { messageResponse, isEmpty } = require("../helpers/commonHelper");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS, ADD_SUCCESS, UPDATE_ERROR } = require("../config/messages");
const { createHash } = require("crypto");
const { Campus } = require("../models/campus");
const { College } = require("../models/college");
const { Course } = require("../models/course");
const commonClass = require("../helpers/commonClass");

const validate = async(model, res, action, campusValidationReq = true) => {
  try {
    let oldEntry;
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(model.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }

      oldEntry = await Course.findById(new mongoose.Types.ObjectId(model.id));
      if (!oldEntry) {
        return messageResponse(NOT_FOUND, "Course", false, 404, null);
      }
    }

    if (!model.code) {
      return messageResponse(INVALID_MISSING, "Code", false, 400, null)
    }

    if (!model.title) {
      return messageResponse(INVALID_MISSING, "Title", false, 400, null)
    }

    if (!model.description) {
      return messageResponse(INVALID_MISSING, "Description", false, 400, null)
    }

    if (!model.collegeId || !mongoose.isValidObjectId(model.collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null)
    }

    if (campusValidationReq) {
      if(action !== 'edit') {
        for(let id of model.campusId) {
          if (!mongoose.isValidObjectId(id)) {
            return messageResponse(INVALID_MISSING, "Campus ID", false, 400, null)
          }

          const selectedCampus = await Campus.findById(new mongoose.Types.ObjectId(id));
          if (!selectedCampus) {
            return messageResponse(INVALID_MISSING, "Campus", false, 400, null)
          }
        }
      }
      else {
          if (!mongoose.isValidObjectId(model.campusId)) {
          return messageResponse(INVALID_MISSING, "Campus ID", false, 400, null)
        }

        const selectedCampus = await Campus.findById(new mongoose.Types.ObjectId(model.campusId));
        if (!selectedCampus) {
          return messageResponse(INVALID_MISSING, "Campus", false, 400, null)
        }
      }
    }

    // if (!model.sectors || !model.sectors.length) {
    //   return { success: false, msg: "Invalid/missing Sector(s)." };
    // }

    let query;
    if (action == 'edit') {
      //query = { $and: [{ $or: [{ code: { $eq: model.code } }, { title: { $eq: model.title } }] }, { campusId: { $eq: new mongoose.Types.ObjectId(model.campusId) } }, { _id: { $ne: model.id } }] }
      query = { $and: [{ $or: [{ code: { $eq: model.code } }, { $and: [{ title: { $eq: model.title } }, { level: { $eq: model.level } }]} ] }, 
        { campusId: { $eq: new mongoose.Types.ObjectId(model.campusId) } }, { _id: { $ne: model.id } }] }
    }
    else {
      //query = { $and: [{ $or: [{ code: { $eq: model.code } }, { title: { $eq: model.title } }] }, { campusId: { $in: model.campusId } }] }
      query = { $and: [{ $or: [{ code: { $eq: model.code } }, { $and: [{ title: { $eq: model.title } }, { level: { $eq: model.level } }]}] }, { campusId: { $eq: model.campusId } }] }
    }

    const existingEntry = await Course.findOne(query);
    if (existingEntry) {
      if(!campusValidationReq) { // for handling updation in addbulk
        return {message: DUPLICATE, data: existingEntry._id, success: true}
      } else {
        return messageResponse(DUPLICATE, "Course", false, 400, null)
      }
    }

    return { success: true, oldEntry: oldEntry };
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req.body, res, action);
  
    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if(action == 'edit') {
      req.body.campusId = [req.body.campusId];
    }
    

    const {code, title,duration, description, level, campusId, pageURL, applyURL, enquiryURL, collegeId} = req.body
    
    let sectors = [], sectorIds = [], subsectorIds = [];

    if(req.body.sectors?.length){
      req.body.sectors.forEach(sector => {

        sectorIds.push(sector._id);
        let subsectorsOfsector = [];
        sector.subsectors.forEach(subsector => {
          subsectorsOfsector.push(subsector._id);
        });
        subsectorIds.push(...subsectorsOfsector);

        sectors.push({ sectorId: sector._id, subsectorIds: subsectorsOfsector });
      });
    }

    let data = {
      inserted: [],
      notInserted: []
    }
    const pipeline = [
      {$match: {_id: new mongoose.Types.ObjectId(collegeId)}},
      {$lookup: {
        from: 'campuses',
        localField: '_id',
        foreignField: 'collegeId',
        as: 'campuses'
      }},
      {$project: {
        '_id': 1,
        'campuses._id': 1
      }}
    ]
  

    let selectedCollege = await College.aggregate(pipeline)
    selectedCollege = selectedCollege[0]
    let foundCampus, entry;
    for(let id of campusId) {
      foundCampus = selectedCollege.campuses.find(campus => campus._id.toString() === id.toString())
      entry = {
        code,
        title,
        description,
        level,
        campusId: new mongoose.Types.ObjectId(id),
        pageURL,
        duration,
        applyURL,
        enquiryURL, 
        sectors,
        sectorIds,
        subsectorIds,
      }
      
      if (action == 'add') {
        if(!foundCampus){
          data.notInserted.push({
            code,
            title,
            error: EXIST_PERMISSION.replace(new RegExp(":name", "g"), "Campus"),
          });
          continue;
        }
       

        entry.addedBy = getAddedBy(req);
        let newCourse = await Course.create(entry)
        // if (!newCourse) return messageResponse(ADD_ERROR, "Course", false, 400, null, res);
        if (!newCourse) {
          data.notInserted.push({
            code,
            title,
            error: SERVER_ERROR
          })
          continue;
        }
        data.inserted.push({
          id: newCourse._id,
          code,
          title,
        })
      }
    }

    if (action == 'add') {
      res.status(200).json({ success: true, data });
    }
    else {
      if(!foundCampus){
        return messageResponse(EXIST_PERMISSION, "Campus", false, 400, null, res)
      }
      entry.editedBy = getEditedBy(req, 'edit');
      const updatedEntry = await Course.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, entry, { returnOriginal: false })
      if (!updatedEntry) return messageResponse(EXIST_PERMISSION, "Course", false, 400, null, res)
      return messageResponse(UPDATE_SUCCESS, "Course", true, 200, null, res);
    }

  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async(req, res, next) => {
  return await addOrEdit(req, res, 'add')
}
module.exports.add = add;

const get = async(req, res) => {
  try {
    // check collegeId validity if provided
    if (!isEmpty(req.query.collegeId) && !mongoose.isValidObjectId(req.query.collegeId)) {
      return messageResponse(INVALID_MISSING, "ID", false, 400, null, res)
    }
    const pipeline = [
      {
        $lookup: {
          from: "campuses",
          localField: "campusId",
          foreignField: "_id",
          as: "campusName"
        }
      },
      { $set: { campusName: { $arrayElemAt: ["$campusName.name", 0] } } },
      {
        $project: {
          "code": 1,
          "title": 1,
          "description": 1,
          "level": 1,
          "campusId": 1,
          "campusName": 1,
          "duration": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
        }
      },
      { $sort: { 'addedBy.date': -1 } },
    ]
    if (!isEmpty(req.query.collegeId)) {
      pipeline.splice(1, 0, { $match: { 'campusName.collegeId': new mongoose.Types.ObjectId(req.query.collegeId) } })
    }
    if (req.user.role != UserRoles.SUPER_ADMIN) {
      let match;
      const allowedCampuses = await commonClass.getAllowedCampuses(req);
      let campusIds = [];
      allowedCampuses.forEach(campus => {
        campusIds.push(new mongoose.Types.ObjectId(campus._id));
      });
      match = { $match: { campusId: { $in: campusIds } } }
      pipeline.unshift(match);
    }
    const entries = await Course.aggregate(pipeline);
    entries.forEach(course => {
      const {sectorIds, subsectorIds} = course
      if((sectorIds[0]!=null&&sectorIds.length>0)||(subsectorIds[0]!=null&&subsectorIds.length>0)){
        course['sectorOrSubsector'] = true;
      } else if((sectorIds[0]==null||sectorIds.length===0 )&& (subsectorIds[0]==null||subsectorIds.length===0)){
        course['sectorOrSubsector'] = false;
      }
    })

    if (!entries.length) return messageResponse(NOT_FOUND, "Courses", false, 404, [], res)

    return messageResponse(null, "", true, 200, entries, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.get = get;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const pipeline = [
      { $match: { _id: { $eq: new mongoose.Types.ObjectId(req.query.id) } } },
      {
        $lookup: {
          from: "campuses",
          localField: "campusId",
          foreignField: "_id",
          as: "campusName"
        }
      },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectorData"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectorData"
        }
      },
      { $set: { campusName: { $arrayElemAt: ["$campusName.name", 0] } } },
    ]
    const entry = await Course.aggregate(pipeline);
    
    if (!entry || !entry.length) return messageResponse(NOT_FOUND, "Course", false, 404, null, res);

    if(req.user.role != UserRoles.SUPER_ADMIN) {
      const allowedCampuses = await commonClass.getAllowedCampuses(req);
      let foundCampus;
      if(allowedCampuses && allowedCampuses.length) {
        foundCampus = allowedCampuses.find(campus => campus._id.toString() == entry[0].campusId.toString());
      }
      if(!foundCampus) {
        return messageResponse(EXIST_PERMISSION, "Campus", false, 404, null, res);
      }
    }

    let returnResult = entry[0];

    let sectors = [];
    if (returnResult.sectors && returnResult.sectors.length > 0) {
      returnResult.sectors.forEach(sector => {
        let subsectorsData = [];
        const foundSectorData = returnResult.sectorData.find(sectorData => sectorData._id.toString() == sector.sectorId.toString());
        if (foundSectorData) {
          sector.subsectorIds.forEach(subsector => {
            const foundSubsectorData = returnResult.subsectorData.find(subsectorData => subsectorData._id.toString() == subsector.toString());
            subsectorsData.push({ _id: foundSubsectorData._id, label: foundSubsectorData.name });
          });

          sectors.push({ _id: foundSectorData._id, label: foundSectorData.name, subsectors: subsectorsData });
        }
      });

      delete returnResult["sectorIds"];
      delete returnResult["subsectorIds"];
      delete returnResult["sectorData"];
      delete returnResult["subsectorData"];
    }
    returnResult.sectors = sectors;

    return messageResponse(null, "", true, 200, returnResult, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID = getByID;

const remove = async(req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const course = await Course.findOneAndDelete({ _id: req.body.id, defaultEntry: false });
    if (!course) return messageResponse(EXIST_PERMISSION, "Course", false, 404, null, res);

    return messageResponse(REMOVE_SUCCESS, "Course", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove = remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit')
};
module.exports.update = update;

const addBulk = async(req, res, next) => {
  try {
    // if (req.user.role == UserRoles.SUPER_ADMIN) {
    //   return messageResponse(NO_ACCESS, "", false, 400, null, res);
    // }

    if (!req.body.collegeId || !mongoose.isValidObjectId(req.body.collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, res);
    }

    if (!req.body.courses || !req.body.courses.length) {
      return messageResponse(ADD_ERROR, "Data", false, 400, null, res);
    }

    // let match = {};
    // if (req.user.role == UserRoles.COLLEGE_ADMIN) {
    //   // collegeIds.push(new mongoose.Types.ObjectId(req.user.collegeId));
    //   match = { $match: { _id: { $eq: new mongoose.Types.ObjectId(req.user.collegeIds[0]) } } };
    // }
    // else if (req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
    //   // const colleges = Coll
    //   match = { $match: { collegeGroupId: { $eq: new mongoose.Types.ObjectId(req.user.collegeGroupIds[0]) } } };
    // }
    const pipeline = [
      { 
        $match: {
          _id: new mongoose.Types.ObjectId(req.body.collegeId)
        } 
      },
      {
        $lookup: {
          from: "campuses",
          localField: "_id",
          foreignField: "collegeId",
          as: "campuses"
        }
      },
      {
        $project: {
          "name": 1,
          "campuses._id": 1,
          "campuses.name": 1,
          "campuses.collegeId": 1,
        }
      }
    ];
    // if (req.user.role != UserRoles.SUPER_ADMIN) {
    //   pipeline.unshift(match);
    // }
    let existingCollege = await College.aggregate(pipeline);
    if(!existingCollege.length) {
      return messageResponse(NOT_FOUND, "College", false, 400, null, res)
    }
    existingCollege = existingCollege[0]
    if(!existingCollege.campuses.length) {
      return messageResponse(NOT_FOUND, "Campuses from College", false, 400, null, res)
    }

    // return res.status(200).json(resultData)

    let campuses = existingCollege.campuses;
    // resultData.forEach(college => {
    //   campuses.push(...college.campuses);
    // });

    const bulkOperations = [];
    let data = {
      validEntries: {
        insert: [],
        update: []
      },
      invalidEntries: []
    }, totalCount = 0, action = 'add';
    const addedBy = getAddedBy(req);
    for (let course of req.body.courses) {
      if (!course.campus) {
        totalCount++;
        data.invalidEntries.push({ code: course.code, title: course.title, level:course.level, error: "Missing Campus" });
        continue;
      }
      else if (course.campus.includes(',')){
        const campuses = course.campus.split(',')
        campuses.forEach(campus => {
          course.campus = campus
          req.body.courses.push(JSON.parse(JSON.stringify(course)))
        })
        continue;
      } 
      else {
        totalCount++;
        const foundCampus = campuses.find(campus => campus.name.trim().toLowerCase() == course.campus.trim().toLowerCase());
        if (!foundCampus) {
          data.invalidEntries.push({ code: course.code, title: course.title, level:course.level, error: "Cannot find Campus because it does not exist or you do not have permission." });
          continue;
        }
        else {
          course.campusId = foundCampus._id; // for validation in 
          course.collegeId=foundCampus.collegeId 
        }
      }

      // req.body.forEach(async (course) => {
      const validateResult = await validate(course, res, action, false);

      if (!validateResult.success) {
        data.invalidEntries.push({ code: course.code, title: course.title, level:course.level, error: validateResult.message });

        // const statusCode = validateResult.statusCode;
        // delete validateResult["statusCode"];
        // // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
        // if (statusCode == 400) {
        //   return res.status(400).json(validateResult);
        // }
        // else if (statusCode == 404) {
        //   return res.status(404).json(validateResult);
        // }
        // else {
        //   return res.status(500).json(validateResult);
        // }
      }
      else {
        let sectors = [], sectorIds = [], subsectorIds = [];
        if(course.sectors?.length) {
          course.sectors.forEach(sector => {

            sectorIds.push(sector._id);
            let subsectorsOfsector = [];
            sector.subsectors.forEach(subsector => {
              subsectorsOfsector.push(subsector._id);
            });
            subsectorIds.push(...subsectorsOfsector);

            sectors.push({ sectorId: sector._id, subsectorIds: subsectorsOfsector });
          });
        }

        const entry = {
          code: course.code,
          title: course.title,
          description: course.description.replace(/;/g, "\n"),
          level: course.level,
          duration:course.duration,
          campusId: course.campusId,

          pageURL: assignHTTPS(course.pageURL),
          applyURL: assignHTTPS(course.applyURL),
          enquiryURL: assignHTTPS(course.enquiryURL),

          sectors,
          sectorIds,
          subsectorIds,
          addedBy
        }

        if(validateResult.message === DUPLICATE){
          course["_id"] = validateResult.data
          const foundUpdateEntry = data.validEntries.update.find(entry => entry._id != course._id && (entry.code == course.code || (entry.title == course.title && entry.level == course.level)) && entry.campusId == course.campusId);
          if (foundUpdateEntry) {
            data.invalidEntries.push({ code: course.code, title: course.title, level:course.level, error: "Duplicate record" });
            continue;
          }
          
          entry["_id"] = validateResult.data
          
          data.validEntries.update.push(entry)
          bulkOperations.push(
            {
              updateOne: {
                filter: { _id: new mongoose.Types.ObjectId(entry._id) },  // Filter condition for the first update
                update: {$set: entry}  // Update operation for the first update
              }
            }
          )
          // data.validEntries.update.push({ _id:entry._id, code: entry.code, title: entry.title, level: entry.level, campusId:entry.campusId })
        } else {
          const foundInsertEntry = data.validEntries.insert.find(entry => (entry.code == course.code || (entry.title == course.title && entry.level == course.level)) && entry.campusId == course.campusId);
          if (foundInsertEntry) {
            data.invalidEntries.push({ code: course.code, title: course.title, level:course.level, error: "Duplicate record" });
            continue;
          }

          data.validEntries.insert.push(entry);
          // data.validEntries.insert.push({ code: entry.code, title: entry.title, level: entry.level, campusId:entry.campusId });

        }        
      }
    };

    const returnResult = {
      success: false,
      totalCount: totalCount,
      successCount: 0,
      insertCount: 0,
      updateCount: 0,
      errorCount: data.invalidEntries.length,
      data: data
    }

    if (data.validEntries.insert && data.validEntries.insert.length) {
      let createResult = await Course.create(data.validEntries.insert);
      if (!createResult || !createResult.length) return messageResponse(ADD_ERROR, "Course", false, 400, null, res); 
      
      returnResult.success = true
      returnResult.insertCount += createResult.length
      returnResult.successCount += createResult.length
    }

    if(bulkOperations.length){
      const bulkResult = await Course.bulkWrite(bulkOperations)

      returnResult.success = true
      returnResult.updateCount += bulkResult.modifiedCount
      returnResult.successCount += bulkResult.modifiedCount
    }

    res.status(200).json(returnResult);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};
module.exports.addBulk = addBulk;

const exportCourses = async(req, res) => {
  try {
    const { collegeId } = req.body
    if(!collegeId || !mongoose.isValidObjectId(collegeId)){
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, res);
    }

    const pipeline = [
      {
        $lookup: {
          from: "campuses",
          localField: "campusId",
          foreignField: "_id",
          as: "campusName"
        }
      },
      { $match: { 'campusName.collegeId': new mongoose.Types.ObjectId(collegeId) } },
      { $set: { campusName: { $arrayElemAt: ["$campusName.name", 0] } } },
      {
        $addFields: {
          sectors: {
            $map: {
              input: "$sectors",
              as: "sector",
              in: {
                "sectorId": { $toObjectId: "$$sector.sectorId" },
                "subsectorIds": {
                  $map: {
                    input: "$$sector.subsectorIds",
                    as: "subsectorId",
                    in: { $toObjectId: "$$subsectorId" }
                  }
                }
              }
            }
          }
        }
      },    
      {
        $lookup: {
          from: "sectors",
          localField: "sectors.sectorId",
          foreignField: "_id",
          as: "sectorDetails"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "sectors.subsectorIds",
          foreignField: "_id",
          as: "subsectorDetails"
        }
      },
      {
        $addFields: {
          "sectors": {
            $map: {
              input: "$sectors",
              as: "sector",
              in: {
                "name": {
                  $arrayElemAt: [
                    "$sectorDetails.name",
                    { $indexOfArray: ["$sectorDetails._id", "$$sector.sectorId"] }
                  ]
                },
                "subsectors": {
                  $map: {
                    input: "$$sector.subsectorIds",
                    as: "subsectorId",
                    in: {
                      $arrayElemAt: [
                        "$subsectorDetails.name",
                        { $indexOfArray: ["$subsectorDetails._id", "$$subsectorId"] }
                      ]
                    }
                  }
                }
              }
            }
          }
        }
      },
      {
        $project: {
          "code": 1,
          "title": 1,
          "description": 1,
          "level": 1,
          "campusName": 1,
          "duration": 1,
          "pageURL": 1,
          "applyURL": 1,
          "enquiryURL": 1,          
          "sectors": 1,
        }
      },
      { $sort: { 'addedBy.date': -1 } },
    ]
    let entries = await Course.aggregate(pipeline);
    entries = entries.reduce((groups, course) => {
      const {
        code,
        title,
        description,
        level,
        campusName,
        duration,
        pageURL,
        applyURL,
        enquiryURL,
        sectors,
      } = course

      let foundCourse = groups.find(
        group => 
          group.code === code && 
          group.title === title && 
          group.description === description && 
          group.level === level && 
          group.duration === duration || "" &&
          group.pageURL === pageURL &&
          group.applyURL === applyURL &&
          group.enquiryURL === enquiryURL && 
          group.sectors === sectors 
      )
      if(!foundCourse){
        foundCourse = {
          code,
          title,
          description: description.replace(/\n/g, ";"),
          level,
          duration: duration || "",
          pageURL,
          applyURL,
          enquiryURL,
          campus: "",
        }
        groups.push(foundCourse)
      }
      sectors.map((sector, index) => {
        foundCourse[`sector${index+1}`] = sector.name
        foundCourse[`subsector${index+1}`] = "";
        sector.subsectors.map((subsector, index1) => {
          foundCourse[`subsector${index+1}`] += (foundCourse[`subsector${index+1}`].length > 0 ? "," : "") + subsector
        })
      })
      foundCourse.campus += (foundCourse.campus ? "," : "") + campusName;
      // foundCourse.campus.push({
      //   name: campusName,
      //   pageURL,
      //   applyURL,
      //   enquiryURL,
      // })
      return groups
    }, [])

    if (!entries.length) return messageResponse(NOT_FOUND, "Courses", false, 404, [], res)

    return messageResponse(null, "", true, 200, entries, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.exportCourses = exportCourses;

const assignHTTPS = (URL) => {
  return ( URL.startsWith("https://") || URL.startsWith("http://") )? URL : `https://${URL}`
}

const deleteCoursesByCampus = async(req, res, next) => {
  try {
    const campusIds = req.body;
    let objCampusIds = [];
    campusIds.forEach(cmp => objCampusIds.push(new mongoose.Types.ObjectId(cmp)));
    const deletedCourses = await Course.deleteMany({campusId:objCampusIds});
    if(deletedCourses.acknowledged) {
      res.status(200).json({success:true, msg:`${deletedCourses.deletedCount} course(s) deleted successfully`});
    }
    else {
      return res.status(500).json({ success: false, msg: "Something went wrong, please try again later" });  
    }
  }
  catch(error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later", error:error.message });
  }
}
module.exports.deleteCoursesByCampus = deleteCoursesByCampus;