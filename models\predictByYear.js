const mongoose = require("mongoose");

const PredictByYearSchema = new mongoose.Schema({
  year: Number,
  employment: { type: Number },
  careerId: { type: mongoose.Schema.Types.ObjectId, ref: "career" },

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.PredictByYearSchema = PredictByYearSchema;

class PredictByYear extends mongoose.Model {
  
}

mongoose.model(PredictByYear, PredictByYearSchema, "predictByYear");

module.exports.PredictByYear = PredictByYear;