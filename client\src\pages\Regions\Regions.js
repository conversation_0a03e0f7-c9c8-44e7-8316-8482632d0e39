import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import { Container, Link, Typography } from '@mui/material';
// @mui
import { useNavigate, useParams } from 'react-router-dom';
import { isEmpty } from 'lodash';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import CollegeForm from './CollegeForm';
import useLoading from '../../hooks/useLoading';
import { APP_ROUTER_BASE_URL, allowedRoles } from '../../utils';
import useAuth from '../../hooks/useAuth';
import { setSnackbar } from '../../Redux/snackbarSlice';
import FilterForm from '../FilterForm';
import Label from '../../components/Label/Label';
import EditRegion from './EditRegion';
import { getRegions, removeRegion } from './regionsSlice';

// ----------------------------------------------------------------------

export const COLLEGE_TABLE_HEAD = [
  { id: 'name', label: 'Region', alignRight: false },
  //   { id: 'groupName', label: "Group", alignRight: false },
  { id: 'slug', label: 'Slug', alignRight: false },
  //   { id: 'syncCourse', label: "", alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

const slugComponent = (row) =>
  row.slug && (
    <Link underline="hover" href={`${window.location.origin}/region/${row.slug}/`} target="_blank" rel="noopener">
      {row.slug}
    </Link>
  );

export const renderCollegeCells = ['name', slugComponent];

const Regions = () => {
  const { role } = useAuth();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  //   const [Colleges, setColleges] = useState([]);
  const [Regions, setRegions] = useState([]);
  const regionsState = useSelector((state) => state.regions);
  // const colleges = useSelector(state => state.colleges.colleges)
  const groups = useSelector((state) => state.collegeGroups.groups);
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [selectedColleges, setSelectedColleges] = useState([]);
  const [openFilter, setOpenFilter] = useState(false);
  // const [Users, setUsers] = useState([]);
  const [Groups, setGroups] = useState([]);
  // const {id} = useParams()

  // const colleges = useSelector(state => state.colleges.colleges)
  const { regions, status, error } = regionsState;

  const loading = useLoading(status);
  useEffect(() => {
    if (!isEmpty(regions)) {
      setRegions(regions);
    }
  }, [regions]);

  useEffect(() => {
    dispatch(getRegions());
  }, []);

  const deleteTitle = 'Delete Region?';
  const deleteDescription = 'Are you sure you want to delete this Region ?';

  //   const handleFilterSearch = (event) => {
  //     const filtereColleges = !!Colleges ? Colleges.filter(college => college.name?.toLowerCase().includes(event.target.value.toLowerCase()) ||
  //       college.groupName?.toLowerCase().includes(event.target.value.toLowerCase())) : []
  //     return filtereColleges
  //   };

  const handleFilterSearch = (event) => {
    return Regions.filter((region) => region.name?.toLowerCase().includes(event.target.value.toLowerCase()));
  };

  const handleRegionDelete = (Region, handleOpenBackdrop, handleCloseBackdrop) => {
    const data = {
      id: Region._id,
    };
    try {
      handleOpenBackdrop();
      dispatch(removeRegion(data))
        .then((response) => {
          if (response?.payload?.success) {
            dispatch(
              setSnackbar({
                snackbarOpen: true,
                snackbarType: 'success',
                snackbarMessage: 'Region Deleted Succesfully',
              })
            );
          } else {
            const errorMessage = response?.payload?.response?.data?.message;
            dispatch(
              setSnackbar({
                snackbarOpen: true,
                snackbarType: 'error',
                snackbarMessage: errorMessage || 'something went wrong',
              })
            );
          }
        })
        .finally(() => {
          handleCloseBackdrop();
        });
    } catch (error) {
      console.log('delete error', error);
    }
  };

  const editRegion = (region) => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/regions/edit/${region?._id}`);
  };

  useEffect(() => {
    if (openFilter) {
      if (role === '1' || role === '2') {
        dispatch(getCollegeGroups());
      }
      if (role === '1' || role === '2' || role === '3') {
        dispatch(getRegions());
      }
    }
  }, [openFilter]);

  useEffect(() => {
    if (!!groups) {
      setGroups([...groups]);
    }
  }, [groups]);
  const handleCloseFilter = () => {
    // setSelectedColleges([])
    // setSelectedGroups([])
    // setUsers(Users)
    setOpenFilter(false);
  };

  const handleFilter = () => {
    setOpenFilter(true);
  };

  // const handleOpen = () => setOpenModel(true);
  const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/regions/add`);

  return (
    <>
      <Helmet>
        <title> Regions | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        <PopUp open={openFilter} onClose={handleCloseFilter} title={'Filter'}>
          {/* <FilterForm
            selectedGroups={selectedGroups}
            setSelectedGroups={setSelectedGroups}
            openModel={openFilter}
            setOpenModel={setOpenFilter}
            // setColleges={setColleges}
            Groups={Groups}
            // users={users}
            // Colleges={Colleges}
            selectedColleges={selectedColleges}
            setSelectedColleges={setSelectedColleges}
            filterName={'colleges'}
          /> */}
        </PopUp>
        <Typography variant="h4" gutterBottom mb={3}>
          Regions
        </Typography>
        <DataTable
          loading={loading}
          deleteTitle={deleteTitle}
          deleteDescription={deleteDescription}
          TableHead={COLLEGE_TABLE_HEAD}
          //   TableData={Colleges}
          TableData={Regions}
          filterSearch={handleFilterSearch}
          buttonText={allowedRoles(role, ['1', '2']) && 'New Region'}
          disableDelete={role !== '1' && role !== '2'}
          buttonHandler={handleOpen}
          handleEdit={editRegion}
          renderCells={renderCollegeCells}
          handleDelete={handleRegionDelete}
          // filter="true"
          handleFilter={handleFilter}
          pagination="true"
          rowsPerPageProp={10}
        />
      </Container>
    </>
  );
};

export default Regions;
