import { Box, Button, Fade, FormControl, Grid, Stack } from '@mui/material';
import { useFormik } from 'formik';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import PhoneInput from '../../components/PhoneInput';
import SelectComponent from '../../components/SelectComponent';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { collegeValidationSchema } from '../../utils/validationSchemas';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getColleges, postCollege } from './collegesSlice';

const CollegeForm = ({ Colleges, Groups, setOpenModel, openModel }) => {
    const dispatch = useDispatch()
    const formik = useFormik({
        initialValues: {
            name: '',
            groupName: '',
            collegeAddress: '',
            collegeWebsiteAddress: '',
            collegeEmail: '',
            collegeTelNumber: ''
        },
        onSubmit: (values) => {
            if (formik.values.groupName) {
                // const ID = Colleges[Colleges.length - 1].id + 1
                // dispatch(addCollege({ id: ID, ...values }))
                const data = {
                    name: values.name,
                    collegeGroupId: values.groupName,
                    address1: values.collegeAddress,
                    contactNumber: values.collegeTelNumber,
                    email: values.collegeEmail
                }
                dispatch(postCollege(data))
                handleClose()
            }
        },
        validationSchema: collegeValidationSchema
    })
    
    const handleClose = () => {
        setOpenModel(false)
    }
    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            collegeTelNumber: newValue
        })
    }


    return (
        <Fade in={openModel}>
            <Box py={2}>
                <form style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}
                    onSubmit={formik.handleSubmit}
                >
                    <Grid container gap={2} >
                        <Grid item xs={12} md={5.8} >
                            <FormControl sx={{ minWidth: '100%' }}>
                                <SelectComponent
                                    menuName={"Name"}
                                    menuValue={"id"}
                                    menuItems={Groups}
                                    name='groupName'
                                    labelId="group-label"
                                    onBlur={formik.handleBlur}
                                    value={formik.values.groupName}
                                    label="Group *"
                                    inputLabel="Group"
                                    onChange={formik.handleChange}
                                    error={formik.touched.groupName && Boolean(formik.errors.groupName)}
                                    labelColor={formik.touched.groupName && formik.errors.groupName && 'error'}
                                    labelError={formik.touched.groupName && formik.errors.groupName}
                                    helperText={formik.touched.groupName && formik.errors.groupName}
                                />
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} md={5.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='name'
                                label="College Name"
                                onBlur={formik.handleBlur}
                                value={formik.values.name}
                                onChange={formik.handleChange}
                                error={formik.touched.name && Boolean(formik.errors.name)}
                                helperText={formik.touched.name && formik.errors.name}
                            />
                        </Grid>

                        <Grid item xs={11.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='collegeAddress'
                                label="College Address"
                                onBlur={formik.handleBlur}
                                value={formik.values.collegeAddress}
                                onChange={formik.handleChange}
                                error={formik.touched.collegeAddress && Boolean(formik.errors.collegeAddress)}
                                helperText={formik.touched.collegeAddress && formik.errors.collegeAddress}
                            />
                        </Grid>
                        <Grid item xs={11.8} >
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='collegeWebsiteAddress'
                                label="Website Address"
                                onBlur={formik.handleBlur}
                                value={formik.values.collegeWebsiteAddress}
                                onChange={formik.handleChange}
                                error={formik.touched.collegeWebsiteAddress && Boolean(formik.errors.collegeWebsiteAddress)}
                                helperText={formik.touched.collegeWebsiteAddress && formik.errors.collegeWebsiteAddress}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='collegeEmail'
                                label="Email"
                                onBlur={formik.handleBlur}
                                value={formik.values.collegeEmail}
                                onChange={formik.handleChange}
                                error={formik.touched.collegeEmail && Boolean(formik.errors.collegeEmail)}
                                helperText={formik.touched.collegeEmail && formik.errors.collegeEmail}
                            />
                        </Grid>
                        <Grid item xs={12} md={5.8}>
                            <PhoneInput
                                sx={{ width: "100%" }}
                                value={formik.values.collegeTelNumber}
                                name='collegeTelNumber'
                                label="Telephone Number"
                                defaultCountry="GB"
                                onChange={handlePhoneChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.collegeTelNumber && Boolean(formik.errors.collegeTelNumber)}
                                helperText={formik.touched.collegeTelNumber && formik.errors.collegeTelNumber}
                            />
                        </Grid>
                    </Grid>
                    <Stack direction="row" justifyContent="flex-end" >
                        <Button
                            type='submit'
                            variant='contained'
                            sx={{ width: '10%', m: 1, mt: 2 }}
                        >
                            Add
                        </Button>
                    </Stack>
                </form>
            </Box>
        </Fade >
    )
}

export default CollegeForm