import React, { useState } from 'react';
import {
    List, ListItem, ListItemText, ListItemSecondaryAction, IconButton, Collapse, Typography, Box, Button
} from '@mui/material';
import { ExpandLess, ExpandMore, Edit, Delete } from '@mui/icons-material';

const NestedCareersList = ({ data, onEdit, onDelete }) => {
    const [openBroad, setOpenBroad] = useState({});
    const [openCareer, setOpenCareer] = useState({});

    const handleToggleBroad = (id) => {
        setOpenBroad(prev => ({ ...prev, [id]: !prev[id] }));
    };

    const handleToggleCareer = (id) => {
        setOpenCareer(prev => ({ ...prev, [id]: !prev[id] }));
    };

    return (
        <List>
            {data.map(broad => (
                <React.Fragment key={broad._id}>
                    <ListItem button onClick={() => handleToggleBroad(broad._id)}>
                        <ListItemText
                            primary={<Typography variant="h6">{broad.title}</Typography>}
                        />
                        <ListItemSecondaryAction>
                            <IconButton edge="end" onClick={() => onEdit(broad)}><Edit /></IconButton>
                            <IconButton edge="end" color="error" onClick={() => onDelete(broad)}><Delete /></IconButton>
                            <IconButton edge="end" onClick={() => handleToggleBroad(broad._id)}>
                                {openBroad[broad._id] ? <ExpandLess /> : <ExpandMore />}
                            </IconButton>
                        </ListItemSecondaryAction>
                    </ListItem>
                    <Collapse in={openBroad[broad._id]} timeout="auto" unmountOnExit>
                        <Box sx={{ pl: 4 }}>
                            <List disablePadding>
                                {(broad.careers || []).map(career => (
                                    <React.Fragment key={career._id}>
                                        <ListItem button onClick={() => handleToggleCareer(career._id)}>
                                            <ListItemText
                                                primary={<Typography variant="subtitle1">{career.title}</Typography>}
                                            />
                                            <ListItemSecondaryAction>
                                                <IconButton edge="end" onClick={() => onEdit(career)}><Edit /></IconButton>
                                                <IconButton edge="end" color="error" onClick={() => onDelete(career)}><Delete /></IconButton>
                                                <IconButton edge="end" onClick={() => handleToggleCareer(career._id)}>
                                                    {openCareer[career._id] ? <ExpandLess /> : <ExpandMore />}
                                                </IconButton>
                                            </ListItemSecondaryAction>
                                        </ListItem>
                                        <Collapse in={openCareer[career._id]} timeout="auto" unmountOnExit>
                                            <Box sx={{ pl: 4 }}>
                                                <List disablePadding>
                                                    {(career.specialisedRoles || []).map(role => (
                                                        <ListItem key={role._id}>
                                                            <ListItemText
                                                                primary={<Typography variant="body1">{role.title}</Typography>}
                                                            />
                                                            <ListItemSecondaryAction>
                                                                <IconButton edge="end" onClick={() => onEdit(role)}><Edit /></IconButton>
                                                                <IconButton edge="end" color="error" onClick={() => onDelete(role)}><Delete /></IconButton>
                                                            </ListItemSecondaryAction>
                                                        </ListItem>
                                                    ))}
                                                </List>
                                            </Box>
                                        </Collapse>
                                    </React.Fragment>
                                ))}
                            </List>
                        </Box>
                    </Collapse>
                </React.Fragment>
            ))}
        </List>
    );
};

export default NestedCareersList