import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
    Box,
    Button,
    Card,
    Container,
    FormControl,
    Grid,
    MenuItem,
    Stack,
    Typography
} from '@mui/material';
// @mui
import { useFormik } from 'formik';
import { LoadingButton } from '@mui/lab';
import SelectComponent from '../../components/SelectComponent';
import useLoading from '../../hooks/useLoading';
import { AddEditSubSectorsValidationSchema } from '../../utils/validationSchemas';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { CancelButton } from '../../utils/cssStyles';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { getSubSectors, postSubSector, removeSubSector, updateSubSector } from './SubSectorSlice';
import { getSectors } from '../Sector/SectorSlice';

// ----------------------------------------------------------------------
export const SUBSECTORS_TABLE_HEAD = [
    { id: 'name', label: 'SubSector', alignRight: false },
    { id: 'sectorName', label: 'Sector', alignRight: false },
    { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderSubSectorCell = ['name', 'sectorName'];

const SubSector = () => {
    const dispatch = useDispatch()
    const sectors = useSelector(state => state.sectors.sectors)
    const subSectorsState = useSelector(state => state.subSectors)
    const [Sectors, setSectors] = useState([]);
    const [SubSectors, setSubSectors] = useState([]);
    const [loading, setLoading] = useState(false)
    const [editSectorDetails, setEditSectorDetails] = useState('');
    const { subSectors, status, error } = subSectorsState
    const dataLoading = useLoading(status)

    const subSectorformik = useFormik({
        initialValues: {
            name: '',
            sector: '',
        },
        onSubmit: (values, { resetForm }) => {
            setLoading(true)
            let sectorName = '';
            let data = {
                name: values.name,
                sectorId: values.sector,
                sectorName: Sectors.map((sector) => {
                    if (sector._id === values.sector) {
                        sectorName = sector.name
                        return sectorName
                    }
                    return ''
                }).toString()
            }
            data.sectorName = sectorName;
            if (editSectorDetails) {
                data = {
                    ...data,
                    id: editSectorDetails._id ? editSectorDetails._id : editSectorDetails.id
                }
            }
            dispatch(editSectorDetails ? updateSubSector(data) : postSubSector(data)).then(res => {
                if (res?.payload?.success || res?.payload?.data?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: `Succesfully ${editSectorDetails ? 'Updated' : 'Added'} Sub-Sector`
                    }))
                    if (editSectorDetails) {
                        const SubSectorsData = SubSectors.filter((subSector) => {
                            return subSector._id !== editSectorDetails._id
                        })
                        SubSectorsData.push(data);
                        setSubSectors(SubSectorsData);
                    }
                }
                const error = res?.payload?.response?.data
                if (error?.success === false) {
                    console.log(error?.msg)
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error?.msg || "Something went wrong!"
                    }))
                }
            }).finally(() => {
                setLoading(false)
                setEditSectorDetails('');
            })
            resetForm({ values: '' })
        },
        validationSchema: AddEditSubSectorsValidationSchema
    })

    useEffect(() => {
        dispatch(getSubSectors());
        dispatch(getSectors());
    }, [])
    useEffect(() => {
        if (subSectors && subSectors.length > 0) {
            setSubSectors([...subSectors])
        }
    }, [subSectors])

    useEffect(() => {
        setSectors(sectors);
    }, [sectors])

    const handleFilterSearch = (event) => {
        const filteredSector = SubSectors?.filter(subSector => subSector.name?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            subSector.sectorName?.toLowerCase().includes(event.target.value.toLowerCase()))
        return filteredSector
    };

    const handleDeleteSubSector = (subSector,handleOpenBackdrop, handleCloseBackdrop) => {
        handleOpenBackdrop();
        const data = {
            id: subSector._id ? subSector._id : subSector.id
        }
        dispatch(removeSubSector(data)).then(res => {
            if (res?.payload?.success) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Sub-Sector Deleted Succesfully"
                }))
            } else {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: "Something went wrong"
                }))
                console.log('Something went wrong!!', res)
            }
        }).finally(()=>{
            handleCloseBackdrop();
        })
    }

    const editSector = (sector) => {
        setEditSectorDetails(sector);
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }

    const handleCancel = () => {
        setEditSectorDetails('');
        subSectorformik.setValues({
            name: '',
            sector: ''
        })
    }

    useEffect(() => {
        subSectorformik.setValues({
            ...subSectorformik.values,
            name: editSectorDetails?.name,
            sector: editSectorDetails.sectorId
        })
    }, [editSectorDetails])

    return (
        <>
            <Helmet>
                <title> Sub-Sector | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Grid container justifyContent={'space-between'}>
                    <Grid item lg={7}>
                        <Typography variant="h4" gutterBottom mb={3}>
                            Sub-Sector
                        </Typography>
                        <DataTable
                            deleteDescription={"Are you sure want to delete this sub sector ?"}
                            deleteTitle={"Delete Sub Sector ?"}
                            loading={dataLoading}
                            TableHead={SUBSECTORS_TABLE_HEAD}
                            TableData={SubSectors}
                            filterSearch={handleFilterSearch}
                            searchLable={"Search..."}
                            handleEdit={editSector}
                            renderCells={renderSubSectorCell}
                            handleDelete={handleDeleteSubSector}
                            pagination
                            rowsPerPageProp={5}
                        />
                    </Grid>
                    <Grid item lg={4}>
                        <Typography variant="h4" gutterBottom mb={3}>
                            {editSectorDetails ? 'Edit Sub-Sector' : 'Add Sub-Sector'}
                        </Typography>
                        <Card>
                            <Box sx={{ p: 4 }}>
                                <form onSubmit={subSectorformik.handleSubmit}>
                                    <TextFIeldComponent
                                        sx={{ width: '100%' }}
                                        name='name'
                                        label="Name"
                                        onBlur={subSectorformik.handleBlur}
                                        value={subSectorformik.values.name}
                                        onChange={subSectorformik.handleChange}
                                        error={subSectorformik.touched.name && Boolean(subSectorformik.errors.name)}
                                        helperText={subSectorformik.touched.name && subSectorformik.errors.name}
                                    />
                                    <FormControl sx={{ marginTop: '20px', width: '100%' }}>
                                        <SelectComponent
                                            menuName={"name"}
                                            menuValue={"_id"}
                                            labelId="sector-label"
                                            label="Sector *"
                                            inputLabel="Sector"
                                            disableNone
                                            menuItems={Sectors}
                                            sx={{ width: '100%' }}
                                            name='sector'
                                            onBlur={subSectorformik.handleBlur}
                                            defaultValue={subSectorformik.values?.sector}
                                            value={subSectorformik.values?.sector}
                                            onChange={subSectorformik.handleChange}
                                            labelColor={subSectorformik.touched.sector && subSectorformik.errors.sector && 'error'}
                                            labelError={subSectorformik.touched.sector && subSectorformik.errors.sector}
                                            error={subSectorformik.touched.sector && Boolean(subSectorformik.errors.sector)}
                                            helperText={subSectorformik.touched.sector && subSectorformik.errors.sector}
                                        />
                                    </FormControl>
                                    <Stack direction="row" justifyContent="flex-end" >
                                        {editSectorDetails ?
                                            <Button
                                                type='button'
                                                variant='contained'
                                                sx={CancelButton}
                                                color='error'
                                                onClick={() => handleCancel()}
                                            >
                                                Cancel
                                            </Button> : ''
                                        }

                                        <LoadingButton
                                            loading={loading}
                                            type='submit'
                                            variant='contained'
                                            sx={{ width: '10%', m: 1, mt: 2 }}
                                        >
                                            {editSectorDetails ? 'Save' : 'Add'}
                                        </LoadingButton>
                                    </Stack>
                                </form>
                            </Box>
                        </Card>
                    </Grid>
                </Grid>
            </Container>
        </>
    )
}

export default SubSector
