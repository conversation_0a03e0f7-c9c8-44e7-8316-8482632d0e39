import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, Container, Grid, LinearProgress, MenuItem, Stack, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import PhoneInput from '../../components/PhoneInput';
import SelectField from '../../components/SelectedField';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import useAuth from '../../hooks/useAuth';
import { APP_ROUTER_BASE_URL } from '../../utils';
import axiosInstance from '../../utils/axiosInstance';
import { CancelButton, formButton } from '../../utils/cssStyles';
import { groupValidationSchema } from '../../utils/validationSchemas';
import { getAvailableUsers, getUsers } from '../Userspage/usersSlice';
import { editCollegeGroup } from './collegeGroupsSlice';

const AddCollegeGroup = () => {
    const [isLoading, setIsLoading] = useState(false)
    const [loading, setLoading] = useState(false)
    const [AvailableUsers, setAvailableUsers] = useState([])
    const params = useParams()
    const { role } = useAuth()
    const { users, availableUsers } = useSelector(state => state.users)
    useEffect(() => {
        dispatch(getUsers())
    }, [])
    // useEffect(() => {
    //     setAvailableUsers([...AvailableUsers,...availableUsers])
    // }, [availableUsers])
    const formik = useFormik({
        initialValues: {
            Name: '',
            groupAddress: '',
            groupTelNumber: '',
            groupWebsiteAddress: '',
            groupEmail: '',
            groupCity: '',
            groupState: '',
            groupZip: '',
            admin: '',
            id: ''
        },
        onSubmit: (values) => {
            setIsLoading(true)
            try {
                const group = {
                    name: values.Name,
                    // other fields will be added here
                    // "address1", "address2", "city", "state", "zip", "country", "contactNumber", 
                    address1: values.groupAddress,
                    contactNumber: values.groupTelNumber,
                    website: values.groupWebsiteAddress,
                    email: values.groupEmail,
                    city: values.groupCity,
                    state: values.groupState,
                    zip: values.groupZip,
                    // adminUserId: values.admin,
                    id: values.id
                }
                if(values?.admin){
                    group.adminUserId = values.admin
                }
                dispatch(editCollegeGroup(group)).then(res => {
                    if (res?.payload?.success) {
                        dispatch(setSnackbar({
                            snackbarOpen: true,
                            snackbarType: 'success',
                            snackbarMessage: "Succesfully updated College Group"
                        }))
                        navigate(`${APP_ROUTER_BASE_URL}dashboard/collegegroups`)
                    } else {
                        const errorMessage = res?.payload?.response?.data?.msg
                        dispatch(setSnackbar({
                            snackbarOpen: true,
                            snackbarType: 'error',
                            snackbarMessage: errorMessage || "Something Went Wrong"
                        }))

                    }
                }).finally(() => setIsLoading(false))
            } catch (error) {
                console.log("error", error)
            }

        },
        validationSchema: groupValidationSchema
    })
    useEffect(() => {
        const getCollegeGroup = async (id) => {
            setLoading(true)
            try {
                const response = await axiosInstance({
                    url: "collegeGroups/getByID",
                    method: "GET",
                    params: {
                        id
                    }
                })
              
                const groupDetails = response.data?.data;
                formik.setValues({
                    ...formik.values,
                    Name: groupDetails?.name,
                    groupAddress: groupDetails?.address1,
                    groupTelNumber: groupDetails?.contactNumber,
                    groupWebsiteAddress: groupDetails?.website,
                    groupEmail: groupDetails?.email,
                    groupCity: groupDetails?.city,
                    groupState: groupDetails?.state,
                    groupZip: groupDetails?.zip,
                    admin: groupDetails?.adminUserId,
                    id: groupDetails?._id
                })
                if (role !== "1") {
                    // setAvailableUsers([...AvailableUsers, {
                    setAvailableUsers([{
                        _id: groupDetails?.adminUser?._id,
                        firstName: groupDetails?.adminUser?.firstName,
                        lastName: groupDetails?.adminUser?.lastName,
                    }])
                }
                const params = {
                    role: '2',
                    includeIds: groupDetails?.adminUserId
                }
                dispatch(getAvailableUsers(params))
                // localdata set
            } catch (error) {
                const errorMessage = get(error, 'response.data.message', 'Something went wrong')
                console.log("error get colleges ==>", errorMessage)
            } finally {
                setLoading(false)
            }
        }
        getCollegeGroup(params?.id)
    }, [])

    const dispatch = useDispatch()
    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            groupTelNumber: newValue
        })
    }
    const style = {
        p: 4,
    };
    const navigate = useNavigate()
    return (
        <Container maxWidth="lg">
            <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                Edit College Group
            </Typography>
            <>
                {loading ?
                    <LinearProgress /> :
                    <Card>
                        <Box sx={style} >
                            <form
                                onSubmit={formik.handleSubmit}>
                                <Grid container gap={2}>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='Name'
                                            label="Name"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.Name}
                                            onChange={formik.handleChange}
                                            error={formik.touched.Name && Boolean(formik.errors.Name)}
                                            helperText={formik.touched.Name && formik.errors.Name}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        {/* <PhoneInput
                                            sx={{ width: "100%" }}
                                            value={formik.values.groupTelNumber}
                                            name='groupTelNumber'
                                            label="Number"
                                            defaultCountry="GB"
                                            onChange={handlePhoneChange}
                                            onBlur={formik.handleBlur}
                                            error={formik.touched.groupTelNumber && Boolean(formik.errors.groupTelNumber)}
                                            helperText={formik.touched.groupTelNumber && formik.errors.groupTelNumber}
                                        /> */}
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupTelNumber'
                                            label="Number"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupTelNumber}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupTelNumber && Boolean(formik.errors.groupTelNumber)}
                                            helperText={formik.touched.groupTelNumber && formik.errors.groupTelNumber}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupEmail'
                                            label="Email"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupEmail}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupEmail && Boolean(formik.errors.groupEmail)}
                                            helperText={formik.touched.groupEmail && formik.errors.groupEmail}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupCity'
                                            label="City"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupCity}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupCity && Boolean(formik.errors.groupCity)}
                                            helperText={formik.touched.groupCity && formik.errors.groupCity}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupState'
                                            label="Country"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupState}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupState && Boolean(formik.errors.groupState)}
                                            helperText={formik.touched.groupState && formik.errors.groupState}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupZip'
                                            label="Post Code"
                                            // type="number"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupZip}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupZip && Boolean(formik.errors.groupZip)}
                                            helperText={formik.touched.groupZip && formik.errors.groupZip}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupAddress'
                                            label="Address"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupAddress}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupAddress && Boolean(formik.errors.groupAddress)}
                                            helperText={formik.touched.groupAddress && formik.errors.groupAddress}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='groupWebsiteAddress'
                                            label="Website Address"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.groupWebsiteAddress}
                                            onChange={formik.handleChange}
                                            error={formik.touched.groupWebsiteAddress && Boolean(formik.errors.groupWebsiteAddress)}
                                            helperText={formik.touched.groupWebsiteAddress && formik.errors.groupWebsiteAddress}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} >
                                        <SelectField
                                            sx={{ width: '100%' }}
                                            name='admin'
                                            label="Admin"
                                            onBlur={formik.handleBlur}
                                            defaultValue={formik.values.admin}
                                            value={formik.values.admin}
                                            onChange={formik.handleChange}
                                            disabled={role !== "1"}
                                            error={formik.touched.admin && Boolean(formik.errors.admin)}
                                            helperText={formik.touched.admin && formik.errors.admin}
                                        >
                                            <MenuItem value="">
                                                <em>None</em>
                                            </MenuItem>
                                            {(role !== "1" ? AvailableUsers : availableUsers)?.map(user => {
                                                return (<MenuItem value={user?._id}>
                                                    {`${user?.firstName} ${user?.lastName}`}
                                                </MenuItem>)
                                            })}
                                        </SelectField>
                                    </Grid>

                                </Grid>
                                <Stack
                                    direction="row"
                                    justifyContent="flex-end"
                                    mt={2}
                                >
                                    <Button
                                        type='button'
                                        variant='contained'
                                        sx={CancelButton}
                                        color='error'
                                        onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/collegegroups`)}

                                    >
                                        Cancel
                                    </Button>
                                    <LoadingButton
                                        loading={isLoading}
                                        type='submit'
                                        variant='contained'
                                        sx={formButton}
                                    >
                                        Update
                                    </LoadingButton>
                                </Stack>
                            </form>
                        </Box>
                    </Card>}
            </>
        </Container>
    )
}

export default AddCollegeGroup