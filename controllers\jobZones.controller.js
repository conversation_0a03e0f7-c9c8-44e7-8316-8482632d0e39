const { default: mongoose } = require("mongoose");
const { getAddedBy } = require('../tools/database');
const commonHelper = require("../helpers/commonHelper");
const OnetServices = require("../helpers/onetServices");
const { JobZone } = require("../models/jobZone");

const createJobZones = async(req, res, next) => {
  try {

    if (req.body.deleteAll) {
      const deletedJobZones = await JobZone.deleteMany({});
    }

    let onet = new OnetServices();
    const jobZones = await onet.getJobZones();

    let newJobZones;
    if (jobZones && jobZones.data) {
      const addedBy = getAddedBy(req);
      jobZones.data.job_zone.forEach(jobZone => {
        jobZone["addedBy"] = addedBy;
      });
      newJobZones = await JobZone.create(jobZones.data.job_zone);
    }
    else {
      return res.status(500).json({ success: false, msg: "Problem in get data, please try again later" });
    }

    res.status(200).json({ success: true, data: newJobZones });
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later" });
  }
};
module.exports.createJobZones = createJobZones;