const OpenAI = require('openai');
const fs = require('fs');
const path = require('path');

/**
 * OpenAI Fine-Tuning Service
 * Manages fine-tuning jobs, model deployment, and version tracking
 */
class OpenAIFineTuningService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    });
    
    this.baseModel = 'gpt-4o-mini-2024-07-18'; // Cost-effective model for fine-tuning
    this.fineTunedModels = new Map(); // Track deployed models
  }

  /**
   * Create a fine-tuning job
   * @param {Array} trainingData - Training examples in JSONL format
   * @param {Array} validationData - Validation examples (optional)
   * @param {Object} options - Fine-tuning options
   * @returns {Promise<Object>} - Fine-tuning job details
   */
  async createFineTuningJob(trainingData, validationData = null, options = {}) {
    const {
      suffix = 'horizon-chatbot',
      hyperparameters = {},
      integrations = []
    } = options;

    console.log('🚀 Creating fine-tuning job...');
    console.log(`📊 Training examples: ${trainingData.length}`);
    console.log(`📊 Validation examples: ${validationData ? validationData.length : 0}`);

    try {
      // Upload training file
      console.log('📤 Uploading training data...');
      const trainingFileId = await this.uploadTrainingFile(trainingData, 'training');
      
      // Upload validation file if provided
      let validationFileId = null;
      if (validationData && validationData.length > 0) {
        console.log('📤 Uploading validation data...');
        validationFileId = await this.uploadTrainingFile(validationData, 'validation');
      }

      // Create fine-tuning job
      const fineTuningJob = await this.openai.fineTuning.jobs.create({
        training_file: trainingFileId,
        validation_file: validationFileId,
        model: this.baseModel,
        suffix: suffix,
        hyperparameters: {
          n_epochs: hyperparameters.epochs || 3,
          batch_size: hyperparameters.batchSize || 'auto',
          learning_rate_multiplier: hyperparameters.learningRate || 'auto'
        },
        integrations: integrations
      });

      console.log('✅ Fine-tuning job created successfully');
      console.log(`🆔 Job ID: ${fineTuningJob.id}`);
      console.log(`📊 Status: ${fineTuningJob.status}`);

      return {
        jobId: fineTuningJob.id,
        status: fineTuningJob.status,
        model: fineTuningJob.model,
        trainingFileId: trainingFileId,
        validationFileId: validationFileId,
        createdAt: new Date(fineTuningJob.created_at * 1000),
        hyperparameters: fineTuningJob.hyperparameters
      };

    } catch (error) {
      console.error('❌ Error creating fine-tuning job:', error);
      throw error;
    }
  }

  /**
   * Upload training data file to OpenAI
   * @param {Array} data - Training data array
   * @param {string} purpose - File purpose ('training' or 'validation')
   * @returns {Promise<string>} - File ID
   */
  async uploadTrainingFile(data, purpose = 'fine-tune') {
    try {
      // Convert data to JSONL format
      const jsonlData = data.map(example => JSON.stringify(example)).join('\n');
      
      // Create temporary file
      const tempDir = path.join(__dirname, '../temp');
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }
      
      const fileName = `${purpose}_${Date.now()}.jsonl`;
      const filePath = path.join(tempDir, fileName);
      
      // Write data to file
      fs.writeFileSync(filePath, jsonlData, 'utf8');
      
      // Upload file to OpenAI
      const file = await this.openai.files.create({
        file: fs.createReadStream(filePath),
        purpose: 'fine-tune'
      });
      
      // Clean up temporary file
      fs.unlinkSync(filePath);
      
      console.log(`✅ ${purpose} file uploaded: ${file.id}`);
      return file.id;
      
    } catch (error) {
      console.error(`❌ Error uploading ${purpose} file:`, error);
      throw error;
    }
  }

  /**
   * Get fine-tuning job status
   * @param {string} jobId - Fine-tuning job ID
   * @returns {Promise<Object>} - Job status and details
   */
  async getJobStatus(jobId) {
    try {
      const job = await this.openai.fineTuning.jobs.retrieve(jobId);
      return {
        id: job.id,
        status: job.status,
        model: job.model,
        fineTunedModel: job.fine_tuned_model,
        createdAt: new Date(job.created_at * 1000),
        finishedAt: job.finished_at ? new Date(job.finished_at * 1000) : null,
        trainingFile: job.training_file,
        validationFile: job.validation_file,
        resultFiles: job.result_files,
        trainedTokens: job.trained_tokens,
        error: job.error
      };
    } catch (error) {
      console.error('❌ Error getting job status:', error);
      throw error;
    }
  }

  /**
   * List all fine-tuning jobs
   * @param {number} limit - Maximum number of jobs to return
   * @returns {Promise<Array>} - List of fine-tuning jobs
   */
  async listJobs(limit = 20) {
    try {
      const jobs = await this.openai.fineTuning.jobs.list({ limit });
      
      return jobs.data.map(job => ({
        id: job.id,
        status: job.status,
        model: job.model,
        fineTunedModel: job.fine_tuned_model,
        createdAt: new Date(job.created_at * 1000),
        finishedAt: job.finished_at ? new Date(job.finished_at * 1000) : null
      }));
    } catch (error) {
      console.error('❌ Error listing jobs:', error);
      throw error;
    }
  }

  /**
   * Cancel a fine-tuning job
   * @param {string} jobId - Fine-tuning job ID
   * @returns {Promise<Object>} - Cancellation result
   */
  async cancelJob(jobId) {
    try {
      const result = await this.openai.fineTuning.jobs.cancel(jobId);
      console.log(`✅ Fine-tuning job ${jobId} cancelled`);
      return result;
    } catch (error) {
      console.error('❌ Error cancelling job:', error);
      throw error;
    }
  }

  /**
   * Deploy a fine-tuned model
   * @param {string} modelId - Fine-tuned model ID
   * @param {string} alias - Model alias for easy reference
   * @returns {Promise<boolean>} - Deployment success
   */
  async deployModel(modelId, alias = 'default') {
    try {
      // Test the model first
      const testResult = await this.testModel(modelId);
      if (!testResult.success) {
        throw new Error('Model test failed');
      }

      // Store model reference
      this.fineTunedModels.set(alias, {
        modelId: modelId,
        deployedAt: new Date(),
        status: 'active'
      });

      console.log(this.fineTunedModels)
      console.log(`✅ Model ${modelId} deployed with alias '${alias}'`);
      return true;
    } catch (error) {
      console.error('❌ Error deploying model:', error);
      throw error;
    }
  }

  /**
   * Test a fine-tuned model
   * @param {string} modelId - Fine-tuned model ID
   * @returns {Promise<Object>} - Test results
   */
  async testModel(modelId) {
    try {
      const testPrompt = {
        messages: [
          { role: "system", content: "You are a helpful educational assistant." },
          { role: "user", content: "Hello, can you help me with course information?" }
        ]
      };

      const response = await this.openai.chat.completions.create({
        model: modelId,
        messages: testPrompt.messages,
        max_tokens: 100,
        temperature: 0.7
      });

      const success = response.choices && response.choices.length > 0;
      
      return {
        success: success,
        response: success ? response.choices[0].message.content : null,
        usage: response.usage,
        modelId: modelId
      };
    } catch (error) {
      console.error('❌ Error testing model:', error);
      return {
        success: false,
        error: error.message,
        modelId: modelId
      };
    }
  }

  /**
   * Get deployed model by alias
   * @param {string} alias - Model alias
   * @returns {Object|null} - Model details or null
   */
  getDeployedModel(alias = 'default') {
    console.log(this.fineTunedModels)
    return this.fineTunedModels.get(alias) || null;
  }

  /**
   * List all deployed models
   * @returns {Array} - List of deployed models
   */
  listDeployedModels() {
    return Array.from(this.fineTunedModels.entries()).map(([alias, model]) => ({
      alias,
      ...model
    }));
  }

  /**
   * Remove a deployed model
   * @param {string} alias - Model alias
   * @returns {boolean} - Removal success
   */
  removeDeployedModel(alias) {
    return this.fineTunedModels.delete(alias);
  }

  /**
   * Get fine-tuning job events/logs
   * @param {string} jobId - Fine-tuning job ID
   * @returns {Promise<Array>} - Job events
   */
  async getJobEvents(jobId) {
    try {
      const events = await this.openai.fineTuning.jobs.listEvents(jobId);
      return events.data.map(event => ({
        id: event.id,
        createdAt: new Date(event.created_at * 1000),
        level: event.level,
        message: event.message,
        data: event.data
      }));
    } catch (error) {
      console.error('❌ Error getting job events:', error);
      throw error;
    }
  }
}

module.exports = OpenAIFineTuningService;
