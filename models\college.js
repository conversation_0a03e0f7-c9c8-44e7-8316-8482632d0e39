const mongoose = require("mongoose");
const { CollegeGroup, CollegeGroupSchema } = require("./collegeGroup");
const { User, UserSchema } = require("./user");

const CollegeSchema = new mongoose.Schema({
  name: String,
  slug: String,
  logo: String,
  primaryColor: String,
  secondaryColor: String,
  thirdColor: String,
  collegeGroupId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(CollegeGroup, CollegeGroupSchema) },
  address1: String,
  address2: String,
  city: String,
  state: String,
  zip: String,
  countryId: { type: mongoose.Schema.Types.ObjectId },
  contactNumber:String,
  website:String,
  // email: { type: String, unique: true, lowercase: true, trim: true },
  email: { type: String, lowercase: true, trim: true },
  adminUserId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(User, UserSchema) },
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
  cleverHubApiKey: String,
  permissions: {
    dashboard: { type: Boolean, default: false },
    campuses: { type: Boolean, default: false },
    users: { type: Boolean, default: false },
    courses: { type: Boolean, default: false },
  },
  image: String,
  description: String,
});

module.exports.CollegeSchema = CollegeSchema;

class College extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await College.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await College.create(entry);
    }
  }
}

mongoose.model(College, CollegeSchema, "colleges");

module.exports.College = College;

