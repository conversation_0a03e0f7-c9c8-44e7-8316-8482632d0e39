const mongoose = require("mongoose");

const UserRoles = {
  SUPER_ADMIN: 1,
  COLLEGE_GROUP_ADMIN: 2,
  COLLEGE_ADMIN: 3,
  CAMPUS_ADMIN: 4,
  NOT_AVAILABLE: 5,
};
module.exports.UserRoles = UserRoles;

const defaultEntries = [
  {
    _id: "5bc9c927c12696cf7414e92d",
    name: "<PERSON>",
    lastName: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    email: "<EMAIL>",
    password: "$2a$10$LqeH1Y61C4cTT5LrJLO3oOXu6uD.GeEEakB176YRlvPQAaqXAD8w6",
    role: 1,
    customerID: "5d4adde9fd1a797f3bee16d6",
  },
];
module.exports.defaultEntries = defaultEntries;

const UserSchema = mongoose.Schema({
  firstName: String,
  lastName: String,
  email: { type: String, required:true, unique: true, lowercase: true, trim: true },
  password: String,
  postcode: String,
  role: { type: Number, enum: UserRoles, default: UserRoles.NOT_AVAILABLE },
  adminUserId :{ type: mongoose.Schema.Types.ObjectId, ref:"user" },

  contactNumber:String,
  photo: String,

  collegeGroupIds : { type: [mongoose.Schema.Types.ObjectId], default: [], ref:"collegeGroup" },
  collegeIds : { type: [mongoose.Schema.Types.ObjectId], default: [], ref:"college" },
  campusIds : { type: [mongoose.Schema.Types.ObjectId], default: [], ref:"campus" },

  addedBy: Object,
  editedBy: Object,  
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },

//   resetPasswordToken: String,
//   resetPasswordExpires: String,
//   lastPasswordUpdate: Number,
//   usedPasswords: {type: Array, default: []},
});

module.exports.UserSchema = UserSchema;

class User extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await User.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await User.create(entry);
    }
  }
}

mongoose.model(User, UserSchema, "users");

module.exports.User = User;

