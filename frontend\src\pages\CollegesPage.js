import { Box, Container, Grid, Link, Typography } from '@mui/material';
import { useMemo } from 'react';
import { useParams } from 'react-router';
import AuthWrapper from 'src/components/AuthWrapper';
import StepperComponent from 'src/components/stepper/Stepper';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';

const CollegesPage = () => {
  const colleges = [
    {
      name: 'Heart of Worcestershire College (HoW College)',
      description: `We have a wide range of full and part-time courses in a variety of different subject areas; 
      from Engineering, Accounting and Marketing, to NVQs, ESOL and Teacher Training, the possibilities are endless.
  
      Whether you’re looking to progress your career, learn a new skill or try a new hobby, we have a course for you.`,
      location: 'Various',
      website: 'www.linktocollegewebsite.org.uk',
    },
    {
      name: 'Kidderminster College',
      description: `It's always possible to make a change. Whether you're considering a career shift, planning to rejoin the workforce, 
      or aiming to develop the skills to kickstart your business venture, Kidderminster College is here for you. 
      We offer various part-time courses tailored to empower adults like you in their educational journey. 
      Make a fresh start with Kidderminster College and embrace the opportunities that await you.`,
      location: 'Market Street, Kidderminster, DY10 1AB',
      website: 'www.linktocollegewebsite.org.uk',
    },
    {
      name: 'College group WCG',
      description: `From part-time leisure courses to professional qualifications including degrees, 
      we have something for everyone at WCG.
  
      We know that our range of lifelong learning programmes is essential to strengthening our communities and 
      making sure that local people can succeed in their personal goals and career aspirations.`,
      location: 'Various',
      website: 'www.linktocollegewebsite.org.uk',
    },
  ];
  const params = useParams();
  const isRegion = !!params?.rg_name;
  const stepperSteps = useMemo(() => ([
    {
      label: 'Your Skills',
      link: isRegion ? `/region/${params.rg_name}/upskill/skilldar`:`/${params.cg_name}/upskill/skilldar`
    },
    {
      label: 'Your Careers',
      link: isRegion ? `/region/${params.rg_name}/upskill/career-courses`:`/${params.cg_name}/upskill/career-courses`
    },
    {
      label: 'Colleges',
      link: isRegion ? `/region/${params.rg_name}/upskill/colleges`:`/${params.cg_name}/upskill/colleges`
    },
    {
      label: 'Your Region',
      link: isRegion ? `/region/${params.rg_name}/upskill/regional-info`:`/${params.cg_name}/upskill/regional-info`
    },
  ]), [params, isRegion])
  return (
    <>
          <AuthWrapper title="Colleges">
            <Box className="page-content-wrapper">
              <Container maxWidth="lg">
                <Box mb={2} className='content'>
                  <ThinkSkillsHeader />
                  <StepperComponent steps={stepperSteps} activeStep={2} noIcon />
                </Box>
                <Typography sx={{ fontWeight: '700', fontSize: '32px  !important' }} textAlign="start">
                  Colleges
                </Typography>
                <Typography sx={{ fontSize: '22px !important' }} textAlign="start">
                  We recommend the following Colleges
                </Typography>
                <Grid container spacing={4} mt={1}>
                  {colleges.map((college, index) => (
                    <Grid item xs={12} md={4} key={index}>
                      <Box sx={{ textAlign: 'center' }}>
                        {/* Image placeholder */}
                        <Box
                          sx={{
                            width: '100%',
                            height: 180,
                            bgcolor: '#ccc',
                            mb: 2,
                          }}
                        />

                        <Typography sx={{ fontSize: '22px !important', fontWeight: 700 }} gutterBottom>
                          {college.name}
                        </Typography>

                        <Typography
                          sx={{
                            fontSize: '16px !important',
                            fontWeight: 400,
                            textAlign: 'left',
                            whiteSpace: 'pre-line',
                          }}
                        >
                          {college.description}
                        </Typography>

                        <Typography
                          sx={{
                            fontSize: '16px !important',
                            fontWeight: 400,
                            textAlign: 'left',
                            whiteSpace: 'pre-line',
                            mt: 1,
                          }}
                        >
                          <strong>Location:</strong> {college.location}
                        </Typography>

                        <Link
                          href={`https://${college.website}`}
                          target="_blank"
                          underline="hover"
                          sx={{ display: 'block', mt: 1, textAlign: 'left' }}
                        >
                          {college.website}
                        </Link>
                      </Box>
                    </Grid>
                  ))}
                </Grid>
              </Container>
            </Box>
          </AuthWrapper>
        </>
        );
};

        export default CollegesPage;
