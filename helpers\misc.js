const fs = require("fs");

const {
  Types: { ObjectId },
} = require("mongoose");

const toObjectId = (entryId) => {
  if (Array.isArray(entryId)) {
    return entryId.map((entryId) => new ObjectId(entryId));
  }

  return new ObjectId(entryId);
};

const deleteFile = (filePath) => {
  try {
    if (filePath && fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  } catch (error) {
    console.error("Error deleting file:", error);
    // Handle the error as needed, e.g., log it or throw it
  }
};

module.exports = {
  toObjectId,
  deleteFile
};
