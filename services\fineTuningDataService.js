const mongoose = require('mongoose');

// Import all data models
const { Region } = require('../models/region');
const { CollegeGroup } = require('../models/collegeGroup');
const { College } = require('../models/college');
const { Campus } = require('../models/campus');
const { Course } = require('../models/course');
const { Career } = require('../models/career');
const { LMISkillAbility } = require('../models/lmiSkillAbility');
const { Sector } = require('../models/sector');
const { Subsector } = require('../models/subsector');

// Import Phase 4 helpers
const DataExtractor = require('../helpers/dataExtractor');
const TrainingDataGenerator = require('../helpers/trainingDataGenerator');
const DataValidator = require('../helpers/dataValidator');

/**
 * Fine-Tuning Data Preparation Service
 * Extracts and formats data for OpenAI fine-tuning
 * Creates training examples in the required JSONL format
 */
class FineTuningDataService {
  constructor() {
    this.trainingData = [];
    this.validationData = [];
    this.dataStats = {
      regions: 0,
      collegeGroups: 0,
      colleges: 0,
      campuses: 0,
      courses: 0,
      careers: 0,
      skills: 0,
      abilities: 0,
      sectors: 0,
      subsectors: 0,
      totalExamples: 0
    };

    // Initialize Phase 4 helpers
    this.dataExtractor = new DataExtractor();
    this.trainingDataGenerator = new TrainingDataGenerator();
    this.dataValidator = new DataValidator();
  }

  /**
   * Generate complete training dataset for fine-tuning
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - Training data and statistics
   */
  async generateTrainingData(options = {}) {
    const {
      includeCollegeSpecific = true,
      includeGeneralContent = true,
      maxExamplesPerType = 100,
      validationSplit = 0.1
    } = options;

    console.log('🚀 Starting fine-tuning data generation...');
    console.log('📊 Options:', { includeCollegeSpecific, includeGeneralContent, maxExamplesPerType, validationSplit });

    try {
      // Reset data arrays
      this.trainingData = [];
      this.validationData = [];
      this.resetStats();

      // Generate data for each content type
      if (includeGeneralContent) {
        await this.generateRegionData(maxExamplesPerType);
        await this.generateCollegeGroupData(maxExamplesPerType);
        await this.generateSectorData(maxExamplesPerType);
        await this.generateSubsectorData(maxExamplesPerType);
        await this.generateCareerData(maxExamplesPerType);
        await this.generateSkillsAbilitiesData(maxExamplesPerType);
      }

      if (includeCollegeSpecific) {
        await this.generateCollegeSpecificData(maxExamplesPerType);
      }

      // Split data into training and validation
      this.splitTrainingValidation(validationSplit);

      // Update final statistics
      this.dataStats.totalExamples = this.trainingData.length + this.validationData.length;

      console.log('✅ Fine-tuning data generation completed');
      console.log('📊 Final Statistics:', this.dataStats);

      return {
        trainingData: this.trainingData,
        validationData: this.validationData,
        statistics: this.dataStats
      };

    } catch (error) {
      console.error('❌ Error generating training data:', error);
      throw error;
    }
  }

  /**
   * Generate training examples for regions
   */
  async generateRegionData(maxExamples) {
    console.log('📍 Generating region training data...');
    
    const regions = await Region.find({})
      .populate('collegeGroupIds')
      .limit(maxExamples)
      .lean();

    for (const region of regions) {
      // Basic region information
      this.addTrainingExample({
        system: "You are a helpful educational assistant providing information about educational regions and institutions.",
        user: `Tell me about the ${region.name} region.`,
        assistant: `The ${region.name} region is an educational area that includes multiple college groups and institutions. ${region.description || 'This region offers various educational opportunities and programs.'} The region serves students across different locations and provides access to quality education.`
      });

      // Region college groups
      if (region.collegeGroupIds && region.collegeGroupIds.length > 0) {
        const collegeGroupNames = region.collegeGroupIds.map(cg => cg.name).join(', ');
        this.addTrainingExample({
          system: "You are a helpful educational assistant providing information about educational regions and institutions.",
          user: `What college groups are in the ${region.name} region?`,
          assistant: `The ${region.name} region includes the following college groups: ${collegeGroupNames}. Each college group offers various educational programs and services to students in the region.`
        });
      }

      this.dataStats.regions++;
    }

    console.log(`✅ Generated ${this.dataStats.regions} region examples`);
  }

  /**
   * Generate training examples for college groups
   */
  async generateCollegeGroupData(maxExamples) {
    console.log('🏢 Generating college group training data...');
    
    const collegeGroups = await CollegeGroup.find({})
      .limit(maxExamples)
      .lean();

    for (const group of collegeGroups) {
      // Basic college group information
      this.addTrainingExample({
        system: "You are a helpful educational assistant providing information about college groups and educational institutions.",
        user: `What is ${group.name}?`,
        assistant: `${group.name} is a college group that operates educational institutions. They are located at ${group.address1 ? `${group.address1}, ` : ''}${group.city || 'various locations'}${group.state ? `, ${group.state}` : ''}. ${group.website ? `You can visit their website at ${group.website} ` : ''}${group.contactNumber ? `or contact them at ${group.contactNumber}` : ''} for more information about their programs and services.`
      });

      // Contact information
      if (group.contactNumber || group.email) {
        this.addTrainingExample({
          system: "You are a helpful educational assistant providing information about college groups and educational institutions.",
          user: `How can I contact ${group.name}?`,
          assistant: `You can contact ${group.name} through the following methods: ${group.contactNumber ? `Phone: ${group.contactNumber}` : ''}${group.email ? `${group.contactNumber ? ', ' : ''}Email: ${group.email}` : ''}${group.website ? `${group.contactNumber || group.email ? ', ' : ''}Website: ${group.website}` : ''}.`
        });
      }

      this.dataStats.collegeGroups++;
    }

    console.log(`✅ Generated ${this.dataStats.collegeGroups} college group examples`);
  }

  /**
   * Generate training examples for sectors
   */
  async generateSectorData(maxExamples) {
    console.log('🏭 Generating sector training data...');
    
    const sectors = await Sector.find({})
      .limit(maxExamples)
      .lean();

    for (const sector of sectors) {
      // Basic sector information
      this.addTrainingExample({
        system: "You are a helpful career guidance assistant providing information about industry sectors and career opportunities.",
        user: `Tell me about the ${sector.name} sector.`,
        assistant: `The ${sector.name} sector is an important industry area that offers various career opportunities and job roles. This sector includes multiple specializations and career paths for professionals with different skill sets and educational backgrounds.`
      });

      // Career opportunities in sector
      this.addTrainingExample({
        system: "You are a helpful career guidance assistant providing information about industry sectors and career opportunities.",
        user: `What career opportunities are available in the ${sector.name} sector?`,
        assistant: `The ${sector.name} sector offers diverse career opportunities across various roles and specializations. Professionals in this sector can pursue careers in different areas, each requiring specific skills and qualifications. The sector provides growth opportunities for both entry-level and experienced professionals.`
      });

      this.dataStats.sectors++;
    }

    console.log(`✅ Generated ${this.dataStats.sectors} sector examples`);
  }

  /**
   * Generate training examples for subsectors
   */
  async generateSubsectorData(maxExamples) {
    console.log('🔧 Generating subsector training data...');
    
    const subsectors = await Subsector.find({})
      .populate('sectorId')
      .limit(maxExamples)
      .lean();

    for (const subsector of subsectors) {
      // Basic subsector information
      const sectorName = subsector.sectorId?.name || subsector.sector || 'related sector';
      
      this.addTrainingExample({
        system: "You are a helpful career guidance assistant providing information about industry specializations and career paths.",
        user: `What is the ${subsector.name} specialization?`,
        assistant: `${subsector.name} is a specialized area within the ${sectorName} sector. This specialization focuses on specific aspects of the industry and offers targeted career opportunities for professionals with relevant skills and expertise.`
      });

      // Relationship to parent sector
      this.addTrainingExample({
        system: "You are a helpful career guidance assistant providing information about industry specializations and career paths.",
        user: `How does ${subsector.name} relate to the ${sectorName} sector?`,
        assistant: `${subsector.name} is a specialized subsector within the broader ${sectorName} sector. It represents a focused area of expertise that requires specific knowledge and skills related to ${sectorName}. Professionals in this subsector typically have specialized training or experience in this particular area.`
      });

      this.dataStats.subsectors++;
    }

    console.log(`✅ Generated ${this.dataStats.subsectors} subsector examples`);
  }

  /**
   * Generate training examples for careers
   */
  async generateCareerData(maxExamples) {
    console.log('💼 Generating career training data...');

    const careers = await Career.find({})
      .populate('sectorIds subsectorIds')
      .limit(maxExamples)
      .lean();

    for (const career of careers) {
      // Basic career information
      this.addTrainingExample({
        system: "You are a helpful career guidance assistant providing information about careers and job opportunities.",
        user: `Tell me about the ${career.title} career.`,
        assistant: `${career.title} is a ${career.type === 'broad_career' ? 'broad career area' : 'specialized role'} ${career.onetCode ? `with O*NET code ${career.onetCode}` : ''}. ${career.description || 'This career offers various opportunities for professional growth and development.'} ${career.jobZone ? `This career typically requires job zone ${career.jobZone} level preparation.` : ''}`
      });

      // Career requirements and skills
      if (career.tasks) {
        this.addTrainingExample({
          system: "You are a helpful career guidance assistant providing information about careers and job opportunities.",
          user: `What tasks are involved in ${career.title}?`,
          assistant: `As a ${career.title}, typical tasks include: ${career.tasks}`
        });
      }

      // Salary information
      if (career.salaryMean || career.salaryMedian) {
        const salaryInfo = [];
        if (career.salaryMean) salaryInfo.push(`average salary of $${career.salaryMean.toLocaleString()}`);
        if (career.salaryMedian) salaryInfo.push(`median salary of $${career.salaryMedian.toLocaleString()}`);

        this.addTrainingExample({
          system: "You are a helpful career guidance assistant providing information about careers and job opportunities.",
          user: `What is the salary for ${career.title}?`,
          assistant: `The ${career.title} career offers competitive compensation with ${salaryInfo.join(' and ')}. Salary can vary based on experience, location, and specific industry sector.`
        });
      }

      // Growth prospects
      if (career.growthYear2025 || career.growthYear2035) {
        this.addTrainingExample({
          system: "You are a helpful career guidance assistant providing information about careers and job opportunities.",
          user: `What are the growth prospects for ${career.title}?`,
          assistant: `The ${career.title} career shows ${career.growthYear2025 > 0 ? 'positive' : 'stable'} growth prospects. ${career.growthYear2025 ? `Expected growth by 2025: ${career.growthYear2025}%` : ''} ${career.growthYear2035 ? `${career.growthYear2025 ? ', and' : 'Expected growth'} by 2035: ${career.growthYear2035}%` : ''}.`
        });
      }

      this.dataStats.careers++;
    }

    console.log(`✅ Generated ${this.dataStats.careers} career examples`);
  }

  /**
   * Generate training examples for skills and abilities
   */
  async generateSkillsAbilitiesData(maxExamples) {
    console.log('🧠 Generating skills and abilities training data...');

    // Get skills
    const skills = await LMISkillAbility.find({
      status: 'active',
      category: 'skill'
    })
    .populate('radarCategoryId radarSubcategoryId')
    .limit(Math.floor(maxExamples / 2))
    .lean();

    // Get abilities
    const abilities = await LMISkillAbility.find({
      status: 'active',
      category: 'ability'
    })
    .populate('radarCategoryId radarSubcategoryId')
    .limit(Math.floor(maxExamples / 2))
    .lean();

    // Process skills
    for (const skill of skills) {
      this.addTrainingExample({
        system: "You are a helpful career development assistant providing information about professional skills and competencies.",
        user: `What is the ${skill.lmiName} skill?`,
        assistant: `${skill.lmiName} is an important professional skill ${skill.radarCategoryId?.name ? `in the ${skill.radarCategoryId.name} category` : ''}. This skill is valuable for career development and professional success across various industries and roles.`
      });

      // Skill category information
      if (skill.radarCategoryId) {
        this.addTrainingExample({
          system: "You are a helpful career development assistant providing information about professional skills and competencies.",
          user: `What category does the ${skill.lmiName} skill belong to?`,
          assistant: `The ${skill.lmiName} skill belongs to the ${skill.radarCategoryId.name} category${skill.radarSubcategoryId ? `, specifically in the ${skill.radarSubcategoryId.name} subcategory` : ''}. This categorization helps identify related skills and career development paths.`
        });
      }

      this.dataStats.skills++;
    }

    // Process abilities
    for (const ability of abilities) {
      this.addTrainingExample({
        system: "You are a helpful career development assistant providing information about professional abilities and competencies.",
        user: `What is the ${ability.lmiName} ability?`,
        assistant: `${ability.lmiName} is a key professional ability ${ability.radarCategoryId?.name ? `in the ${ability.radarCategoryId.name} category` : ''}. This ability is essential for effective performance in various career roles and professional situations.`
      });

      // Ability category information
      if (ability.radarCategoryId) {
        this.addTrainingExample({
          system: "You are a helpful career development assistant providing information about professional abilities and competencies.",
          user: `What category does the ${ability.lmiName} ability belong to?`,
          assistant: `The ${ability.lmiName} ability is categorized under ${ability.radarCategoryId.name}${ability.radarSubcategoryId ? `, specifically in the ${ability.radarSubcategoryId.name} subcategory` : ''}. Understanding ability categories helps in career planning and skill development.`
        });
      }

      this.dataStats.abilities++;
    }

    console.log(`✅ Generated ${this.dataStats.skills} skill examples and ${this.dataStats.abilities} ability examples`);
  }

  /**
   * Add a training example to the dataset
   */
  addTrainingExample(example) {
    const trainingExample = {
      messages: [
        { role: "system", content: example.system },
        { role: "user", content: example.user },
        { role: "assistant", content: example.assistant }
      ]
    };

    this.trainingData.push(trainingExample);
  }

  /**
   * Generate college-specific training data
   */
  async generateCollegeSpecificData(maxExamples) {
    console.log('🏫 Generating college-specific training data...');

    const colleges = await College.find({})
      .populate('collegeGroupId')
      .limit(50) // Limit colleges to prevent too much data
      .lean();

    for (const college of colleges) {
      // Generate campus data for this college
      await this.generateCampusData(college, Math.floor(maxExamples / 4));

      // Generate course data for this college
      await this.generateCourseData(college, Math.floor(maxExamples / 2));

      // Basic college information
      this.addCollegeSpecificExample(college, {
        user: `Tell me about ${college.name}.`,
        assistant: `${college.name} is ${college.collegeGroupId ? `part of the ${college.collegeGroupId.name} college group and ` : ''}located at ${college.address1 ? `${college.address1}, ` : ''}${college.city || 'various locations'}${college.state ? `, ${college.state}` : ''}. ${college.description || 'This institution offers quality education and various programs for students.'} ${college.website ? `You can visit their website at ${college.website} ` : ''}${college.contactNumber ? `or contact them at ${college.contactNumber}` : ''} for more information.`
      });

      // College contact information
      if (college.contactNumber || college.email || college.website) {
        this.addCollegeSpecificExample(college, {
          user: `How can I contact ${college.name}?`,
          assistant: `You can contact ${college.name} through: ${college.contactNumber ? `Phone: ${college.contactNumber}` : ''}${college.email ? `${college.contactNumber ? ', ' : ''}Email: ${college.email}` : ''}${college.website ? `${college.contactNumber || college.email ? ', ' : ''}Website: ${college.website}` : ''}. They will be happy to help with your inquiries about programs and admissions.`
        });
      }

      this.dataStats.colleges++;
    }

    console.log(`✅ Generated college-specific data for ${this.dataStats.colleges} colleges`);
  }

  /**
   * Generate campus data for a specific college
   */
  async generateCampusData(college, maxExamples) {
    const campuses = await Campus.find({
      collegeId: college._id,
    })
    .limit(maxExamples)
    .lean();

    for (const campus of campuses) {
      // Basic campus information
      this.addCollegeSpecificExample(college, {
        user: `Tell me about the ${campus.name} campus.`,
        assistant: `The ${campus.name} campus is part of ${college.name} and is located at ${campus.address1 ? `${campus.address1}, ` : ''}${campus.city || 'the campus location'}${campus.state ? `, ${campus.state}` : ''}. ${campus.contactNumber ? `You can contact this campus at ${campus.contactNumber}` : ''} ${campus.email ? `${campus.contactNumber ? 'or' : 'You can contact them at'} ${campus.email}` : ''} for specific information about programs and services available at this location.`
      });

      // Campus location and contact
      if (campus.address1 || campus.city) {
        this.addCollegeSpecificExample(college, {
          user: `Where is the ${campus.name} campus located?`,
          assistant: `The ${campus.name} campus of ${college.name} is located at ${campus.address1 ? `${campus.address1}, ` : ''}${campus.city}${campus.state ? `, ${campus.state}` : ''}${campus.zip ? ` ${campus.zip}` : ''}. This campus provides convenient access to ${college.name}'s programs and services.`
        });
      }

      this.dataStats.campuses++;
    }
  }

  /**
   * Generate course data for a specific college
   */
  async generateCourseData(college, maxExamples) {
    const courses = await Course.find({})
    .populate({
      path: 'campusId',
      match: { collegeId: college._id },
      select: 'name collegeId'
    })
    .populate('sectorIds subsectorIds')
    .limit(maxExamples)
    .lean();

    // Filter courses that belong to this college's campuses
    const collegeCourses = courses.filter(course => course.campusId && course.campusId.collegeId.toString() === college._id.toString());

    for (const course of collegeCourses) {
      // Basic course information
      this.addCollegeSpecificExample(college, {
        user: `Tell me about the ${course.title} course.`,
        assistant: `${course.title} ${course.code ? `(${course.code})` : ''} is offered at ${college.name}${course.campusId ? ` at the ${course.campusId.name} campus` : ''}. ${course.description || 'This course provides comprehensive education in the subject area.'} ${course.level ? `This is a ${course.level} level course` : ''}${course.duration ? ` with a duration of ${course.duration}` : ''}. ${course.pageURL ? `More details are available at ${course.pageURL}` : ''}`
      });

      // Course application information
      if (course.applyURL || course.enquiryURL) {
        this.addCollegeSpecificExample(college, {
          user: `How can I apply for the ${course.title} course?`,
          assistant: `To apply for ${course.title} at ${college.name}, ${course.applyURL ? `you can apply directly at ${course.applyURL}` : ''}${course.enquiryURL ? `${course.applyURL ? ' or make an enquiry at' : 'you can make an enquiry at'} ${course.enquiryURL}` : ''}. ${!course.applyURL && !course.enquiryURL ? `please contact ${college.name} directly for application information` : ''}.`
        });
      }

      // Course sectors and career paths
      if (course.sectorIds && course.sectorIds.length > 0) {
        const sectorNames = course.sectorIds.map(s => s.name).join(', ');
        this.addCollegeSpecificExample(college, {
          user: `What career sectors does the ${course.title} course prepare me for?`,
          assistant: `The ${course.title} course at ${college.name} prepares you for careers in the following sectors: ${sectorNames}. This course provides the knowledge and skills needed to succeed in these industry areas.`
        });
      }

      this.dataStats.courses++;
    }
  }

  /**
   * Add a college-specific training example
   */
  addCollegeSpecificExample(college, example) {
    const trainingExample = {
      messages: [
        {
          role: "system",
          content: `You are a helpful assistant for ${college.name}. You provide information about ${college.name}'s programs, courses, campuses, and services. Always focus on ${college.name}-specific information when answering questions.`
        },
        { role: "user", content: example.user },
        { role: "assistant", content: example.assistant }
      ]
    };

    this.trainingData.push(trainingExample);
  }

  /**
   * Reset statistics counters
   */
  resetStats() {
    Object.keys(this.dataStats).forEach(key => {
      this.dataStats[key] = 0;
    });
  }

  /**
   * Split data into training and validation sets
   */
  splitTrainingValidation(validationSplit) {
    if (validationSplit <= 0) return;

    const totalExamples = this.trainingData.length;
    const validationCount = Math.floor(totalExamples * validationSplit);
    
    // Shuffle the data
    for (let i = this.trainingData.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [this.trainingData[i], this.trainingData[j]] = [this.trainingData[j], this.trainingData[i]];
    }
    
    // Split into validation and training
    this.validationData = this.trainingData.splice(0, validationCount);
    
    console.log(`📊 Data split: ${this.trainingData.length} training, ${this.validationData.length} validation`);
  }

  /**
   * Export training data to JSONL format
   */
  exportToJSONL(data) {
    return data.map(example => JSON.stringify(example)).join('\n');
  }

  /**
   * Get training data statistics
   */
  getStatistics() {
    return {
      ...this.dataStats,
      trainingExamples: this.trainingData.length,
      validationExamples: this.validationData.length
    };
  }

  /**
   * Enhanced data generation using Phase 4 helpers
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - Enhanced training data with validation
   */
  async generateEnhancedTrainingData(options = {}) {
    const {
      includeCollegeSpecific = true,
      includeGeneralContent = true,
      maxExamplesPerType = 100,
      validationSplit = 0.1,
      validateData = true,
      examplesPerRecord = 3
    } = options;

    console.log('🚀 Starting enhanced training data generation...');
    console.log('📊 Using Phase 4 data pipeline...');

    try {
      // Step 1: Extract all data with relationships
      console.log('🔍 Extracting data with relationships...');
      const extractedData = await this.dataExtractor.extractAllData({
        includeInactive: true,
        populateRelationships: true,
        maxRecordsPerType: maxExamplesPerType,
        includeMetadata: true
      });

      // Step 2: Generate training examples using advanced generator
      console.log('🎯 Generating training examples...');
      const trainingExamples = await this.trainingDataGenerator.generateTrainingData(extractedData, {
        examplesPerRecord: examplesPerRecord,
        includeMultiTurn: true,
        includeVariations: true,
        responseStyle: 'helpful',
        maxExamplesTotal: maxExamplesPerType * 10
      });

      // Step 3: Validate data quality
      let validationResults = null;
      if (validateData) {
        console.log('🔍 Validating data quality...');
        validationResults = await this.dataValidator.validateTrainingData(trainingExamples, {
          strictMode: false,
          checkDuplicates: true,
          validateContent: true,
          generateReport: true
        });
      }

      // Step 4: Split into training and validation
      this.trainingData = trainingExamples;
      this.splitTrainingValidation(validationSplit);

      // Step 5: Update statistics
      this.updateStatsFromExtraction(extractedData.metadata.statistics);
      this.dataStats.totalExamples = this.trainingData.length + this.validationData.length;

      console.log('✅ Enhanced training data generation completed');
      console.log(`📊 Generated ${this.dataStats.totalExamples} total examples`);
      console.log(`📊 Training: ${this.trainingData.length}, Validation: ${this.validationData.length}`);

      return {
        trainingData: this.trainingData,
        validationData: this.validationData,
        statistics: this.dataStats,
        extractionMetadata: extractedData.metadata,
        validationResults: validationResults
      };

    } catch (error) {
      console.error('❌ Enhanced data generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate college-specific training data
   * @param {string} collegeId - College ID
   * @param {Object} options - Generation options
   * @returns {Promise<Object>} - College-specific training data
   */
  async generateCollegeSpecificTrainingData(collegeId, options = {}) {
    const {
      examplesPerRecord = 5,
      validateData = true,
      validationSplit = 0.1
    } = options;

    console.log(`🎯 Generating college-specific training data for: ${collegeId}`);

    try {
      // Extract college-specific data
      const collegeData = await this.dataExtractor.extractCollegeSpecificData(collegeId);

      // Generate training examples
      const trainingExamples = await this.trainingDataGenerator.generateTrainingData({
        colleges: [collegeData.college],
        campuses: collegeData.campuses,
        courses: collegeData.courses,
        careers: [], // Use general careers
        skills: [], // Use general skills
        abilities: [], // Use general abilities
        sectors: [], // Use general sectors
        subsectors: [], // Use general subsectors
        regions: [], // Use general regions
        collegeGroups: [] // Use general college groups
      }, {
        examplesPerRecord: examplesPerRecord,
        includeMultiTurn: false,
        includeVariations: true,
        responseStyle: 'helpful'
      });

      // Validate if requested
      let validationResults = null;
      if (validateData) {
        validationResults = await this.dataValidator.validateTrainingData(trainingExamples, {
          strictMode: false,
          checkDuplicates: true,
          validateContent: true
        });
      }

      // Split data
      this.trainingData = trainingExamples;
      this.splitTrainingValidation(validationSplit);

      console.log(`✅ College-specific data generated: ${trainingExamples.length} examples`);

      return {
        trainingData: this.trainingData,
        validationData: this.validationData,
        collegeInfo: collegeData.college,
        statistics: collegeData.metadata.recordCounts,
        validationResults: validationResults
      };

    } catch (error) {
      console.error('❌ College-specific data generation failed:', error);
      throw error;
    }
  }

  /**
   * Validate existing training data
   * @param {Array} trainingData - Training data to validate
   * @param {Object} options - Validation options
   * @returns {Promise<Object>} - Validation results
   */
  async validateTrainingData(trainingData = null, options = {}) {
    const dataToValidate = trainingData || this.trainingData;

    if (!dataToValidate || dataToValidate.length === 0) {
      throw new Error('No training data to validate');
    }

    console.log(`🔍 Validating ${dataToValidate.length} training examples...`);

    try {
      const validationResults = await this.dataValidator.validateTrainingData(dataToValidate, {
        strictMode: options.strictMode || false,
        checkDuplicates: options.checkDuplicates !== false,
        validateContent: options.validateContent !== false,
        generateReport: options.generateReport !== false
      });

      console.log('✅ Validation completed');
      console.log(`📊 Quality score: ${validationResults.report?.qualityScore?.toFixed(2) || 'N/A'}`);
      console.log(`⚠️ Errors: ${validationResults.errors.length}, Warnings: ${validationResults.warnings.length}`);

      return validationResults;

    } catch (error) {
      console.error('❌ Training data validation failed:', error);
      throw error;
    }
  }

  /**
   * Update statistics from extraction metadata
   */
  updateStatsFromExtraction(extractionStats) {
    if (extractionStats) {
      Object.keys(this.dataStats).forEach(key => {
        if (extractionStats[key] !== undefined) {
          this.dataStats[key] = extractionStats[key];
        }
      });
    }
  }
}

module.exports = FineTuningDataService;
