const getNestedCareersAggregation = (searchTerm) => {
  const commonProjection = {
    _id: 1,
    type: 1,
    onetCode: 1,
    socCode: 1,
    title: 1,
    description: 1,
    addedBy: 1,
    interests: 1,
  };

  return [
    // 1. Match broad_careers by title
    {
      $match: {
        type: "broad_career",
        $or: [
          { title: { $regex: searchTerm, $options: "i" } },
          { title: { $regex: searchTerm.split(' ').join('|'), $options: "i" } }
        ]
      },
    },
    // 2. Lookup their specialised roles
    {
      $lookup: {
        from: "careers",
        let: { broadId: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ["$type", "specialised_role"] },
                  {
                    $cond: [
                      {
                        $and: [
                          { $isArray: "$broadCareerIds" },
                          { $gt: [{ $size: "$broadCareerIds" }, 0] }
                        ]
                      },
                      { $in: ["$$broadId", "$broadCareerIds"] },
                      false
                    ]
                  },
                ],
              },
            },
          },
          { $project: commonProjection },
        ],
        as: "specialisedRoles",
      },
    },
    {
      $project: {
        ...commonProjection,
        specialisedRoles: 1,
      },
    },
    // 3. Union: Specialised roles that match title directly
    {
      $unionWith: {
        coll: "careers",
        pipeline: [
          {
            $match: {
              type: "specialised_role",
              $or: [
                { title: { $regex: searchTerm, $options: "i" } },
                { title: { $regex: searchTerm.split(' ').join('|'), $options: "i" } }
              ],
              broadCareerIds: { $exists: true, $ne: null }
            },
          },
          { $unwind: "$broadCareerIds" },
          {
            $lookup: {
              from: "careers",
              localField: "broadCareerIds",
              foreignField: "_id",
              as: "broadCareer",
            },
          },
          { $unwind: "$broadCareer" },
          {
            $group: {
              _id: "$broadCareer._id",
              broadDoc: { $first: "$broadCareer" },
              specialisedRoles: {
                $push: {
                  _id: "$_id",
                  type: "$type",
                  onetCode: "$onetCode",
                  socCode: "$socCode",
                  title: "$title",
                  description: "$description",
                  addedBy: "$addedBy",
                  interests: "$interests",
                },
              },
            },
          },
          {
            $replaceRoot: {
              newRoot: {
                $mergeObjects: ["$broadDoc", { specialisedRoles: "$specialisedRoles" }],
              },
            },
          },
          {
            $project: {
              ...commonProjection,
              specialisedRoles: 1,
            },
          },
        ],
      },
    },
    // 4. De-duplicate on broad_career _id
    {
      $group: {
        _id: "$_id",
        doc: { $first: "$$ROOT" },
      },
    },
    {
      $replaceRoot: { newRoot: "$doc" },
    },
  ];
};

module.exports = {
  getNestedCareersAggregation,
};
