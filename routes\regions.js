const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const regionsController = require('../controllers/regions.controller');
const SuperAdminGuard = require("../guards/super-user.guard");

router.post("/add", SuperAdminGuard, regionsController.add);

router.get("/get", SuperAdminGuard , regionsController.get);

router.get("/getById", SuperAdminGuard, regionsController.getByID);

router.delete("/remove", SuperAdminGuard, regionsController.remove);

router.put("/update", SuperAdminGuard, regionsController.update);

module.exports = router;