import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance'
import { postSubSector, removeSubSector, updateSubSector } from '../SubSector/SubSectorSlice';

export const getSectors = createAsyncThunk('sectors/getSector', async () => {
    try {
        const response = await axiosInstance({
            url: "sectors/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        console.log("error get users", error)
        return error
    }
})

export const postSector = createAsyncThunk("sectors/addSector", async (data) => {
    try {
        const response = await axiosInstance({
            url: "sectors/add",
            method: "POST",
            data
        })
        response.data.name = data?.name;
        response.data._id = response.data.id;
        return response
    } catch (error) {
        console.log("error post users", error)
        return error
    }
})

export const updateSector = createAsyncThunk('sectors/updateSector', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "sectors/update",
            method: "PUT",
            data
        })
        response.data.name = data?.name;
        response.data._id = data?.id;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeSector = createAsyncThunk('sectors/removeSectors', async (data) => {

    try {
        const response = await axiosInstance({
            url: "sectors/remove",
            method: "DELETE",
            data
        })
        response.data._id = data.id
        return response.data;
    } catch (error) {
        return error
    }
})

const sectorSlice = createSlice({
    name: 'sectors',
    initialState: {
        status: 'idle',
        sectors: []
    },
    reducers: {
        addSector: (state, action) => {
            state.sectors.push(action.payload)
        },
        updateSector: (state, action) => {

        },
        deleteSector: (state, action) => {
            state.sectors = state.sectors.filter(sector => sector.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getSectors.pending]: (state) => {
            state.status = 'pending'
        },
        [getSectors.fulfilled]: (state, action) => {
            state.sectors = action.payload?.data
            state.status = 'succeded'
        },
        [getSectors.rejected]: (state, action) => {
            console.log("error", action.payload)
            state.status = 'rejected'
        },
        [postSector.fulfilled]: (state, action) => {
            const data = action.payload.data
            data.subsectors =[]
            if (data) {
                state.sectors?.push(data)
            }
        },
        [postSector.rejected]: (state, action) => {
            const error = action.payload
            console.log("error post sector", error)
        },
        [updateSector.fulfilled]: (state, action) => {
            state.sectors = state.sectors.map((item) => {
                if (item._id === action.payload._id) {
                    item.name = action.payload.name
                    return item
                }
                return item
            })
        },
        [updateSector.rejected]: (state, action) => {
            const error = action.payload
            console.log("error update sector", error)
        },
        [removeSector.fulfilled]: (state, action) => {
            const data = action.payload
            state.sectors = state.sectors?.filter(sector => sector._id !== data._id)
        },
        [removeSubSector.fulfilled]: (state, action) => {
            state.sectors.find(sector => sector._id === action.payload.sectorId).subsectors =
                state.sectors.find(sector => sector._id === action.payload.sectorId).subsectors.
                    filter(subsector => subsector?._id !== action.payload._id)
        },
        [updateSubSector.fulfilled]: (state, action) => {
            state.sectors.find(sector => sector._id === action.payload.sectorId).subsectors =
                state.sectors.find(sector => sector._id === action.payload.sectorId).subsectors.map(subsector => {
                    if (subsector?._id === action.payload._id) {
                        subsector.name = action.payload.name
                        subsector.sectorId = action.payload.sectorId
                        subsector.sectorName = action.payload.sectorName
                        return subsector
                    }
                    return subsector
                })
        },
        [postSubSector.fulfilled]: (state, action) => {
            const { name, id, sectorName, sectorId } = action.payload.data
            const data = { sectorId, name, sectorName, _id: id }
            state.sectors.find(sector => sector._id === sectorId).subsectors.push(data)
        },
        [removeSector.rejected]: (state, action) => {
            const error = action.payload
            console.log("remove sector error", error)
        },
    }
})
export const { addSector } = sectorSlice.actions;
export default sectorSlice.reducer