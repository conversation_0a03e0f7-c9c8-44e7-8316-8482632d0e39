const mongoose = require("mongoose");

const ChatSessionSchema = new mongoose.Schema({
  collegeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "college",
    required: true,
    index: true
  },
  ipAddress: {
    type: String,
    required: true
  },
  userAgent: {
    type: String,
    default: ""
  },
  status: {
    type: String,
    enum: ['active', 'ended', 'flagged'],
    default: 'active',
    index: true
  },
  violationCount: {
    type: Number,
    default: 0
  },
  messageCount: {
    type: Number,
    default: 0
  },
  lastActivity: {
    type: Date,
    default: Date.now
  },
  metadata: {
    type: Object,
    default: {}
  }
}, {
  timestamps: true
});

// Auto-expire sessions after 24 hours of inactivity
ChatSessionSchema.index({ lastActivity: 1 }, { expireAfterSeconds: 86400 });

// Instance methods
ChatSessionSchema.methods.updateActivity = function() {
  this.lastActivity = new Date();
  this.messageCount += 1;
  return this.save();
};

ChatSessionSchema.methods.addViolation = function() {
  this.violationCount += 1;
  if (this.violationCount >= 3) {
    this.status = 'flagged';
  }
  return this.save();
};

// Static methods
ChatSessionSchema.statics.findActiveById = function(sessionId) {
  return this.findOne({ _id: sessionId, status: 'active' });
};

ChatSessionSchema.statics.getCollegeActiveSessions = function(collegeId) {
  return this.find({ collegeId, status: 'active' }).countDocuments();
};

class ChatSession extends mongoose.Model {}

mongoose.model(ChatSession, ChatSessionSchema, "chatSessions");

module.exports.ChatSession = ChatSession;
module.exports.ChatSessionSchema = ChatSessionSchema;
