import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
    Avatar,
    Container,
    Stack,
    Typography
} from '@mui/material';
import { get } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import useAuth from '../../hooks/useAuth';
import useLoading from '../../hooks/useLoading';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getColleges } from '../Colleges/collegesSlice';
import UserFilterForm from './UserFilterForm';
import { getUsers, removeUser } from './usersSlice';

// ----------------------------------------------------------------------

export const USER_TABLE_HEAD = [
    { id: 'firstName', label: 'Name', alignRight: false },
    { id: 'role', label: "Role", alignRight: false },
    { id: 'groupName', label: "Group", alignRight: false },
    { id: 'collegeName', label: "College", alignRight: false },
    { id: 'campusName', label: "Campus", alignRight: false },
    { id: 'email', label: "Email", alignRight: false },
    { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];
// export const renderUserCells = [["firstName", "lastName"],"userRole", "groupName", "collegeName","campusName", "email"]
const userNameComponent = (cell) =>
(
    <Stack gap={1} alignItems='center' direction='row'>
        <Avatar 
        src={cell.photo}
        sx={{ width: 36, height: 36 }}
        />
        {/* {`${cell.firstName.charAt(0)}${cell.lastName.charAt(0)}`}
        </Avatar> */}
        <Typography>
            {cell.firstName} {cell.lastName}
        </Typography>
    </Stack>
)

export const renderUserCells = [userNameComponent, "userRole", "groupName", "collegeName", "campusName", "email"]

const UsersPage = () => {
    const dispatch = useDispatch()
    const [openFilter, setOpenFilter] = useState(false);
    const [Users, setUsers] = useState([]);
    const [Colleges, setColleges] = useState([]);
    const [Groups, setGroups] = useState([]);
    const [selectedGroups, setSelectedGroups] = useState([]);
    const [selectedColleges, setSelectedColleges] = useState([]);
    const colleges = useSelector(state => state.colleges.colleges)
    const groups = useSelector(state => state.collegeGroups.groups)
    const { users, status } = useSelector(state => state.users)
    const navigate = useNavigate()
    const { role } = useAuth()
    const deleteTitle = "Delete User ?"
    const deleteDescription = "Are you sure you want to delete this user"
    useEffect(() => {
        setColleges([...colleges])
    }, [colleges])
    useEffect(() => {
        dispatch(getUsers())
    }, [])
    useEffect(() => {
        if (openFilter) {
            if (role === '1' || role === '2') {
                dispatch(getCollegeGroups())
            }
            if (role === '1' || role === '2' || role === '3') {
                dispatch(getColleges())
            }
        }
    }, [openFilter])

    useEffect(() => {
        if (!!groups) {
            setGroups([...groups])
        }
    }, [groups])

    useEffect(() => {
        if (!!users) {
            setUsers([...users])
        }
    }, [users])

    useEffect(() => {
        if (selectedGroups.length !== 0) {
            if (selectedColleges.length !== 0) {
                // setUsers(Users.filter(user => (selectedGroups.includes(user?.collegeGroups?.name) && selectedColleges.includes(user?.colleges?.name))))
                setUsers(users.filter(user => (selectedGroups.includes(user?.groupName) && selectedColleges.includes(user?.collegeName))))
            } else {
                // setUsers(Users.filter(user => selectedGroups.includes(user?.collegeGroups?.name)))
                setUsers(users.filter(user => selectedGroups.includes(user?.groupName)))
            }
        } else {
            setUsers(users)
        }
    }, [selectedColleges, selectedGroups])

    const loading = useLoading(status)
    const handleCloseFilter = () => {
        // setSelectedColleges([])
        // setSelectedGroups([])
        // setUsers(Users)
        setOpenFilter(false)
    }
    // const handleOpen = () => setOpenModel(true);
    const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/user/add`);
    const handleFilterSearch = (event) => {
        const filteredUsers = !!Users ? Users.filter(user => user && user.firstName.toLowerCase().includes(event.target.value.toLowerCase()) ||
            user && user.lastName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            user && user.userRole?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            user && user.collegeName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            user && user.groupName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            user && user.campusName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            user && user.email?.toLowerCase().includes(event.target.value.toLowerCase())
        ) : []
        return filteredUsers
    };

    const handleDeleteAdmin = (Admin, handleOpenBackdrop, handleCloseBackdrop) => {
        const user = { id: Admin._id }
        handleOpenBackdrop()
        dispatch(removeUser(user)).then(res => {
            if (res?.payload?.success) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "User Deleted Succesfully"
                }))
            } else {
                const errorMessage = get(res, 'payload.response.data.message', "Something went wrong")
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: errorMessage
                }))
                console.log('error ==>', res)
            }
        }).finally(() => {
            handleCloseBackdrop()
        })
    }

    const editAdmin = (admin) => {
        navigate(`${APP_ROUTER_BASE_URL}dashboard/user/edit/${admin?._id}`)
    }


    const handleFilter = () => {
        setOpenFilter(true)
    }

    return (
        <>
            <Helmet>
                <title> Admins | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                {/* <PopUp
                    open={openModel}
                    onClose={handleClose}
                    title={"Add User"}
                >
                    <UserForm
                        openModel={openModel}
                        User={User}
                        setUser={setUser}
                        Groups={Groups}
                        Colleges={Colleges}
                        Users={Users}
                        setOpenModel={setOpenModel}
                    />
                </PopUp> */}
                <PopUp
                    open={openFilter}
                    onClose={handleCloseFilter}
                    title={"Filter"}
                >
                    <UserFilterForm
                        selectedGroups={selectedGroups}
                        setSelectedGroups={setSelectedGroups}
                        openModel={openFilter}
                        setOpenModel={setOpenFilter}
                        Groups={Groups}
                        setUsers={setUsers}
                        users={users}
                        Colleges={Colleges}
                        selectedColleges={selectedColleges}
                        setSelectedColleges={setSelectedColleges}
                        filterName={"colleges"}
                    />
                </PopUp>
                <Typography variant="h4" gutterBottom mb={3}>
                    Users
                </Typography>
                <DataTable
                    loading={loading}
                    deleteTitle={deleteTitle}
                    deleteDescription={deleteDescription}
                    filter="true"
                    TableHead={USER_TABLE_HEAD}
                    TableData={Users}
                    filterSearch={handleFilterSearch}
                    buttonText={"New User"}
                    buttonHandler={handleOpen}
                    handleEdit={editAdmin}
                    renderCells={renderUserCells}
                    handleDelete={handleDeleteAdmin}
                    handleFilter={handleFilter}
                    selectedGroups={selectedGroups}
                    selectedColleges={selectedColleges}
                    setSelectedColleges={setSelectedColleges}
                    setSelectedGroups={setSelectedGroups}
                    setUsers={setUsers}
                    Users={Users}
                    Colleges={Colleges}
                    pagination={"true"}
                />
            </Container>
        </>
    )
}

export default UsersPage;



