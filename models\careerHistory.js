const mongoose = require("mongoose");
const { Career, CareerSchema } = require("./career");

const careerHistorySchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "user",
  },
  collegeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "college",
  },
  regionId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "region",
  },
  skillType: {
    type: String,
    enum: ["reskill", "upskill"],
    default: "reskill",
  },
  currentCareerIds: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: mongoose.model(Career, CareerSchema),
    },
  ],
  careerGoal: {
    type: mongoose.Schema.Types.ObjectId,
    ref: mongoose.model(Career, CareerSchema),
  },
  compareCareerIds: [
    {
      type: mongoose.Schema.Types.ObjectId,
      ref: mongoose.model(Career, CareerSchema),
    },
  ],
});

class CareerHistory extends mongoose.Model {}

mongoose.model(CareerHistory, careerHistorySchema, "careerHistories");

module.exports.CareerHistory = CareerHistory;
