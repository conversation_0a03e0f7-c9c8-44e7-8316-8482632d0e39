const forgotPassword = {
    emailSubject: "Forgot Password",
    htmlTemplate: (data) => {   
        // #fd1c99
        // background-image: url('background.png')
        const {user, newPassword} = data  
        return `
        <body style="background-color: #592A7B; position: relative; padding: 40px 0; font-family: Arial, sans-serif;">
            <table cellpadding="0" cellspacing="0" border="0" style=" margin-left: auto; margin-right: auto; width: 560px; height: auto; background-color: #fff; border-radius: 6px; padding: 40px 50px;">
                <tr>
                    <td style="width: 460px; padding-bottom: 30px;  padding-top: 30px; height: 290px; background-color: #F6E9FF; border-radius: 6px; text-align: center; box-shadow: 0px 4px 12px rgba(0,0,0,0.09);">
                        <img src="assets/UP_RElogo.png" alt="Upskill/Reskill" style="width: 200px;">
                        <h2 style="font-size: 20px; max-width: 300px; margin: 0 auto; text-align: center; color: #FF951A;">
                            Hi ${user.firstName} ${user.lastName}, <br> Have you forgotten your old password?
                        </h2>
                        <p style="font-size: 15px; max-width: 300px; margin: 0 auto; padding-top: 24px; text-align: center; color: #592A7B;">
                            Here is your temporary password:
                            <p style="display: block; line-height: 32px; color: #592A7B; text-decoration: underline;">${newPassword}</p>
                        </p>
                        <p style="font-size: 15px; max-width: 300px; margin: 0 auto; padding-top: 10px; padding-bottom: 40px; text-align: center; color: #592A7B;">Login to access your account.</p>
                        <a href="https://upskillreskill-stg.absolutebyte.co.uk" style="padding: 10px 20px; color: #fff; background-color: #7b30fb; text-decoration: none; border-radius: 20px; font-size: 12px; font-weight: 600; ">Upskill/Reskill</a>
                    </td>
                </tr>
            </table>
        </body>
        `
    }
}

const skillReportUser = {
    emailSubject: "Your Skills Report from Upskill Reskill",
    htmlTemplate: (data) => {    
        const {firstName, lastName, college} = data 
        return `<body style="font-family:sans-serif">
            <div style="padding: 40px 20px; display: flex; justify-content: center;">
                <div>
                    <p style="font-weight: 500; font-size: 17px; line-height: 1.5; color: #5e5e5e;">
                        Hi<br>
                        ${firstName} ${lastName} here is your skills report from Upskill Reskill with ${college.name} as requested.<br><br>
                        Your report contains your personalised skilldar and a breakdown of the ten key employability skills.<br><br>
                        It also includes your careers of interest and the courses that will help you break out of your career box.<br><br>
                        We wish you all the best in your future career!<br><br>
                        From the Upskill Reskill Team
                    </p>
                </div>
            </div>
        </body>`
    }
}

const skillReportCollege = {
    emailSubject: "Upskill Reskill Skills Report",
    htmlTemplate: (data) => {
        const {firstName, lastName, email} = data    
        return `<body style="font-family:sans-serif">
            <div style="padding: 40px 20px; display: flex; justify-content: center;">
                <div>
                    <p style="font-weight: 500; font-size: 17px; line-height: 1.5; color: #5e5e5e;">
                        Hi<br>
                        A skills report has been downloaded from Upskill Reskill.<br>
                        Attached is a copy of the report including the careers and courses they are interested in.<br> <br>
                        <b>User contact details;</b><br>
                        ${firstName} ${lastName}<br> 
                        ${email}<br> <br>
                        Many Thanks<br>
                        From the Upskill Reskill Team
                    </p>
                </div>
            </div>
        </body>`
    }
}

module.exports = {
    forgotPassword,
    skillReportUser,
    skillReportCollege
}