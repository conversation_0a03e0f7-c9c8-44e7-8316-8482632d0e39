import React from 'react';
import PropTypes from 'prop-types';
import { Box, Typography } from '@mui/material';

const StepDot = ({ active }) => (
  <Box
    sx={{
      width: 14,
      height: 14,
      borderRadius: '50%',
      backgroundColor: active ? '#212121' : '#ccc',
    }}
  />
);

const Step = ({ label, active }) => (
  <Box display="flex" flexDirection="column" alignItems="center">
    <StepDot active={active} />
    <Typography
      sx={{
        fontSize: '11px !important',
        color: active ? '#212121' : '#888',
        fontWeight: active ? 600 : 400,
        mt: '4px',
        textAlign: 'center',
      }}
    >
      {label}
    </Typography>
  </Box>
);

Step.propTypes = {
  label: PropTypes.string.isRequired,
  active: PropTypes.bool.isRequired,
};

const CustomStepper = ({ steps, activeStep }) => {
  let l;
  return (
    <Box display="flex" alignItems="center" justifyContent="center">
      {steps.map((label, index) => (
        <React.Fragment key={label}>
          <Step label={label} active={index === activeStep} />
          {index < steps.length - 1 && (
            <Box
              sx={{
                height: '1px !important',
                width: 32,
                mx: 1,
                backgroundColor: '#ccc',
                mb:2.4,
              }}
            />
          )}
        </React.Fragment>
      ))}
    </Box>
  );
};

CustomStepper.propTypes = {
  steps: PropTypes.arrayOf(PropTypes.string).isRequired,
  activeStep: PropTypes.number.isRequired,
};
StepDot.propTypes = {
  active: PropTypes.bool.isRequired,
};

export default CustomStepper;
