const { Schema, Model, model } = require("mongoose");


const CareerQualificationSchema = new Schema({
  regionID: {
    type: Schema.Types.ObjectId,
    ref: "regions",
  },
  socCode: { // first 2 digits
    type: String,
    default: ""
  },
  values: {
    0: {
      type: Number,
      default: null
    },
    1: {
      type: Number,
      default: null
    },
    2: {
      type: Number,
      default: null
    },
    3: {
      type: Number,
      default: null
    },
    4: {
      type: Number,
      default: null
    },
    5: {
      type: Number,
      default: null
    },
    6: {
      type: Number,
      default: null
    },
    7: {
      type: Number,
      default: null
    },
    8: {
      type: Number,
      default: null
    },
  }
})

module.exports.CareerQualificationSchema = CareerQualificationSchema;

class CareerQualification extends Model {

}

model(CareerQualification, CareerQualificationSchema, "careerQualification");

module.exports.CareerQualification = CareerQualification;