const { default: mongoose } = require("mongoose");
const { CollegeGroup } = require("../models/collegeGroup");
const { Country } = require("../models/country");
const { User, UserRoles } = require("../models/user");
const { getAddedBy, getEditedBy } = require('../tools/database')
const commonHelper = require("../helpers/commonHelper");
const {messageResponse} = require("../helpers/commonHelper");
const commonClass = require("../helpers/commonClass");
const { College } = require("../models/college");
const { updateUserRole, revokeUserRole } = require("../controllers/users.controller");
const { REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_UID, INVALID_USER_EXISTS, INVALID, DUPLICATE, SERVER_ERROR, UPDATE_SUCCESS, UPDATE_ERROR, REMOVE_ERROR_ENGAGED, REMOVE_SUCCESS, UNAUTHORIZED, INVALID_USER_ROLE, ADD_ERROR } = require("../config/messages");
const { INVALID_MISSING } = require("../config/messages");

const validate = async(req, res, action) => {
  try {
    let collegeGroup;
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null)
      }

      collegeGroup = await CollegeGroup.findById(new mongoose.Types.ObjectId(req.body.id));
      if (!collegeGroup) {
        return messageResponse(NOT_FOUND, "College Group", false, 404, null);
      }
      else if (req.user.role == UserRoles.COLLEGE_GROUP_ADMIN && collegeGroup.adminUserId.toString() != req.user._id.toString()) {
        return messageResponse(EXIST_PERMISSION, "College Group", false, 404, null)
      }
    }

    if (!req.body.name) {
      return messageResponse(INVALID_MISSING, "Name", false, 400, null)
    }

    if (req.body.countryId) {
      if (!mongoose.isValidObjectId(req.body.countryId)) {
        return messageResponse(INVALID_MISSING, "Country ID", false, 400, null)
      }

      const selectedCountry = await Country.findById(new mongoose.Types.ObjectId(req.body.countryId));
      if (!selectedCountry) {
        return messageResponse(INVALID_MISSING, "Country", false, 400, null)
      }
    }

    // if (req.body.adminUserId) {
      if (!mongoose.isValidObjectId(req.body.adminUserId)) {
        return messageResponse(INVALID_UID, "", false, 400, null)
      }

      const adminUser = await User.findById(new mongoose.Types.ObjectId(req.body.adminUserId));
      if (!adminUser) {
        return messageResponse(INVALID, "Admin User", false, 400, null)
      }

      if (action == 'edit' && req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
        if (collegeGroup.adminUserId != req.body.adminUserId) {
          return messageResponse(UNAUTHORIZED, "", false, 400, null)
        }
      }
      else {
        if (adminUser) {
          if (adminUser.role != 5 && adminUser.role != 2) {
            return messageResponse(INVALID_USER_ROLE, "", false, 400, null)
          }
          else {
            if (adminUser.role == UserRoles.COLLEGE_GROUP_ADMIN) {
              if (adminUser.collegeGroupIds[0] != req.body.id) {
                return messageResponse(INVALID_USER_EXISTS, "College Group", false, 400, null);
              }
            }
          }
        }
        else {
          return messageResponse(INVALID, "Admin User", false, 400, null)
        }
      }
    // }

    // let alreadyAdminQuery = { adminUserId: req.body.adminUserId };
    // if (action == 'edit') {
    //   alreadyAdminQuery = { $and: [{ adminUserId: { $eq: req.body.adminUserId } }, { _id: { $ne: req.body.id } }] };
    // }
    // const collegeGroupAdmin = await CollegeGroup.findOne(alreadyAdminQuery);
    // if (collegeGroupAdmin) {
    //   return { statusCode: 400, success: false, msg: "User is already an admin of another college group" };
    // }

    let query = { name: req.body.name };
    if (action == 'edit') {
      query = { $and: [{ name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } }] };
    }
    const existingCG = await CollegeGroup.findOne(query);
    if (existingCG != null) {
      return messageResponse(DUPLICATE, "", false, 400, null);
    }

    return { success: true, collegeGroup };
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == 'add') {
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newCollegeGroup = await CollegeGroup.create(req.body)

      if(!newCollegeGroup) return messageResponse(ADD_ERROR, "College Group", false, 400, null, res)

      await updateUserRole(req.body.adminUserId, 2, newCollegeGroup._id);
      res.status(200).json({ success: true, id: newCollegeGroup._id })
    }
    else {
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedCollegeGroup = await CollegeGroup.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updatedCollegeGroup) return messageResponse(EXIST_PERMISSION, "College Group", false, 404, null, res) 

      await updateUserRole(req.body.adminUserId, 2, req.body.id, validateResult.collegeGroup);
      return messageResponse(UPDATE_SUCCESS, "College Group", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async(req, res, next) => {
  return await addOrEdit(req, res, 'add');
}
module.exports.add = add;

const get = async(req, res) => {
  try {

    const allowedCGs = await commonClass.getAllowedCollegeGroups(req);

    if(!allowedCGs.length) return messageResponse(NOT_FOUND, "College Groups", false, 404, null, res) 

    return messageResponse(null, "", true, 200, allowedCGs, res)


    // let query = {};
    // if(req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
    //   query = { adminUserId:req.user._id }
    // }
    // const collegeGroups = await CollegeGroup.find(query);
    // return res.json(collegeGroups);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.get = get;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const pipeline = [
      { $match: { _id: new mongoose.Types.ObjectId(req.query.id) } },
      {
        $lookup: {
          from: "users",
          localField: "adminUserId",
          foreignField: "_id",
          as: "adminUser"
        }
      },
      {
        $project: {
          "adminUser.password": 0,
          "adminUser.collegeGroupIds": 0,
          "adminUser.collegeIds": 0,
          "adminUser.campusIds": 0,
        }
      },
      {
        $unwind: {
          path: "$adminUser",
        }
      },
      { $limit: 1 }
    ]
    let collegeGroup = await CollegeGroup.aggregate(pipeline)
    collegeGroup = collegeGroup[0]

    if(!collegeGroup) return messageResponse(NOT_FOUND, "College Groups", false, 404, null, res)

    if(req.user.role == UserRoles.COLLEGE_GROUP_ADMIN && collegeGroup.adminUserId.toString() != req.user._id.toString()) {
      return messageResponse(EXIST_PERMISSION, "College Group", false, 404, null, res)
    }

    return messageResponse(null, "", true, 200, collegeGroup, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};
module.exports.getByID = getByID;

const remove = async(req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const allowedCGs = await commonClass.getAllowedCollegeGroups(req);
    // console.log('allowedCGs', allowedCGs);
    const reqCG = allowedCGs.find(cg => cg._id == req.body.id);
    if (!reqCG) return messageResponse(EXIST_PERMISSION, "College Group", false, 404, null, res);

    const college = await College.findOne({ collegeGroupId: req.body.id });
    if (college) return messageResponse(REMOVE_ERROR_ENGAGED, "College Group", false, 400, null, res);

    const collegeGroup = await CollegeGroup.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

    if(!collegeGroup) return messageResponse(EXIST_PERMISSION, "College Group", false, 404, null, res);

    revokeUserRole(2, req.body.id, collegeGroup)
    return messageResponse(REMOVE_SUCCESS, "College Group", true, 200, null, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove = remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit');
};
module.exports.update = update;