// @mui
import PropTypes from 'prop-types';
import { alpha, styled } from '@mui/material/styles';
import { Box, Card, Skeleton, Typography } from '@mui/material';
// utils
import { fShortenNumber } from '../../../utils/formatNumber';
// components
import Iconify from '../../../components/Iconify';

// ----------------------------------------------------------------------

const StyledIcon = styled('div')(({ theme }) => ({
  margin: 'auto',
  display: 'flex',
  borderRadius: '50%',
  alignItems: 'center',
  width: theme.spacing(8),
  height: theme.spacing(8),
  justifyContent: 'center',
  marginBottom: theme.spacing(3),
}));

// ----------------------------------------------------------------------

AppWidgetSummary.propTypes = {
  color: PropTypes.string,
  icon: PropTypes.string,
  title: PropTypes.string.isRequired,
  total: PropTypes.any.isRequired,
  sx: PropTypes.object,
};

export default function AppWidgetSummary({ loading, title, total, icon, color = 'primary', sx, ...other }) {
  return (
    <Card
      sx={{
        py: 3,
        px: 2,
        textAlign: 'center',
        bgcolor: (theme) => theme.palette.grey[100],
        border: (theme) => (`1px solid ${theme.palette.grey[200]}`),
        ...sx,
      }}
      {...other}
    >
      {
        loading ?
          <Box sx={{
            width: '100%',
            display: 'flex',
            justifyContent: 'center'
          }}>
            <Skeleton animation="wave" sx={{my:1}} variant="circular" width={60} height={60} />
          </Box> :
          <StyledIcon
            sx={{
              color: (theme) => theme.palette.primary.dark,
              bgcolor: (theme) => theme.palette.primary.lighter,
              marginBottom: '14px'
            }}
          >
            <Iconify icon={icon} width={36} height={36} />
          </StyledIcon>
      }

      {loading ?
        <Box sx={{
          width: '100%',
          display: 'flex',
          justifyContent: 'center'
        }}>
          <Skeleton animation="wave" variant="rounded" sx={{ my: 2 }} width={60} height={60} />
        </Box> :
        <Typography sx={{ color: (theme) => theme.palette.primary.dark, }} variant="h2">
          {fShortenNumber(total)}
        </Typography>

      }

      {loading ?
      <>
        <Skeleton animation="wave" variant="text" sx={{ fontSize: '1rem' }} /> 
        </>:
        <Typography variant="subtitle2" sx={{ opacity: 0.7, color: (theme) => theme.palette.primary.darker, minHeight: '44px', mt: '10px' }}>
          {title}
        </Typography>
      }
    </Card>
  );
}
