import React from 'react';
import PropTypes, { bool } from 'prop-types';
import { TextField } from '@mui/material';
import Typography from 'src/theme/overrides/Typography';
import { Controller } from 'react-hook-form';

function TextFIeldComponent({
  id,
  label,
  value,
  handleChange,
  error,
  showError,
  placeholder,
  className,
  name,
  control,
  type,
  ...rest
}) {
  if (control) {
    console.log('connnnn', control  );
    
    return (
      <Controller
        name={name}
        control={control}
        defaultValue=""
        render={({ field, fieldState }) => (
          <TextField
            {...field}
            id={id}
            type={type}
            label={label}
            placeholder={placeholder}
            variant="outlined"
            // value={value}
            // onChange={handleChange}
            className={className}
            name={name}
            error={!!fieldState?.error}
            helperText={fieldState?.error?.message}
          />
        )}
      />
    );
  }
  return (
    <>
      <TextField
        id={id}
        label={label}
        placeholder={placeholder}
        variant="outlined"
        value={value}
        onChange={handleChange}
        className={className}
        name={name}
        {...rest}
      />
      {showError ? (
        <Typography variant="error" color="primary.error">
          {error}
        </Typography>
      ) : (
        ''
      )}
    </>
  );
}
TextFIeldComponent.propTypes = {
  id: PropTypes.string,
  label: PropTypes.string,
  value: PropTypes.string,
  handleChange: PropTypes.func,
  showError: PropTypes.bool,
  error: PropTypes.string,
  placeholder: PropTypes.string,
  className: PropTypes.string,
  control: PropTypes.any,
  name: PropTypes.string,
  type: PropTypes.string,
};

export default TextFIeldComponent;
