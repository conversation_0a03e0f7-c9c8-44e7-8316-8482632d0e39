const mongoose = require('mongoose');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import all services and models for testing
const FineTuningDataService = require('../services/fineTuningDataService');
const MessageProcessingService = require('../services/messageProcessingService');
const ModerationService = require('../services/moderationService');
const ChatGPTService = require('../services/chatGPTService');
const OpenAIFineTuningService = require('../services/openAIFineTuningService');

const { ChatSession } = require('../models/chatbotModels');
const { ChatMessage } = require('../models/chatbotModels');
const { ChatViolation } = require('../models/chatbotModels');
const { College } = require('../models/college');

/**
 * Complete System Testing Suite
 * Comprehensive end-to-end testing of the entire fine-tuning chatbot system
 */
class CompleteSystemTester {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      testSuites: {},
      startTime: null,
      endTime: null,
      duration: 0
    };

    this.testSessions = [];
    this.testColleges = [];
    this.apiBaseURL = process.env.API_BASE_URL || 'http://localhost:3000';
  }

  /**
   * Run complete system test suite
   */
  async runCompleteTests(options = {}) {
    const {
      includeDataPipeline = true,
      includeAPITests = true,
      includeServiceTests = true,
      includeIntegrationTests = true,
      includeStressTests = false,
      cleanupAfterTests = true
    } = options;

    console.log('🧪 Starting Complete System Testing Suite');
    console.log('==========================================');
    console.log(`📊 Test Configuration:`);
    console.log(`   Data Pipeline: ${includeDataPipeline}`);
    console.log(`   API Tests: ${includeAPITests}`);
    console.log(`   Service Tests: ${includeServiceTests}`);
    console.log(`   Integration Tests: ${includeIntegrationTests}`);
    console.log(`   Stress Tests: ${includeStressTests}`);

    this.testResults.startTime = new Date();

    try {
      // Connect to MongoDB
      await this.connectToDatabase();

      // Setup test data
      await this.setupTestData();

      // Test Suite 1: Data Pipeline Tests
      if (includeDataPipeline) {
        await this.runDataPipelineTests();
      }

      // Test Suite 2: Service Layer Tests
      if (includeServiceTests) {
        await this.runServiceLayerTests();
      }

      // Test Suite 3: API Endpoint Tests
      if (includeAPITests) {
        await this.runAPIEndpointTests();
      }

      // Test Suite 4: Integration Tests
      if (includeIntegrationTests) {
        await this.runIntegrationTests();
      }

      // Test Suite 5: Stress Tests (optional)
      if (includeStressTests) {
        await this.runStressTests();
      }

      // Generate comprehensive report
      await this.generateTestReport();

      // Cleanup
      if (cleanupAfterTests) {
        await this.cleanupTestData();
      }

      this.testResults.endTime = new Date();
      this.testResults.duration = this.testResults.endTime - this.testResults.startTime;

      console.log('\n🎉 Complete System Testing Finished!');
      console.log('====================================');
      console.log(`📊 Total Tests: ${this.testResults.totalTests}`);
      console.log(`✅ Passed: ${this.testResults.passedTests}`);
      console.log(`❌ Failed: ${this.testResults.failedTests}`);
      console.log(`⏭️ Skipped: ${this.testResults.skippedTests}`);
      console.log(`⏱️ Duration: ${Math.round(this.testResults.duration / 1000)}s`);
      console.log(`📊 Success Rate: ${((this.testResults.passedTests / this.testResults.totalTests) * 100).toFixed(1)}%`);

      return this.testResults;

    } catch (error) {
      console.error('❌ Complete system testing failed:', error);
      throw error;
    } finally {
      await mongoose.disconnect();
      console.log('✅ Disconnected from MongoDB');
    }
  }

  /**
   * Connect to database
   */
  async connectToDatabase() {
    console.log('\n🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
    console.log('✅ Connected to MongoDB');
  }

  /**
   * Setup test data
   */
  async setupTestData() {
    console.log('\n🔧 Setting up test data...');
    
    // Get test colleges
    this.testColleges = await College.find({}).limit(3).lean();
    if (this.testColleges.length === 0) {
      throw new Error('No colleges found for testing');
    }

    console.log(`✅ Found ${this.testColleges.length} test colleges`);
  }

  /**
   * Test Suite 1: Data Pipeline Tests
   */
  async runDataPipelineTests() {
    console.log('\n📊 Running Data Pipeline Tests...');
    const suiteResults = { passed: 0, failed: 0, total: 0 };

    try {
      // Test 1: Data Extraction
      await this.runTest('Data Extraction', async () => {
        const service = new FineTuningDataService();
        const result = await service.dataExtractor.extractAllData({
          maxRecordsPerType: 5,
          includeMetadata: true
        });
        
        if (!result.metadata || result.metadata.statistics.totalRecords === 0) {
          throw new Error('No data extracted');
        }
        
        return { recordsExtracted: result.metadata.statistics.totalRecords };
      }, suiteResults);

      // Test 2: Training Data Generation
      await this.runTest('Training Data Generation', async () => {
        const service = new FineTuningDataService();
        const result = await service.generateEnhancedTrainingData({
          maxExamplesPerType: 5,
          validationSplit: 0.2,
          validateData: true,
          examplesPerRecord: 2
        });
        
        if (result.trainingData.length === 0) {
          throw new Error('No training data generated');
        }
        
        return { 
          trainingExamples: result.trainingData.length,
          validationExamples: result.validationData.length,
          qualityScore: result.validationResults?.report?.qualityScore || 'N/A'
        };
      }, suiteResults);

      // Test 3: Data Validation
      await this.runTest('Data Validation', async () => {
        const service = new FineTuningDataService();
        const sampleData = [
          {
            messages: [
              { role: 'system', content: 'You are a helpful assistant.' },
              { role: 'user', content: 'Test question' },
              { role: 'assistant', content: 'Test response' }
            ]
          }
        ];
        
        const result = await service.validateTrainingData(sampleData);
        
        if (result.validExamples === 0) {
          throw new Error('Validation failed');
        }
        
        return { 
          validExamples: result.validExamples,
          qualityScore: result.report?.qualityScore || 'N/A'
        };
      }, suiteResults);

    } catch (error) {
      console.error('❌ Data Pipeline Tests failed:', error);
    }

    this.testResults.testSuites.dataPipeline = suiteResults;
    console.log(`📊 Data Pipeline Tests: ${suiteResults.passed}/${suiteResults.total} passed`);
  }

  /**
   * Test Suite 2: Service Layer Tests
   */
  async runServiceLayerTests() {
    console.log('\n🔧 Running Service Layer Tests...');
    const suiteResults = { passed: 0, failed: 0, total: 0 };

    try {
      // Test 1: Message Processing Service
      await this.runTest('Message Processing Service', async () => {
        const service = new MessageProcessingService();
        
        // Create test session
        const testSession = new ChatSession({
          collegeId: this.testColleges[0]._id,
          ipAddress: '127.0.0.1',
          userAgent: 'Test Agent',
          status: 'active'
        });
        const savedSession = await testSession.save();
        this.testSessions.push(savedSession._id);

        const result = await service.processMessage(
          savedSession._id.toString(),
          'What programs do you offer?',
          { maxTokens: 100 }
        );
        
        if (!result.success) {
          throw new Error('Message processing failed');
        }
        
        return { 
          responseType: result.responseType,
          modelUsed: result.modelUsed,
          responseLength: result.response.length
        };
      }, suiteResults);

      // Test 2: Moderation Service
      await this.runTest('Moderation Service', async () => {
        const service = new ModerationService();
        
        // Test appropriate content
        const goodResult = await service.moderateMessage('test-session', 'What courses do you offer?');
        if (!goodResult.allowed) {
          throw new Error('Good content was blocked');
        }
        
        // Test inappropriate content
        const badResult = await service.moderateMessage('test-session', 'spam spam spam spam spam!!!');
        if (badResult.allowed) {
          console.warn('⚠️ Spam content was not blocked (may need tuning)');
        }
        
        return { 
          goodContentAllowed: goodResult.allowed,
          spamContentBlocked: !badResult.allowed
        };
      }, suiteResults);

      // Test 3: ChatGPT Service
      await this.runTest('ChatGPT Service', async () => {
        const service = new ChatGPTService();
        
        const messages = [
          { role: 'system', content: 'You are a helpful assistant.' },
          { role: 'user', content: 'Hello, this is a test.' }
        ];
        
        const result = await service.generateResponse(messages, {
          maxTokens: 50,
          temperature: 0.5
        });
        
        if (!result.content) {
          throw new Error('No response generated');
        }
        
        return { 
          responseType: result.type,
          modelUsed: result.modelUsed,
          responseLength: result.content.length
        };
      }, suiteResults);

      // Test 4: Fine-Tuning Service
      await this.runTest('Fine-Tuning Service', async () => {
        const service = new OpenAIFineTuningService();
        
        // Test model testing functionality
        const testResult = await service.testModel();
        
        return { 
          testPassed: testResult.success,
          modelUsed: testResult.modelUsed || 'unknown'
        };
      }, suiteResults);

    } catch (error) {
      console.error('❌ Service Layer Tests failed:', error);
    }

    this.testResults.testSuites.serviceLayer = suiteResults;
    console.log(`📊 Service Layer Tests: ${suiteResults.passed}/${suiteResults.total} passed`);
  }

  /**
   * Test Suite 3: API Endpoint Tests
   */
  async runAPIEndpointTests() {
    console.log('\n🌐 Running API Endpoint Tests...');
    const suiteResults = { passed: 0, failed: 0, total: 0 };

    try {
      let testSessionId = null;

      // Test 1: Session Initialization
      await this.runTest('Session Initialization API', async () => {
        const response = await this.makeAPICall('POST', '/api/chatbot/session/init', {
          collegeId: this.testColleges[0]._id.toString(),
          userAgent: 'Test Agent'
        });
        
        if (!response.success || !response.data.sessionId) {
          throw new Error('Session initialization failed');
        }
        
        testSessionId = response.data.sessionId;
        this.testSessions.push(testSessionId);
        
        return { 
          sessionId: testSessionId,
          collegeName: response.data.collegeInfo.name
        };
      }, suiteResults);

      // Test 2: Message Processing API
      if (testSessionId) {
        await this.runTest('Message Processing API', async () => {
          const response = await this.makeAPICall('POST', '/api/chatbot/message', {
            sessionId: testSessionId,
            message: 'What computer science courses do you offer?',
            options: { maxTokens: 150 }
          });
          
          if (!response.success || !response.data.response) {
            throw new Error('Message processing API failed');
          }
          
          return { 
            responseType: response.data.responseType,
            modelUsed: response.data.modelUsed,
            responseLength: response.data.response.length
          };
        }, suiteResults);
      }

      // Test 3: Session History API
      if (testSessionId) {
        await this.runTest('Session History API', async () => {
          const response = await this.makeAPICall('GET', `/api/chatbot/session/${testSessionId}/history`);
          
          if (!response.success || !response.data.messages) {
            throw new Error('Session history API failed');
          }
          
          return { 
            messageCount: response.data.messages.length,
            totalMessages: response.data.pagination.total
          };
        }, suiteResults);
      }

      // Test 4: Admin Status API
      await this.runTest('Admin Status API', async () => {
        const response = await this.makeAPICall('GET', '/api/chatbot/admin/status');
        
        if (!response.success || !response.data.system) {
          throw new Error('Admin status API failed');
        }
        
        return { 
          systemStatus: response.data.system.status,
          fineTunedModelAvailable: response.data.system.fineTunedModelAvailable,
          totalSessions: response.data.statistics.sessions.total
        };
      }, suiteResults);

      // Test 5: Model Testing API
      await this.runTest('Model Testing API', async () => {
        const response = await this.makeAPICall('POST', '/api/chatbot/admin/test-model', {
          testMessage: 'Hello, this is a test message.'
        });
        
        if (!response.success) {
          throw new Error('Model testing API failed');
        }
        
        return { 
          testPassed: response.data.testPassed,
          modelUsed: response.data.modelUsed,
          responseType: response.data.responseType
        };
      }, suiteResults);

    } catch (error) {
      console.error('❌ API Endpoint Tests failed:', error);
    }

    this.testResults.testSuites.apiEndpoints = suiteResults;
    console.log(`📊 API Endpoint Tests: ${suiteResults.passed}/${suiteResults.total} passed`);
  }

  /**
   * Helper method to run individual tests
   */
  async runTest(testName, testFunction, suiteResults) {
    this.testResults.totalTests++;
    suiteResults.total++;
    
    try {
      console.log(`   🧪 Running: ${testName}`);
      const result = await testFunction();
      
      this.testResults.passedTests++;
      suiteResults.passed++;
      
      console.log(`   ✅ ${testName}: PASSED`);
      if (result && typeof result === 'object') {
        Object.entries(result).forEach(([key, value]) => {
          console.log(`      ${key}: ${value}`);
        });
      }
      
    } catch (error) {
      this.testResults.failedTests++;
      suiteResults.failed++;
      
      console.log(`   ❌ ${testName}: FAILED`);
      console.log(`      Error: ${error.message}`);
    }
  }

  /**
   * Helper method to make API calls
   */
  async makeAPICall(method, endpoint, data = null) {
    const url = `${this.apiBaseURL}${endpoint}`;
    
    try {
      let response;
      
      if (method === 'GET') {
        response = await axios.get(url);
      } else if (method === 'POST') {
        response = await axios.post(url, data);
      } else {
        throw new Error(`Unsupported method: ${method}`);
      }
      
      return response.data;
    } catch (error) {
      if (error.response) {
        return error.response.data;
      } else {
        throw error;
      }
    }
  }

  /**
   * Cleanup test data
   */
  async cleanupTestData() {
    console.log('\n🧹 Cleaning up test data...');
    
    try {
      // Clean up test sessions
      if (this.testSessions.length > 0) {
        await ChatSession.deleteMany({ _id: { $in: this.testSessions } });
        await ChatMessage.deleteMany({ sessionId: { $in: this.testSessions } });
        await ChatViolation.deleteMany({ sessionId: { $in: this.testSessions } });
        console.log(`✅ Cleaned up ${this.testSessions.length} test sessions`);
      }
      
    } catch (error) {
      console.warn('⚠️ Cleanup warning:', error.message);
    }
  }

  /**
   * Test Suite 4: Integration Tests
   */
  async runIntegrationTests() {
    console.log('\n🔗 Running Integration Tests...');
    const suiteResults = { passed: 0, failed: 0, total: 0 };

    try {
      // Test 1: Complete User Journey
      await this.runTest('Complete User Journey', async () => {
        // Step 1: Initialize session
        const initResponse = await this.makeAPICall('POST', '/api/chatbot/session/init', {
          collegeId: this.testColleges[0]._id.toString(),
          userAgent: 'Integration Test Agent'
        });

        if (!initResponse.success) {
          throw new Error('Session initialization failed');
        }

        const sessionId = initResponse.data.sessionId;
        this.testSessions.push(sessionId);

        // Step 2: Send multiple messages
        const messages = [
          'What programs do you offer?',
          'Tell me about computer science courses.',
          'What are the admission requirements?'
        ];

        let responseCount = 0;
        for (const message of messages) {
          const msgResponse = await this.makeAPICall('POST', '/api/chatbot/message', {
            sessionId: sessionId,
            message: message,
            options: { maxTokens: 100 }
          });

          if (msgResponse.success) {
            responseCount++;
          }

          // Wait between messages
          await new Promise(resolve => setTimeout(resolve, 500));
        }

        // Step 3: Get conversation history
        const historyResponse = await this.makeAPICall('GET', `/api/chatbot/session/${sessionId}/history`);

        if (!historyResponse.success) {
          throw new Error('History retrieval failed');
        }

        // Step 4: End session
        const endResponse = await this.makeAPICall('POST', `/api/chatbot/session/${sessionId}/end`);

        return {
          sessionCreated: true,
          messagesProcessed: responseCount,
          totalMessages: messages.length,
          historyRetrieved: historyResponse.success,
          sessionEnded: endResponse.success
        };
      }, suiteResults);

      // Test 2: Content Moderation Integration
      await this.runTest('Content Moderation Integration', async () => {
        const initResponse = await this.makeAPICall('POST', '/api/chatbot/session/init', {
          collegeId: this.testColleges[0]._id.toString(),
          userAgent: 'Moderation Test Agent'
        });

        const sessionId = initResponse.data.sessionId;
        this.testSessions.push(sessionId);

        // Test inappropriate content
        const badResponse = await this.makeAPICall('POST', '/api/chatbot/message', {
          sessionId: sessionId,
          message: 'spam spam spam spam spam!!!'
        });

        // Test good content
        const goodResponse = await this.makeAPICall('POST', '/api/chatbot/message', {
          sessionId: sessionId,
          message: 'What courses do you offer in engineering?'
        });

        return {
          inappropriateContentHandled: badResponse.success && badResponse.data.responseType === 'violation',
          appropriateContentProcessed: goodResponse.success && goodResponse.data.responseType !== 'violation'
        };
      }, suiteResults);

      // Test 3: Database Consistency
      await this.runTest('Database Consistency', async () => {
        const initResponse = await this.makeAPICall('POST', '/api/chatbot/session/init', {
          collegeId: this.testColleges[0]._id.toString(),
          userAgent: 'DB Test Agent'
        });

        const sessionId = initResponse.data.sessionId;
        this.testSessions.push(sessionId);

        // Send a message
        await this.makeAPICall('POST', '/api/chatbot/message', {
          sessionId: sessionId,
          message: 'Test message for database consistency'
        });

        // Check database directly
        const session = await ChatSession.findById(sessionId);
        const messages = await ChatMessage.find({ sessionId: sessionId });

        if (!session) {
          throw new Error('Session not found in database');
        }

        if (messages.length === 0) {
          throw new Error('Messages not saved to database');
        }

        return {
          sessionInDB: !!session,
          messagesInDB: messages.length,
          sessionStatus: session.status
        };
      }, suiteResults);

    } catch (error) {
      console.error('❌ Integration Tests failed:', error);
    }

    this.testResults.testSuites.integration = suiteResults;
    console.log(`📊 Integration Tests: ${suiteResults.passed}/${suiteResults.total} passed`);
  }

  /**
   * Test Suite 5: Stress Tests
   */
  async runStressTests() {
    console.log('\n💪 Running Stress Tests...');
    const suiteResults = { passed: 0, failed: 0, total: 0 };

    try {
      // Test 1: Concurrent Sessions
      await this.runTest('Concurrent Sessions', async () => {
        const concurrentSessions = 5;
        const promises = [];

        for (let i = 0; i < concurrentSessions; i++) {
          promises.push(this.createAndTestSession(`Stress Test ${i + 1}`));
        }

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled').length;

        return {
          concurrentSessions: concurrentSessions,
          successfulSessions: successful,
          successRate: `${((successful / concurrentSessions) * 100).toFixed(1)}%`
        };
      }, suiteResults);

      // Test 2: Rapid Message Processing
      await this.runTest('Rapid Message Processing', async () => {
        const initResponse = await this.makeAPICall('POST', '/api/chatbot/session/init', {
          collegeId: this.testColleges[0]._id.toString(),
          userAgent: 'Rapid Test Agent'
        });

        const sessionId = initResponse.data.sessionId;
        this.testSessions.push(sessionId);

        const rapidMessages = 10;
        const promises = [];

        for (let i = 0; i < rapidMessages; i++) {
          promises.push(this.makeAPICall('POST', '/api/chatbot/message', {
            sessionId: sessionId,
            message: `Rapid test message ${i + 1}`,
            options: { maxTokens: 50 }
          }));
        }

        const results = await Promise.allSettled(promises);
        const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;

        return {
          rapidMessages: rapidMessages,
          successfulMessages: successful,
          successRate: `${((successful / rapidMessages) * 100).toFixed(1)}%`
        };
      }, suiteResults);

    } catch (error) {
      console.error('❌ Stress Tests failed:', error);
    }

    this.testResults.testSuites.stress = suiteResults;
    console.log(`📊 Stress Tests: ${suiteResults.passed}/${suiteResults.total} passed`);
  }

  /**
   * Helper method to create and test a session
   */
  async createAndTestSession(userAgent) {
    const initResponse = await this.makeAPICall('POST', '/api/chatbot/session/init', {
      collegeId: this.testColleges[0]._id.toString(),
      userAgent: userAgent
    });

    if (!initResponse.success) {
      throw new Error('Session creation failed');
    }

    const sessionId = initResponse.data.sessionId;
    this.testSessions.push(sessionId);

    const msgResponse = await this.makeAPICall('POST', '/api/chatbot/message', {
      sessionId: sessionId,
      message: 'Test message',
      options: { maxTokens: 50 }
    });

    return {
      sessionId: sessionId,
      messageProcessed: msgResponse.success
    };
  }

  /**
   * Generate comprehensive test report
   */
  async generateTestReport() {
    console.log('\n📋 Generating Test Report...');

    const report = {
      summary: {
        totalTests: this.testResults.totalTests,
        passedTests: this.testResults.passedTests,
        failedTests: this.testResults.failedTests,
        skippedTests: this.testResults.skippedTests,
        successRate: ((this.testResults.passedTests / this.testResults.totalTests) * 100).toFixed(1),
        duration: Math.round(this.testResults.duration / 1000),
        timestamp: new Date()
      },
      testSuites: this.testResults.testSuites,
      environment: {
        nodeVersion: process.version,
        mongooseVersion: mongoose.version,
        apiBaseURL: this.apiBaseURL,
        testColleges: this.testColleges.length
      },
      recommendations: this.generateRecommendations()
    };

    // Save report to file
    const reportDir = path.join(__dirname, '../test-reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const reportFile = path.join(reportDir, `complete-system-test-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');

    console.log(`📋 Test report saved: ${reportFile}`);
    return report;
  }

  /**
   * Generate recommendations based on test results
   */
  generateRecommendations() {
    const recommendations = [];

    if (this.testResults.failedTests > 0) {
      recommendations.push('Review and fix failed tests before deployment');
    }

    if (this.testResults.passedTests / this.testResults.totalTests < 0.9) {
      recommendations.push('Improve test success rate to at least 90% before production');
    }

    // Check specific test suite results
    Object.entries(this.testResults.testSuites).forEach(([suiteName, results]) => {
      if (results.failed > 0) {
        recommendations.push(`Address failures in ${suiteName} test suite`);
      }
    });

    if (recommendations.length === 0) {
      recommendations.push('All tests passed! System is ready for production deployment.');
    }

    return recommendations;
  }
}

// Command line interface
const runCompleteSystemTests = async () => {
  const args = process.argv.slice(2);
  const tester = new CompleteSystemTester();

  const options = {
    includeDataPipeline: !args.includes('--skip-data'),
    includeAPITests: !args.includes('--skip-api'),
    includeServiceTests: !args.includes('--skip-services'),
    includeIntegrationTests: !args.includes('--skip-integration'),
    includeStressTests: args.includes('--include-stress'),
    cleanupAfterTests: !args.includes('--no-cleanup')
  };

  try {
    await tester.runCompleteTests(options);
  } catch (error) {
    console.error('❌ Testing failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runCompleteSystemTests();
}

module.exports = CompleteSystemTester;
