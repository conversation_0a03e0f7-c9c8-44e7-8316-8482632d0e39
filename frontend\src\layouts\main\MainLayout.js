import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate, Outlet, useLocation, useParams } from 'react-router-dom';
import Cookies from 'js-cookie';
import { useDispatch, useSelector } from 'react-redux';
// @mui
import { Box, CircularProgress } from '@mui/material';
// config
import { HEADER } from 'src/config-global';
//
import AlertComponent from 'src/components/Alert/Alert';
import { analytics, getCurrentPath } from 'src/utils';
import Header from './header/Header';
import Footer from './footer/Footer';
import {
  addCollegeDetails,
  addRegionDetails,
  addSlug,
  setAlert,
  useGetClgBySlugQuery,
  useGetRegionBySlugQuery,
} from './MainLayoutSlice';

// ----------------------------------------------------------------------

const pathsOnDark = ['/career/landing', '/travel/landing'];

const spacingLayout = [...pathsOnDark, '/', '/e-learning/landing', '/marketing/landing'];

// ----------------------------------------------------------------------

MainLayout.propTypes = {
  getCollegeColors: PropTypes.func,
};

export default function MainLayout({ getCollegeColors }) {
  const { pathname } = useLocation();
  const { cg_name, rg_name } = useParams();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [skip, setSkip] = useState(true);
  const [clgName, setClgName] = useState(Cookies.get('url-slug'));
  const {
    data: clgBySlug,
    error: slugError,
    isLoading: slugLoading,
  } = useGetClgBySlugQuery(cg_name, { skip: !cg_name });

  const {
    data: clgByRegion,
    error: regionError,
    isLoading: regionLoading,
  } = useGetRegionBySlugQuery(rg_name, { skip: !rg_name });

  // Use the data you need:
  const clgDetails = clgBySlug || clgByRegion;
  const isLoading = slugLoading || regionLoading;
  const error = slugError || regionError;
  const alertState = useSelector((state) => state.mainLayout.alert);
  useEffect(() => {
    if (cg_name !== clgName && cg_name) {
      Cookies.set('url-slug', cg_name);
    }
    if (cg_name || rg_name) {
      setSkip(false);
      if (cg_name) dispatch(addSlug(cg_name));
      if (rg_name) dispatch(addSlug(rg_name));
    }
  }, [cg_name, rg_name, clgName]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (clgDetails?.success) {
      getCollegeColors({
        primary: clgDetails.data.primaryColor,
        secondary: clgDetails.data.secondaryColor,
      });
      Cookies.set('college-id', clgDetails.data._id);

      dispatch(addCollegeDetails(clgDetails.data));
      dispatch(addRegionDetails(clgDetails.data));
      analytics({
        collegeId: clgDetails?.data?._id,
        event: 'LANDING_PAGE',
        from: getCurrentPath(),
      });
    } else if (error?.data?.success === false) {
      navigate('/college-not-found');
      dispatch(
        setAlert({
          open: true,
          msg: error?.data.message ? error?.data.message : 'Something went wrong',
        })
      );
    }
  }, [clgDetails, error]); // eslint-disable-line react-hooks/exhaustive-deps

  const actionPage = (arr) => arr.some((path) => pathname === path);
  const onClose = () => {
    dispatch(
      setAlert({
        open: false,
        msg: '',
        isSuccess: false
      })
    );
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: 1 }}>
      <Header
        clgDetails={clgDetails}
        isLoading={isLoading}
        headerOnDark={actionPage(pathsOnDark)}
      />
      <AlertComponent alert={alertState} handleClose={onClose} />
      {isLoading ? (
        <Box
          component="main"
          sx={{
            flexGrow: 1,
            minHeight: '100vh',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box
          component="main"
          sx={{
            flexGrow: 1,
          }}
        >
          {!actionPage(spacingLayout) && <Spacing />}
          <Outlet />
        </Box>
      )}
      <Footer />
    </Box>
  );
}

// ----------------------------------------------------------------------

function Spacing() {
  return (
    <Box
      sx={{
        height: { xs: HEADER.H_MOBILE, md: HEADER.H_MAIN_DESKTOP },
      }}
    />
  );
}
