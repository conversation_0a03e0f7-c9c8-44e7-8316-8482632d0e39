import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, Checkbox, Container, FormControl, FormControlLabel, FormGroup, FormLabel, Grid, InputAdornment, MenuItem, Stack, TextField, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { get } from 'lodash';
import jwtDecode from "jwt-decode";
import Cookies from 'universal-cookie';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import PhoneInput from '../../components/PhoneInput';
import SelectComponent from '../../components/SelectComponent';
import SelectField from '../../components/SelectedField';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { CancelButton, label } from '../../utils/cssStyles';
import { collegeValidationSchema } from '../../utils/validationSchemas';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getAvailableUsers, getUsers } from '../Userspage/usersSlice';
import noImagePlaceholder from '../../assets/images/no-image-icon-0.jpg';
import { postCollege } from './collegesSlice';
import UrlComponent from '../../components/UrlComponent';

// groups,

const AddColleges = () => {
    const [Groups, setGroups] = useState([]);
    const [loading, setLoading] = useState(false)
    const [BluredOnce, setBluredOnce] = useState(false)
    const groups = useSelector(state => state.collegeGroups.groups)
    const dispatch = useDispatch()
    const { availableUsers } = useSelector(state => state.users)
    const cookies = new Cookies();
    const jwtToken = cookies.get("token")
    const Role = jwtToken && jwtDecode(jwtToken)
    const role = String(Role?.role)
    useEffect(() => {
        setGroups(groups)
    }, [groups])
    useEffect(() => {

        const currentUrl = window.location.host;
        dispatch(getCollegeGroups())
        const params = {
            role: '2',
            includeIds: ""
        }
        dispatch(getAvailableUsers(params))
    }, [])

    const navigate = useNavigate()
    const removePic = () => {
        formik.setValues({
            ...formik.values,
            logo: ''
        })
        document.getElementById("logo").value = ''
    }
    const formik = useFormik({
        initialValues: {
            name: '',
            groupName: '',
            collegeAddress: '',
            collegeWebsiteAddress: '',
            collegeEmail: '',
            collegeTelNumber: '',
            collegeState: '',
            collegeCity: '',
            collegeZip: '',
            admin: '',
            slug: '',
            primaryColor: '#7040f1',
            secondaryColor: '#fd1f97',
            thirdColor: '#ffa236',
            image: '',
            description: '',
            logo: '',
            permissions: {
                dashboard: false,
                campuses: false,
                users: false,
                courses: false
            }
        },
        onSubmit: (values) => {
            // const ID = Colleges[Colleges.length - 1].id + 1
            // dispatch(addCollege({ id: ID, ...values }))
            setLoading(true)
            const data = {
                ...values,
                name: values.name,
                collegeGroupId: values.groupName,
                address1: values.collegeAddress,
                contactNumber: values.collegeTelNumber,
                email: values.collegeEmail,
                website: values.collegeWebsiteAddress,
                city: values.collegeCity,
                state: values.collegeState,
                zip: values.collegeZip,
                groupName: Groups.find(group => group.id === values.groupName).Name || '',
                // adminUserId: values.admin,
            }
            if (values?.admin) {
                data.adminUserId = values.admin
            }
            dispatch(postCollege(data)).then(res => {
                if (res?.payload?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: "Succesfully added college"
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges`)

                } else {
                    const errorMessage = get(res, 'payload.response.data.message', "Something went wrong!")
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }))
                }
            }).finally(() => setLoading(false))
        },
        validationSchema: collegeValidationSchema
    })
    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            collegeTelNumber: newValue
        })
    }
    const style = {
        p: 4,
    };
    const handleCollegeNameBlur = () => {
        // formik.handleBlur()
        if (!BluredOnce && formik.values.name) {
            const slug = formik.values.name.split(" ").join('-').toLocaleLowerCase()
            formik.setValues({
                ...formik.values,
                slug
            })
            setBluredOnce(true)
        }
    }
    return (
        <Container maxWidth="xl">
            <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                Add College
            </Typography>
            <Grid container wrap='wrap' width={'100%'} justifyContent='flex-end' gap={2}>
                <Grid item xs={12} lg={2.8} mx={'auto'} >
                    <Stack direction='column' gap={2}>
                     
                        <Card variant="outlined" sx={{ p: 2 }}>
                            <Typography variant="subtitle1" mb={1} >
                                Upload College Logo
                            </Typography>

                            <Box
                                onDrop={(e) => {
                                    e.preventDefault();
                                    const file = e.dataTransfer.files[0];
                                    const reader = new FileReader();
                                    reader.readAsDataURL(file);
                                    reader.onload = (ev) => {
                                        formik.setFieldValue('logo', ev.target.result);
                                    };
                                }}
                                onDragOver={(e) => e.preventDefault()}
                                sx={{
                                    border: '2px dashed #ccc',
                                    borderRadius: 2,
                                    p: 2,
                                    textAlign: 'center',
                                    cursor: 'pointer',
                                    mb: 2,
                                    minHeight: 150,
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                }}
                            >
                                {formik.values.logo ? (
                                    <img
                                        src={formik.values.logo}
                                        alt="Logo"
                                        style={{ width: '100%', maxHeight: 150, objectFit: 'contain' }}
                                    />
                                ) : (
                                    <Typography variant="body2" color="text.secondary">
                                        Drag & drop a logo image here or click below
                                    </Typography>
                                )}
                            </Box>

                            <Button
                                variant="outlined"
                                component="label"
                                fullWidth
                            >
                                Browse Logo
                                <input
                                    type="file"
                                    hidden
                                    accept="image/*"
                                    onChange={(e) => {
                                        const file = e.target.files[0];
                                        const reader = new FileReader();
                                        reader.readAsDataURL(file);
                                        reader.onload = (ev) => {
                                            formik.setFieldValue('logo', ev.target.result);
                                        };
                                    }}
                                />
                            </Button>

                            {formik.values.logo && (
                                <Button
                                    color="error"
                                    fullWidth
                                    sx={{ mt: 1 }}
                                    onClick={() => formik.setFieldValue('logo', '')}
                                >
                                    Remove Logo
                                </Button>
                            )}
                        </Card>
                        <Grid item xs={11.8}>
                            <Card variant="outlined" sx={{ p: 2 }}>
                                <Typography variant="subtitle1" mb={1}>
                                    Upload College Image
                                </Typography>
                                <Box
                                    onDrop={(e) => {
                                        e.preventDefault();
                                        const file = e.dataTransfer.files[0];
                                        const reader = new FileReader();
                                        reader.readAsDataURL(file);
                                        reader.onload = (ev) => {
                                            formik.setFieldValue('image', ev.target.result);
                                        };
                                    }}
                                    onDragOver={(e) => e.preventDefault()}
                                    sx={{
                                        border: '2px dashed #ccc',
                                        borderRadius: 2,
                                        p: 2,
                                        textAlign: 'center',
                                        cursor: 'pointer',
                                        mb: 2,
                                        minHeight: 150,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    {formik.values.image ? (
                                        <img
                                            src={formik.values.image}
                                            alt="Uploaded"
                                            style={{ width: '100%', maxHeight: 150, objectFit: 'contain' }}
                                        />
                                    ) : (
                                        <Typography variant="body2" color="text.secondary">
                                            Drag & drop an image here or click below
                                        </Typography>
                                    )}
                                </Box>

                                <Button
                                    variant="outlined"
                                    component="label"
                                    fullWidth
                                >
                                    Browse Image
                                    <input
                                        type="file"
                                        hidden
                                        accept="image/*"
                                        onChange={(e) => {
                                            const file = e.target.files[0];
                                            const reader = new FileReader();
                                            reader.readAsDataURL(file);
                                            reader.onload = (ev) => {
                                                formik.setFieldValue('image', ev.target.result);
                                            };
                                        }}
                                    />
                                </Button>

                                {formik.values.image && (
                                    <Button
                                        color="error"
                                        fullWidth
                                        sx={{ mt: 1 }}
                                        onClick={() => formik.setFieldValue('image', '')}
                                    >
                                        Remove Image
                                    </Button>
                                )}
                            </Card>
                        </Grid>
                    </Stack>
                </Grid>

                <Grid xs={12} lg={8.8}>
                    <Card>
                        <Box sx={style}>
                            <form style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}
                                onSubmit={formik.handleSubmit}
                            >
                                <Grid container gap={2} >
                                    <Grid item xs={12} md={5.8} >
                                        <FormControl sx={{ minWidth: '100%' }}>
                                            <SelectComponent
                                                menuName={"Name"}
                                                menuValue={"id"}
                                                menuItems={Groups}
                                                name='groupName'
                                                labelId="group-label"
                                                onBlur={formik.handleBlur}
                                                value={formik.values.groupName}
                                                label="Group *"
                                                inputLabel="Group"
                                                onChange={formik.handleChange}
                                                error={formik.touched.groupName && Boolean(formik.errors.groupName)}
                                                labelColor={formik.touched.groupName && formik.errors.groupName && 'error'}
                                                labelError={formik.touched.groupName && formik.errors.groupName}
                                                helperText={formik.touched.groupName && formik.errors.groupName}
                                            />
                                        </FormControl>
                                    </Grid>
                                    <Grid item xs={12} md={5.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='name'
                                            label="Name"
                                            onBlur={handleCollegeNameBlur}
                                            value={formik.values.name}
                                            onChange={formik.handleChange}
                                            error={formik.touched.name && Boolean(formik.errors.name)}
                                            helperText={formik.touched.name && formik.errors.name}
                                        />
                                    </Grid>

                                    {
                                        BluredOnce &&
                                        <Grid xs={11.8}>
                                            <UrlComponent
                                                url={formik.values.slug}
                                                onInputChange={(value) => formik.setValues({
                                                    ...formik.values,
                                                    slug: value
                                                })}
                                                formik={formik}
                                                error={formik.errors.slug}
                                            />
                                        </Grid>}
                                    <Grid item xs={11.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name="description"
                                            label="Description"
                                            multiline
                                            rows={4}
                                            onBlur={formik.handleBlur}
                                            value={formik.values.description}
                                            onChange={formik.handleChange}
                                            error={formik.touched.description && Boolean(formik.errors.description)}
                                            helperText={formik.touched.description && formik.errors.description}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeAddress'
                                            label="Address"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeAddress}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeAddress && Boolean(formik.errors.collegeAddress)}
                                            helperText={formik.touched.collegeAddress && formik.errors.collegeAddress}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeWebsiteAddress'
                                            label="Website Address"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeWebsiteAddress}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeWebsiteAddress && Boolean(formik.errors.collegeWebsiteAddress)}
                                            helperText={formik.touched.collegeWebsiteAddress && formik.errors.collegeWebsiteAddress}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeEmail'
                                            label="Email"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeEmail}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeEmail && Boolean(formik.errors.collegeEmail)}
                                            helperText={formik.touched.collegeEmail && formik.errors.collegeEmail}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        {/* <PhoneInput
                                            sx={{ width: "100%" }}
                                            value={formik.values.collegeTelNumber}
                                            name='collegeTelNumber'
                                            label="Number"
                                            defaultCountry="GB"
                                            onChange={handlePhoneChange}
                                            onBlur={formik.handleBlur}
                                            error={formik.touched.collegeTelNumber && Boolean(formik.errors.collegeTelNumber)}
                                            helperText={formik.touched.collegeTelNumber && formik.errors.collegeTelNumber}
                                        /> */}
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeTelNumber'
                                            label="Number"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeTelNumber}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeTelNumber && Boolean(formik.errors.collegeTelNumber)}
                                            helperText={formik.touched.collegeTelNumber && formik.errors.collegeTelNumber}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeCity'
                                            label="City"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeCity}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeCity && Boolean(formik.errors.collegeCity)}
                                            helperText={formik.touched.collegeCity && formik.errors.collegeCity}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeState'
                                            label="Country"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeState}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeState && Boolean(formik.errors.collegeState)}
                                            helperText={formik.touched.collegeState && formik.errors.collegeState}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='collegeZip'
                                            label="Post Code"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.collegeZip}
                                            onChange={formik.handleChange}
                                            error={formik.touched.collegeZip && Boolean(formik.errors.collegeZip)}
                                            helperText={formik.touched.collegeZip && formik.errors.collegeZip}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} md={5.8} lg={3.8} >
                                        <SelectField
                                            sx={{ width: '100%' }}
                                            name='admin'
                                            label="Admin"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.admin}
                                            onChange={formik.handleChange}
                                            error={formik.touched.admin && Boolean(formik.errors.admin)}
                                            helperText={formik.touched.admin && formik.errors.admin}
                                        >
                                            <MenuItem value="">
                                                <em>None</em>
                                            </MenuItem>
                                            {availableUsers?.map(user => {
                                                return (<MenuItem value={user?._id}>
                                                    {`${user?.firstName} ${user?.lastName}`}
                                                </MenuItem>)
                                            })}
                                        </SelectField>
                                    </Grid>
                                    <Grid item xs={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            type='text'
                                            name='celeveHubApiKey'
                                            label="CeleveHub API Key"
                                            // value={formik.values.primaryColor}
                                            onChange={formik.handleChange}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} md={5.8} lg={3.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            type='color'
                                            name='primaryColor'
                                            label="Primary Color"
                                            value={formik.values.primaryColor}
                                            onChange={formik.handleChange}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} md={5.8} lg={3.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            type='color'
                                            name='secondaryColor'
                                            label="Secondary Color"
                                            value={formik.values.secondaryColor}
                                            onChange={formik.handleChange}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} md={5.8} lg={3.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            type='color'
                                            name='thirdColor'
                                            label="Third Color"
                                            value={formik.values.thirdColor}
                                            onChange={formik.handleChange}
                                        />
                                    </Grid>
                                </Grid>
                                {role !== '1' && <Stack direction="row" justifyContent="flex-end" >
                                    <Button
                                        type='button'
                                        variant='contained'
                                        sx={CancelButton}
                                        color='error'
                                        onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges`)}

                                    >
                                        Cancel
                                    </Button>
                                    <LoadingButton
                                        onClick={formik.handleSubmit}
                                        loading={loading}
                                        type='submit'
                                        variant='contained'
                                        sx={{ width: '10%', m: 1, mt: 2 }}
                                    >
                                        Add
                                    </LoadingButton>
                                </Stack>}
                            </form>

                        </Box>
                    </Card>
                </Grid>
                {role === '1' && <Grid item xs={12} lg={8.8}>
                    <Card>
                        <Box sx={style}>
                            <Typography variant='h6'>
                                Permissions
                            </Typography>
                            <FormGroup sx={{ display: 'flex', flexWrap: 'wrap', flexDirection: 'row', gap: 4 }}>
                                <FormControlLabel control={<Checkbox name='permissions.dashboard' checked={formik.values?.permissions?.dashboard} onChange={formik.handleChange} />} label="Dashboard" />
                                <FormControlLabel control={<Checkbox name='permissions.campuses' checked={formik.values?.permissions?.campuses} onChange={formik.handleChange} />} label="Campuses" />
                                <FormControlLabel control={<Checkbox name='permissions.users' checked={formik.values?.permissions?.users} onChange={formik.handleChange} />} label="Users" />
                                <FormControlLabel control={<Checkbox name='permissions.courses' checked={formik.values?.permissions?.courses} onChange={formik.handleChange} />} label="Courses" />
                            </FormGroup>
                            <Stack direction="row" justifyContent="flex-end" >
                                <Button
                                    type='button'
                                    variant='contained'
                                    sx={CancelButton}
                                    color='error'
                                    onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges`)}

                                >
                                    Cancel
                                </Button>
                                <LoadingButton
                                    onClick={formik.handleSubmit}
                                    loading={loading}
                                    type='submit'
                                    variant='contained'
                                    sx={{ width: '10%', m: 1, mt: 2 }}
                                >
                                    Add
                                </LoadingButton>
                            </Stack>
                        </Box>
                    </Card>
                </Grid>}
            </Grid>
        </Container>
    )
}

export default AddColleges