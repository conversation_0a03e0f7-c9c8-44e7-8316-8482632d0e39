const mongoose = require("mongoose");
const { messageResponse } = require("../helpers/commonHelper");
const { INVALID_MISSING, NOT_FOUND, SERVER_ERROR } = require("../config/messages");
const { College } = require("../models/college");

// Ensure chatbot models are loaded
const { ChatSession } = require("../models/chatbotModels");

/**
 * Simple college ID validation middleware
 * Validates that the college exists and is active
 */
const validateCollegeAccess = async (req, res, next) => {
  try {
    const collegeId = req.body.collegeId || req.query.collegeId || req.params.collegeId;

    if (!collegeId) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, res);
    }

    if (!mongoose.isValidObjectId(collegeId)) {
      return messageResponse(INVALID_MISSING, "Valid College ID", false, 400, null, res);
    }

    // Check if college exists and is active
    const college = await College.findById(collegeId).lean();
    
    if (!college) {
      return messageResponse(NOT_FOUND, "College", false, 404, null, res);
    }

    if (college.status !== 'active') {
      return messageResponse(INVALID_MISSING, "Active College", false, 403, null, res);
    }

    // Add college info to request for use in controllers
    req.college = {
      id: college._id,
      name: college.name,
      slug: college.slug,
      status: college.status
    };

    next();

  } catch (error) {
    console.error('Error in college validation middleware:', error);
    return messageResponse(SERVER_ERROR, "College validation", false, 500, null, res);
  }
};

/**
 * Extract and validate session from request
 * Used for endpoints that require an existing session
 */
const validateSession = async (req, res, next) => {
  try {
    
    const sessionId = req.body.sessionId || req.params.sessionId;

    if (!sessionId) {
      return messageResponse(INVALID_MISSING, "Session ID", false, 400, null, res);
    }

    if (!mongoose.isValidObjectId(sessionId)) {
      return messageResponse(INVALID_MISSING, "Valid Session ID", false, 400, null, res);
    }

    // Check if session exists and is active
    const session = await ChatSession.findById(sessionId).lean();
    
    if (!session) {
      return messageResponse(NOT_FOUND, "Session", false, 404, null, res);
    }

    if (session.status !== 'active') {
      return messageResponse(INVALID_MISSING, "Active Session", false, 403, null, res);
    }

    // Get college info separately
    const college = await College.findById(session.collegeId).lean();
    if (!college || college.status !== 'active') {
      return messageResponse(INVALID_MISSING, "Active College", false, 403, null, res);
    }

    // Add session and college info to request
    req.session = {
      id: session._id,
      collegeId: session.collegeId,
      status: session.status,
      violationCount: session.violationCount,
      messageCount: session.messageCount
    };

    req.college = {
      id: college._id,
      name: college.name,
      slug: college.slug,
      status: college.status
    };

    next();

  } catch (error) {
    console.error('Error in session validation middleware:', error);
    return messageResponse(SERVER_ERROR, "Session validation", false, 500, null, res);
  }
};

/**
 * Check if session has too many violations
 */
const checkViolationLimit = (req, res, next) => {
  const maxViolations = process.env.CHATBOT_MAX_VIOLATIONS || 3;
  
  if (req.session && req.session.violationCount >= maxViolations) {
    return messageResponse(
      "Session has been flagged due to multiple violations", 
      "Session", 
      false, 
      403, 
      { violationCount: req.session.violationCount }, 
      res
    );
  }

  next();
};

/**
 * Log request for analytics
 */
const logChatbotRequest = (req, res, next) => {
  const clientIP = req.headers['x-forwarded-for'] || 
                   req.connection.remoteAddress || 
                   req.socket.remoteAddress ||
                   (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
                   req.ip ||
                   'unknown';

  console.log(`Chatbot Request: ${req.method} ${req.path} from IP: ${clientIP}`);
  
  // You could enhance this to log to database for analytics
  // await logChatbotAnalytics(req.method, req.path, clientIP, req.college?.id);

  next();
};

/**
 * Security headers for chatbot endpoints
 */
const setChatbotSecurityHeaders = (req, res, next) => {
  // Prevent caching of chatbot responses
  res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
  res.set('Pragma', 'no-cache');
  res.set('Expires', '0');
  
  // Security headers
  res.set('X-Content-Type-Options', 'nosniff');
  res.set('X-Frame-Options', 'DENY');
  res.set('X-XSS-Protection', '1; mode=block');
  
  next();
};

module.exports = {
  validateCollegeAccess,
  validateSession,
  checkViolationLimit,
  logChatbotRequest,
  setChatbotSecurityHeaders
};
