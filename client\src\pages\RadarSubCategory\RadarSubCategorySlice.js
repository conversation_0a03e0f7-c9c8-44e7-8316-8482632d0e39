import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance'

export const getRadarSubCategories = createAsyncThunk('radar-category/getRadarSubCategories', async () => {
    try {
        const response = await axiosInstance({
            url: "sub-radar-category/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        console.log("error get Radar Sub Categories", error)
        return error
    }
})

export const postRadarSubCategory = createAsyncThunk("sub-radar-category/add", async ({name, radarCategoryId, radarCategoryName}, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "sub-radar-category/add",
            method: "POST",
            data: {name, radarCategoryId}
        })
        response.data.name = name;
        response.data = {...response.data,radarCategoryName,radarCategoryId};
        response.data._id = response.data.id;
        return response
    } catch (error) {
        console.log("error post Radar Sub Categories", error)
        return rejectWithValue(error)
    }
})

export const updateRadarSubCategory = createAsyncThunk('sub-radar-category/update', async ({name, radarCategoryId, radarCategoryName, id}, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "sub-radar-category/update",
            method: "PUT",
            data: {name, radarCategoryId,id}
        })
        response.data = {...response.data,radarCategoryName,radarCategoryId};
        response.data.name = name;
        response.data._id = id;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeRadarSubCategory = createAsyncThunk('sub-radar-category/remove', async (data) => {

    try {
        const response = await axiosInstance({
            url: "sub-radar-category/remove",
            method: "DELETE",
            data
        })
        response.data._id = data.id
        return response.data;
    } catch (error) {
        return error
    }
})

const radarSubCategorySlice = createSlice({
    name:'radarSubCategories',
    initialState: {
        status:'idle',
        radarSubCategories: []
    },
    reducers:{
        addRadarSubCategory : (state, action) =>{
            state.radarSubCategories.push(action.payload)
        },
        updateRadarCategory : (state, action) =>{

        },
        deleteRadarSubCategory : (state, action) =>{
            state.radarSubCategories = state.radarSubCategories.filter(category => category.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getRadarSubCategories.pending]: (state) => {
            state.status = 'pending'
        },
        [getRadarSubCategories.fulfilled]: (state, action) => {
            state.radarSubCategories = action.payload.data
            state.status = 'succeded'
        },
        [getRadarSubCategories.rejected]: (state, action) => {
            console.log("error",action.payload)
            state.status = 'rejected'
        },
        [postRadarSubCategory.fulfilled]: (state, action) => {
            const {name,id,radarCategoryId,radarCategoryName} = action.payload.data
            const data = {radarCategoryId, name, radarCategoryName, _id:id};
            if(data){
                state.radarSubCategories.push(data)
            }
        },
        [postRadarSubCategory.rejected]: (state, action) => {
            const error = action.payload
            console.log("error post sector", error)
        },
        [updateRadarSubCategory.fulfilled]: (state, action) => {
            state.radarSubCategories = state.radarSubCategories.map((item)=>{
                if(item._id === action.payload._id){
                    item.name = action.payload.name
                    item.radarCategoryId = action.payload.radarCategoryId
                    item.radarCategoryName = action.payload.radarCategoryName
                    return item
                }
                return item
            })
        },
        [updateRadarSubCategory.rejected]: (state, action) => {
            const error = action.payload
            console.log("error update sector", error)
        },
        [removeRadarSubCategory.fulfilled]: (state, action) => {
            const data = action.payload
            state.radarSubCategories = state.radarSubCategories?.filter(item => item._id !== data._id)
        },
        [removeRadarSubCategory.rejected]: (state, action) => {
            // const = action.payload
            const error = action.payload
            console.log("remove sector error", error)
        },
    }
})
export const { addRadarSubCategory, deleteRadarSubCategory } = radarSubCategorySlice.actions;
export default radarSubCategorySlice.reducer