import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
    Backdrop,
    Box,
    Button,
    Card,
    CircularProgress,
    Container,
    FormControl,
    Grid,
    InputAdornment,
    LinearProgress,
    Stack,
    Typography
} from '@mui/material';
// @mui
import { useFormik } from 'formik';
import { LoadingButton } from '@mui/lab';
import DataTable from 'react-data-table-component';
import { StyledSearch } from '../../sections/@dashboard/user/UserListToolbar';
import useLoading from '../../hooks/useLoading';
import { addRadarCategoryValidationSchema, addRadarSubCategoryValidationSchema } from '../../utils/validationSchemas';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import SelectComponent from '../../components/SelectComponent';
import { CancelButton } from '../../utils/cssStyles';
import { setSnackbar } from '../../Redux/snackbarSlice';
// import DataTable from '../../components/DataTable/DataTable';
import { getRadarCategories, postRadarCategory, removeRadarCategory, updateRadarCategory } from './RadarCategorySlice';
import Iconify from '../../components/Iconify/Iconify';
import { sectorStyles, subSectorStyles } from '../Sector/Sector';
import { postRadarSubCategory, removeRadarSubCategory, updateRadarSubCategory } from '../RadarSubCategory/RadarSubCategorySlice';
import ConfirmDialog from '../../components/ConfirmDialog';

// ----------------------------------------------------------------------
export const RADAR_CATEGORY_TABLE_HEAD = [
    { id: 'name', label: 'Radar Category', alignRight: false },
    { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderRadarCategoryCells = ['name']

const RadarCategory = () => {
    const dispatch = useDispatch()
    const RadarCategoryState = useSelector(state => state.radarCategories)
    const [RadarCategory, setRadarCategory] = useState([]);
    const [loading, setLoading] = useState(false)
    const [radarSubmitting, setRadarSubmitting] = useState(false)
    const [editRadarCategoryDetails, setEditRadarCategoryDetails] = useState('');
    const [editRadarSubCategoryDetails, setEditRadarSubCategoryDetails] = useState('');
    const { radarCategories, status, error } = RadarCategoryState;
    const [openBackdrop, setOpenBackdrop] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [open, setOpen] = useState(false);
    const [selectedField, setSelectedField] = useState()
    const [openSubRadar, setOpenSubRadar] = useState(false)
    const dataLoading = useLoading(status)

    const handleCloseBackdrop = () => {
        setOpenBackdrop(false);
    };
    const handleOpenBackdrop = () => {
        setOpenBackdrop(true);
    };
    const onFilterName = (event) => {
        setSearchTerm(event.target.value)
    }

    const filteredRadarCategory = RadarCategory.filter(
        radar => radar.name && radar.name.toLowerCase().includes(searchTerm.toLocaleLowerCase()) ||
            radar.radarSubcategories && radar.radarSubcategories.some(radarSub => radarSub.name.toLocaleLowerCase().includes(searchTerm.toLowerCase()))
    );

    const addRadarCategoryformik = useFormik({
        initialValues: {
            title: '',
        },
        onSubmit: (values, { resetForm }) => {
            setRadarSubmitting(true)
            let data = {
                name: values.title,
            }
            if (editRadarCategoryDetails) {
                data = {
                    ...data,
                    id: editRadarCategoryDetails._id ? editRadarCategoryDetails._id : editRadarCategoryDetails.id
                }
            }
            dispatch(editRadarCategoryDetails ? updateRadarCategory(data) : postRadarCategory(data)).then(res => {
                if (res?.payload?.success || res?.payload?.data?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: `Succesfully ${editRadarCategoryDetails ? 'Updated' : 'Added'} RadarCategory`
                    }))
                }
                const error = res?.payload?.response?.data
                if (error?.success === false) {
                    console.log(error?.msg)
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error?.msg || "Something went wrong!"
                    }))
                }
            }).finally(() => {
                setRadarSubmitting(false)
                setEditRadarCategoryDetails('');
            })
            resetForm({ values: '' })
        },
        validationSchema: addRadarCategoryValidationSchema
    })

    const addRadarSubCategoryformik = useFormik({
        initialValues: {
            title: '',
            radarCategory: ''
        },
        onSubmit: (values, { resetForm }) => {
            setLoading(true)
            let radarCategoryName = '';
            for (let i = 0; i < RadarCategory.length; i += 1) {
                if (values?.radarCategory === RadarCategory[i]?._id) {
                    radarCategoryName = RadarCategory[i].name;
                }
            }
            let data = {
                name: values.title,
                radarCategoryId: values.radarCategory,
                radarCategoryName
            }
            if (editRadarSubCategoryDetails) {
                data = {
                    ...data,
                    id: editRadarSubCategoryDetails._id ? editRadarSubCategoryDetails._id : editRadarSubCategoryDetails.id
                }
            }
            dispatch(editRadarSubCategoryDetails ? updateRadarSubCategory(data) : postRadarSubCategory(data)).then(res => {
                dispatch(getRadarCategories())
                if (res?.payload?.success || res?.payload?.data?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: `Succesfully ${editRadarSubCategoryDetails ? 'Updated' : 'Added'} RadarCategory`
                    }))
                }
                else {
                    const error = res?.payload?.response?.data
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: error?.msg || "Something went wrong!"
                    }))
                }
            }).finally(() => {
                setLoading(false)
                setEditRadarSubCategoryDetails('');
            })
            resetForm({ values: '' })
        },
        validationSchema: addRadarSubCategoryValidationSchema
    })

    useEffect(() => {
        dispatch(getRadarCategories());
    }, []);

    useEffect(() => {
        if (radarCategories?.length > 0) {
            setRadarCategory([...radarCategories])
        }
    }, [radarCategories])

    useEffect(() => {
        addRadarSubCategoryformik.setValues({
            ...addRadarSubCategoryformik.values,
            title: editRadarSubCategoryDetails?.name,
            radarCategory: editRadarSubCategoryDetails?.radarCategoryId
        })
    }, [editRadarSubCategoryDetails])


    const handleFilterSearch = (event) => {
        const filteredRadarCategory = RadarCategory.filter(category => category.name.toLowerCase().includes(event.target.value.toLowerCase()))
        return filteredRadarCategory
    };

    const handleDeleteRadarCategory = (radar) => {
        handleOpenBackdrop()
        const data = {
            id: radar.id ? radar.id : radar._id
        }
        dispatch(removeRadarCategory(data)).then(res => {
            if (res?.payload?.success) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Radar Category Deleted Succesfully"
                }))
            }
            const error = res?.payload?.response?.data
            if (error?.success === false) {
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: error?.message || "Something went wrong"
                }))
                console.log('Something went wrong!!', res)
            }
        }).finally(() => {
            handleCloseBackdrop()
        })
    }
    const handleDeleteRadarSubCategory = (item) => {
        handleOpenBackdrop()
        const data = {
            id: item.id ? item.id : item._id
        }
        dispatch(removeRadarSubCategory(data)).then(res => {
            if (res?.payload?.success) {
                dispatch(getRadarCategories())
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: "Sector Deleted Succesfully"
                }))
            }
            else {
                const error = res?.payload?.response?.data
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: error?.message || "Something went wrong"
                }))
                console.log('Something went wrong!!', res)
            }
        }).finally(() => {
            handleCloseBackdrop()
        })
    }

    const editRadarCategory = (radar) => {
        setEditRadarCategoryDetails(radar);
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }

    const editRadarSubCategory = (item) => {
        setEditRadarSubCategoryDetails(item);
        window.scrollTo({
            top: 0,
            behavior: 'smooth',
        });
    }
    const handleCancel = () => {
        setEditRadarCategoryDetails('');
        addRadarCategoryformik.setValues({
            title: ''
        })
    }

    useEffect(() => {
        addRadarCategoryformik.setValues({
            ...addRadarCategoryformik.values,
            title: editRadarCategoryDetails?.name
        })
    }, [editRadarCategoryDetails])

    const columns = [
        {
            name: 'Radar Category',
            selector: row => row.name,
            sortable: true,
        },
        {
            name: '',
            button: true,
            cell: (row) =>
                <Button
                    onClick={() => editRadarCategory(row)}
                >
                    <Iconify icon={'eva:edit-fill'} />
                </Button>
        },
        {
            button: true,
            cell: (row) =>
                <>
                    <Button
                        color='error'
                        // onClick={() => handleDeleteRadarCategory(row)}
                        onClick={() => {
                            setSelectedField(row)
                            setOpen(true)
                        }}
                    >
                        <Iconify icon={'eva:trash-2-outline'} />
                    </Button>
                </>
        },
    ];
    const SubColumns = [
        {
            name: 'Radar Sub-Categories',
            selector: row => row.name,
            sortable: true,
        },
        {
            name: '',
            button: true,
            cell: (row) =>
                <Button
                    onClick={() => editRadarSubCategory(row)}
                >
                    <Iconify icon={'eva:edit-fill'} />
                </Button>
        },
        {
            button: true,
            cell: (row) =>
                <>
                    <Button
                        color='error'
                        // onClick={() => handleDeleteRadarSubCategory(row)}
                        onClick={() => {
                            setSelectedField(row)
                            setOpenSubRadar(true)
                        }}
                    >
                        <Iconify icon={'eva:trash-2-outline'} />
                    </Button>
                </>
        },
    ];
    const noDataComponent = (data) =>
        <>
            <Typography
                my={4}
                variant='subtitle2'>
                No Radar Subcategory Found For <b>{data.name}</b>
            </Typography>
        </>
    const ExpandedComponent = ({ data }) =>
        <DataTable
            columns={SubColumns}
            data={data.radarSubcategories}
            customStyles={subSectorStyles}
            noDataComponent={noDataComponent(data)}

        // noTableHead
        />
    const progressComponent =
        <Box sx={{ width: 'inherit' }}>
            <LinearProgress />
        </Box>

    const subHeaderComponent = (
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
            <StyledSearch
                sx={{ my: 1 }}
                value={searchTerm}
                onChange={onFilterName}
                placeholder={"Search..."}
                startAdornment={
                    <InputAdornment position="start">
                        <Iconify icon="eva:search-fill" sx={{ color: 'text.disabled', width: 20, height: 20 }} />
                    </InputAdornment>
                }
            />
            <Box flexGrow={1} />
        </div>
    );
    const handleSubCancel = () => {
        setEditRadarSubCategoryDetails('');
        addRadarSubCategoryformik.setValues({
            title: ''
        })
    }
    const handleDeleteRadarField = () => {
        handleDeleteRadarCategory(selectedField)
        setOpen(false)
    }
    const handleDeleteSubRadarField = () => {
        handleDeleteRadarSubCategory(selectedField)
        setOpenSubRadar(false)
    }

    return (
        <>
            <Helmet>
                <title> Radar Categories | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Backdrop
                    sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 1 }}
                    open={openBackdrop}
                >
                    <CircularProgress color="inherit" />
                </Backdrop>
                <ConfirmDialog
                    title={"Delete Radar Category?"}
                    open={open}
                    setOpen={setOpen}
                    // handleClose={closeDialog}
                    selectedField={selectedField}
                    onConfirm={handleDeleteRadarField}
                >
                    <Typography variant='body1'>
                        {"Are you sure you want to delete this Radar Category"}
                    </Typography>
                </ConfirmDialog>
                <ConfirmDialog
                    title={"Delete Subsector?"}
                    open={openSubRadar}
                    setOpen={setOpenSubRadar}
                    // handleClose={closeDialog}
                    selectedField={selectedField}
                    onConfirm={handleDeleteSubRadarField}
                >
                    <Typography variant='body1'>
                        {"Are you sure you want to delete this Radar Sub-Category"}
                    </Typography>
                </ConfirmDialog>
                <Typography variant="h4" gutterBottom mb={3}>
                    Radar Categories
                </Typography>
                <Grid container justifyContent={'space-between'} gap={2} flexWrap={'nowrap'}>
                    <Grid item xs={9}>
                        {/* <DataTable
                            loading={dataLoading}
                            deleteDescription={"Are you sure want to delete this redar category ?"}
                            deleteTitle={"Delete Redar Category ?"}
                            TableHead={RADAR_CATEGORY_TABLE_HEAD}
                            TableData={RadarCategory}
                            filterSearch={handleFilterSearch}
                            searchLable={"Search..."}
                            handleEdit={editRadarCategory}
                            renderCells={renderRadarCategoryCells}
                            handleDelete={handleDeleteRadarCategory}
                            pagination
                        /> */}
                        <Card
                            sx={{ p: 0, my: 3, width: '95%' }}
                        >
                            <DataTable
                                columns={columns}
                                data={filteredRadarCategory}
                                customStyles={sectorStyles}
                                expandableRows
                                expandableRowsComponent={ExpandedComponent}
                                expandOnRowClicked="false"
                                highlightOnHover
                                subHeader
                                subHeaderComponent={subHeaderComponent}
                                // subHeaderAlign="left"
                                progressPending={dataLoading}
                                progressComponent={progressComponent}
                                pagination
                                noDataComponent="No Radar Categories Found"
                            />
                        </Card>
                    </Grid>
                    <Grid item xs={4}>
                        <Grid container>
                            <Grid item lg={12}>
                                <Typography variant="button" gutterBottom mb={3}>
                                    {editRadarCategoryDetails ? 'Edit Radar Categories' : 'Add Radar Categories'}
                                </Typography>
                                <Card sx={{ mt: 2 }} >
                                    <Box sx={{ p: 4 }}>
                                        <form onSubmit={addRadarCategoryformik.handleSubmit}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='title'
                                                label="Title"
                                                value={addRadarCategoryformik.values.title}
                                                onChange={addRadarCategoryformik.handleChange}
                                                error={addRadarCategoryformik.touched.title && Boolean(addRadarCategoryformik.errors.title)}
                                                helperText={addRadarCategoryformik.touched.title && addRadarCategoryformik.errors.title}
                                            />
                                            <Stack direction="row" justifyContent="flex-end" >
                                                {editRadarCategoryDetails ?
                                                    <Button
                                                        type='button'
                                                        variant='contained'
                                                        sx={CancelButton}
                                                        color='error'
                                                        onClick={() => handleCancel()}
                                                    >
                                                        Cancel
                                                    </Button> : ''
                                                }

                                                <LoadingButton
                                                    loading={radarSubmitting}
                                                    type='submit'
                                                    variant='contained'
                                                    sx={{ width: '10%', m: 1, mt: 2 }}
                                                >
                                                    {editRadarCategoryDetails ? 'Save' : 'Add'}
                                                </LoadingButton>
                                            </Stack>
                                        </form>
                                    </Box>
                                </Card>
                            </Grid>
                            <Grid item mt={4} lg={12}>
                                <Typography variant="button" gutterBottom mb={3}>
                                    {editRadarCategoryDetails ? 'Edit Radar Sub-Category' : 'Add Radar Sub-Categories'}
                                </Typography>
                                <Card sx={{ mt: 2 }}>
                                    <Box sx={{ p: 4 }}>
                                        <form onSubmit={addRadarSubCategoryformik.handleSubmit}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='title'
                                                label="Title"
                                                value={addRadarSubCategoryformik.values.title}
                                                onChange={addRadarSubCategoryformik.handleChange}
                                                error={addRadarSubCategoryformik.touched.title && Boolean(addRadarSubCategoryformik.errors.title)}
                                                helperText={addRadarSubCategoryformik.touched.title && addRadarSubCategoryformik.errors.title}
                                            />
                                            <FormControl sx={{ marginTop: '20px', width: '100%' }}>
                                                <SelectComponent
                                                    menuName={"name"}
                                                    menuValue={"_id"}
                                                    labelId="radar-label"
                                                    label="Radar Category *"
                                                    inputLabel="Radar Category"
                                                    disableNone
                                                    menuItems={RadarCategory}
                                                    sx={{ width: '100%' }}
                                                    name='radarCategory'
                                                    onBlur={addRadarSubCategoryformik.handleBlur}
                                                    defaultValue={addRadarSubCategoryformik.values?.radarCategory}
                                                    value={addRadarSubCategoryformik.values?.radarCategory}
                                                    onChange={addRadarSubCategoryformik.handleChange}
                                                    labelColor={addRadarSubCategoryformik.touched.radarCategory && addRadarSubCategoryformik.errors.radarCategory && 'error'}
                                                    labelError={addRadarSubCategoryformik.touched.radarCategory && addRadarSubCategoryformik.errors.radarCategory}
                                                    error={addRadarSubCategoryformik.touched.radarCategory && Boolean(addRadarSubCategoryformik.errors.radarCategory)}
                                                    helperText={addRadarSubCategoryformik.touched.radarCategory && addRadarSubCategoryformik.errors.radarCategory}
                                                />
                                            </FormControl>
                                            <Stack direction="row" justifyContent="flex-end" >
                                                {editRadarSubCategoryDetails ?
                                                    <Button
                                                        type='button'
                                                        variant='contained'
                                                        sx={CancelButton}
                                                        color='error'
                                                        onClick={() => handleSubCancel()}
                                                    >
                                                        Cancel
                                                    </Button> : ''
                                                }

                                                <LoadingButton
                                                    loading={loading}
                                                    type='submit'
                                                    variant='contained'
                                                    sx={{ width: '10%', m: 1, mt: 2 }}
                                                >
                                                    {editRadarSubCategoryDetails ? 'Save' : 'Add'}
                                                </LoadingButton>
                                            </Stack>
                                        </form>
                                    </Box>
                                </Card>
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
            </Container>
        </>
    )
}

export default RadarCategory






