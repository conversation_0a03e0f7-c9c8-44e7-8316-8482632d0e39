import { RemoveCircleOutlineOutlined } from '@mui/icons-material';
import BadgeIcon from '@mui/icons-material/Badge';
import CompareIcon from '@mui/icons-material/Compare';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FileDownloadOutlinedIcon from '@mui/icons-material/FileDownloadOutlined';
import FormatListBulletedIcon from '@mui/icons-material/FormatListBulleted';
import InfoIcon from '@mui/icons-material/Info';
import LeaderboardIcon from '@mui/icons-material/Leaderboard';
import { Backdrop, Button, CircularProgress, IconButton, SvgIcon, Switch, ToggleButton, ToggleButtonGroup, Tooltip, Typography, styled, tooltipClasses, useMediaQuery, useTheme } from '@mui/material';
import Accordion from '@mui/material/Accordion';
import AccordionDetails from '@mui/material/AccordionDetails';
import AccordionSummary from '@mui/material/AccordionSummary';
import { Box, Stack } from '@mui/system';
import PropTypes from 'prop-types';
import React, { useEffect, useState } from 'react';
import { Ai_Video_Url } from 'src/config-global';
import { analytics, currencyFormat } from 'src/utils';
import BarsComponent from './bar/BarsComponent';
import { BarSlider } from './BarSlider';
import { SelectComponent } from './hook-form';
import ListComponent from './list/ListComponent';
// import reSkillBarVideo from '../assets/videos/video-3a.mp4'
// import upSkillBarVideo from '../assets/videos/video-3b.mp4'
import { ReactComponent as flagIcon } from '../assets/images/flag.svg';
import svgMobile from '../assets/images/speech-bubble-mobilev2.svg';
import svg from '../assets/images/speech-bubblev2.svg';
import AiVideo from './player/AiVideo';

const sortingOptions = [
    {
        label: "Highest Salary First",
        value: "highestSalary"
    },
    {
        label: "Most Courses Available First",
        value: "mostCourses"
    },
    {
        label: "Shortest Transfer Window First",
        value: "shortestTransfer"
    },
]

const BootstrapTooltip = styled(({ className, ...props }) => (
    <Tooltip {...props} arrow classes={{ popper: className }} />
))(({ theme }) => ({
    [`& .${tooltipClasses.arrow}`]: {
        color: 'black',
    },
    [`& .${tooltipClasses.tooltip}`]: {
        backgroundColor: 'black',
        fontSize: '0.9rem !important',
        maxWidth: '260px !important',
        padding: '15px !important',
    },
}));

const barChartToolTip = `This chart shows you all of the careers that require a skill set that is similar to your own. \n
                        Select the sectors or subsectors to see the exciting careers options you have the potential to upskill or reskill into. \n  
                        Press > to see more information including the courses that support each career.`
const listViewToolTip = `This list shows you all of the careers that require a skill set that is similar to your own. \n
                        Use the sorting menu to explore the exciting careers options you have the potential to upskill or reskill into. \n 
                        Press > to see more information including the courses that support each career.`

const GraphsData = ({ regionData, isReskill, handleToggle, isRegion, scrollRef, status, reskillBar, showCompareCareersPopup, compareCareersState, careersUpskillTime, addToCompare, showCareerDetailsPopup, isLoading, careerHistoryId, IP, careerGoalId, getSkillReport, collegeId }) => {
    const [Careers, setCareers] = useState([])
    const [sortCareerBy, setSortCareerBy] = useState(sortingOptions[0])
    const [filteredSectors, setFilteredSectors] = useState(careersUpskillTime?.sectorOrSubsector)
    const [toggleValue, setToggleValue] = useState('bar');
    const [salaryRange, setSalaryRange] = React.useState([0, 0]);
    const [minMaxSalary, setMinMaxSalary] = useState([0, 0])
    const [minMaxTransfer, setMinMaxTransfer] = useState([0, 0])
    const [transferWindowRange, setTransferWindowRange] = React.useState([0, 0]);
    const [careerCount, setCareerCount] = useState(0);
    const [showLabel, setShowLabel] = useState(false);
    const [openPopup, setOpenPopup] = useState(false);
    const [widthClass, setWidthClass] = useState('bar-wrapper-mob');
    const [preSelectedCareers, setPreSelectedCareers] = useState({
        CurrentRole: false,
        CareerGoal: false
    });
    const handleChangeView = (event, newAlignment) => {
        if (newAlignment !== null) {
            setToggleValue(newAlignment);
        }
        if (newAlignment === 'bar') {
            analytics({
                collegeId,
                event: 'SCATTER_GRAPH_CLICK',
                from: 'career-courses',
                // from: openCompareCareersPopup ? 'comparision' : 'career-courses',
            })
        }
        else if (newAlignment === 'list') {
            analytics({
                collegeId,
                event: 'LIST_GRAPH_CLICK',
                from: 'career-courses',
                // from: openCompareCareersPopup ? 'comparision' : 'career-courses',
            })
        }
    };
    const videoClassOpen = {
        position: 'relative',
        width: '100%',
        zIndex: 999,
        bottom: 0,
        left: '50%',
        mb: 4,
        transform: 'translateX(-50%)',
    }
    const videoClassClose = {
        mb: 4
    }
    useEffect(() => {
        if (careersUpskillTime?.sectorOrSubsector?.length > 0 && collegeId) {

            // careersUpskillTime?.sectorOrSubsector?.map(sector =>
            //     sector?.careers?.length > 0 && sector?.careers?.map(career => {
            //         setCareers(prevState => [...prevState, {...career,color: sector?.color, name: sector?.name }])
            //         return ''
            //     })
            // )
            const newCareerState = []
            let maxSalary = 0;
            let minSalary = careersUpskillTime?.sectorOrSubsector[0]?.careers[0]?.salary || 0;
            let maxTransfer = 0;
            let minTransfer = careersUpskillTime?.sectorOrSubsector[0]?.careers[0]?.transferWindowMax || 0;
            careersUpskillTime?.sectorOrSubsector?.map(sector =>
                // filteredSectors?.map(sector =>
                sector.careers.map(career => {
                    newCareerState.push({
                        ...career,
                        color: sector?.color,
                        sectorName: sector?.name
                    })
                    if (career.salary > maxSalary) {
                        maxSalary = career.salary
                    }
                    if (career.salary < minSalary) {
                        minSalary = career.salary
                    }
                    if (career?.transferWindowMin < minTransfer) {
                        minTransfer = career?.transferWindowMin
                    }
                    if (career?.transferWindowMax > maxTransfer) {
                        maxTransfer = career?.transferWindowMax
                    }
                    return newCareerState
                })
            )
            setSalaryRange([minSalary, maxSalary])
            setMinMaxSalary([minSalary, maxSalary])
            setMinMaxTransfer([minTransfer, maxTransfer])
            setTransferWindowRange([minTransfer, maxTransfer])
            setCareers(newCareerState)
            if (newCareerState && collegeId) {
                const filteredCareers = newCareerState?.filter((item) => item?.careerType !== "CAREER_GOAL" && item?.careerType !== "CURRENT_ROLE")
                const filteredCareersId = filteredCareers.map((item) => item.id);
                const date = new Date();
                if (collegeId) {
                    analytics({
                        collegeId,
                        event: 'CAREER_RECOMMEND_IDS',
                        from: 'career-courses',
                        data: {
                            "careerRecommedIds": filteredCareersId
                        },
                        date: date.toISOString,
                    })

                }
            }
        }
    }, [careersUpskillTime, collegeId])
    useEffect(() => {
        const newCareerState = []
        filteredSectors?.map(sector =>
            sector.careers.map(career => {
                newCareerState.push({
                    ...career,
                    color: sector?.color,
                    sectorName: sector?.name
                })
                return newCareerState
            })
        )
        setCareers(newCareerState)

    }, [filteredSectors])

    // useEffect(() => {
    //     if (openPopup) {
    //         setTimeout(() => {
    //             openLabel()
    //         }, 400);
    //     }
    // }, [openPopup])


    useEffect(() => {
        const filtereddata = careersUpskillTime?.sectorOrSubsector
            // .map(sector => sector.careers.map(career => (career?.salary >= salaryRange[0] && career?.salary <= salaryRange[1]) ))
            .map(sector => ({
                ...sector,
                careers: sector?.careers?.filter(career => career?.salary >= salaryRange[0] && career?.salary <= salaryRange[1] &&
                    career?.transferWindowMin >= transferWindowRange[0] && career?.transferWindowMax <= transferWindowRange[1])
            })
            )
        // filtereddata?.sort((a, b) => (a?.careers.length > b?.careers.length) ? -1 : 1)
        setFilteredSectors(filtereddata)
    }, [salaryRange, careersUpskillTime?.sectorOrSubsector, transferWindowRange])

    // useEffect(() => {
    //     if (status?.isSuccess) {
    //         setTimeout(() => {
    //             scrollToTop()
    //             // openLabel()
    //         }, 500);
    //     }
    //     // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [status.isSuccess])

    const closeLabel = () => {
        setShowLabel(false);
        document.body.style.overflow = 'unset';
        window.scrollTo({
            top: 0,
            behavior: "smooth"
        });
        setWidthClass('bar-wrapper-mob')
    }
    const openLabel = () => {
        setShowLabel(true);
        // if (typeof window !== 'undefined' && window.document) {
        //     document.body.style.overflow = 'unset';
        //     // window.scrollTo({
        //     //   top: 60,
        //     //   behavior: "smooth"
        //     // });
        // }
    }
    // const scrollRef = useRef(null)
    const scrollToTop = () => {
        const element = scrollRef?.current
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'end' })
            setOpenPopup(true)
        }
    }
    const handleSalaryChange = (event, newValue) => {
        setSalaryRange(newValue);
    };
    const handleTransferChange = (event, newValue) => {
        setTransferWindowRange(newValue);
    };

    const handleSortingOptionChange = (event) => {
        setSortCareerBy(event)
    }

    useEffect(() => {
        // const count = 0;
        // if (careerHistoryId && careerHistoryId.length) {
        //     if (careerGoalId) {
        //         count = 2
        //     } else {
        //         count = 1
        //     }
        // }
        careersUpskillTime?.sectorOrSubsector.map(sector => sector?.careers?.map(career => {
            if (career?.careerType === "CAREER_GOAL") {
                setPreSelectedCareers(previousSelected => ({ ...previousSelected, CareerGoal: true }))
            } else if (career?.careerType === "CURRENT_ROLE") {
                setPreSelectedCareers(previousSelected => ({ ...previousSelected, CurrentRole: true }))
            }
            return []
        }))
        // if (compareCareersState) {
        //     const countD = count + compareCareersState.length
        //     setCareerCount(countD)
        // }

    }, [compareCareersState, careerHistoryId, careersUpskillTime?.sectorOrSubsector]) // eslint-disable-line react-hooks/exhaustive-deps
    useEffect(() => {
        let count = 0;
        if (preSelectedCareers?.CareerGoal && preSelectedCareers?.CurrentRole) {
            count = 2
        } else if (preSelectedCareers?.CareerGoal || preSelectedCareers?.CurrentRole) {
            count = 1
        }
        if (compareCareersState) {
            const countD = count + compareCareersState.length
            setCareerCount(countD)
        }
    }, [compareCareersState, preSelectedCareers])

    // useEffect(()=>{
    //     if(Careers){
    //         const filteredCareers = Careers?.filter((item)=> item?.careerType !== "CAREER_GOAL" && item?.careerType !== "CURRENT_ROLE")
    //         const filteredCareersId = filteredCareers.map((item)=> item.id);
    //         const date = new Date();
    //         // if(collegeId){
    //         //     analytics({
    //         //         collegeId,
    //         //         event: 'CAREER_RECOMMEND_IDS',
    //         //         from: 'career-courses',
    //         //         data: {
    //         //             "careerRecommedIds": filteredCareersId
    //         //         },
    //         //         date: date.toISOString,
    //         //         IP
    //         //     })

    //         // }
    //     }
    // }, [Careers]) // eslint-disable-line react-hooks/exhaustive-deps
    const thm = useTheme()
    const isMobile = useMediaQuery(thm.breakpoints.down(1025))
    const isXs = useMediaQuery(thm.breakpoints.down("sm"));
    const buttonColor = regionData ? (regionData?.button?.bgColor || regionData?.buttonColor) : '';
    const buttonFontColor = regionData ? (regionData?.button?.color || regionData?.buttonFontColor) : '';
    const primaryColor = regionData ? (regionData?.primaryColor) : thm.palette.primary.main;
    return (
        <>
            <Box display='flex'
                //  columnGap={2} 
                // flexDirection="column"
                justifyContent="space-between"
                className=" bar-chart-wrapper"
                mt={0.2}
                mb={3}
            // sx={{ border: (theme) => `2px solid ${theme.palette.primary.main}` }}
            >
                <>
                    {isLoading ?
                        <Box
                            // className="bar-chart"
                            sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', marginX: 'auto', bgcolor: isRegion ? 'white' : 'unset', width: '100%' }}
                        >
                            <CircularProgress />
                        </Box> :
                        <>
                            <Box className="bar-chart" sx={{ pt:2, overflow: 'hidden', bgcolor: isRegion ? 'white' : 'unset', borderRadius: '0 0 0 12px' }}>
                                <Box className="bar-head">
                                    <Box sx={{ color: '#000c3b',  mr: 2, width: { md: '100%', sm: '100%' } }} >
                                        <Typography sx={{fontSize: '20px !important', fontWeight:700}} textAlign='start' color='primary.dark'>
                                            Press {'>'} to see more information about careers in each sector.
                                        </Typography>
                                    </Box>

                                    <Box className="toggle-btn-wrapper">
                                        {toggleValue !== 'bar' && <SelectComponent
                                            name="careerSorting"
                                            isSearchable={false}
                                            placeholder="Sort Careers"
                                            selectOptions={sortingOptions}
                                            className="black small"
                                            value={sortCareerBy}
                                            handleChange={handleSortingOptionChange}
                                        />}
                                        <ToggleButtonGroup
                                            sx={{ border: 'none' }}
                                            value={toggleValue}
                                            exclusive
                                            onChange={handleChangeView}
                                        >
                                            <ToggleButton
                                                key='right' size='small' value="bar">
                                                <LeaderboardIcon />
                                            </ToggleButton>
                                            <ToggleButton key='left' size='small' value="list">
                                                <FormatListBulletedIcon />
                                            </ToggleButton>
                                        </ToggleButtonGroup>
                                    </Box>
                                </Box>
                                {
                                    isXs && <Accordion sx={{ mx: 2 }}>
                                        <AccordionSummary
                                            expandIcon={<ExpandMoreIcon />}
                                            aria-controls="panel1-content"
                                            id="panel1-header"
                                        >
                                            <Typography variant='h4'>
                                                Filters
                                            </Typography>
                                        </AccordionSummary>
                                        <AccordionDetails>
                                            <Box className="filter">
                                                <Stack mt={1} gap={{ xs: 0, sm: 4 }} direction={{ xs: 'column', sm: 'row', mx: 4 }} >
                                                    <Typography variant='body' sx={{ width: '100%', textAlign: 'left', marginTop: '5px' }}>
                                                        Salary Preference
                                                    </Typography>
                                                    <Box mx={2}>
                                                        <BarSlider
                                                            min={minMaxSalary[0]}
                                                            max={minMaxSalary[1]}
                                                            value={salaryRange}
                                                            onChange={handleSalaryChange}
                                                            sliderColor={primaryColor}   // dynamic color for track/valueLabel
                                                            thumbColor={buttonColor}       // dynamic color for thumb
                                                        // valueLabelDisplay="auto"
                                                        // defaultValue={20}
                                                        />
                                                        <Typography sx={{ fontSize: '0.9rem !important', marginTop: '-12px', textAlign: 'center' }}>
                                                            {`${currencyFormat(salaryRange[0])}\u00A0 to \u00A0 ${currencyFormat(salaryRange[1])}`}
                                                        </Typography>
                                                    </Box>
                                                </Stack>

                                                <Stack gap={{ xs: 0, sm: 4 }} direction={{ xs: 'column', sm: 'row' }} >
                                                    <Typography variant='body' sx={{ width: '100%', textAlign: 'left', marginTop: '5px' }}>
                                                        Transfer Window
                                                    </Typography>
                                                    <Box mx={2}>
                                                        <BarSlider
                                                            min={minMaxTransfer[0]}
                                                            max={minMaxTransfer[1]}
                                                            value={transferWindowRange}
                                                            onChange={handleTransferChange}
                                                            sliderColor={primaryColor}   // dynamic color for track/valueLabel
                                                            thumbColor={buttonColor}       // dynamic color for thumb
                                                        // valueLabelDisplay="auto"
                                                        // defaultValue={20}
                                                        />
                                                        <Typography sx={{ fontSize: '0.9rem !important', marginTop: '-12px', textAlign: 'center' }}>
                                                            {`${transferWindowRange[0]}\u00A0 to \u00A0 ${transferWindowRange[1]} \u00A0 Years`}
                                                        </Typography>
                                                    </Box>
                                                </Stack>
                                            </Box>
                                        </AccordionDetails>
                                    </Accordion>
                                }

                                {toggleValue === 'bar' ?
                                    <BarsComponent
                                        data={filteredSectors}
                                        // data={careersUpskillTime?.sectorOrSubsector}
                                        addToCompare={addToCompare}
                                        compareCareersState={compareCareersState}
                                        showCareerDetailsPopup={showCareerDetailsPopup}
                                        widthClass={widthClass}
                                    /> :
                                    <ListComponent
                                        sortCareerBy={sortCareerBy}
                                        data={Careers}
                                        addToCompare={addToCompare}
                                        compareCareersState={compareCareersState}
                                        showCareerDetailsPopup={showCareerDetailsPopup}
                                    />}
                            </Box>
                            {!isXs && <Box className="filter-wrapper" sx={{ background: (theme) => isRegion ? 'white' : `${theme.palette.primary.main}`, borderRadius: '0 0 12px 0' }}>
                                <Stack
                                    sx={{ height: '100%' }}
                                    direction='column'
                                    justifyContent='space-between'
                                >
                                    <Box
                                        display='flex'
                                        flexDirection='column'
                                    >
                                        <Box className="filter">
                                            <Typography sx={{ color: isRegion ? 'black' : 'unset', mt:10 }} variant='h4'>
                                                Filters:
                                            </Typography>

                                            <Stack mt={1} gap={{ xs: 0, sm: 4 }} direction={{ xs: 'column', sm: 'row' }} >
                                                <Typography variant='body' sx={{ width: { xs: '100%', sm: '32%' }, textAlign: 'left', marginTop: '5px', color: isRegion ? 'black' : 'unset' }}>
                                                    Salary Preference
                                                </Typography>
                                                <Box sx={{ maxWidth: { xs: 300, sm: 200 }, width: { xs: '100%', sm: '50%' } }}>
                                                    <BarSlider
                                                        min={minMaxSalary[0]}
                                                        max={minMaxSalary[1]}
                                                        value={salaryRange}
                                                        onChange={handleSalaryChange}
                                                        sliderColor={primaryColor}   // dynamic color for track/valueLabel
                                                        thumbColor={buttonColor}       // dynamic color for thumb
                                                    // valueLabelDisplay="auto"
                                                    // defaultValue={20}
                                                    />
                                                    <Typography sx={{ color: isRegion ? 'black' : 'unset', fontSize: '0.9rem !important', marginTop: '-12px', textAlign: 'center' }}>
                                                        {`${currencyFormat(salaryRange[0])}\u00A0 to \u00A0 ${currencyFormat(salaryRange[1])}`}
                                                    </Typography>
                                                </Box>
                                            </Stack>

                                            <Stack gap={{ xs: 0, sm: 4 }} direction={{ xs: 'column', sm: 'row' }} >
                                                <Typography variant='body' sx={{ color: isRegion ? 'black' : 'unset', marginTop: '5px', width: { xs: '100%', sm: '32%' }, textAlign: 'left' }}>
                                                    Transfer Window
                                                </Typography>
                                                <Box sx={{ maxWidth: { xs: 300, sm: 200 }, width: { xs: '100%', sm: '50%' } }}>
                                                    <BarSlider
                                                        min={minMaxTransfer[0]}
                                                        max={minMaxTransfer[1]}
                                                        value={transferWindowRange}
                                                        onChange={handleTransferChange}
                                                        sliderColor={primaryColor}   // dynamic color for track/valueLabel
                                                        thumbColor={buttonColor}       // dynamic color for thumb
                                                    // valueLabelDisplay="auto"
                                                    // defaultValue={20}
                                                    />
                                                    <Typography sx={{ color: isRegion ? 'black' : 'unset', fontSize: '0.9rem !important', marginTop: '-12px', textAlign: 'center' }}>
                                                        {`${transferWindowRange[0]}\u00A0 to \u00A0 ${transferWindowRange[1]} \u00A0 Years`}
                                                    </Typography>
                                                </Box>
                                            </Stack>
                                        </Box>
                                        {isRegion && handleToggle && <Box display="flex" alignItems="center" gap={2}>
                                            <Typography sx={{ color: regionData?.buttonFontColor || 'black' }} variant="body1">Upskill</Typography>
                                            <Switch
                                                checked={isReskill}
                                                onChange={handleToggle}
                                                inputProps={{ 'aria-label': 'Skill Toggle' }}
                                            />
                                            <Typography sx={{ color: regionData?.buttonFontColor || 'black' }} variant="body1">Reskill</Typography>
                                        </Box>}
                                        <Box className="selected-career-wrapper">
                                            <Stack mt={2} gap={1} direction='column' alignItems='flex-start' >
                                                <Typography sx={{ color: isRegion ? 'black' : 'unset' }} variant='h4'>
                                                    Selected Careers ({careerCount} / {preSelectedCareers?.CareerGoal && preSelectedCareers?.CurrentRole ? 7 : 6})
                                                </Typography>
                                                <Stack alignItems='flex-start'>
                                                    {careersUpskillTime?.sectorOrSubsector?.some(sector =>
                                                        sector?.careers?.some(current => current?.careerType === "CURRENT_ROLE")) &&
                                                        <Stack direction='row-reverse' pb='4px' gap={{ xs: 0, sm: 1 }} alignItems='center'>
                                                            <Typography sx={{ color: isRegion ? 'black' : 'unset' }} lineHeight={1.7} pb='4px' variant='body'>
                                                                <em>{careersUpskillTime?.sectorOrSubsector?.find(sector =>
                                                                    sector?.careers?.some(current => current?.careerType === "CURRENT_ROLE"))?.careers.find(curr => curr?.careerType === "CURRENT_ROLE")?.name}</em>
                                                                {/* {Careers.find(current => current.careerType === "CURRENT_ROLE")?.name} */}
                                                            </Typography>
                                                            <Box sx={{ ml: 1.7, mr: 0.7, padding: { sm: '0px !important' } }}>
                                                                {/* <SvgIcon
                                                                    component={flagIcon}
                                                                    inheritViewBox
                                                                /> */}
                                                                <BadgeIcon />
                                                            </Box>
                                                        </Stack>}
                                                    {careersUpskillTime?.sectorOrSubsector?.some(sector =>
                                                        sector?.careers?.some(current => current?.careerType === "CAREER_GOAL")) &&
                                                        <Stack direction='row-reverse' pb='4px' gap={{ xs: 0, sm: 1 }} alignItems='center'>
                                                            <Typography sx={{ color: isRegion ? 'black' : 'unset' }} lineHeight={1.7} pb='4px' variant='body'>
                                                                <em>{careersUpskillTime?.sectorOrSubsector?.find(sector =>
                                                                    sector?.careers?.some(current => current?.careerType === "CAREER_GOAL"))?.careers.find(curr => curr?.careerType === "CAREER_GOAL")?.name}</em>
                                                                {/* {Careers.find(current => current.careerType === "CURRENT_ROLE")?.name} */}
                                                            </Typography>
                                                            <Box sx={{ ml: 1.7, mr: 0.7 }}>
                                                                <SvgIcon
                                                                    component={flagIcon}
                                                                    inheritViewBox
                                                                />
                                                            </Box>
                                                        </Stack>}
                                                    {/* <Typography lineHeight={1.7} pb='4px' variant='body'>
                                                        <em>{careersUpskillTime?.sectorOrSubsector?.find(sector =>
                                                            sector?.careers?.some(current => current?.careerType === "CAREER_GOAL"))?.careers.find(curr => curr?.careerType === "CAREER_GOAL")?.name}
                                                        </em>
                                                    </Typography> */}
                                                    {
                                                        compareCareersState.map(career =>
                                                            <Stack direction='row-reverse' pb='4px' gap={{ xs: 0, sm: 1 }} alignItems='center' >
                                                                <Typography sx={{ color: isRegion ? 'black' : 'unset' }} lineHeight={1.7} textAlign='left' variant='body'>
                                                                    {/* {Careers.find(current => current?.id === career)?.name} */}
                                                                    {careersUpskillTime?.sectorOrSubsector?.find(sector =>
                                                                        sector?.careers?.some(current => current?.id === career))?.careers.find(curr => curr?.id === career)?.name}
                                                                </Typography>
                                                                <IconButton
                                                                    color='inherit'
                                                                    onClick={() => addToCompare(career)}
                                                                    sx={{ padding: '6px 10px', color: isRegion ? 'black' : 'unset' }}

                                                                >
                                                                    <RemoveCircleOutlineOutlined sx={{ color: isRegion ? 'black' : 'unset' }} fontSize='medium' />
                                                                </IconButton>
                                                            </Stack>
                                                        )

                                                    }
                                                </Stack>
                                            </Stack>

                                            <Stack direction='row' justifyContent='center' flexWrap='wrap' gap={{ xs: 1.2, sm: 2 }} mt={2}>
                                                {/* <Button variant='contained'  onClick={showCompareCareersPopup}>
                                <Typography variant='body1'  >Compare Careers</Typography><CompareIcon /> <span className='badge'>{compareCareersState?.length}</span>
                            </Button> */}
                                                <Button
                                                    sx={{
                                                        textTransform: 'none',
                                                        width: { xs: '100%', sm: 'unset' },
                                                        borderRadius: 'unset',
                                                        backgroundColor: (theme) =>
                                                            isRegion && buttonColor
                                                                ? buttonColor
                                                                : `${theme.palette.secondary.main} !important`,
                                                        ...(isRegion && buttonColor && {
                                                            '&:hover': {
                                                                backgroundColor: buttonColor,
                                                                opacity: 0.9,
                                                            },
                                                        }),
                                                    }}
                                                    variant="contained"
                                                    endIcon={
                                                        <CompareIcon
                                                            sx={{
                                                                color:
                                                                    isRegion && buttonFontColor ? buttonFontColor : 'primary.white',
                                                            }}
                                                        />
                                                    }
                                                    onClick={showCompareCareersPopup}
                                                >
                                                    <Typography
                                                        sx={{
                                                            color:
                                                                isRegion && buttonFontColor ? buttonFontColor : 'primary.white',
                                                        }}
                                                        variant="button1"
                                                    >
                                                        Compare Careers
                                                    </Typography>
                                                </Button>
                                                <Button
                                                    sx={{
                                                        textTransform: "none", width: { xs: '100%', sm: 'unset' }, ...(isRegion && buttonColor && {
                                                            '&:hover': {
                                                                backgroundColor: buttonColor,
                                                                opacity: 0.9,
                                                            },
                                                        }), borderRadius: 'unset', backgroundColor: (theme) => isRegion && buttonColor ? buttonColor : `${theme.palette.secondary.main} !important`
                                                    }}
                                                    variant='contained'
                                                    endIcon={<FileDownloadOutlinedIcon sx={{ color: isRegion && buttonFontColor ? buttonFontColor : 'primary.white' }} />}
                                                    onClick={() => getSkillReport('careers-page')}
                                                >
                                                    <Typography sx={{ color: isRegion && buttonFontColor ? buttonFontColor : 'primary.white' }} variant='button1' >Get Skills Report</Typography>
                                                </Button>
                                            </Stack>
                                        </Box>

                                    </Box>
                                    {/* <Box
                                        // sx={{ mb: 4 }}
                                        className='ai-video-wrapper'
                                    >
                                        <Box sx={showLabel ? videoClassOpen : videoClassClose} className='video-position'>
                                            <AiVideo
                                                closeLabel={closeLabel}
                                                url={reskillBar ? `${Ai_Video_Url}bar-chart-reskill.mp4` : `${Ai_Video_Url}bar-chart-upskill.mp4`}
                                                position='default'
                                            />
                                        </Box>
                                    </Box> */}
                                </Stack>
                            </Box>}
                        </>
                    }
                </>
            </Box >
        </>
    )
}

export default GraphsData

GraphsData.propTypes = {
    showCompareCareersPopup: PropTypes.bool,
    reskillBar: PropTypes.bool,
    isLoading: PropTypes.bool,
    compareCareersState: PropTypes.array,
    careerHistoryId: PropTypes.array,
    careerGoalId: PropTypes.array,
    careersUpskillTime: PropTypes.object,
    regionData: PropTypes.object,
    addToCompare: PropTypes.func,
    showCareerDetailsPopup: PropTypes.func,
    getSkillReport: PropTypes.func,
    status: PropTypes.func,
    scrollRef: PropTypes.func,
    isRegion: PropTypes.bool,
    collegeId: PropTypes.string,
    IP: PropTypes.string,
    handleToggle: PropTypes.func,
    isReskill: PropTypes.bool,
}

