const mongoose = require("mongoose");

const PredictByWorktypeSchema = new mongoose.Schema({
  code: Number,
  name: String,
  employment: { type: Number },
  note: String,
  careerId: { type: mongoose.Schema.Types.ObjectId, ref: "career" },

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.PredictByWorktypeSchema = PredictByWorktypeSchema;

class PredictByWorktype extends mongoose.Model {
  
}

mongoose.model(PredictByWorktype, PredictByWorktypeSchema, "predictByWorktype");

module.exports.PredictByWorktype = PredictByWorktype;