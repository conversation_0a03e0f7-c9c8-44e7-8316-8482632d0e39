const mongoose = require('mongoose');
require('dotenv').config();

const FineTuningDataService = require('../services/fineTuningDataService');

/**
 * Test script for Fine-Tuning Data Service
 * Verifies data generation without affecting existing system
 */
const testFineTuningData = async () => {
  console.log('🧪 Testing Fine-Tuning Data Service');
  console.log('===================================');

  try {
    // Connect to MongoDB
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
    console.log('✅ Connected to MongoDB');

    // Initialize service
    const dataService = new FineTuningDataService();

    // Test data generation with small sample
    console.log('\n📊 Generating sample training data...');
    const result = await dataService.generateTrainingData({
      includeCollegeSpecific: true,
      includeGeneralContent: true,
      maxExamplesPerType: 5, // Small sample for testing
      validationSplit: 0.2
    });

    console.log('\n✅ Data generation completed!');
    console.log('📊 Statistics:');
    console.log(`   Training examples: ${result.trainingData.length}`);
    console.log(`   Validation examples: ${result.validationData.length}`);
    console.log(`   Total examples: ${result.statistics.totalExamples}`);
    
    console.log('\n📋 Content breakdown:');
    Object.entries(result.statistics).forEach(([key, value]) => {
      if (key !== 'totalExamples' && value > 0) {
        console.log(`   ${key}: ${value}`);
      }
    });

    // Show sample training examples
    console.log('\n📝 Sample training examples:');
    result.trainingData.slice(0, 3).forEach((example, index) => {
      console.log(`\n${index + 1}. System: ${example.messages[0].content.substring(0, 80)}...`);
      console.log(`   User: ${example.messages[1].content}`);
      console.log(`   Assistant: ${example.messages[2].content.substring(0, 100)}...`);
    });

    // Test JSONL export
    console.log('\n📤 Testing JSONL export...');
    const jsonlData = dataService.exportToJSONL(result.trainingData.slice(0, 2));
    console.log('✅ JSONL export successful');
    console.log('📄 Sample JSONL:');
    console.log(jsonlData.split('\n')[0].substring(0, 200) + '...');

    console.log('\n🎉 All tests passed successfully!');
    console.log('✅ Fine-tuning data service is working correctly');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  } finally {
    // Cleanup
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
  }
};

// Run test if called directly
if (require.main === module) {
  testFineTuningData();
}

module.exports = testFineTuningData;
