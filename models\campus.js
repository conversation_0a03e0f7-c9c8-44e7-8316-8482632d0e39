const mongoose = require("mongoose");
const { College, CollegeSchema } = require("./college");
const { Country, CountrySchema } = require("./country");
const { User, UserSchema } = require("./user");

const CampusSchema = new mongoose.Schema({
  name: String,
  collegeId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(College, CollegeSchema) },
  address1: String,
  address2: String,
  city: String,
  state: String,
  zip: String,
  countryId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(Country, CountrySchema) },
  contactNumber: String,
  website: String,
  // email: { type: String, unique: true, lowercase: true, trim: true },
  email: { type: String, lowercase: true, trim: true },
  adminUserId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(User, UserSchema) },
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.CampusSchema = CampusSchema;

class Campus extends mongoose.Model {
  static async createDefaultEntries() {
    for (const entry of defaultEntries) {
      const count = await Campus.count({ _id: entry._id });
      if (count) {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await Campus.create(entry);
    }

    Campus.createIndexes(
      {
        "name": 1
      },
      {
        collation: {
          locale: 'en',
          strength: 2,
        }
      });
  }
}

mongoose.model(Campus, CampusSchema, "campuses");

module.exports.Campus = Campus;

