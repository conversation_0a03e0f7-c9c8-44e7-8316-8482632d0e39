const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const campusesController = require('../controllers/campuses.controller');
const CollegeAdminGuard = require("../guards/collegeAdmin.guard");
const CampusAdminGuard = require("../guards/campusAdmin.guard");

router.post("/add", CollegeAdminGuard, campusesController.add);

router.get("/get", CampusAdminGuard, campusesController.get);

router.get("/getById", CampusAdminGuard, campusesController.getByID);

router.delete("/remove", CollegeAdminGuard, campusesController.remove);

router.put("/update", CampusAdminGuard, campusesController.update);



module.exports = router;