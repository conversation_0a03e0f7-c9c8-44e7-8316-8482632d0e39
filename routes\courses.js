
const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const coursesController = require('../controllers/courses.controller')
const CampusAdminGuard = require("../guards/campusAdmin.guard");
const CollegeAdminGuard = require("../guards/collegeAdmin.guard");

router.post("/add", CampusAdminGuard, coursesController.add);

router.get("/get", CampusAdminGuard, coursesController.get);

router.get("/getById", CampusAdminGuard, coursesController.getByID);

router.delete("/remove", CampusAdminGuard, coursesController.remove);

router.put("/update", CampusAdminGuard, coursesController.update);

router.post("/addBulk", CollegeAdminGuard, coursesController.addBulk);

router.delete("/deleteCoursesByCampus", CollegeAdminGuard, coursesController.deleteCoursesByCampus);

router.post("/export", CollegeAdminGuard, coursesController.exportCourses);

module.exports = router;