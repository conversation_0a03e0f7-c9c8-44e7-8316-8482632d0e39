const OpenAI = require("openai");
const commonHelper = require("./commonHelper");

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const convertToBritishEnglish = async (description) => {
  try {
    const prompt = `
Convert the following text to British English spelling and phrasing. Do not change the meaning or add any content.

Text:
"""
${description}
"""
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        { role: "system", content: "You are an expert editor who converts text to British English." },
        { role: "user", content: prompt }
      ]
    });

    return completion.choices[0].message.content.trim();
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return description;
  }
}

module.exports = {
  convertToBritishEnglish
}
