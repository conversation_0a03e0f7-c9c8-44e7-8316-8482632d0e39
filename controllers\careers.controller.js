const { default: mongoose } = require("mongoose");
const { Subsector } = require("../models/subsector");
const { getAddedBy, getEditedBy } = require('../tools/database');
const { Sector } = require("../models/sector");
const { Career, careerTypes } = require("../models/career");
const { SheetCareer } = require("../models/sheetCareer");
const commonHelper = require("../helpers/commonHelper");
const { messageResponse } = require("../helpers/commonHelper");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, UPDATE_SUCCESS, REMOVE_SUCCESS, CUSTOM } = require("../config/messages");
const LMIForAll = require("../helpers/LMIForAll");
const OnetServices = require("../helpers/onetServices");
const { PredictByQualification } = require("../models/predictByQualification");
const { PredictByRegion } = require("../models/predictByRegion");
const { PredictByWorktype } = require("../models/predictByWorktype");
const { LMISkillAbility } = require("../models/lmiSkillAbility");
const { LMISkillAbilityLevel } = require("../models/lmiSkillAbilityLevel");
const { PredictByYear } = require("../models/predictByYear");
const { ONetCareer } = require("../models/oNetCareer");
const { LmiSkills } = require("../models/lmiSkills");
const { LmiAbilities } = require("../models/lmiAbilities");
const { CareerDetail_LMI } = require("../models/careerDetail_LMI");
const axios = require('axios');
const { LmiInterests } = require("../models/lmiInterests");
const xlsx = require("xlsx");
const { deleteFile, toObjectId } = require("../helpers/misc");
const { convertToBritishEnglish } = require("../helpers/openAI");
const { mean } = require("lodash");
const { CareerWorktype } = require("../models/careerWorktype");
const { CareerGrowth } = require("../models/careerGrowth");
const { CareerQualification } = require("../models/careerQualification");
const { CareerHours } = require("../models/careerHours");
const { CareerSalary } = require("../models/careerSalary");

const validate = async(req, res, action) => {
  try {
    let oldEntry;
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }

      oldEntry = await Career.findById(new mongoose.Types.ObjectId(req.body.id));
      if (!oldEntry) {
        return messageResponse(NOT_FOUND, "Career", false, 404, null);
      }
    }

    if (!req.body.title) {
      return messageResponse(INVALID_MISSING, "Title", false, 400, null)
    }

    if (!req.body.onetCode) {
      return { success: false, msg: "Invalid/missing ONet code." };
    }

    if (!req.body.socCode) {
      return { success: false, msg: "Invalid/missing SOC code." };
    }

    if (!req.body.sectors || !req.body.sectors.length) {
      return { success: false, msg: "Invalid/missing Sector(s)." };
    }

    if (!req.body.onetCodeForSkillAbility) {
      return { success: false, msg: "Invalid/missing ONet code for Skill/Ability." };
    }

    if (!req.body.skills || !req.body.skills.length) {
      return { success: false, msg: "Invalid/missing Skills." };
    }

    if (!req.body.abilities || !req.body.abilities.length) {
      return { success: false, msg: "Invalid/missing Abilities." };
    }

    if(!req.body.type) {
      return { success: false, msg: "Invalid/missing Type." };
    } else if (req.body.type === careerTypes.BROAD_CAREER) {
      if(!req.body.unitGroupId || req.body.unitGroupId && !mongoose.isValidObjectId(req.body.unitGroupId)) {
        return { success: false, msg: "Invalid/missing Unit Group." };
      }
    }

    // if (!mongoose.isValidObjectId(req.body.subsectorId)) {
    //   return { success: false, msg: "Invalid/missing Subsector Id." };
    // }

    // const selectedSubsector = await Subsector.findById(new mongoose.Types.ObjectId(req.body.subsectorId));
    // if (!selectedSubsector) {
    //   return { success: false, msg: "Invalid/missing Subsector." };
    // }

    // let query = { $or: [{ title: { $eq: req.body.title } }, { onetCode: { $eq: req.body.onetCode } }, { socCode: { $eq: req.body.socCode } }] }
    let query = { $or: [{ title: { $eq: req.body.title } }] }
    if (action == 'edit') {
      // query = { $and: [{ $or: [{ title: { $eq: req.body.title } }, { onetCode: { $eq: req.body.onetCode } }, { socCode: { $eq: req.body.socCode } }] }, { _id: { $ne: req.body.id } }] };
      query = { $and: [query, { _id: { $ne: req.body.id } }] };
    }

    const existingCareer = await Career.findOne(query);
    if (existingCareer != null) {
      // return { success: false, msg: "An entry with the same onet/title/soc already exists." };
      return { success: false, msg: "An entry with the same title already exists." };
    }

    return { success: true, oldEntry: oldEntry };
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async (req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    const {
      type,
      jobZone,
      unitGroupId,
      broadCareerIds,
      specialisedRoleIds,
      removeSpecialisedRoleIds,
      skills,
      abilities,
      metadata
    } = req.body;
    let sectors = [], sectorIds = [], subsectorIds = [];

    req.body.sectors.forEach(sector => {

      sectorIds.push(sector._id);
      let subsectorsOfsector = [];
      sector.subsectors.forEach(subsector => {
        subsectorsOfsector.push(subsector._id);
      });
      subsectorIds.push(...subsectorsOfsector);

      sectors.push({ sectorId: sector._id, subsectorIds: subsectorsOfsector });
    });

    const entry = {
      ...req.body,
      sectors,
      sectorIds,
      subsectorIds,
      jobZone: jobZone.value,
    }
    if(type === careerTypes.BROAD_CAREER) {
      entry.unitGroupId = unitGroupId;
    }
    if(type === careerTypes.SPECIALISED_ROLE && broadCareerIds) {
      entry.broadCareerIds = broadCareerIds;
    }

    let careerId;
    const addedBy = getAddedBy(req);
    if (action == 'add') {
      entry.addedBy = addedBy;
      let newCareer = await Career.create(entry)
      if (!newCareer) return messageResponse(ADD_ERROR, "Career", false, 400, null, res);
      
      careerId = newCareer._id;
    }
    else {
      entry.editedBy = getEditedBy(req, 'edit');
      const updatedCareer = await Career.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, { $set: entry }, { returnOriginal: false })
      if (!updatedCareer) return messageResponse(EXIST_PERMISSION, "Career", false, 404, null, res)
        
      careerId = req.body.id;
    }

    if (type === careerTypes.BROAD_CAREER && specialisedRoleIds && specialisedRoleIds.length) {
      if (removeSpecialisedRoleIds && removeSpecialisedRoleIds.length) {
        await Career.updateMany(
          { _id: { $in: toObjectId(removeSpecialisedRoleIds) } },
          { $pull: { broadCareerIds: toObjectId(careerId) } }
        )
      }
      
      await Career.updateMany(
        { _id: { $in: toObjectId(specialisedRoleIds) } },
        { $addToSet: { broadCareerIds: toObjectId(careerId) } }
      )
    }

    addEditMetadata(metadata)

    addOrEditSkillsAbilities(action, skills, abilities, addedBy, careerId, req.body, validateResult.oldEntry);

    if (action == 'add') {
      res.status(200).json({ success: true, id: careerId });
    }
    else {
      return messageResponse(UPDATE_SUCCESS, "Career", true, 200, null, res);
    }

  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async (req, res, next) => {
  return await addOrEdit(req, res, 'add')
}
module.exports.add = add;

const get = async (req, res) => {
  try {
    let careers = await Career.find({},
      { onetCode: 1, title: 1, socCode: 1, description: 1, addedBy: 1, interests: 1 }).sort({ "addedBy.date": -1 });

    if (!careers.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res)

    return messageResponse(null, "", true, 200, careers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.get = get;

const fetchAllData = async (req, res) => {
  try {
    const pipeline = [
      {
        $lookup: {
          from: "predictbyqualifications",
          localField: "_id",
          foreignField: "careerId",
          as: "predictByQualification"
        }
      },
      {
        $lookup: {
          from: "predictbyregions",
          localField: "_id",
          foreignField: "careerId",
          as: "predictByRegion"
        }
      },
      {
        $lookup: {
          from: "predictbyworktypes",
          localField: "_id",
          foreignField: "careerId",
          as: "predictByWorktype"
        }
      },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectors"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectors"
        }
      },
      {
        $sort: { _id: -1 }
      }
    ]
    const careers = await Career.aggregate(pipeline);
    const allLmiSkills = await LmiSkills.find()
    const allLmiAbilities = await LmiAbilities.find()

    careers.forEach(career => {
      const { onetCode } = career

      const lmiSkills = allLmiSkills.find(lmiSkill => lmiSkill.onetcode === onetCode);

      if (lmiSkills && lmiSkills.scales) {
        if (lmiSkills.scales.length > 0) {
          let skills = [];
          lmiSkills.scales.forEach(scale => {
            skills.push(...scale.skills);
          });
          career["skillsExist"] = true;
          career["skills"] = skills;
        }
        else {
          career["skillsExist"] = false;
        }
      }
  
      const lmiAbilities = allLmiAbilities.find(lmiAbility => lmiAbility.onetcode === onetCode);

      if (lmiAbilities && lmiAbilities.scales) {
        if (lmiAbilities.scales.length > 0) {
          let abilities = [];
          lmiAbilities.scales.forEach(scale => {
            abilities.push(...scale.abilities);
          });
          career["abilitiesExist"] = true;
          career["abilities"] = abilities;
        }
        else {
          career["abilitiesExist"] = false;
        }
      }
    })

    return messageResponse(null, "", true, 200, careers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.fetchAllData = fetchAllData;

const addSkillsAndAbilities = async(req, res) => {
  try {
    const careers = await Career.find().select("onetCode");
    const lmi = new LMIForAll()
    const skillsInsertQuery = []
    const abilitiesInsertQuery = []

    for(const career of careers) {
      const { onetCode } = career

      const lmiSkills = await lmi.getSkills(onetCode)
      if (lmiSkills && lmiSkills.data && lmiSkills.data.scales) {
        skillsInsertQuery.push(lmiSkills.data)
      }

      const lmiAbilities = await lmi.getAbilities(onetCode)
      if (lmiAbilities && lmiAbilities.data && lmiAbilities.data.scales) {
        abilitiesInsertQuery.push(lmiAbilities.data)
      }
    }

    await LmiSkills.insertMany(skillsInsertQuery)
    await LmiAbilities.insertMany(abilitiesInsertQuery)

    return messageResponse(null, "", true, 200, careers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.addSkillsAndAbilities = addSkillsAndAbilities;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const pipeline = [
      { $match: { _id: { $eq: new mongoose.Types.ObjectId(req.query.id) } } },
      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobZone"
        }
      },
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectorData"
        }
      },
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectorData"
        }
      },
      {
        $lookup: {
          from: "lmiSkillAbilityLevels",
          localField: "_id",
          foreignField: "careerId",
          as: "lmiSkillAbilityLevels"
        }
      },
      {
        $lookup: {
          from: "lmiSkillsAbilities",
          localField: "lmiSkillAbilityLevels.lmiSkillAbilityId",
          foreignField: "_id",
          as: "skillsAbilities"
        }
      },
      {
        $lookup: {
          from: "careerSalary",
          let: { socCode: "$socCode" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$socCode", "$$socCode"] }
              }
            },
            {
              $project: {
                _id: 0,
                regionID: 1,
                meanSalary: "$meanValue",
                medianSalary: "$medianValue"
              }
            }
          ],
          as: "salaryData"
        }
      },
      {
        $lookup: {
          from: "careerHours", 
          let: { socCode: "$socCode" },
          pipeline: [
            {
              $match: {
                $expr: { $eq: ["$socCode", "$$socCode"] }
              }
            },
            {
              $project: {
                _id: 0,
                regionID: 1,
                meanHours: "$meanValue",
                medianHours: "$medianValue"
              }
            }
          ],
          as: "hoursData"
        }
      },
      {
        $lookup: {
          from: "regions",
          pipeline: [
            { $project: { _id: 0, regionID: "$_id", isSystem: 1 } },
            { $sort: { isSystem: -1 } }
          ],
          as: "regions"
        }
      },
      {
        $addFields: {
          metadata: {
            $map: {
              input: "$regions",
              as: "region",
              in: {
                regionId: "$$region.regionID",
                socCode: "$socCode",
                salaryMean: {
                  $let: {
                    vars: {
                      salary: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$salaryData",
                              as: "s",
                              cond: { $eq: ["$$s.regionID", "$$region.regionID"] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: "$$salary.meanSalary"
                  }
                },
                salaryMedian: {
                  $let: {
                    vars: {
                      salary: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$salaryData",
                              as: "s",
                              cond: { $eq: ["$$s.regionID", "$$region.regionID"] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: "$$salary.medianSalary"
                  }
                },
                hoursMean: {
                  $let: {
                    vars: {
                      hours: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$hoursData",
                              as: "h",
                              cond: { $eq: ["$$h.regionID", "$$region.regionID"] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: "$$hours.meanHours"
                  }
                },
                hoursMedian: {
                  $let: {
                    vars: {
                      hours: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: "$hoursData",
                              as: "h",
                              cond: { $eq: ["$$h.regionID", "$$region.regionID"] }
                            }
                          },
                          0
                        ]
                      }
                    },
                    in: "$$hours.medianHours"
                  }
                }
              }
            }
          }
        }
      },
      {
        $lookup: {
          from: "careers",
          localField: "_id",
          foreignField: "broadCareerIds",
          as: "specialisedRoles"
        }
      },
      {
        $addFields: {
          specialisedRoleIds: {
            $map: {
              input: "$specialisedRoles",
              as: "role",
              in: "$$role._id"
            }
          }
        }
      },
      {
        $project: {
          "onetCode": 1,
          "title": 1,
          "socCode": 1,
          "description": 1,
          "tasks": 1,
          "sectors": 1,
          "sectorIds": 1,
          "subsectorIds": 1,
          "addedBy": 1,
          "editedBy": 1,
          "onetCodeForSkillAbility": 1,
          "broadCareerIds": 1,
          "unitGroupId": 1,
          "specialisedRoleIds": 1,
          "specialisedRoles._id": 1,
          "specialisedRoles.title": 1,
          "type": 1,

          "jobZone.value": 1,
          "jobZone.title": 1,

          "sectorData._id": 1,
          "sectorData.name": 1,

          "subsectorData._id": 1,
          "subsectorData.name": 1,

          "lmiSkillAbilityLevels.lmiSkillAbilityId": 1,
          "lmiSkillAbilityLevels.level": 1,

          "skillsAbilities._id": 1,
          "skillsAbilities.lmiId": 1,
          "skillsAbilities.lmiName": 1,
          "skillsAbilities.category": 1,

          "careerSalary": 1,
          "careerHours": 1,
          "metadata": 1,

          "videoUrl": 1,
        }
      },
    ]
    const aggrResult = await Career.aggregate(pipeline);

    if (!aggrResult || aggrResult.length == 0) return messageResponse(NOT_FOUND, "Career", false, 404, null, res);

    let returnResult = aggrResult[0];
    if (!returnResult.onetCodeForSkillAbility) {
      returnResult.onetCodeForSkillAbility = returnResult.onetCode
    }
    if (returnResult.jobZone && returnResult.jobZone.length > 0) {
      returnResult.jobZone = returnResult.jobZone[0];
    }

    let skills = [], abilities = [];
    if (returnResult.lmiSkillAbilityLevels && returnResult.lmiSkillAbilityLevels.length > 0) {
      returnResult.lmiSkillAbilityLevels.forEach(skillabilitylevels => {
        const skillAbility = returnResult.skillsAbilities.find(skillAbility => skillAbility._id.toString() == skillabilitylevels.lmiSkillAbilityId.toString());
        if (skillAbility) {
          if (skillAbility.category == 'skill') {
            skills.push({ id: skillAbility.lmiId, name: skillAbility.lmiName, value: skillabilitylevels.level });
          }
          else if (skillAbility.category == 'ability') {
            abilities.push({ id: skillAbility.lmiId, name: skillAbility.lmiName, value: skillabilitylevels.level });
          }
        }
      });

      delete returnResult["lmiSkillAbilityLevels"];
      delete returnResult["skillsAbilities"];
    }
    returnResult.skills = skills;
    returnResult.abilities = abilities;

    let sectors = [];
    if (returnResult.sectors && returnResult.sectors.length > 0) {
      returnResult.sectors.forEach(sector => {
        let subsectorsData = [];
        const foundSectorData = returnResult.sectorData.find(sectorData => sectorData._id.toString() == sector.sectorId.toString());
        if (foundSectorData) {
          sector.subsectorIds.forEach(subsector => {
            const foundSubsectorData = returnResult.subsectorData.find(subsectorData => subsectorData._id.toString() == subsector.toString());
            subsectorsData.push({ _id: foundSubsectorData._id, label: foundSubsectorData.name });
          });
          sectors.push({ _id: foundSectorData._id, label: foundSectorData.name, subsectors: subsectorsData });
        }
      });

      delete returnResult["sectorIds"];
      delete returnResult["subsectorIds"];
      delete returnResult["sectorData"];
      delete returnResult["subsectorData"];
    }
    returnResult.sectors = sectors;

    return messageResponse(null, "", true, 200, returnResult, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID = getByID;

const remove = async (req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const career = await Career.findOneAndDelete({ _id: req.body.id, defaultEntry: false });

    if (!career) return messageResponse(EXIST_PERMISSION, "Career", false, 404, null, res);

    removeCareerCore(req.body.id);
    return messageResponse(REMOVE_SUCCESS, "Career", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove = remove;

const removeCareerCore = async (careerId) => {
  const predictByQualification = await PredictByQualification.deleteMany({ careerId: careerId });
  const predictByRegion = await PredictByRegion.deleteMany({ careerId: careerId });
  const predictByWorktype = await PredictByWorktype.deleteMany({ careerId: careerId });
  const predictByYear = await PredictByYear.deleteMany({ careerId: careerId });

  const lmiSkillAbilityLevels = await LMISkillAbilityLevel.deleteMany({ careerId: careerId });
}

const update = async (req, res, next) => {
  return await addOrEdit(req, res, 'edit')
};
module.exports.update = update;

const createSectorNSubsectors = async (req, res, next) => {
  try {

    if (req.body.deleteAll) {
      const careersDeleteResult = await Career.deleteMany({});
      const subsectorsDeleteResult = await Subsector.deleteMany({});
      const sectorsDeleteResult = await Sector.deleteMany({});
    }

    // let sheetCareers = await SheetCareer.find({sector:'Agriculture'});
    let sheetCareers = await SheetCareer.find();
    const addedBy = getAddedBy(req, 'add');

    const sectors = [], subsectors = [];
    // careers.map(career => careersWithSectorNameOnly)

    sheetCareers.forEach(sheetCareer => {
      const sectorIndex = sectors.findIndex(sec => sec.name === sheetCareer.sector);
      if (sectorIndex == -1) {
        let sector = { name: sheetCareer.sector, addedBy, defaultEntry: false, status: "active" }
        sectors.push(sector);
      }
    });

    let newSectors;
    try {
      newSectors = await Sector.create(sectors);
    }
    catch (err) {
      console.log('error in create sectors', err);
    }

    sheetCareers.forEach(sheetCareer => {
      const sector = newSectors.find(sec => sec.name == sheetCareer.sector);
      sheetCareer.sectorId = sector._id;

      const subsectorIndex = subsectors.findIndex(subsec => subsec.name === sheetCareer.subsector);
      if (subsectorIndex == -1) {
        let subsector = { name: sheetCareer.subsector, sectorId: sheetCareer.sectorId, addedBy, defaultEntry: false, status: "active" }
        subsectors.push(subsector);
      }
    });

    let newSubsectors;
    try {
      newSubsectors = await Subsector.create(subsectors);
    }
    catch (err) {
      console.log('error in create subsectors', err);
    }

    let careers = [];
    sheetCareers.forEach(sheetCareer => {
      const subsector = newSubsectors.find(subsec => subsec.name == sheetCareer.subsector);
      sheetCareer.subsectorId = subsector._id;

      let { name, subsectorId, onetcode } = sheetCareer;
      let career = { name, subsectorId, onetcode, addedBy, defaultEntry: false, status: "active" };
      careers.push(career);
    });

    if (req.body.createCareers) {
      Career.create(careers)
        .then((newEntries) => {
          // console.log('added careers', newEntries); 
        })
        .catch((err) => { console.log('error in create careers', err) });
    }

    res.status(200).json({ success: true });
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later" });
  }
};
module.exports.createSectorNSubsectors = createSectorNSubsectors;

const getSocCodes = async(req, res) => {
  try {
    if (!req.query.onetCode) {
      return res.status(400).json({ success: false, msg: "Onet code not provided" });
    }

    if (req.query.onetCode.includes(',')) {
      return res.status(400).json({ success: false, msg: "Invalid character in onet code" });
    }

    let lmi = new LMIForAll();
    const socCodes = await lmi.getSocCodes(req.query.onetCode);

    res.status(200).json(socCodes.data);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later", detail: error });
  }
};
module.exports.getSocCodes = getSocCodes;

const getCareersByKeyword = async (req, res) => {
  try {
    if (!req.query.keyword) {
      return res.status(400).json({ msg: "keyword not provided" });
    }

    let onet = new OnetServices();
    const careers = await onet.getCareerByKeyword(req.query.keyword);

    let newCarrers = [];
    if (careers.data && careers.data.career) {
      careers.data.career.forEach(career => {
        const { code, title } = career;
        newCarrers.push({ code, title });
      });
    }
    res.status(200).json(newCarrers);
    // res.status(200).json(careers.data);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later", detail: error });
  }
};
module.exports.getCareersByKeyword = getCareersByKeyword;

const getCareerDetails = async(req, res) => {
  const isReturn = res ? true : false;
  try {
    if (!req.query.socCode) {
      return !isReturn ? 
        { success: false, msg: "SOC Code not provided" } : 
        res.status(400).json({ msg: "SOC Code not provided" });
    }

    if (!req.query.onetCode) {
      return !isReturn ? 
        { success: false, msg: "ONet Code not provided" } : 
        res.status(400).json({ msg: "ONet Code not provided" });
    }

    const oNetCode = req.query.onetCode, socCode = req.query.socCode;

    let returnCareerDetails = {};

    let lmi = new LMIForAll();
    const careerDetails = await lmi.getSocDetails(socCode);

    if (careerDetails) {
      returnCareerDetails["socCode"] = socCode;
      returnCareerDetails["onetCode"] = oNetCode;
      returnCareerDetails.description = careerDetails.data.description;
      returnCareerDetails.tasks = careerDetails.data.tasks;
    }

    let onet = new OnetServices();
    const jobZone = await onet.getJobZone(oNetCode);

    if (jobZone && jobZone.jobZone && jobZone.jobZone.length > 0) {
      returnCareerDetails["jobZone"] = jobZone.jobZone[0];
    }

    const estimatePay = await lmi.getEstimatePay(socCode);
    if (estimatePay && estimatePay.data && estimatePay.data.series && estimatePay.data.series.length > 0) {
      let latestYear = 0, latestPay;
      estimatePay.data.series.forEach(estPay => {
        if (estPay.year > latestYear) {
          latestYear = estPay.year;
          latestPay = estPay.estpay;
        }
      });
      returnCareerDetails["estimatePay"] = latestPay
      returnCareerDetails["estimatePayYear"] = latestYear
    }

    const estimateHours = await lmi.getEstimateHours(socCode);
    if (estimateHours && estimateHours.data && estimateHours.data.series && estimateHours.data.series.length > 0) {
      let latestYear = 0, latestHours;
      estimateHours.data.series.forEach(estHrs => {
        if (estHrs.year > latestYear) {
          latestYear = estHrs.year;
          latestHours = estHrs.hours;
        }
      });
      returnCareerDetails["estimateHours"] = latestHours
      returnCareerDetails["estimateHoursYear"] = latestYear
    }

    const predictByQualification = await lmi.getPredictByQualification(socCode);
    if (predictByQualification && predictByQualification.data && predictByQualification.data.predictedEmployment &&
      predictByQualification.data.predictedEmployment.length > 0) {
      let latestYear = 0, latestPredict;
      predictByQualification.data.predictedEmployment.forEach(predict => {
        if (predict.year > latestYear) {
          latestYear = predict.year;
          latestPredict = predict.breakdown;
        }
      });
      returnCareerDetails["predictByQualification"] = latestPredict
      returnCareerDetails["predictByQualificationYear"] = latestYear
    }

    const predictByRegion = await lmi.getPredictByRegion(socCode);
    if (predictByRegion && predictByRegion.data && predictByRegion.data.predictedEmployment &&
      predictByRegion.data.predictedEmployment.length > 0) {
      let latestYear = 0, latestPredict;
      predictByRegion.data.predictedEmployment.forEach(predict => {
        if (predict.year > latestYear) {
          latestYear = predict.year;
          latestPredict = predict.breakdown;
        }
      });
      returnCareerDetails["predictByRegion"] = latestPredict
      returnCareerDetails["predictByRegionYear"] = latestYear
    }

    const predictByWorktype = await lmi.getPredictByWorktype(socCode);
    if (predictByWorktype && predictByWorktype.data && predictByWorktype.data.predictedEmployment &&
      predictByWorktype.data.predictedEmployment.length > 0) {
      let latestYear = 0, latestPredict;
      predictByWorktype.data.predictedEmployment.forEach(predict => {
        if (predict.year > latestYear) {
          latestYear = predict.year;
          latestPredict = predict.breakdown;
        }
      });
      returnCareerDetails["predictByWorktype"] = latestPredict
      returnCareerDetails["predictByWorktypeYear"] = latestYear
    }

    const predictByYear = await lmi.getPredictByYear(socCode);
    if (predictByYear && predictByYear.data && predictByYear.data.predictedEmployment &&
      predictByYear.data.predictedEmployment.length > 0) {
      returnCareerDetails["predictByYear"] = predictByYear.data.predictedEmployment;
    }

    const lmiSkills = await lmi.getSkills(oNetCode);
    if (lmiSkills && lmiSkills.data && lmiSkills.data.scales) {
      if (lmiSkills.data.scales.length > 0) {
        let skills = [];
        lmiSkills.data.scales.forEach(scale => {
          skills.push(...scale.skills);
        });
        returnCareerDetails["skillsExist"] = true;
        returnCareerDetails["skills"] = skills;
      }
      else {
        returnCareerDetails["skillsExist"] = false;
      }
    }

    const lmiAbilities = await lmi.getAbilities(oNetCode);
    if (lmiAbilities && lmiAbilities.data && lmiAbilities.data.scales) {
      if (lmiAbilities.data.scales.length > 0) {
        let abilities = [];
        lmiAbilities.data.scales.forEach(scale => {
          abilities.push(...scale.abilities);
        });
        returnCareerDetails["abilitiesExist"] = true;
        returnCareerDetails["abilities"] = abilities;
      }
      else {
        returnCareerDetails["abilitiesExist"] = false;
      }
    }

    return !isReturn ? 
      { success: true, data: returnCareerDetails } : 
      res.status(200).json(returnCareerDetails);
  }
  catch (error) {
    const errMsg = error?.response?.data?.error || error?.message
    // console.log(errMsg)
    // commonHelper.doReqActionOnError(error);
    return !isReturn ? 
      { success: false, msg: errMsg } : 
      res.status(500).json({ success: false, msg: errMsg });
  }
};
module.exports.getCareerDetails = getCareerDetails;

const getSkillsAbilities = async (req, res) => {
  try {
    if (!req.query.onetCode) {
      return messageResponse(REQUIRED, "ONet Code", false, 400, null, res);
    }

    const onetCode = req.query.onetCode;
    const onetCareer = await ONetCareer.findOne({ code: onetCode });

    if (!onetCareer) {
      return messageResponse(INVALID_MISSING, "ONet Code", false, 400, null, res);
    }

    // var re = /^[7-9][0-9]{9}$/;
    // if (re.test(user)) {
    //     alert("done");
    //     return true;
    // }

    let returnCareerDetails = {};

    // let lmi = new LMIForAll();
    // const lmiSkills = await lmi.getSkills(onetCode);
    const lmiSkills = await LmiSkills.findOne({ onetcode: onetCode }, { _id: 0 }).lean();
    if (lmiSkills && lmiSkills && lmiSkills.scales) {
      if (lmiSkills.scales.length > 0) {
        let skills = [];
        lmiSkills.scales.forEach(scale => {
          skills.push(...scale.skills);
        });
        returnCareerDetails["skillsExist"] = true;
        returnCareerDetails["skills"] = skills;
      }
      else {
        returnCareerDetails["skillsExist"] = false;
      }
    }

    // const lmiAbilities = await lmi.getAbilities(onetCode);
    const lmiAbilities = await LmiAbilities.findOne({ onetcode: onetCode }, { _id: 0 }).lean();
    if (lmiAbilities && lmiAbilities && lmiAbilities.scales) {
      if (lmiAbilities.scales.length > 0) {
        let abilities = [];
        lmiAbilities.scales.forEach(scale => {
          abilities.push(...scale.abilities);
        });
        returnCareerDetails["abilitiesExist"] = true;
        returnCareerDetails["abilities"] = abilities;
      }
      else {
        returnCareerDetails["abilitiesExist"] = false;
      }
    }

    res.status(200).json(returnCareerDetails);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later", detail: error.message });
  }
};
module.exports.getSkillsAbilities = getSkillsAbilities;

const addEditMetadata = async (metadata) => {
  if(!metadata.length) {
    return
  }

  const hoursOps = [], salaryOps = []
  metadata.map(meta => {
    hoursOps.push({
      updateOne: {
        filter: {
          regionID: toObjectId(meta.regionId),
          socCode: Number(meta.socCode)
        },
        update: {
          meanValue: meta.hoursMean,
          medianValue: meta.hoursMedian,
        },
        upsert: true
      }
    })
    salaryOps.push({
      updateOne: {
        filter: {
          regionID: toObjectId(meta.regionId),
          socCode: Number(meta.socCode)
        },
        update: {
          meanValue: meta.salaryMean,
          medianValue: meta.salaryMedian,
        },
        upsert: true
      }
    })
  })

  await Promise.all([
    CareerHours.bulkWrite(hoursOps),
    CareerSalary.bulkWrite(salaryOps),
  ]);
}

const addOrEditSkillsAbilities = async (action, skills, abilities, addedBy, careerId, career, oldCareer) => {
  let isDeleteReq = false, isInsertReq = false;
  if (action == 'edit') {
    if (career.oNetCode != oldCareer.oNetCode || career.socCode != oldCareer.socCode) {
      isDeleteReq = true;
      isInsertReq = true;
    }
  }
  else {
    isInsertReq = true;
  }

  if (isDeleteReq) {
    const deleteResult = await LMISkillAbilityLevel.deleteMany({ careerId: careerId });
  }
  if (isInsertReq) {
    if (!skills && skills.length == 0 && !abilities && abilities.length == 0) {
      return;
    }

    let skillAbilityIds = [];
    if (skills) {
      skills.forEach(skill => {
        skillAbilityIds.push(skill.id)
      });
    }

    if (abilities) {
      abilities.forEach(ability => {
        skillAbilityIds.push(ability.id)
      });
    }

    const pipeline = [
      { $match: { lmiId: { $in: skillAbilityIds } } },
    ]
    const existSkillsAndAbilities = await LMISkillAbility.aggregate(pipeline);

    let lmiSkillsNAbilities = [];
    let lmiSkillAbilityLevels = [];

    if (skills) {
      skills.forEach(skill => {
        let isExist = false;
        if (existSkillsAndAbilities && existSkillsAndAbilities.length > 0) {
          const existSkill = existSkillsAndAbilities.find(existSkill => existSkill.lmiId == skill.id);
          if (existSkill) {
            isExist = true;
            lmiSkillAbilityLevels.push({ lmiSkillAbilityId: existSkill._id, careerId: careerId, level: skill.value });
          }
        }

        if (!isExist) {
          lmiSkillsNAbilities.push({ lmiId: skill.id, lmiName: skill.name, category: 'skill', addedBy: addedBy })
        }
      });
    }

    if (abilities) {
      abilities.forEach(ability => {
        let isExist = false;
        if (existSkillsAndAbilities && existSkillsAndAbilities.length > 0) {
          const existAbility = existSkillsAndAbilities.find(existAbility => existAbility.lmiId == ability.id);
          if (existAbility) {
            isExist = true;
            lmiSkillAbilityLevels.push({ lmiSkillAbilityId: existAbility._id, careerId: careerId, level: ability.value });
          }
        }

        if (!isExist) {
          lmiSkillsNAbilities.push({ lmiId: ability.id, lmiName: ability.name, category: 'ability', addedBy: addedBy })
        }
      });
    }

    let newLmiSkillsAbilities;
    if (lmiSkillsNAbilities) {
      newLmiSkillsAbilities = await LMISkillAbility.create(lmiSkillsNAbilities);
    }

    if (skills) {
      skills.forEach(skill => {
        if (newLmiSkillsAbilities && newLmiSkillsAbilities.length > 0) {

          const newFoundSkill = newLmiSkillsAbilities.find(newSkill => newSkill.lmiId == skill.id);
          if (newFoundSkill) {
            lmiSkillAbilityLevels.push({ lmiSkillAbilityId: newFoundSkill._id, careerId: careerId, level: skill.value });
          }
        }
      });
    }

    if (abilities) {
      abilities.forEach(ability => {
        if (newLmiSkillsAbilities && newLmiSkillsAbilities.length > 0) {
          const existAbility = newLmiSkillsAbilities.find(existAbility => existAbility.lmiId == ability.id);
          if (existAbility) {
            lmiSkillAbilityLevels.push({ lmiSkillAbilityId: existAbility._id, careerId: careerId, level: ability.value });
          }
        }
      });
    }

    let skillAbilityLevelInsertManyResult
    if (lmiSkillAbilityLevels) {
      skillAbilityLevelInsertManyResult = await LMISkillAbilityLevel.insertMany(lmiSkillAbilityLevels);
    }
  }
}

const getTransferDetail = (jobZone) => {
  let randomTransferWindowMonths, transferWindow;
  if (jobZone == 1) {
    // Returns a random integer from 1 to 3:
    randomTransferWindowMonths = Math.floor(Math.random() * 3) + 1;
    transferWindow = "up to 3 months";
  }
  else if (jobZone == 2) {
    // Returns a random integer from 4 to 12:
    randomTransferWindowMonths = Math.floor(Math.random() * 9) + 4;
    transferWindow = "up to 1 year";
  }
  else if (jobZone == 3) {
    // Returns a random integer from 13 to 24:
    randomTransferWindowMonths = Math.floor(Math.random() * 12) + 13;
    transferWindow = "1 - 2 years";
  }
  else if (jobZone == 4) {
    // Returns a random integer from 25 to 48:
    randomTransferWindowMonths = Math.floor(Math.random() * 24) + 25;
    transferWindow = "2 - 4 years";
  }
  else if (jobZone == 5) {
    // Returns a random integer from 49 to 60:
    randomTransferWindowMonths = Math.floor(Math.random() * 12) + 49;
    transferWindow = "4+ years";
  }

  return { randomTransferWindowMonths, transferWindow };
}

const assignInterests = async (req, res) => {
  let lmi = new LMIForAll();
  let careers = await Career.find().select("onetCode");
  // console.log("Total careers:", careers.length)
  for(const [index, career] of careers.entries()) {
    const { onetCode } = career

    const lmiInterests = await lmi.getInterests(onetCode);
    if (lmiInterests && lmiInterests.data && lmiInterests.data.scales) {
      if (lmiInterests.data.scales.length > 0) {
        const interests = lmiInterests.data.scales[1].interests.sort((a, b) => b.value - a.value).slice(0, 2);
        
        career["interests"] = interests
        await career.save()
      }
    }
    // console.log("Updated Career:", index+1, "/", careers.length)
  }

  return messageResponse(UPDATE_SUCCESS, "Careers", true, 200, null, res);
}
module.exports.assignInterests = assignInterests;

const getCareerDetailsV2 = async (req, res) => {
  const isReturn = res ? true : false;
  const {
    careerDetails,
    skillDetails,
    abilityDetails,
    type,
  } = req.body;

  try {
    if (!req.query.socCode) {
      return !isReturn ? 
        { success: false, msg: "SOC Code not provided" } : 
        res.status(400).json({ msg: "SOC Code not provided" });
    }
    if (req.query.socCode.length !== 4) {
      return !isReturn ? 
        { success: false, msg: "SOC Code must be a 4-digit integer" } : 
        res.status(400).json({ msg: "SOC Code must be a 4-digit integer" });
    }

    if(type !== careerTypes.UNIT_GROUP) {
      if (!req.query.onetCode) {
        return !isReturn ? 
          { success: false, msg: "ONet Code not provided" } : 
          res.status(400).json({ msg: "ONet Code not provided" });
      }
      const onetCodeRegex = /^\d{2}-\d{4}\.\d{2}$/;
      if (!onetCodeRegex.test(req.query.onetCode)) {
        return !isReturn ? 
          { success: false, msg: "O*NET code must follow the pattern: two digits, a hyphen, four digits, a dot, and two digits (e.g., XX-XXXX.XX)" } : 
          res.status(400).json({ msg: "O*NET code must follow the pattern: two digits, a hyphen, four digits, a dot, and two digits (e.g., XX-XXXX.XX)" });
      }
    }

    const oNetCode = req.query.onetCode, socCode = req.query.socCode;
    let returnCareerDetails = {
      socCode,
      onetCode: oNetCode,
    };

    let careerData = isReturn ? 
      await CareerDetail_LMI.findOne({ socCode }, { socCode: 1, description: 1, tasks: 1 }).lean() :
      careerDetails?.find(c => c.socCode === socCode);

    if (careerData) {
      returnCareerDetails.description = careerData.description;
      returnCareerDetails.tasks = careerData.tasks;
    }

    let onet = new OnetServices();
    const jobZone = await onet.getJobZone(oNetCode);

    if (jobZone && jobZone.jobZone && jobZone.jobZone.length > 0) {
      returnCareerDetails["jobZone"] = jobZone.jobZone[0];
    }

    const lmiSkills = isReturn ?
      await LmiSkills.findOne({ onetcode: oNetCode }, { _id: 0 }).lean() :
      skillDetails?.find(s => s.onetcode === oNetCode);

    if (lmiSkills && lmiSkills && lmiSkills.scales) {
      if (lmiSkills.scales.length > 0) {
        let skills = [];
        lmiSkills.scales.forEach(scale => {
          skills.push(...scale.skills);
        });
        returnCareerDetails["skillsExist"] = true;
        returnCareerDetails["skills"] = skills;
      }
      else {
        returnCareerDetails["skillsExist"] = false;
      }
    }

    const lmiAbilities = isReturn ?
      await LmiAbilities.findOne({ onetcode: oNetCode }, { _id: 0 }).lean() :
      abilityDetails?.find(a => a.onetcode === oNetCode);

    if (lmiAbilities && lmiAbilities && lmiAbilities.scales) {
      if (lmiAbilities.scales.length > 0) {
        let abilities = [];
        lmiAbilities.scales.forEach(scale => {
          abilities.push(...scale.abilities);
        });
        returnCareerDetails["abilitiesExist"] = true;
        returnCareerDetails["abilities"] = abilities;
      }
      else {
        returnCareerDetails["abilitiesExist"] = false;
      }
    }

    return !isReturn ? 
      { success: true, data: returnCareerDetails } : 
      res.status(200).json(returnCareerDetails);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return !isReturn ? 
      { success: false, msg: error.message } : 
      res.status(500).json({ success: false, msg: "Something went wrong, please try again later", detail: error.message });
  }
};
module.exports.getCareerDetailsV2 = getCareerDetailsV2;


const importCareerDetailsLMI = async (req, res) => {
  try {
    let careers = await Career.find();
    if (!careers.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res);

    let importedCount = 0;
    let skippedCount = 0;
    let failedSOC = [];

    for (const [index, career] of careers.entries()) {
      console.log(`Processing career ${index + 1} of ${careers.length}`);
      const socCode = career.socCode;
      const careerID = career._id;

      if (!socCode) {
        skippedCount++;
        continue;
      }

      try {
        const response = await axios.get(`http://api.lmiforall.org.uk/api/v1/soc/code/${socCode}`);
        const lmiData = response.data;
        if (lmiData) {
          await CareerDetail_LMI.create({
            careerID,
            socCode,
            title: lmiData.title,
            description: lmiData.description,
            tasks: lmiData.tasks,
            qualifications: lmiData.qualifications,
            add_titles: lmiData.add_titles
          });
          importedCount++;
        }
      } catch (error) {
        console.error(`Failed to fetch LMI data for SOC: ${socCode}`, error.message);
        failedSOC.push(socCode);
        continue;
      }
    }
    return messageResponse(null, `LMI import completed. ✅ Imported: ${importedCount}, ⏭️ Skipped (no SOC): ${skippedCount}, ❌ Failed: ${failedSOC.length}`, true, 200, { importedCount, skippedCount, failedSOC }, res);

  } catch (error) {
    console.error("Unexpected server error:", error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.importCareerDetailsLMI = importCareerDetailsLMI;


const importInterestsLMI = async (req, res) => {
  try {
    let careers = await Career.find();
    if (!careers.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res);

    let importedCount = 0;
    let skippedCount = 0;
    let failedOnetCode = [];

    for (const [index, career] of careers.entries()) {
      console.log(`Processing career ${index + 1} of ${careers.length}`);
      const onetCode = career.onetCode;
      const careerID = career._id;
      const socCode = career.socCode;

      if (!onetCode) {
        skippedCount++;
        continue;
      }

      try {
        const response = await axios.get(`http://api.lmiforall.org.uk/api/v1/o-net/interests/${onetCode}`);
        const lmiData = response.data;
        if (lmiData) {
          await LmiInterests.create({
            careerID,
            soc: socCode,
            onetcode: onetCode,
            scales: lmiData.scales,
          });
          importedCount++;
        }
      } catch (error) {
        console.error(`Failed to fetch LMI data for onetCode: ${onetCode}`, error.message);
        failedOnetCode.push(socCode);
        continue;
      }
    }
    return messageResponse(null, `LMI import completed.`, true, 200, { importedCount, skippedCount, failedOnetCode }, res);

  } catch (error) {
    console.error("Unexpected server error:", error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.importInterestsLMI = importInterestsLMI;

// V2 controllers
const getNestedCareers = async (req, res) => {
  try {
    const pipeline = [
      {
        $match: { type: { $exists: true } },
      },
      {
        $facet: {
          unitGroups: [{ $match: { type: careerTypes.UNIT_GROUP } }],
          broadCareers: [{ $match: { type: careerTypes.BROAD_CAREER } }],
          specialisedRoles: [{ $match: { type: careerTypes.SPECIALISED_ROLE } }],
        },
      },
      {
        $project: {
          result: {
            $map: {
              input: "$unitGroups",
              as: "ug",
              in: {
                $mergeObjects: [
                  {
                    _id: "$$ug._id",
                    onetCode: "$$ug.onetCode",
                    socCode: "$$ug.socCode",
                    title: "$$ug.title",
                    description: "$$ug.description",
                    addedBy: "$$ug.addedBy",
                    interests: "$$ug.interests",
                    type: "$$ug.type",
                    videoUrl: "$$ug.videoUrl",
                  },
                  {
                    careers: {
                      $map: {
                        input: {
                          $filter: {
                            input: "$broadCareers",
                            as: "broad",
                            cond: {
                              $eq: ["$$broad.unitGroupId", "$$ug._id"],
                            },
                          },
                        },
                        as: "broad",
                        in: {
                          $mergeObjects: [
                            {
                              _id: "$$broad._id",
                              onetCode: "$$broad.onetCode",
                              socCode: "$$broad.socCode",
                              title: "$$broad.title",
                              description: "$$broad.description",
                              addedBy: "$$broad.addedBy",
                              interests: "$$broad.interests",
                              type: "$$broad.type",
                              videoUrl: "$$broad.videoUrl",
                            },
                            {
                              specialisedRoles: {
                                $map: {
                                  input: {
                                    $filter: {
                                      input: "$specialisedRoles",
                                      as: "sr",
                                      cond: {
                                        $and: [
                                          { $isArray: "$$sr.broadCareerIds" },
                                          { $in: ["$$broad._id", "$$sr.broadCareerIds"] }
                                        ]
                                      },
                                    },
                                  },
                                  as: "sr",
                                  in: {
                                    _id: "$$sr._id",
                                    onetCode: "$$sr.onetCode",
                                    socCode: "$$sr.socCode",
                                    title: "$$sr.title",
                                    description: "$$sr.description",
                                    addedBy: "$$sr.addedBy",
                                    interests: "$$sr.interests",
                                    type: "$$sr.type",
                                    videoUrl: "$$sr.videoUrl",
                                  },
                                },
                              },
                            },
                          ],
                        },
                      },
                    },
                  },
                ],
              },
            },
          },
        },
      },
      {
        $sort: {
          "addedBy.date": -1, // Sort by most recent broad_career (if addedBy has `date`)
        },
      },
    ];


    let careers = await Career.aggregate(pipeline);
    if (!careers[0].result.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res)

    return messageResponse(null, "", true, 200, careers[0].result, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.getNestedCareers = getNestedCareers;

const getCareersByType = async (req, res) => {
  try {
    const { type } = req.query
    if(!type || !Object.values(careerTypes).includes(type)) {
      return messageResponse(INVALID_MISSING, "Career Type", false, 400, null, res)
    }

    const careers = await Career.find({ type }).select("title")
    if (!careers.length) return messageResponse(NOT_FOUND, "Careers", false, 404, null, res)

    return messageResponse(null, "", true, 200, careers, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.getCareersByType = getCareersByType;

const getCareerMetaData = async () => {
  const [
    careerDetails,
    skillDetails,
    abilityDetails
  ] = await Promise.all([
    CareerDetail_LMI.find({}, { socCode: 1, description: 1, tasks: 1 }).lean(),
    LmiSkills.find({}, { _id: 0 }).lean(),
    LmiAbilities.find({}, { _id: 0 }).lean()
  ])
  const careerMetaData = {
    careerDetails,
    skillDetails,
    abilityDetails,
  }

  return careerMetaData
}

const getCareerData = async (careerData, type, title, socCode, onetCode) => {
  const fakeReq = {
    query: { 
      socCode: socCode.toString().trim(),
      onetCode: onetCode.toString().trim()
    },
    body: {...careerData, type}
  }
  const result = await getCareerDetailsV2(fakeReq)
  if(!result.success) {
    // console.log(`Something went wrong for career: ${title} - ${result.msg}`)
    return { success: false, msg: result.msg }
  };

  let tasks = result.data.tasks
  let description = result.data.description
  if(description) {
    // console.log(`Something went wrong for career: ${title} - ${result.msg}`)
    // return { success: false, msg: "Tasks/Description data was not found for the career" }
    description = await convertToBritishEnglish(description)
  }

  const response = { success: true, data: { ...result.data, title, description, tasks } }

  return response
}

const prepareCareerBulkOp = (type, data, unitGroupTitle = null) => {
  const updateFields = {
    tasks: data.tasks,
    description: data.description,
    jobZone: data.jobZone?.value,
    sectors: data.sectors || [],
    sectorIds: data.sectorIds || [],
    subsectorIds: data.subsectorIds || [],
    isFuture: data.isFuture,
    isEmerging: data.isEmerging,
    socCode: data.socCode,
    onetCode: data.onetCode,
    type,
  }

  switch (type) {
    case careerTypes.BROAD_CAREER:
      updateFields.unitGroupId = data.unitGroupId      
      break;

    case careerTypes.SPECIALISED_ROLE:
      updateFields.broadCareerIds = data.broadCareerIds      
      break;
  
    default:
      break;
  }

  const bulkOp = {
    updateOne: {
      filter: { title: data.title, type },
      update: {
        $set: updateFields,
      },
      upsert: true,
    },
  }

  return bulkOp
}

const prepareSectors = (careerSectors, careerSubsectors, sectorMap, subsectorMap) => {
  const sectors = [];
  if (careerSectors.split(",").length) {
    for (const sector of careerSectors.split(",")) {
      const sectorId = sectorMap.get(sector.trim().toLowerCase());
      if (!sectorId) continue;
      
      const subsectorIds = [];
      if (careerSubsectors.split(",").length) {
        for (const subsector of careerSubsectors.split(",")) {
          const key = `${subsector.trim().toLowerCase()}-${sectorId}`;
          const subsectorId = subsectorMap.get(key);
          if (subsectorId) subsectorIds.push(subsectorId.toString());
        }
      }

      sectors.push({
        sectorId: sectorId.toString(),
        subsectorIds: subsectorIds.length
          ? subsectorIds
          : Array.from(subsectorMap)
              .filter(([key]) => key.includes(sectorId.toString()))
              .map(([, value]) => value.toString())
      });
    }
  }

  return {
    sectors,
    sectorIds: sectors.map(s => s.sectorId),
    subsectorIds: sectors.flatMap(s => s.subsectorIds)
  }
}

const isExists = (title) => {
  const ugTitles = [];

  return ugTitles.includes(title.trim())
}

const importCareers = async (req, res) => {
  try {
    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);

    const expectedSheets = ["Careers"];
    const actualSheets = workbook.SheetNames;

    const missingSheets = expectedSheets.filter(
      (name) => !actualSheets.includes(name)
    );
    if (missingSheets.length > 0) {
      deleteFile(filePath);
      return messageResponse(
        INVALID_MISSING,
        `${missingSheets.join(", ")} Sheet`,
        false,
        400,
        null,
        res
      );
    }

    const parsedData = {};
    for (const sheetName of expectedSheets) {
      const sheet = workbook.Sheets[sheetName];
      const rows = xlsx.utils.sheet_to_json(sheet, { defval: null });
      parsedData[sheetName] = rows;
    }

    const [sectors, subsectors] = await Promise.all([
      Sector.find({}, {name: 1}),
      Subsector.find({}, {name: 1, sectorId: 1})
    ]);
    const careerMetaData = await getCareerMetaData()
    const unitOps = [], broadOps = [], roleOps = [];
    const unitMap = new Map(), broadMap = new Map(), roleMap = new Map();
    const sectorMap = new Map(sectors.map(sector => 
      [sector.name.trim().toLowerCase(), sector._id]
    ));
    const subsectorMap = new Map();
    subsectors.forEach(subsector => {
      const key = `${subsector.name.trim().toLowerCase()}-${subsector.sectorId}`;
      subsectorMap.set(key, subsector._id);
    });
    const response = {
      total: 0,
      unitOps: {
        total: 0,
        totalValidEntries: 0,
        totalInvalidEntries: 0,
        validEntries: [],
        invalidEntries: [],
      },
      broadOps: {
        total: 0,
        totalValidEntries: 0,
        totalInvalidEntries: 0,
        validEntries: [],
        invalidEntries: [],
      },
      roleOps: {
        total: 0,
        totalValidEntries: 0,
        totalInvalidEntries: 0,
        validEntries: [],
        invalidEntries: [],
      },
    }
    // return messageResponse(null, "", true, 200, parsedData, res);
    
    for (const [index, item] of parsedData["Careers"].entries()) {
      console.log(`Processing career ${index + 1} of ${parsedData["Careers"].length}`);
      let careerType = null,
        careerTitle = null,
        careerOnet = null,
        careerSoc = null
      
      
      const unitGroupTitle = item["UG_Title"]
      const broadCareerTitle = item["Broad_Title"]
      const specialisedRoleTitle = item["Specialised_Title"]

      if(unitGroupTitle && !unitMap.get(unitGroupTitle)) {
        careerType = careerTypes.UNIT_GROUP
        careerTitle = unitGroupTitle
        careerSoc = item["UG_SOC"] || ""
        careerOnet = ""

        const result = await getCareerData(careerMetaData, careerType, careerTitle, String(careerSoc), careerOnet)
        response.total++
        response.unitOps.total++
        if(!result.success) {
          response.unitOps.totalInvalidEntries++
          response.unitOps.invalidEntries.push({
            title: careerTitle,
            error: result.msg,
            entryNo: index + 1,
          })
        } else {
          response.unitOps.totalValidEntries++
          response.unitOps.validEntries.push({
            title: careerTitle,
          })
        }        

        const { sectors, sectorIds, subsectorIds } = prepareSectors(item["Sector"], item["Subsector"], sectorMap, subsectorMap)
        const data = { 
          ...result.data, 
          isFuture: item["Future_Role"] === "Yes", 
          isEmerging: item["Emerging_Role"] === "Yes", 
          sectors,
          sectorIds,
          subsectorIds,
          type: careerType 
        }
        unitMap.set(careerTitle, {isValid: result.success, data})

        unitOps.push(prepareCareerBulkOp(careerType, data))
      }
      if(broadCareerTitle && !broadMap.get(broadCareerTitle) && unitMap.get(unitGroupTitle)?.isValid) {
        careerType = careerTypes.BROAD_CAREER
        careerTitle = broadCareerTitle
        careerSoc = item["Broad_SOC"] || ""
        careerOnet = item["Broad_ONET"] || ""

        const result = await getCareerData(careerMetaData, careerType, careerTitle, String(careerSoc), careerOnet)
        response.total++
        response.broadOps.total++
        if(!result.success) {
          response.broadOps.totalInvalidEntries++
          response.broadOps.invalidEntries.push({
            title: careerTitle,
            error: result.msg,
            entryNo: index + 1,
          })
        } else {
          response.broadOps.totalValidEntries++
          response.broadOps.validEntries.push({
            title: careerTitle,
          })
        }

        const { sectors, sectorIds, subsectorIds } = prepareSectors(item["Sector"], item["Subsector"], sectorMap, subsectorMap)
        const data = { 
          ...result.data, 
          unitGroupTitle,
          isFuture: item["Future_Role"] === "Yes", 
          isEmerging: item["Emerging_Role"] === "Yes", 
          sectors,
          sectorIds,
          subsectorIds,
          type: careerType 
        }
        broadMap.set(careerTitle, {isValid: result.success, data})
      }
      if(specialisedRoleTitle && broadMap.get(broadCareerTitle)?.isValid) {
        careerType = careerTypes.SPECIALISED_ROLE
        careerTitle = specialisedRoleTitle
        careerSoc = item["Specialised_SOC"] || ""
        careerOnet = item["Specialised_ONET"] || ""

        const roleData = roleMap.get(careerTitle)?.data

        if(!roleData) {
          const result = await getCareerData(careerMetaData, careerType, careerTitle, String(careerSoc), careerOnet)
          response.total++
          response.roleOps.total++
          if(!result.success) {
            response.roleOps.totalInvalidEntries++
            response.roleOps.invalidEntries.push({
              title: careerTitle,
              error: result.msg,
              entryNo: index + 1,
            })
          } else {
            response.roleOps.totalValidEntries++
            response.roleOps.validEntries.push({
              title: careerTitle,
            })
          }
          
          const { sectors, sectorIds, subsectorIds } = prepareSectors(item["Sector"], item["Subsector"], sectorMap, subsectorMap)
          const data = { 
            ...result.data, 
            broadCareerTitles: [broadCareerTitle],
            isFuture: item["Future_Role"] === "Yes", 
            isEmerging: item["Emerging_Role"] === "Yes", 
            sectors,
            sectorIds,
            subsectorIds,
            type: careerType 
          }
          roleMap.set(careerTitle, {isValid: result.success, data})
        } else {
          const existingTitles = roleData.broadCareerTitles
          if(!existingTitles.includes(broadCareerTitle)) {
            existingTitles.push(broadCareerTitle)
          }
          const data = { ...roleData, broadCareerTitles: existingTitles }
          roleMap.set(careerTitle, {isValid: true, data})
        }
      }    
    }

    await Career.bulkWrite(unitOps);

    const existingUnitGroups = await Career.find({ type: careerTypes.UNIT_GROUP }, { title: 1, _id: 1 }).lean()
    for (const [broadCareerTitle, broadCareerData] of broadMap) {
      if (broadCareerData.isValid) {
        const unitGroup = existingUnitGroups.find(ug => ug.title === broadCareerData.data.unitGroupTitle);
        if (unitGroup) {
          const data = { ...broadCareerData.data, unitGroupId: unitGroup._id };
          broadOps.push(prepareCareerBulkOp(careerTypes.BROAD_CAREER, data));
        }
      }
    }
    await Career.bulkWrite(broadOps);

    const existingBroadCareers = await Career.find({ type: careerTypes.BROAD_CAREER }, { title: 1, _id: 1 }).lean()
    for (const [roleTitle, roleData] of roleMap) {
      if (roleData.isValid) {
        const broadCareerIds = roleData.data.broadCareerTitles
          .map(title => existingBroadCareers.find(bc => bc.title === title)?._id)
          .filter(id => id); // Remove any undefined values

        if (broadCareerIds.length > 0) {
          const data = { ...roleData.data, broadCareerIds };
          roleOps.push(prepareCareerBulkOp(careerTypes.SPECIALISED_ROLE, data));
        }
      }
    }
    await Career.bulkWrite(roleOps);

    deleteFile(filePath);

    return messageResponse(null, "", true, 200, response, res);
  } catch (error) {
    if (req.file && req.file.path) {
      deleteFile(req.file.path);
    }
    console.error(error);
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};

module.exports.importCareers = importCareers;

const exportCareers = async (req, res) => {
  try {
    const pipeline = [
      {
        $match: {
          type: "unit_group"
        }
      },
      // Lookup sector names for unit group
      {
        $lookup: {
          from: "sectors",
          localField: "sectorIds",
          foreignField: "_id",
          as: "sectorDetails"
        }
      },
      // Lookup subsector names for unit group
      {
        $lookup: {
          from: "subsectors",
          localField: "subsectorIds",
          foreignField: "_id",
          as: "subsectorDetails"
        }
      },
      {
        $lookup: {
          from: "careers",
          localField: "_id",
          foreignField: "unitGroupId",
          as: "broadCareers",
          pipeline: [
            // Lookup sector names for broadCareer
            {
              $lookup: {
                from: "sectors",
                localField: "sectorIds",
                foreignField: "_id",
                as: "sectorDetails"
              }
            },
            // Lookup subsector names for broadCareer
            {
              $lookup: {
                from: "subsectors",
                localField: "subsectorIds",
                foreignField: "_id",
                as: "subsectorDetails"
              }
            },
            // Lookup roles for this broadCareer
            {
              $lookup: {
                from: "careers",
                let: { broadCareerId: "$_id" },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $and: [
                          { $isArray: "$broadCareerIds" },
                          { $in: ["$$broadCareerId", "$broadCareerIds"] }
                        ]
                      }
                    }
                  },
                  // Lookup sector names for roles
                  {
                    $lookup: {
                      from: "sectors",
                      localField: "sectorIds",
                      foreignField: "_id",
                      as: "sectorDetails"
                    }
                  },
                  // Lookup subsector names for roles
                  {
                    $lookup: {
                      from: "subsectors",
                      localField: "subsectorIds",
                      foreignField: "_id",
                      as: "subsectorDetails"
                    }
                  }
                ],
                as: "roles"
              }
            }
          ]
        }
      },
      // Final projection
      {
        $project: {
          socCode: 1,
          onetCode: 1,
          title: 1,
          isFuture: 1,
          isEmerging: 1,
          sectorNames: {
            $map: {
              input: "$sectorDetails",
              as: "s",
              in: "$$s.name"
            }
          },
          subsectorNames: {
            $map: {
              input: "$subsectorDetails",
              as: "s",
              in: "$$s.name"
            }
          },
          broadCareers: {
            $map: {
              input: "$broadCareers",
              as: "broad",
              in: {
                socCode: "$$broad.socCode",
                onetCode: "$$broad.onetCode",
                title: "$$broad.title",
                isFuture: "$$broad.isFuture",
                isEmerging: "$$broad.isEmerging",
                sectorNames: {
                  $map: {
                    input: "$$broad.sectorDetails",
                    as: "s",
                    in: "$$s.name"
                  }
                },
                subsectorNames: {
                  $map: {
                    input: "$$broad.subsectorDetails",
                    as: "s",
                    in: "$$s.name"
                  }
                },
                roles: {
                  $map: {
                    input: "$$broad.roles",
                    as: "role",
                    in: {
                      socCode: "$$role.socCode",
                      onetCode: "$$role.onetCode",
                      title: "$$role.title",
                      isFuture: "$$role.isFuture",
                      isEmerging: "$$role.isEmerging",
                      sectorNames: {
                        $map: {
                          input: "$$role.sectorDetails",
                          as: "s",
                          in: "$$s.name"
                        }
                      },
                      subsectorNames: {
                        $map: {
                          input: "$$role.subsectorDetails",
                          as: "s",
                          in: "$$s.name"
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    ]

    const unitGroups = await Career.aggregate(pipeline);
    const careers = []

    unitGroups.map(unitGroup => {
      careers.push({
        UG_SOC: unitGroup.socCode,
        UG_Title: unitGroup.title,
        Broad_SOC: "",
        Broad_ONET: "",
        Broad_Title: "",
        Specialised_SOC: "",
        Specialised_ONET: "",
        Specialised_Title: "",
        Sector: unitGroup.sectorNames.join(", "),
        Subsector: unitGroup.subsectorNames.join(", "),
        Future_Role: unitGroup.isFuture ? "Yes" : "",
        Emerging_Role: unitGroup.isEmerging ? "Yes" : "",
      })

      unitGroup.broadCareers.map(broadCareer => {
        careers.push({
          UG_SOC: unitGroup.socCode,
          UG_Title: unitGroup.title,
          Broad_SOC: broadCareer.socCode,
          Broad_ONET: broadCareer.onetCode,
          Broad_Title: broadCareer.title,
          Specialised_SOC: "",
          Specialised_ONET: "",
          Specialised_Title: "",
          Sector: broadCareer.sectorNames.join(", "),
          Subsector: broadCareer.subsectorNames.join(", "),
          Future_Role: broadCareer.isFuture ? "Yes" : "",
          Emerging_Role: broadCareer.isEmerging ? "Yes" : "",
        })

        broadCareer.roles.map(role => {
          careers.push({
            UG_SOC: unitGroup.socCode,
            UG_Title: unitGroup.title,
            Broad_SOC: broadCareer.socCode,
            Broad_ONET: broadCareer.onetCode,
            Broad_Title: broadCareer.title,
            Specialised_SOC: role.socCode,
            Specialised_ONET: role.onetCode,
            Specialised_Title: role.title,
            Sector: role.sectorNames.join(", "),
            Subsector: role.subsectorNames.join(", "),
            Future_Role: role.isFuture ? "Yes" : "",
            Emerging_Role: role.isEmerging ? "Yes" : "",
          })
        })
      })
    })

    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(
      workbook,
      xlsx.utils.json_to_sheet(careers),
      "Careers"
    );

    const buffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    res.setHeader(
      "Content-Disposition",
      "attachment; filename=careers_export.xlsx"
    );
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.send(buffer);
  } catch (error) {
    console.error("Export error:", error.stack || error);
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};
module.exports.exportCareers = exportCareers;

const importCareersMetadata = async (req, res) => {
  try {
    const { regionId } = req.body
    if(!regionId || !mongoose.isValidObjectId(regionId)) {
      return messageResponse(REQUIRED, "Region ID", false, 400, null, res);
    }

    const filePath = req.file.path;
    const workbook = xlsx.readFile(filePath);

    const expectedSheets = ["Work Type", "Growth", "Qualification", "Salary", "Hours"];
    const actualSheets = workbook.SheetNames;

    const missingSheets = expectedSheets.filter(
      (name) => !actualSheets.includes(name)
    );
    if (missingSheets.length > 0) {
      deleteFile(filePath);
      return messageResponse(
        INVALID_MISSING,
        `${missingSheets.join(", ")} Sheet`,
        false,
        400,
        null,
        res
      );
    }

    messageResponse(CUSTOM, "Metadata shall be imported shortly", true, 200, null, res);

    const parsedData = {};
    for (const sheetName of expectedSheets) {
      const sheet = workbook.Sheets[sheetName];
      const rows = xlsx.utils.sheet_to_json(sheet, { defval: null });
      parsedData[sheetName] = rows;
    }

    const workTypeOps = []
    workTypeOps.push(...parsedData["Work Type"].map(({ soc_code, full_time, part_time, self_employed }) => ({
      updateOne: {
        filter: {
          regionID: toObjectId(regionId),
          socCode: String(soc_code)
        },
        update: {
          FTValue: full_time,
          PTValue: part_time,
          SEValue: self_employed,
        },
        upsert: true
      }
    })));
    console.log("worktypes", workTypeOps.length)

    const growthOps = []
    growthOps.push(...parsedData["Growth"].map((item) => ({
      updateOne: {
        filter: {
          regionID: toObjectId(regionId),
          socCode: String(item.soc_code)
        },
        update: {
          values: Object.fromEntries(
            Array.from({length: 11}, (_, i) => [2025 + i, item[`${2025 + i}`]])
          )
        },
        upsert: true
      }
    })));
    console.log("growth", growthOps.length)

    const qualificationOps = []
    qualificationOps.push(...parsedData["Qualification"].map((item) => ({
      updateOne: {
        filter: {
          regionID: toObjectId(regionId),
          socCode: String(item.soc_code)
        },
        update: {
          values: Object.fromEntries(
            Array.from({length: 9}, (_, i) => [i, item[`Level ${i}`]])
          )
        },
        upsert: true
      }
    })));
    console.log("qualification", qualificationOps.length)

    const hoursOps = []
    hoursOps.push(...parsedData["Hours"].map((item, index, hoursArray) => {
      let meanHours = item.mean_hours;
      let medianHours = item.median_hours;

      if (!meanHours || !medianHours) {
        const socCodePrefix = String(item.soc_code).substring(0, 3);
        const matchingEntry = hoursArray.find(entry =>
          String(entry.soc_code) === socCodePrefix
        );

        if (matchingEntry) {
          meanHours = meanHours || matchingEntry.mean_hours;
          medianHours = medianHours || matchingEntry.median_hours;
        }
      }

      return {
        updateOne: {
          filter: {
            regionID: toObjectId(regionId),
            socCode: String(item.soc_code)
          },
          update: {
            meanValue: meanHours || null,
            medianValue: medianHours || null,
          },
          upsert: true
        }
      };
    }));
    console.log("hours", hoursOps.length)

    const salaryOps = []
    salaryOps.push(...parsedData["Salary"].map((item, index, salaryArray) => {
      let meanSalary = item.mean_salary;
      let medianSalary = item.median_salary;

      if (!meanSalary || !medianSalary) {
        const socCodePrefix = String(item.soc_code).substring(0, 3);
        const matchingEntry = salaryArray.find(entry =>
          String(entry.soc_code) === socCodePrefix
        );

        if (matchingEntry) {
          meanSalary = meanSalary || matchingEntry.mean_salary;
          medianSalary = medianSalary || matchingEntry.median_salary;
        }
      }

      return {
        updateOne: {
          filter: {
            regionID: toObjectId(regionId),
            socCode: String(item.soc_code)
          },
          update: {
            meanValue: meanSalary || null,
            medianValue: medianSalary || null,
          },
          upsert: true
        }
      };
    }));
    console.log("salary", salaryOps.length)

    console.log("Importing data")
    await Promise.all([
      CareerWorktype.bulkWrite(workTypeOps),
      CareerGrowth.bulkWrite(growthOps),
      CareerQualification.bulkWrite(qualificationOps),
      CareerHours.bulkWrite(hoursOps),
      CareerSalary.bulkWrite(salaryOps),
    ]);
    console.log("Imported data")
  } catch (error) {
    console.error("Export error:", error.stack || error);
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.importCareersMetadata = importCareersMetadata

const exportCareersMetadata = async (req, res) => {
  try {
    const { regionId } = req.query

    let [careerWorkType, careerGrowth, careerQualification, careerSalary, careerHours] = await Promise.all([
      CareerWorktype.find(regionId ? { regionID: toObjectId(regionId) } : {}).lean(),
      CareerGrowth.find(regionId ? { regionID: toObjectId(regionId) } : {}).lean(),
      CareerQualification.find(regionId ? { regionID: toObjectId(regionId) } : {}).lean(),
      CareerSalary.find(regionId ? { regionID: toObjectId(regionId) } : {}).lean(),
      CareerHours.find(regionId ? { regionID: toObjectId(regionId) } : {}).lean(),
    ])

    careerWorkType = careerWorkType.map(item => ({
      soc_code: item.socCode,
      full_time: item.FTValue,
      part_time: item.PTValue,
      self_employed: item.SEValue,
    }))
    careerGrowth = careerGrowth.map(item => ({
      soc_code: item.socCode,
      ...item.values
    }))
    careerQualification = careerQualification.map(item => ({
      soc_code: item.socCode,
      ...item.values
    }))
    careerSalary = careerSalary.map(item => ({
      soc_code: item.socCode,
      median_salary: item.medianValue,
      mean_salary: item.meanValue,
    }))
    careerHours = careerHours.map(item => ({
      soc_code: item.socCode,
      median_hours: item.medianValue,
      mean_hours: item.meanValue,
    }))
    
    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(
      workbook,
      xlsx.utils.json_to_sheet(careerWorkType),
      "Work Type"
    );
    xlsx.utils.book_append_sheet(
      workbook,
      xlsx.utils.json_to_sheet(careerGrowth),
      "Growth"
    );
    xlsx.utils.book_append_sheet(
      workbook,
      xlsx.utils.json_to_sheet(careerQualification),
      "Qualification"
    );
    xlsx.utils.book_append_sheet(
      workbook,
      xlsx.utils.json_to_sheet(careerSalary),
      "Salary"
    );
    xlsx.utils.book_append_sheet(
      workbook,
      xlsx.utils.json_to_sheet(careerHours),
      "Hours"
    );

    const buffer = xlsx.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });

    res.setHeader(
      "Content-Disposition",
      "attachment; filename=careers_metadata_export.xlsx"
    );
    res.setHeader(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    res.send(buffer);
  } catch (error) {
    console.error("Export error:", error.stack || error);
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.exportCareersMetadata = exportCareersMetadata