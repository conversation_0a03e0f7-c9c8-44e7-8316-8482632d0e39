import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, Container, FormControl, FormHelperText, Grid, InputLabel, LinearProgress, MenuItem, Select, Stack, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import PhoneInput from '../../components/PhoneInput';
import SelectField from '../../components/SelectedField';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { CancelButton, formButton } from '../../utils/cssStyles';
import { campusValidationSchema } from '../../utils/validationSchemas';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getColleges } from '../Colleges/collegesSlice';
import { editCampus, postCampus } from './campusesSlice';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { getAvailableUsers } from '../Userspage/usersSlice';
import axiosInstance from '../../utils/axiosInstance';
import useAuth from '../../hooks/useAuth';

const EditCampus = () => {
    const [Groups, setGroups] = useState([])
    const [loading, setLoading] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [Colleges, setColleges] = useState([])
    const [AvailableUsers, setAvailableUsers] = useState([])
    const groups = useSelector(state => state.collegeGroups.groups)
    const colleges = useSelector(state => state.colleges.colleges)
    const { availableUsers } = useSelector(state => state.users)

    const navigate = useNavigate()
    const params = useParams()
    const { role } = useAuth()
    useEffect(() => {
        setGroups(groups)
    }, [groups])
    useEffect(() => {
        if (role === "1" || role === "2") {
            dispatch(getCollegeGroups())
        }
    }, [])
    useEffect(() => {
        if (role === "1" || role === "2" || role === "3") {
            dispatch(getColleges())
        }
    }, [])
    // useEffect(() => {
    //     if(availableUsers){
    //         setAvailableUsers(availableUsers)
    //     }
    // }, [availableUsers])

    const dispatch = useDispatch()
    const formik = useFormik({
        initialValues: {
            name: '',
            collegeName: '',
            groupName: '',
            campusAddress: '',
            campusWebsiteAddress: '',
            campusEmail: '',
            campusTelNumber: '',
            campusCity: '',
            campusState: '',
            campusZip: '',
            admin: '',
            id: ''
        },
        onSubmit: (values) => {
            setLoading(true)
            const campus = {
                name: values.name,
                collegeId: values.collegeName,
                groupId: values.groupName,
                // adminUserId: values.admin,
                contactNumber: values.campusTelNumber,
                email: values.campusEmail,
                website: values.campusWebsiteAddress,
                city: values.campusCity,
                state: values.campusState,
                zip: values.campusZip,
                address1: values.campusAddress,
                id: values.id

                // collegeName: Colleges.find(
                //     college => college?._id === values.collegeName
                // )?.name,
                // groupName: Groups.find(
                //     group => group?.id === values.groupName
                // )?.Name,
            }
            if(values?.admin){
                campus.adminUserId = values.admin
            }
            dispatch(editCampus(campus))
                .then(response => {
                    if (response?.payload?.success) {
                        dispatch(setSnackbar({
                            snackbarOpen: true,
                            snackbarType: 'success',
                            snackbarMessage: "Succesfully updated Campus"
                        }))
                        navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses`)
                    } else {
                        // error occoured
                        const errorMessage = response?.payload?.response?.data?.msg
                        dispatch(setSnackbar({
                            snackbarOpen: true,
                            snackbarType: 'error',
                            snackbarMessage: errorMessage || "Something went wrong!"
                        }))
                    }
                }).finally(() => {
                    setLoading(false)
                })
            handleClose()
        },
        validationSchema: campusValidationSchema
    })
    useEffect(() => {
        const getCampus = async (id) => {
            setIsLoading(true)
            try {
                const response = await axiosInstance({
                    url: "campuses/getByID",
                    method: "GET",
                    params: {
                        id
                    }
                })
                const campusDetails = response.data?.data;
                // setCollegeDetails(campusDetails)
                formik.setValues({
                    ...formik.values,
                    name: campusDetails?.name,
                    groupName: campusDetails?.collegeGroup?._id,
                    collegeName: campusDetails?.college?._id,
                    campusAddress: campusDetails?.address1,
                    campusWebsiteAddress: campusDetails?.website,
                    campusEmail: campusDetails?.email,
                    campusTelNumber: campusDetails?.contactNumber,
                    campusState: campusDetails?.state,
                    campusCity: campusDetails?.city,
                    campusZip: campusDetails?.zip,
                    id: campusDetails?._id,
                    admin: campusDetails?.adminUser?._id
                })
                if (role === "4" || role === '3') {
                    setGroups([
                        campusDetails ? {
                            id: campusDetails?.collegeGroup?._id,
                            Name: campusDetails?.collegeGroup?.name
                        } : null
                    ])
                }
                if (role === "4") {
                    setColleges([
                        campusDetails ? {
                            _id: campusDetails?.college?._id,
                            name: campusDetails?.college?.name
                        } : null
                    ])
                    // setAvailableUsers([...AvailableUsers, {
                    setAvailableUsers([{
                        _id: campusDetails?.adminUser?._id,
                        firstName: campusDetails?.adminUser?.firstName,
                        lastName: campusDetails?.adminUser?.lastName,
                    }])
                }

                const params = {
                    role: '4',
                    includeIds: campusDetails?.adminUserId
                }
                dispatch(getAvailableUsers(params))
            } catch (error) {
                const errorMessage = error?.response?.data?.msg
                console.log("error get colleges", errorMessage)
            } finally {
                setIsLoading(false)
            }
        }
        getCampus(params?.id)
    }, [])
    // useEffect(() => {
    //  if(availableUsers){
    //     formik.setValues({
    //         ...formik.values,
    //         // admin:
    //     })
    //  }
    // }, [availableUsers])

    const groupName = formik?.values?.groupName
    useEffect(() => {
        const filteredColleges = colleges.filter(college => college.groupId === formik.values.groupName)
        if (role === '1' || role === '2' || role === '3') {
            setColleges(filteredColleges)
        }
    }, [colleges, groupName])
    const handleClose = () => {
        // setOpenModel(false)
    }

    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            campusTelNumber: newValue
        })
    }
    const style = {
        p: 4,
    };
    return (
        <div>
            <Container maxWidth="lg">
                <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                    Edit Campus
                </Typography>
                <>{isLoading ? <LinearProgress /> :
                    <Card>
                        <Box sx={style} >
                            {/* <form onSubmit={formik.handleSubmit}> */}
                            <form onSubmit={formik.handleSubmit}>
                                <Grid container gap={2} >
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <FormControl sx={{ minWidth: '100%' }}>
                                            <InputLabel id="group-label">Group</InputLabel>
                                            <Select
                                                name='groupName'
                                                labelId="group-label"
                                                value={formik.values.groupName}
                                                label="Group *"
                                                onChange={formik.handleChange}
                                                // disabled={!(role === '1' || role === '2')}
                                                disabled
                                            >
                                                <MenuItem value="">
                                                    <em>None</em>
                                                </MenuItem>
                                                {Groups.map(group => (<MenuItem
                                                    key={group.id}
                                                    value={group.id}
                                                >
                                                    {group.Name}
                                                </MenuItem>))}
                                            </Select>
                                            <FormHelperText>{""}</FormHelperText>
                                        </FormControl>
                                    </Grid>

                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <FormControl sx={{ minWidth: '100%' }}>
                                            <InputLabel id="college-label">College</InputLabel>
                                            <Select
                                                name='collegeName'
                                                labelId="college-label"
                                                value={formik.values.collegeName}
                                                label="College *"
                                                onChange={formik.handleChange}
                                                // disabled={!(role === '1' || role === '2' || role === '3')}
                                                disabled
                                            >
                                                <MenuItem value="">
                                                    <em>None</em>
                                                </MenuItem>
                                                {Colleges.map(college => (<MenuItem
                                                    key={college._id}
                                                    value={college._id}
                                                >
                                                    {college.name}
                                                </MenuItem>))}
                                            </Select>
                                            <FormHelperText>{""}</FormHelperText>
                                        </FormControl>
                                    </Grid>

                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='name'
                                            label="Name"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.name}
                                            onChange={formik.handleChange}
                                            error={formik.touched.name && Boolean(formik.errors.name)}
                                            helperText={formik.touched.name && formik.errors.name}
                                        />
                                    </Grid>


                                    <Grid item xs={12} md={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusAddress'
                                            label="Address"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusAddress}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusAddress && Boolean(formik.errors.campusAddress)}
                                            helperText={formik.touched.campusAddress && formik.errors.campusAddress}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={11.8} >
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusWebsiteAddress'
                                            label="Website Address"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusWebsiteAddress}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusWebsiteAddress && Boolean(formik.errors.campusWebsiteAddress)}
                                            helperText={formik.touched.campusWebsiteAddress && formik.errors.campusWebsiteAddress}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusEmail'
                                            label="Email"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusEmail}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusEmail && Boolean(formik.errors.campusEmail)}
                                            helperText={formik.touched.campusEmail && formik.errors.campusEmail}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        {/* <PhoneInput
                                            sx={{ width: "100%" }}
                                            value={formik.values.campusTelNumber}
                                            name='campusTelNumber'
                                            label="Number"
                                            defaultCountry="GB"
                                            onChange={handlePhoneChange}
                                            onBlur={formik.handleBlur}
                                            error={formik.touched.campusTelNumber && Boolean(formik.errors.campusTelNumber)}
                                            helperText={formik.touched.campusTelNumber && formik.errors.campusTelNumber}
                                        /> */}
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusTelNumber'
                                            label="Number"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusTelNumber}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusTelNumber && Boolean(formik.errors.campusTelNumber)}
                                            helperText={formik.touched.campusTelNumber && formik.errors.campusTelNumber}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusCity'
                                            label="City"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusCity}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusCity && Boolean(formik.errors.campusCity)}
                                            helperText={formik.touched.campusCity && formik.errors.campusCity}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusState'
                                            label="Country"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusState}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusState && Boolean(formik.errors.campusState)}
                                            helperText={formik.touched.campusState && formik.errors.campusState}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={5.8} lg={3.8}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='campusZip'
                                            label="Post Code"
                                            // type="number"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.campusZip}
                                            onChange={formik.handleChange}
                                            error={formik.touched.campusZip && Boolean(formik.errors.campusZip)}
                                            helperText={formik.touched.campusZip && formik.errors.campusZip}
                                        />
                                    </Grid>
                                    <Grid item xs={11.8} md={5.8} lg={3.8} >
                                        <SelectField
                                            sx={{ width: '100%' }}
                                            name='admin'
                                            label="Admin"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.admin}
                                            onChange={formik.handleChange}
                                            error={formik.touched.admin && Boolean(formik.errors.admin)}
                                            helperText={formik.touched.admin && formik.errors.admin}
                                            disabled={!(role === '1' || role === '2' || role === '3')}
                                        >
                                            <MenuItem value="">
                                                <em>None</em>
                                            </MenuItem>
                                            {(role === "4" ? AvailableUsers : availableUsers)?.map(user => {
                                                return (<MenuItem
                                                    key={user?._id}
                                                    value={user?._id}
                                                >
                                                    {`${user?.firstName} ${user?.lastName}`}
                                                </MenuItem>)
                                            })}
                                        </SelectField>
                                    </Grid>
                                </Grid>
                                <Stack direction="row" justifyContent="flex-end">
                                    <Button
                                        type='button'
                                        variant='contained'
                                        color='error'
                                        onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses`)}
                                        sx={CancelButton}>
                                        Cancel
                                    </Button>
                                    <LoadingButton
                                        loading={loading}
                                        type='submit'
                                        variant='contained'
                                        sx={formButton}>
                                        Update
                                    </LoadingButton>
                                </Stack>
                            </form>
                        </Box>
                    </Card>}
                </>
            </Container>
        </div>
    )
}

export default EditCampus