import { Box, Collapse, Stack, SvgIcon, Typography, keyframes } from '@mui/material'
import { PropTypes } from 'prop-types'
import React, { useEffect, useState } from 'react'
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import KeyboardArrowDownOutlinedIcon from '@mui/icons-material/KeyboardArrowDownOutlined';
import StarIcon from '@mui/icons-material/Star';
import CustomCareerBar from './CustomCareerBar';
import { ReactComponent as flagIcon } from '../../assets/images/flag.svg'


const CustomBar = ({ isWidget,data, maxLength, addToCompare, compareCareersState, showCareerDetailsPopup, index }) => {
    const minLength = 3
    const [checked, setChecked] = useState(false)
    const [careers, setCareers] = useState([])
    const [styleData, setStyleData] = useState({
        barColor: '#fff',
        barWidth: 0
    })
    const minWidth = 550
    const arbitarWidth = 330 / maxLength
    const { barColor, barWidth } = styleData
    useEffect(() => {
        if (data) {
            setCareers(data?.careers)
            setStyleData(prevData => ({
                ...prevData,
                barColor: data?.color,
                // barWidth:  data?.careers?.length ? (minWidth + data.careers.length * arbitarWidth) : 100 - len/max
                // eslint-disable-next-line no-unsafe-optional-chaining
                barWidth: data?.careers?.length ? `${(100 - Math.min((maxLength / data?.careers?.length === 1 ? maxLength / data?.careers?.length : maxLength / data?.careers?.length * 2), 40))}%` : 10
                // barWidth:  '60%'

            }))
        }
    }, [data, arbitarWidth, maxLength])

    const graphWidth = keyframes`from {
    width: 30%
    }
`;

    return (
        <>
            {data?.careers?.length > 0 &&
                <Box
                    className='bar'
                    sx={(theme) => ({
                        transition: theme.transitions.create(["background", "background-color"], {
                            duration: theme.transitions.duration.shorter,
                        }),
                        backgroundColor: barColor,
                        width: { xs: '100%', sm: barWidth },
                        animation: `${graphWidth} ${Math.min(0.6 + index / 20, 1)}s ease`
                    })}
                    onClick={() => setChecked(!checked)}
                >
                    <Box display="flex" justifyContent='space-between' pl={2} alignItems='center'>
                        <Stack direction='row' alignItems='center' gap={1}>
                           { data?.priority && <StarIcon />}
                            <Typography sx={{ overflow: 'hidden', textOverflow: 'ellipsis', textAlign: 'start' }} variant='capitalize'>
                                {data?.name}
                                {/* {data?.sector_name} */}
                            </Typography>
                        </Stack>
                        {data?.careers.some(career => career?.careerType === 'CAREER_GOAL') &&
                            <Box sx={{ mx: 1 }}>
                                <SvgIcon
                                    component={flagIcon}
                                    inheritViewBox
                                />
                            </Box>}
                        <Box flex={1} />
                        <Typography noWrap variant='capitalize'>
                            {`${data?.careers?.length} Careers`}
                        </Typography>
                        {checked ?
                            <KeyboardArrowDownOutlinedIcon sx={{ mr: 1 }} fontSize='large' /> :
                            <ChevronRightIcon sx={{ mr: 1 }} fontSize='large' />}
                    </Box>
                </Box>}
            <Collapse
                in={checked}
            >
                <Stack
                    gap={0.5}
                >
                    {data?.careers?.map(career =>
                        <CustomCareerBar
                            isWidget={isWidget}
                            career={career}
                            barColor={barColor}
                            barWidth={barWidth}
                            addToCompare={addToCompare}
                            compareCareersState={compareCareersState}
                            showCareerDetailsPopup={showCareerDetailsPopup}
                        />
                    )}
                </Stack>
            </Collapse>
        </>
    )
}

export default CustomBar

CustomBar.propTypes = {
    data: PropTypes.object,
    maxLength: PropTypes.number,
    addToCompare: PropTypes.func,
    compareCareersState: PropTypes.array,
    showCareerDetailsPopup: PropTypes.func,
    index: PropTypes.number,
    isWidget: PropTypes.bool,
}