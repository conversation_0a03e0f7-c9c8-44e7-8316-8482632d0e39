const { default: mongoose } = require("mongoose");
const { User, UserRoles } = require("../models/user");
const { CollegeGroup } = require("../models/collegeGroup");
const { College } = require("../models/college");
const { Campus } = require("../models/campus");
const { getAddedBy, getEditedBy } = require('../tools/database')
const forAsync = require('../tools/forAsync')
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const keys = require("../config/keys");
const commonHelper = require("../helpers/commonHelper");
const {messageResponse, generateCode, getPermissions} = require("../helpers/commonHelper");
const emailTemplate = require("../helpers/emailTemplate");
const commonClass = require("../helpers/commonClass");
const mailchimpClient = require("@mailchimp/mailchimp_transactional")(
  keys.SMTP_PWD
);
const { REQUIRED, NO_ACCESS, ACCESS_DENIED, IN_USE, SERVER_ERROR, NOT_FOUND, EXIST_PERMISSION, ENGAGED, REMOVE_SUCCESS, ADD_ERROR, UPDATE_ERROR, UPDATE_SUCCESS, INVALID, INVALID_MISSING , MATCHING_WITH_OLDER, SEND_EMAIL_SUCCESS, ADD_SUCCESS} = require("../config/messages");

const validateUser = async(req, res, action) => {
  try {
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null)
      }

      //if (req.user.role != UserRoles.SUPER_ADMIN) {
      if (req.user.role != UserRoles.SUPER_ADMIN && req.body.id != req.user._id) {
        const allowedUsers = await commonClass.getAllowedUsers(req);
        const Users = allowedUsers.find(user => user._id == req.body.id);
        if (!Users) {
          return messageResponse(EXIST_PERMISSION, "User", false, 404, null, res)
        }
      }
    }

    let validationOn = ["firstName", "lastName", "email", "password"];
    if (action == 'edit') {
      validationOn = ["firstName", "lastName", "email"];
      delete req.body['password'];
    }
    const errors = commonHelper._validate(validationOn, req.body);
    if (!commonHelper.isEmpty(errors)) {
      return messageResponse(null, "", false, 400, errors)
    }

    // if(!req.body.firstName) {
    //     return { success: false, msg: "Invalid/missing First name." };
    // }

    // if(!req.body.lastName) {
    //     return { success: false, msg: "Invalid/missing Last name." };
    // }

    // if(!req.body.email) {
    //     return { success: false, msg: "Invalid/missing Email." };
    // }

    // if(!req.body.password) {
    //     return { success: false, msg: "Invalid/missing Password." };
    // }

    // if(!req.body.role || req.body.role < 1 || req.body.role > 5) {
    //     return { success: false, statusCode: 400, msg: "Invalid/missing Role." };
    // }

    // Only a super user can create another super user
    const callByUser = req.user.role;
    if (callByUser != UserRoles.SUPER_ADMIN) {
      if (req.body.role) {
        return messageResponse(ACCESS_DENIED, "", false, 400, null)
      }
    }

    // if(req.body.role == UserRoles.SUPER_ADMIN) {
    //     if(req.user.role != UserRoles.SUPER_ADMIN) {
    //         return { success: false, statusCode: 400, msg: "Access denied.", 
    //             detail:"not authorised to create user with selected role" };
    //     }
    // }
    // else if(req.body.role) {
    //     return { success: false, statusCode: 400, msg: "Access denied." };
    // }

    // if(req.body.role == UserRoles.COLLEGE_GROUP_ADMIN) {
    //     if(!mongoose.isValidObjectId(req.body.collegeGroupId)) {
    //         return { success: false, statusCode:400, msg: "Invalid/missing College group Id." };
    //     }
    //     else {
    //         const selectedCG = await CollegeGroup.findById(new mongoose.Types.ObjectId(req.body.collegeGroupId));
    //         if(!selectedCG) {
    //             return { success: false, statusCode:400, msg: "Invalid/missing College Group." };
    //         }
    //     }
    // }

    // if(req.body.role == UserRoles.COLLEGE_ADMIN) {
    //     if(!mongoose.isValidObjectId(req.body.collegeId)) {
    //         return { success: false, statusCode:400, msg: "Invalid/missing College Id." };
    //     }
    //     else {
    //         const selectedCollege = await College.findById(new mongoose.Types.ObjectId(req.body.collegeId));
    //         if(!selectedCollege) {
    //             return { success: false, statusCode:400, msg: "Invalid/missing College." };
    //         }
    //     }
    // }

    // if(req.body.role == UserRoles.CAMPUS_ADMIN) {
    //     if(!mongoose.isValidObjectId(req.body.campusId)) {
    //         return { success: false, statusCode:400, msg: "Invalid/missing Campus Id." };
    //     }
    //     else {
    //         const selectedCampus = await Campus.findById(new mongoose.Types.ObjectId(req.body.campusId));
    //         if(!selectedCampus) {
    //             return { success: false, statusCode:400, msg: "Invalid/missing Campus." };
    //         }
    //     }
    // }

    let query;
    if (action == 'edit') {
      query = { $and: [{ email: { $eq: req.body.email } }, { _id: { $ne: req.body.id } }] };
    }
    else {
      query = { email: req.body.email };
    }

    const existingUser = await User.findOne(query);
    if (existingUser != null) {
      return messageResponse(IN_USE, "Email", false, 400, null)
    }

    return messageResponse(null, "", true, 200, null);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(null, "", false, 500, error)
  }
}

const register = async (req, res) => {
  try {
    const { email, password } = req.body;

    let validationOn = ["firstName", "lastName", "email", "password", "postcode"];
    const errors = commonHelper._validate(validationOn, req.body);
    if (!commonHelper.isEmpty(errors)) {
      return messageResponse(null, "", false, 400, errors, res)
    }

    const existingUser = await User.findOne({ email });
    if (existingUser != null) {
      return messageResponse(IN_USE, "Email", false, 400, null, res)
    }

    const hashedPassword = await commonHelper.generateHash(password);
    const newUser = await User.create({...req.body, password: hashedPassword})

    if(!newUser) return messageResponse(ADD_ERROR, "User", false, 400, null, res)

    return messageResponse(ADD_SUCCESS, "User", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }  
}
module.exports.register = register;

const add = async(req, res, next) => {
  addOrEdit(req, res, 'add');
}
module.exports.add = add;

const get = async(req, res) => {
  try {
    // const listUsers = await getAllowedUsers(req);

    const listUsers = await commonClass.getAllowedUsers(req);
    if(!listUsers) return messageResponse(NOT_FOUND, "", false, 404, null, res)

    return messageResponse(null, "", true, 200, listUsers, res);
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.get = get;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    if (req.user.role != UserRoles.SUPER_ADMIN && req.query.id != req.user._id) {
      const allowedUsers = await commonClass.getAllowedUsers(req);
      const user = allowedUsers.find(user => user._id == req.query.id);

      if(!user) return messageResponse(EXIST_PERMISSION, "User", false, 404, null, res)

      return messageResponse(null, "", true, 200, user, res)
    }

    // const existingUser = await User.findById(req.query.id, { password: 0 })
    const existingUser = await User.findById(req.query.id)

    if(!existingUser) return messageResponse(NOT_FOUND, "User", false, 404, null, res)

    return messageResponse(null, "", true, 200, existingUser, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID = getByID;

const remove = async(req, res) => {
  try {
    if (!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const user = await User.findById(req.body.id, { password: 0 });
    if (!user) {
      return messageResponse(NOT_FOUND, "User", false, 404, null, res)
    }
 
    if(user.email == "<EMAIL>") {
      return messageResponse(EXIST_PERMISSION, "User", false, 404, null, res)
    }

    if (user.role && (user.role == UserRoles.COLLEGE_GROUP_ADMIN ||user.role == UserRoles.COLLEGE_ADMIN || user.role == UserRoles.CAMPUS_ADMIN )) {
      return messageResponse(ENGAGED, "User", false, 400, null, res)
    }

    if (req.user.role != UserRoles.SUPER_ADMIN) {
      const allowedUsers = await commonClass.getAllowedUsers(req);
      const deleteUser = allowedUsers.find(user => user._id == req.body.id);
      if (!deleteUser) {
        return messageResponse(EXIST_PERMISSION, "User", false, 404, null, res)
      }
    }

    const deletedUser = await User.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

    if(!deletedUser) return messageResponse(EXIST_PERMISSION, "User", false, 404, null, res)

    return messageResponse(REMOVE_SUCCESS, "User", true, 200, null, res)

  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove = remove;

const update = async(req, res, next) => {
  addOrEdit(req, res, 'edit');
};
module.exports.update = update;

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validateUser(req, res, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
      // const statusCode = (validateResult.statusCode ? statusCode : 400);
      // res.status(statusCode).json(validateResult);
      // res.status(validateResult);
    }

    if (action == 'add') {
      req.body.password = await commonHelper.generateHash(req.body.password);
      req.body.adminUserId = req.user;
      // req.body.adminUserId = req.body.adminUserId; // temporary
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newUser = await User.create(req.body)

      if(!newUser) return messageResponse(ADD_ERROR, "User", false, 400, null, res)

      return res.status(200).json({ success: true, id: newUser._id })
    }
    else {
      delete req.body["adminUserId"];
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedUser = await User.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body)

      if(!updatedUser) return messageResponse(EXIST_PERMISSION, "User", false, 404, null, res)

      return messageResponse(UPDATE_SUCCESS, "User", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}

const authenticate = async(req, res, next) => {
  try {
    const email = req.body.email.toLowerCase();
    const password = req.body.password;

    if (!email || !password) {
      return messageResponse(REQUIRED, "Email/Password", false, 400, null, res)
    }

    const user = await User.findOne({ email: email });

    if (!user) {
      return messageResponse(INVALID, "Email/Password", false, 400, null, res)
    }

    const isMatch = await comparePassword(password, user.password);

    if (!isMatch) {
      return messageResponse(INVALID, "Email/Password", false, 400, null, res)
    }
    else {
      if (user.status.toLowerCase() === "archived") {
        messageResponse(NO_ACCESS, "", false, 400, null, res)
      }

      const loguser = {
        _id: user._id, firstName: user.firstName, lastName: user.lastName, email: email, role: user.role,
        collegeGroupIds: user.collegeGroupIds, collegeIds: user.collegeIds, campusIds: user.campusIds
      }

      await getPermissions(loguser, res)

      const token = jwt.sign(
        { id: user._id, role: user.role, user: loguser },
        keys.JWT_SECRET_KEY,
        { expiresIn: 604800 }
      ); // 1 week
      req.token = token;[]

      req.user = user;
      // next();
      res.status(200).json({ success: true, token: req.token, user: loguser });
    }
  }
  catch (err) {
    commonHelper.doReqActionOnError(err, req, res);
    return messageResponse(SERVER_ERROR, "", false, 500, err, res)
  }
}
module.exports.authenticate = authenticate;

const comparePassword = async(candidatePassword, hash) => {
  const isMatch = await bcrypt.compare(candidatePassword, hash);
  return isMatch;
}

const updateUserRole = async(userId, role, modelId, modelOldObject) => {
  let arrayName = '';
  if (role == 2) {
    arrayName = 'collegeGroupIds'
  }
  else if (role == 3) {
    arrayName = 'collegeIds'
  }
  else if (role == 4) {
    arrayName = 'campusIds'
  }

  let updateQuery = { $set: { role: role } };
  if (modelOldObject && modelOldObject.adminUserId && modelOldObject.adminUserId != userId) {

    revokeUserRole(role, modelId, modelOldObject);

  }

  const adminUser = await User.findById(userId);
  if(!adminUser) {
    return;
  }
  let findIndex = -1;
  if (adminUser[arrayName] && adminUser[arrayName].length > 0) {
    findIndex = adminUser[arrayName].findIndex(cg => cg == modelId);
  }
  if (findIndex == -1) {
    adminUser[arrayName].push(modelId);
  }
  updateQuery["$set"][arrayName] = adminUser[arrayName];

  // console.log('updateQuery', updateQuery);
  const updatedUser = await User.findOneAndUpdate({ _id: userId }, updateQuery)

  if(!updatedUser) return messageResponse(UPDATE_ERROR, "User Role", false, 400, null, res)

}
module.exports.updateUserRole = updateUserRole;

const revokeUserRole = async(role, modelId, modelOldObject) => {
  if(!modelOldObject.adminUserId) {
    return;
  }

  let adminOfCaption = ''
  if (role == 4) {
    adminOfCaption = 'campusIds'
  }
  else if (role == 3) {
    adminOfCaption = 'collegeIds'
  }
  else if (role == 2) {
    adminOfCaption = 'collegeGroupIds'
  }
  const oldAdminUser = await User.findById(modelOldObject.adminUserId);
  if (oldAdminUser[adminOfCaption] && oldAdminUser[adminOfCaption].length > 0) {
    const findIndex = oldAdminUser[adminOfCaption].findIndex(cg => cg == modelId);
    if (findIndex != -1) {
      oldAdminUser[adminOfCaption].splice(findIndex, 1);
    }
  }
  let newRole = 5;
  if (oldAdminUser[adminOfCaption] && oldAdminUser[adminOfCaption].length > 0) {
    newRole = role;
  }

  let query = { $set: { role: newRole } };
  if (adminOfCaption && adminOfCaption.length > 0) {
    query['$set'][adminOfCaption] = oldAdminUser[adminOfCaption];
  }

  const updatedUser = await User.findOneAndUpdate({ _id: modelOldObject.adminUserId }, query)

  if(!updatedUser) return messageResponse(UPDATE_ERROR, "User Role", false, 400, null, res)
}
module.exports.revokeUserRole = revokeUserRole;

const getAvailableUsers = async(req, res) => {
  try {
    const role = req.query.role;
    let includeIds = req.query.includeIds;

    if (includeIds) {
      if (!Array.isArray(includeIds)) {
        includeIds = [includeIds];
      }
    }
    else {
      includeIds=[]; 
    }

    if (!role || role < 1 || role > 4) {
      return messageResponse(INVALID_MISSING, "Role", false, 400, null, res)
    }

    let users = await commonClass.getAllowedUsers(req);

    if (role == UserRoles.COLLEGE_GROUP_ADMIN) {
      users = users.filter(user => checkExists(user, includeIds) ||
        (user.role != UserRoles.SUPER_ADMIN && user.role == UserRoles.NOT_AVAILABLE));
    }
    else if (role == UserRoles.COLLEGE_ADMIN) {
      users = users.filter(user => checkExists(user, includeIds) ||
        // This line is for if you want to allow a user to be admin of multiple colleges
        // (user.role != UserRoles.SUPER_ADMIN && (user.role == UserRoles.NOT_AVAILABLE || user.role == UserRoles.COLLEGE_ADMIN)));
        // This line is for if you want to allow a user to be admin of a single college
        (user.role != UserRoles.SUPER_ADMIN && (user.role == UserRoles.NOT_AVAILABLE)));
    }
    else if (role == UserRoles.CAMPUS_ADMIN) {
      users = users.filter(user => checkExists(user, includeIds) ||
        // This line is for if you want to allow a user to be admin of multiple campuses
        // (user.role != UserRoles.SUPER_ADMIN && (user.role == UserRoles.NOT_AVAILABLE || user.role == UserRoles.CAMPUS_ADMIN)));
        // This line is for if you want to allow a user to be admin of a single college
        (user.role != UserRoles.SUPER_ADMIN && (user.role == UserRoles.NOT_AVAILABLE)));
    }

    return messageResponse(null, "", true, 200, users, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.getAvailableUsers = getAvailableUsers;

const dashboard = async(req, res) => {
  try {
    let dashboardData = {}
    const users = await commonClass.getAllowedUsers(req)
    const collegeGroups = await commonClass.getAllowedCollegeGroups(req)
    const colleges = await commonClass.getAllowedColleges(req)
    const campuses = await commonClass.getAllowedCampuses(req)

    dashboardData.users = users.length 
    dashboardData.collegeGroups = collegeGroups.length
    dashboardData.colleges = colleges.length
    dashboardData.campuses = campuses.length

    return messageResponse(null, "", true, 200, dashboardData, res)

  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.dashboard = dashboard;

const checkExists = (user, includeIds) => {
  let returnValue = false;
  if (includeIds && includeIds.length > 0) {
    const findIndex = includeIds.findIndex(includeId => includeId.toString() == user._id.toString());
    returnValue = (findIndex == -1 ? false : true);
  }
  return returnValue;
}

const resetPassword = async(req,res) => {
  try {      
    let {userId, oldPassword , newPassword } = req.body;

    if(!userId || !mongoose.isValidObjectId(userId)) {
      return messageResponse(INVALID_MISSING, "User ID", false, 400, null, res)
    } 
    if(!oldPassword) {
      return messageResponse(INVALID_MISSING, "Old Password", false, 400, null, res)
    } 
    if(!newPassword) {
      return messageResponse(INVALID_MISSING, "New Password", false, 400, null, res)
    } 

    if(oldPassword === newPassword) {
      return messageResponse(MATCHING_WITH_OLDER, "", false, 400, null, res)
    } 

    const existingUser = await User.findById(userId).select({"password":1})
    if(!existingUser) return messageResponse(NOT_FOUND, "User", false, 404, null, res)

    const isMatch = await comparePassword(oldPassword, existingUser.password);

    if (!isMatch) {
      return messageResponse(INVALID, "Old Password", false, 400, null, res)
    }
    newPassword = await commonHelper.generateHash(newPassword);
    const changePasswordInDb = await User.findByIdAndUpdate(userId, {password : newPassword},{new : true})

    if(!changePasswordInDb)
    return messageResponse(SERVER_ERROR, "", false, 500, null, res)

    return messageResponse(UPDATE_SUCCESS, "Password", true, 200, newPassword, res)
  } catch(error) {
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.resetPassword= resetPassword;

const forgotPassword = async (req,res) =>{
  try {
    let {email} = req.body;

    const isUser = await User.findOne({email})
    if(!isUser) {
      return messageResponse(INVALID, "Email", false, 400, null, res)
    } 

    const result = await sendMail(isUser);
    if(result.emailResponse.status === "error") {
      return messageResponse(SERVER_ERROR, "", false, 500, "Something went wrong", res);
    }
    else {
      const changePasswordInDb = await User.findByIdAndUpdate(isUser._id, {password: result.hashedPassword}, {new: true})
      if(!changePasswordInDb){
        return messageResponse(SERVER_ERROR, "", false, 500, null, res)
      }
      return messageResponse(SEND_EMAIL_SUCCESS, "", true, 200, null, res);
    }
  }
  catch (error){
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.forgotPassword=forgotPassword;

async function sendMail(user){
  let newPassword = generateCode(8)
  let hashedPassword = await commonHelper.generateHash(newPassword);
  
  const message = {
    subject: emailTemplate.forgotPassword.emailSubject, 
    html: emailTemplate.forgotPassword.htmlTemplate({user, newPassword}),
    from_email: keys.NOREPLY_EMAIL,
    to: [ { email: user.email } ],
  }
  const emailResponse = await mailchimpClient.messages.send({ message: message });  
  return {emailResponse, hashedPassword}
}