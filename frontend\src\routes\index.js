import { useRoutes } from 'react-router-dom';

import LandingPage from 'src/pages/LandingPage';
import { base_route_url } from 'src/utils';
// layouts
import SignIn from 'src/components/SignIn';
import SignInRegion from 'src/components/SignInRegion';
import SignUp from 'src/components/SignUp';
import CareerHistoryRegion from 'src/pages/CareerHistory/CareerHistoryRegion';
import CollegesPage from 'src/pages/CollegesPage';
import CollegesPageReskill from 'src/pages/CollegesPageReskill';
import CollegesPageReskillRegion from 'src/pages/CollegesPageReskillRegion';
import HomePage from 'src/pages/Home/Home';
import RegionalInfo from 'src/pages/RegionalInfo';
import RegionalInfoReskill from 'src/pages/RegionalInfoReskill';
import CareerCoursesReskillRegion from 'src/pages/Reskill_Flow/Steps/CareerCoursesReskillRegion';
import SkilldarReskillRegion from 'src/pages/Reskill_Flow/Steps/SkilldarReskillRegion';
import Widget from 'src/pages/widgets';
import SkilldarUpskillRegion from 'src/pages/Upskill_Flow/Steps/SkilldarUpskillRegion';
import CareerCoursesUpskillRegion from 'src/pages/Upskill_Flow/Steps/CareerCoursesUpskillRegion';
import SignUpRegion from 'src/components/SignUpRegion';
import UpskillCollegesPageRegion from 'src/pages/UpskillCollegesPageRegion';
import WidgetWrapper from 'src/pages/widgets/WidgetWrapper';
import MainLayout from '../layouts/main';
//
import {
  CareerHistory,
  CollegeNotFound,
  Disclaimer,
  HaveACode,
  Page404,
  PrivacyPolicy,
  ReportPage,
  ReskillCareerCourses,
  ReskillSkilldar,
  TearmsAndConditions,
  UpskillCareerCourses,
  UpskillSkilldar
} from './elements';

// ----------------------------------------------------------------------

export default function Router({ getCollegeColors }) {
  return useRoutes([
    // Main layout
    {
      element: <MainLayout getCollegeColors={getCollegeColors} />,
      children: [
        // {element: <HomePage/>, path: `${base_route_url}:cg_name`},
        { element: <HomePage />, path: `${base_route_url}/college/:cg_name` },
        { element: <LandingPage />, path: `${base_route_url}/region/:rg_name` },
        { element: <HaveACode />, path: `${base_route_url}code` },
        { element: <CareerHistory />, path: `${base_route_url}:cg_name/career-history` },

        { element: <CareerHistoryRegion />, path: `${base_route_url}region/:rg_name/career-history` },
        { element: <UpskillSkilldar />, path: `${base_route_url}:cg_name/upskill/skilldar` },
        { element: <SkilldarUpskillRegion />, path: `${base_route_url}region/:rg_name/upskill/skilldar` },
        { element: <UpskillCareerCourses />, path: `${base_route_url}:cg_name/upskill/career-courses` },
        { element: <CareerCoursesUpskillRegion />, path: `${base_route_url}region/:rg_name/upskill/career-courses` },
        { element: <ReskillSkilldar />, path: `${base_route_url}:cg_name/reskill/skilldar` },
        { element: <SkilldarReskillRegion />, path: `${base_route_url}region/:rg_name/reskill/skilldar` },
        { element: <ReskillCareerCourses />, path: `${base_route_url}:cg_name/reskill/career-courses` },
        { element: <CareerCoursesReskillRegion />, path: `${base_route_url}region/:rg_name/reskill/career-courses` },
        { element: <ReportPage />, path: `${base_route_url}report` },
        { element: <PrivacyPolicy />, path: `${base_route_url}:cg_name/privacy-policy` },
        { element: <TearmsAndConditions />, path: `${base_route_url}:cg_name/terms-conditions` },
        { element: <Disclaimer />, path: `${base_route_url}:cg_name/disclaimer` },
        { element: <SignUp />, path: `${base_route_url}:cg_name/sign-up` },
        { element: <SignIn />, path: `${base_route_url}:cg_name/sign-in` },
        { element: <SignUpRegion />, path: `${base_route_url}/region/:rg_name/sign-up` },
        { element: <SignInRegion />, path: `${base_route_url}/region/:rg_name/sign-in` },
        { element: <LandingPage />, path: `${base_route_url}:cg_name/landing-page` },
        { element: <CollegesPageReskill />, path: `${base_route_url}:cg_name/reskill/colleges` },
        { element: <CollegesPageReskillRegion />, path: `${base_route_url}region/:rg_name/reskill/colleges` },
        { element: <CollegesPage />, path: `${base_route_url}:cg_name/upskill/colleges` },
        { element: <UpskillCollegesPageRegion />, path: `${base_route_url}region/:rg_name/upskill/colleges` },
        { element: <RegionalInfo />, path: `${base_route_url}:cg_name/upskill/regional-info` },
        { element: <RegionalInfoReskill />, path: `${base_route_url}:cg_name/reskill/regional-info` },
        { element: <RegionalInfo />, path: `${base_route_url}region/:rg_name/upskill/regional-info` },
        { element: <RegionalInfoReskill />, path: `${base_route_url}region/:rg_name/reskill/regional-info` },
        { element: <CollegeNotFound />, path: '/college-not-found' },
        { element: <Page404 />, path: '*' },
      ],
    },
    { element: <WidgetWrapper />, path: `${base_route_url}:cg_name/widget` },
  ]);
}
