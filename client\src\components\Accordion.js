import React, { useState } from 'react'
import { AccordionDetails, Accordion,AccordionSummary, Typography } from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

const AccordionComponent = ({secondaryTitle, title, children, name}) => {
    const [expanded, setExpanded] = useState(false);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  return (
        <Accordion expanded={expanded === name} onChange={handleChange(name)}>
            <AccordionSummary
                expandIcon={<ExpandMoreIcon fontSize='large' sx={{color: (theme)=> theme.palette.primary.dark}} />}
                aria-controls={`${name}-content`}
                id={`${name}-header`}
            >
                <Typography sx={{ width: secondaryTitle ? '33%' : '70%', flexShrink: 0 }} variant='h6' color='primary.dark'>{title}</Typography>
                {secondaryTitle &&
                    <Typography sx={{ color: 'text.secondary' }}>{secondaryTitle}</Typography>
                }
            </AccordionSummary>
            <AccordionDetails>
                {children}
            </AccordionDetails>
        </Accordion>
  )
}

export default AccordionComponent