import { LoadingButton } from '@mui/lab';
import { Box, Button, Card, Container, FormLabel, Grid, LinearProgress, Stack, TextField, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router-dom';
import { get } from 'lodash';
import PhoneInput from '../../components/PhoneInput';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { CancelButton, label } from '../../utils/cssStyles';
import { editUserValidationSchema } from '../../utils/validationSchemas';
// import { postCollege } from '../collegesSlice';
import { setSnackbar } from '../../Redux/snackbarSlice';
import useAuth from '../../hooks/useAuth';
import { APP_ROUTER_BASE_URL } from '../../utils';
import axiosInstance from '../../utils/axiosInstance';
import noImagePlaceholder from '../../assets/images/no-image-icon-0.jpg';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { editUser } from './usersSlice';

// groups,

const EditUser = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const dispatch = useDispatch()
    const params = useParams()
    const { role } = useAuth()

    const navigate = useNavigate()
    const formik = useFormik({
        initialValues: {
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            password: '',
            id: '',
            profile: ''
            // confirmPassword: '',
        },
        validationSchema: editUserValidationSchema,
        onSubmit: (values) => {
            setIsLoading(true)
            const user = {
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email,
                id: values.id,
                contactNumber: values.phone,
                photo: values.profile
                // password: values.password,
            }
            dispatch(editUser(user)).then(res => {
                if (res?.payload?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: "Succesfully updated User Info"
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/user`)
                } else {
                    const errorMessage = get(res, 'payload.data.msg', 'something went wrong!')
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }))
                }
            }).finally(() => setIsLoading(false))
        },
    })

    useEffect(() => {
        const getUser = async (id) => {
            setLoading(true)
            try {
                const response = await axiosInstance({
                    url: "users/getByID",
                    method: "GET",
                    params: {
                        id
                    }
                })
                const userDetails = response.data?.data;
                formik.setValues({
                    ...formik.values,
                    firstName: userDetails.firstName,
                    lastName: userDetails.lastName,
                    email: userDetails.email,
                    phone: userDetails.contactNumber,
                    password: userDetails.password,
                    id: userDetails._id,
                    profile: userDetails?.photo
                })
            } catch (error) {
                const errorMessage = error?.response?.data?.msg
                console.log("error get colleges", errorMessage)
            } finally {
                setLoading(false)
            }
        }
        getUser(params?.id)
    }, [])

    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            phone: newValue
        })
    }
    const removePic = () => {
        formik.setValues({
            ...formik.values,
            profile: ''
        })
        document.getElementById("profilePic").value = ''
    }
    const style = {
        p: 4,
    };

    return (
        <Container maxWidth="xl">
            <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                Edit User
            </Typography>
            <>{loading ? <LinearProgress /> :
                <Grid container wrap='wrap' width={'100%'} gap={2}>
                    <Grid item xs={6} lg={2.8} mx={'auto'} >
                        <Card sx={{ minHeight: 330 }} >
                            <Box sx={{
                                p: 4,
                                display: 'flex',
                                flexDirection: 'column',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}>
                                <label htmlFor='profilePic' >
                                    {/* <img className='user-profile' alt='logo' src={Image ? URL.createObjectURL(Image) : noImagePlaceholder} /> */}
                                    <img className='user-profile' alt='logo' src={formik.values.profile ? formik.values.profile : noImagePlaceholder} />
                                </label>
                                <TextField
                                    sx={{ width: '100%', display: 'none' }}
                                    id='profilePic'
                                    name='profile'
                                    onChange={(e) => {
                                        const fileReader = new FileReader();
                                        fileReader.readAsDataURL(e.target.files[0]);
                                        fileReader.onload = (event) => {
                                            formik.setValues({
                                                ...formik.values,
                                                profile: event.target.result
                                            })
                                        }
                                    }}
                                    type='file'
                                />
                            </Box>
                            <Stack
                                direction={'row'}
                                justifyContent={'center'}
                                gap={1}
                                alignItems={'center'}
                            >
                                <Button
                                    variant='contained'
                                    sx={{ minWidth: 80, p: 0 }}
                                >
                                    <FormLabel sx={label} htmlFor='profilePic'>
                                        {formik.values.profile ? "Change" : "Add"}
                                    </FormLabel>
                                    {/* <label htmlFor='profilePic' >
                                        {formik.values.profile ? "Change" : "Add"}
                                    </label> */}
                                </Button>
                                {formik.values.profile &&
                                    <Button
                                        sx={{ minWidth: 80 }}
                                        variant='contained'
                                        color='error'
                                        onClick={removePic}
                                    >
                                        Remove
                                    </Button>}
                            </Stack>
                        </Card>
                    </Grid>
                    <Grid xs={12} lg={8.8}>
                        <Card>
                            <Box sx={style}>
                                <form
                                    // onSubmit={formik.handleSubmit}
                                    onSubmit={formik.handleSubmit}
                                >
                                    <Grid container gap={2} >
                                        <Grid item xs={12} md={5.8}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='firstName'
                                                label="First Name"
                                                onBlur={formik.handleBlur}
                                                value={formik.values.firstName}
                                                onChange={formik.handleChange}
                                                error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                                                helperText={formik.touched.firstName && formik.errors.firstName}
                                            />
                                        </Grid>
                                        <Grid item xs={12} md={5.8}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                value={formik.values.lastName}
                                                onBlur={formik.handleBlur}
                                                name='lastName'
                                                label="Last Name"
                                                onChange={formik.handleChange}
                                                error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                                                helperText={formik.touched.lastName && formik.errors.lastName}
                                            />
                                        </Grid>
                                        <Grid item xs={12} md={5.8}>
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                value={formik.values.email}
                                                name='email'
                                                type={'email'}
                                                label="Email"
                                                onChange={formik.handleChange}
                                                error={formik.touched.email && Boolean(formik.errors.email)}
                                                helperText={formik.touched.email && formik.errors.email}
                                                onBlur={formik.handleBlur}
                                            />
                                        </Grid>
                                        <Grid item xs={12} md={5.8}>
                                            {/* <PhoneInput
                                                sx={{ width: "100%" }}
                                                value={formik.values.phone}
                                                name='phone'
                                                label="Phone"
                                                defaultCountry="GB"
                                                onChange={handlePhoneChange}
                                                onBlur={formik.handleBlur}
                                                error={formik.touched.phone && Boolean(formik.errors.phone)}
                                                helperText={formik.touched.phone && formik.errors.phone}
                                            /> */}
                                            <TextFIeldComponent
                                                sx={{ width: '100%' }}
                                                name='phone'
                                                label="Phone"
                                                onBlur={formik.handleBlur}
                                                value={formik.values.phone}
                                                onChange={formik.handleChange}
                                                error={formik.touched.phone && Boolean(formik.errors.phone)}
                                                helperText={formik.touched.phone && formik.errors.phone}
                                            />
                                        </Grid>
                                        {/* <Grid item xs={12} md={5.8}>
                                <TextFIeldComponent
                                    sx={{ width: '100%' }}
                                    value={formik.values.password}
                                    name='password'
                                    type={'password'}
                                    label="Password"
                                    onChange={formik.handleChange}
                                    error={formik.touched.password && Boolean(formik.errors.password)}
                                    helperText={formik.touched.password && formik.errors.password}
                                    onBlur={formik.handleBlur}
                                />
                            </Grid> */}
                                    </Grid>
                                    <Stack direction="row" justifyContent="flex-end" >
                                        <Button
                                            type='button'
                                            variant='contained'
                                            sx={CancelButton}
                                            color='error'
                                            onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/user`)}
                                        >
                                            Cancel
                                        </Button>
                                        <LoadingButton
                                            loading={isLoading}
                                            type='submit'
                                            variant='contained'
                                            sx={{ width: '10%', m: 1, mt: 2 }}
                                        >
                                            Update
                                        </LoadingButton>
                                    </Stack>
                                </form>
                            </Box>
                        </Card>
                    </Grid>
                </Grid>}</>
        </Container>
    )
}

export default EditUser