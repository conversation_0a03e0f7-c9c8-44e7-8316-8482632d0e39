const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import services
const FineTuningDataService = require('../services/fineTuningDataService');
const OpenAIFineTuningService = require('../services/openAIFineTuningService');

/**
 * Fine-Tuning Management Tool
 * Command-line interface for managing fine-tuning operations
 */
class FineTuningManager {
  constructor() {
    this.dataService = new FineTuningDataService();
    this.fineTuningService = new OpenAIFineTuningService();
    this.outputDir = path.join(__dirname, '../fine-tuning-data');
  }

  /**
   * Initialize the fine-tuning manager
   */
  async initialize() {
    try {
      // Connect to MongoDB
      await mongoose.connect(process.env.MONGODB_URI || process.env.DB_CONNECTION_STRING);
      console.log('✅ Connected to MongoDB');

      // Create output directory
      if (!fs.existsSync(this.outputDir)) {
        fs.mkdirSync(this.outputDir, { recursive: true });
        console.log('✅ Created output directory');
      }

      return true;
    } catch (error) {
      console.error('❌ Initialization failed:', error);
      throw error;
    }
  }

  /**
   * Generate training data (legacy method)
   */
  async generateData(options = {}) {
    console.log('🚀 Starting training data generation...');
    console.log('=====================================');

    try {
      const result = await this.dataService.generateTrainingData(options);

      // Save training data
      const trainingFile = path.join(this.outputDir, 'training_data.jsonl');
      const trainingJsonl = this.dataService.exportToJSONL(result.trainingData);
      fs.writeFileSync(trainingFile, trainingJsonl, 'utf8');
      console.log(`💾 Training data saved: ${trainingFile}`);

      // Save validation data if exists
      if (result.validationData.length > 0) {
        const validationFile = path.join(this.outputDir, 'validation_data.jsonl');
        const validationJsonl = this.dataService.exportToJSONL(result.validationData);
        fs.writeFileSync(validationFile, validationJsonl, 'utf8');
        console.log(`💾 Validation data saved: ${validationFile}`);
      }

      // Save statistics
      const statsFile = path.join(this.outputDir, 'data_statistics.json');
      fs.writeFileSync(statsFile, JSON.stringify(result.statistics, null, 2), 'utf8');
      console.log(`📊 Statistics saved: ${statsFile}`);

      console.log('\n🎉 Data generation completed successfully!');
      console.log('📊 Final Statistics:');
      console.log(JSON.stringify(result.statistics, null, 2));

      return result;
    } catch (error) {
      console.error('❌ Data generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate enhanced training data using Phase 4 pipeline
   */
  async generateEnhancedData(options = {}) {
    console.log('🚀 Starting enhanced training data generation...');
    console.log('===============================================');
    console.log('📊 Using Phase 4 data pipeline with validation...');

    try {
      const result = await this.dataService.generateEnhancedTrainingData(options);

      // Save training data
      const trainingFile = path.join(this.outputDir, 'enhanced_training_data.jsonl');
      const trainingJsonl = this.dataService.exportToJSONL(result.trainingData);
      fs.writeFileSync(trainingFile, trainingJsonl, 'utf8');
      console.log(`💾 Enhanced training data saved: ${trainingFile}`);

      // Save validation data if exists
      if (result.validationData.length > 0) {
        const validationFile = path.join(this.outputDir, 'enhanced_validation_data.jsonl');
        const validationJsonl = this.dataService.exportToJSONL(result.validationData);
        fs.writeFileSync(validationFile, validationJsonl, 'utf8');
        console.log(`💾 Enhanced validation data saved: ${validationFile}`);
      }

      // Save comprehensive statistics
      const statsFile = path.join(this.outputDir, 'enhanced_statistics.json');
      const comprehensiveStats = {
        generation: result.statistics,
        extraction: result.extractionMetadata,
        validation: result.validationResults?.report || null,
        timestamp: new Date()
      };
      fs.writeFileSync(statsFile, JSON.stringify(comprehensiveStats, null, 2), 'utf8');
      console.log(`📊 Comprehensive statistics saved: ${statsFile}`);

      // Save validation report if available
      if (result.validationResults) {
        const validationFile = path.join(this.outputDir, 'validation_report.json');
        fs.writeFileSync(validationFile, JSON.stringify(result.validationResults, null, 2), 'utf8');
        console.log(`📋 Validation report saved: ${validationFile}`);
      }

      console.log('\n🎉 Enhanced data generation completed successfully!');
      console.log('📊 Final Statistics:');
      console.log(`   Training examples: ${result.trainingData.length}`);
      console.log(`   Validation examples: ${result.validationData.length}`);
      console.log(`   Total records processed: ${result.extractionMetadata?.statistics?.totalRecords || 'N/A'}`);
      console.log(`   Data quality score: ${result.validationResults?.report?.qualityScore?.toFixed(2) || 'N/A'}`);
      console.log(`   Validation errors: ${result.validationResults?.errors?.length || 0}`);
      console.log(`   Validation warnings: ${result.validationResults?.warnings?.length || 0}`);

      return result;
    } catch (error) {
      console.error('❌ Enhanced data generation failed:', error);
      throw error;
    }
  }

  /**
   * Create a fine-tuning job
   */
  async createFineTuningJob(options = {}) {
    console.log('🚀 Creating fine-tuning job...');
    console.log('==============================');

    try {
      // Load training data
      const trainingFile = path.join(this.outputDir, 'training_data.jsonl');
      if (!fs.existsSync(trainingFile)) {
        throw new Error('Training data not found. Run data generation first.');
      }

      const trainingData = this.loadJsonlFile(trainingFile);
      console.log(`📊 Loaded ${trainingData.length} training examples`);

      // Load validation data if exists
      let validationData = null;
      const validationFile = path.join(this.outputDir, 'validation_data.jsonl');
      if (fs.existsSync(validationFile)) {
        validationData = this.loadJsonlFile(validationFile);
        console.log(`📊 Loaded ${validationData.length} validation examples`);
      }

      // Create fine-tuning job
      const job = await this.fineTuningService.createFineTuningJob(
        trainingData,
        validationData,
        options
      );

      // Save job details
      const jobFile = path.join(this.outputDir, `job_${job.jobId}.json`);
      fs.writeFileSync(jobFile, JSON.stringify(job, null, 2), 'utf8');
      console.log(`💾 Job details saved: ${jobFile}`);

      console.log('\n🎉 Fine-tuning job created successfully!');
      console.log(`🆔 Job ID: ${job.jobId}`);
      console.log(`📊 Status: ${job.status}`);

      return job;
    } catch (error) {
      console.error('❌ Fine-tuning job creation failed:', error);
      throw error;
    }
  }

  /**
   * Monitor fine-tuning job progress
   */
  async monitorJob(jobId) {
    console.log(`🔍 Monitoring fine-tuning job: ${jobId}`);
    console.log('==========================================');

    try {
      let isCompleted = false;
      let attempts = 0;
      const maxAttempts = 100; // Prevent infinite loop

      while (!isCompleted && attempts < maxAttempts) {
        const status = await this.fineTuningService.getJobStatus(jobId);
        
        console.log(`📊 Status: ${status.status}`);
        console.log(`⏰ Last checked: ${new Date().toISOString()}`);
        
        if (status.trainedTokens) {
          console.log(`🎯 Trained tokens: ${status.trainedTokens}`);
        }

        if (status.error) {
          console.error('❌ Job failed with error:', status.error);
          return status;
        }

        if (status.status === 'succeeded') {
          console.log('🎉 Fine-tuning completed successfully!');
          console.log(`🤖 Fine-tuned model: ${status.fineTunedModel}`);
          isCompleted = true;
          
          // Save final status
          const statusFile = path.join(this.outputDir, `job_${jobId}_final.json`);
          fs.writeFileSync(statusFile, JSON.stringify(status, null, 2), 'utf8');
          
          return status;
        }

        if (status.status === 'failed' || status.status === 'cancelled') {
          console.log(`❌ Job ${status.status}`);
          return status;
        }

        // Wait before next check
        console.log('⏳ Waiting 30 seconds before next check...\n');
        await new Promise(resolve => setTimeout(resolve, 30000));
        attempts++;
      }

      if (attempts >= maxAttempts) {
        console.log('⚠️ Maximum monitoring attempts reached');
      }

    } catch (error) {
      console.error('❌ Error monitoring job:', error);
      throw error;
    }
  }

  /**
   * Test a fine-tuned model
   */
  async testModel(modelId) {
    console.log(`🧪 Testing fine-tuned model: ${modelId}`);
    console.log('=====================================');

    try {
      const testResult = await this.fineTuningService.testModel(modelId);
      
      console.log(`✅ Test ${testResult.success ? 'passed' : 'failed'}`);
      
      if (testResult.success) {
        console.log('🤖 Model response:', testResult.response);
        console.log('📊 Token usage:', testResult.usage);
      } else {
        console.log('❌ Error:', testResult.error);
      }

      return testResult;
    } catch (error) {
      console.error('❌ Model testing failed:', error);
      throw error;
    }
  }

  /**
   * Deploy a fine-tuned model
   */
  async deployModel(modelId, alias = 'default') {
    console.log(`🚀 Deploying model: ${modelId}`);
    console.log('==========================');

    try {
      const success = await this.fineTuningService.deployModel(modelId, alias);
      
      if (success) {
        console.log(`✅ Model deployed successfully with alias '${alias}'`);
        
        // Save deployment info
        const deploymentFile = path.join(this.outputDir, 'deployed_models.json');
        const deployedModels = this.fineTuningService.listDeployedModels();
        fs.writeFileSync(deploymentFile, JSON.stringify(deployedModels, null, 2), 'utf8');
      }

      return success;
    } catch (error) {
      console.error('❌ Model deployment failed:', error);
      throw error;
    }
  }

  /**
   * List all fine-tuning jobs
   */
  async listJobs() {
    console.log('📋 Fine-tuning Jobs');
    console.log('===================');

    try {
      const jobs = await this.fineTuningService.listJobs();
      
      if (jobs.length === 0) {
        console.log('No fine-tuning jobs found');
        return jobs;
      }

      jobs.forEach((job, index) => {
        console.log(`\n${index + 1}. Job ID: ${job.id}`);
        console.log(`   Status: ${job.status}`);
        console.log(`   Model: ${job.model}`);
        console.log(`   Fine-tuned Model: ${job.fineTunedModel || 'N/A'}`);
        console.log(`   Created: ${job.createdAt.toISOString()}`);
        console.log(`   Finished: ${job.finishedAt ? job.finishedAt.toISOString() : 'N/A'}`);
      });

      return jobs;
    } catch (error) {
      console.error('❌ Error listing jobs:', error);
      throw error;
    }
  }

  /**
   * Load JSONL file
   */
  loadJsonlFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    return content.trim().split('\n').map(line => JSON.parse(line));
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      await mongoose.disconnect();
      console.log('✅ Disconnected from MongoDB');
    } catch (error) {
      console.error('❌ Cleanup error:', error);
    }
  }
}

// Command line interface
const runCommand = async () => {
  const args = process.argv.slice(2);
  const command = args[0];
  const manager = new FineTuningManager();

  try {
    await manager.initialize();

    switch (command) {
      case 'generate-data':
        await manager.generateData({
          maxExamplesPerType: parseInt(args[1]) || 100,
          validationSplit: parseFloat(args[2]) || 0.1
        });
        break;

      case 'generate-enhanced-data':
        await manager.generateEnhancedData({
          maxExamplesPerType: parseInt(args[1]) || 100,
          validationSplit: parseFloat(args[2]) || 0.1,
          validateData: args[3] !== 'false',
          examplesPerRecord: parseInt(args[4]) || 3
        });
        break;

      case 'create-job':
        await manager.createFineTuningJob({
          suffix: args[1] || 'horizon-chatbot',
          hyperparameters: {
            epochs: parseInt(args[2]) || 3
          }
        });
        break;

      case 'monitor':
        if (!args[1]) {
          console.error('❌ Job ID required');
          process.exit(1);
        }
        await manager.monitorJob(args[1]);
        break;

      case 'test':
        if (!args[1]) {
          console.error('❌ Model ID required');
          process.exit(1);
        }
        await manager.testModel(args[1]);
        break;

      case 'deploy':
        if (!args[1]) {
          console.error('❌ Model ID required');
          process.exit(1);
        }
        await manager.deployModel(args[1], args[2] || 'default');
        break;

      case 'list-jobs':
        await manager.listJobs();
        break;

      default:
        console.log('📋 Available commands:');
        console.log('  generate-data [maxExamples] [validationSplit]');
        console.log('  generate-enhanced-data [maxExamples] [validationSplit] [validate] [examplesPerRecord]');
        console.log('  create-job [suffix] [epochs]');
        console.log('  monitor <jobId>');
        console.log('  test <modelId>');
        console.log('  deploy <modelId> [alias]');
        console.log('  list-jobs');
        console.log('');
        console.log('📋 Enhanced commands (Phase 4):');
        console.log('  generate-enhanced-data - Uses advanced data pipeline with validation');
        console.log('  Example: generate-enhanced-data 50 0.1 true 3');
        break;
    }
  } catch (error) {
    console.error('❌ Command failed:', error);
    process.exit(1);
  } finally {
    await manager.cleanup();
  }
};

// Run if called directly
if (require.main === module) {
  runCommand();
}

module.exports = FineTuningManager;
