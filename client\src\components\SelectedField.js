import { FormControl, FormHelperText, InputLabel, Select } from '@mui/material'
import React from 'react'

const SelectField = (props) => {
    const { error, name, label, helperText, children,margin, size,...rest } = props
    return (

        <FormControl sx={{ minWidth: '100%' }}>
            <InputLabel size={size} error={error} id={name}>{label}</InputLabel>
            <Select
                label={label}
                name={name}
                error={error}
                size={size}
                {...rest}
            >
                {children}
            </Select>
            <FormHelperText
                error={error}
            >
                {helperText}
            </FormHelperText>
        </FormControl>
    )
}

export default SelectField
