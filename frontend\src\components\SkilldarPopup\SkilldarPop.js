import { Box, Button, Popover, Typography } from '@mui/material';
import React, { useEffect, useRef, useState } from 'react'
import PropTypes from 'prop-types';
import CloseIcon from '@mui/icons-material/Close';
import RadarChartComponent from '../RadarChart/RadarChartComponent';


const SkilldarPop = ({skillRadarPopup,HandleCloseRadarPopup,popupDetails}) => {
    const refChart = useRef(null);
    const [chartWidth, setChartWidth] = useState('100%');

    // useEffect(() => {
    //     const handleWindowResize = () => {
    //         setChartWidth(refChart?.current?.offsetWidth);
    //     };
    //     window.addEventListener('resize', handleWindowResize);
    
    //     return () => {
    //       window.removeEventListener('resize', handleWindowResize);
    //     };
    // }, []);

    // useEffect(() => {
    //     if(refChart.current){
    //         setChartWidth(refChart.current.offsetWidth); 
    //     }
    // }, [refChart.current]);  // eslint-disable-line react-hooks/exhaustive-deps

    return (
       <Popover
       open={skillRadarPopup}
       onClose={HandleCloseRadarPopup}
           anchorOrigin={{
           vertical: 'top',
           horizontal: 'center',
       }}
       sx={{display:'flex',alignItems:'center',justifyContent:'center'}}
       className='chart-popup'
       >
           <Button className='close-btn' onClick={HandleCloseRadarPopup}><CloseIcon/></Button>
   
           <Box className="col" ref={refChart}>
               <Typography variant='h4' className='heading' color='primary.dark'>Skilldar</Typography>
               <Box className='flex' sx={{justifyContent: 'center'}}>
                   <RadarChartComponent
                       width={chartWidth}
                       legend
                       radarApiDetails={popupDetails}
                       height={650}
                       careersDetails ='true'
                       showlabels
                       disableTooltip
                       chartAnimation
                       legend
                   />
               </Box>
           </Box>
       </Popover>
     )
}

export default SkilldarPop

SkilldarPop.propTypes = {
    skillRadarPopup: PropTypes.bool,
    HandleCloseRadarPopup: PropTypes.func,
    popupDetails: PropTypes.object,

};
