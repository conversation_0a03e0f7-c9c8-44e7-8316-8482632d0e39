import { Helmet } from 'react-helmet-async';
import { useEffect, useRef, useState } from 'react';
// @mui
import { Grid, Container, Backdrop, CircularProgress, Box, FormControl, Card, Typography } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { createAsyncThunk } from '@reduxjs/toolkit';
import { get } from 'lodash';
import { format, addDays } from 'date-fns';
import AccordionComponent from 'src/components/Accordion';
import DataTable from 'src/components/DataTable/DataTable';
import SelectComponent from '../components/SelectComponent';
import DateRangeField from '../components/DateRangeField/DateRangeField';
import axiosInstance from '../utils/axiosInstance';
// sections
import {
  AppWidgetSummary,
} from '../sections/@dashboard/app';

// ----------------------------------------------------------------------

export default function DashboardAppPage() {
  const dispatch = useDispatch();
  const refOne = useRef(null);
  const [dashboardDetails, setDashboardDetails] = useState({})
  const [loading, setLoading] = useState(false)
  const [dateValue, setDateValue] = useState();
  const [dateRangeValue, setDateRangeValue] = useState([
    {
      // startDate: new Date(),
      // endDate: addDays(new Date(), 7),
      startDate: addDays(new Date(), -7),
      endDate: new Date(),
      key: 'selection'
    }
  ]);
  const [simpleDateRangeValue, setSimpleDateRangeValue] = useState();
  const [open, setOpen] = useState(false);
  const [AnalyticsData, setAnalyticsData] = useState({
    CoursesData: [],
    CareersData: [],
    PageLeaveData: []
  });
  const { selectedCollege } = useSelector(state => state.selectedCollege)
  const renderCourseCell = ['title', 'apply', 'details', 'enquiry']
  // const renderCareersCell = ['title', 'details', 'compared', 'recommended', 'goal', 'history']
  const renderCareersCell = ['title', 'current', 'history','goal', 'recommended', 'details', 'compared' ]
  const pageLeaveCell = ['page','userLeaves']
  const devicesRenderCells = ['device','frequency']
  const PAGE_LEAVE_HEAD = [
    { id: 'page', label: 'Page', alignRight: false },
    { id: 'userLeaves', label: 'User Leaves', alignRight: false },
  ]

  const CAREERS_TABLE_HEAD = [
    { id: 'title', label: 'Careers', alignRight: false },
    { id: 'current', label: 'Current', alignRight: false },
    { id: 'history', label: 'User Enters',alignRight: false },
    { id: 'goal', label: 'Destination', alignRight: false },
    { id: 'recommended', label: 'Recommended', alignRight: false },
    { id: 'details', label: 'Viewed', alignRight: false },
    { id: 'compared', label: 'Selected for Comparison', alignRight: false },
  ];
  const COURSES_TABLE_HEAD = [
    { id: 'title', label: 'Courses', alignRight: false },
    { id: 'apply', label: 'Apply', alignRight: false },
    { id: 'details', label: 'More Info', alignRight: false },
    { id: 'enquiry', label: 'Enquiry', pr: 7 },
  ];
  const DEVICES_TYPE_HEAD = [
    { id: 'device', label: 'Device', alignRight: false },
    { id: 'frequency', label: 'Frequency', alignRight: false },
  ];
  const { CoursesData, CareersData, PageLeaveData, DevicesData } = AnalyticsData
  const dateOptions = [
    { label: 'Last 28 days', value: -28 },
    { label: 'Last 7 days', value: -7 },
    { label: 'Custom', value: 0 },
  ]

  const handleDateChange = (e) => {
    setDateValue(e.target);
  }
  const handleDateRangeChange = (e) => {
    setDateRangeValue([e.selection]);
  }
  const getPageLeaveData = (pageLeaveResponse) => {
    const keys = Object.keys(pageLeaveResponse)
    const values = Object.values(pageLeaveResponse)
    const finalData = []
    for (let i = 0; i < keys?.length; i += 1) {
      // console.log({key:keys[i], value: values[i]});
      if (keys[i] !== "undefined") {
        const pageObject = {
          page: keys[i],
          userLeaves: values[i]
        }
        if(keys[i] !== ''){
          finalData.push(pageObject)
        }
        
      }
    }
    return finalData;
  }
  const getDevicesData = (devicesResponse) => {
    const keys = Object.keys(devicesResponse)
    const values = Object.values(devicesResponse)
    const finalData = []
    for (let i = 0; i < keys?.length; i += 1) {
      // console.log({key:keys[i], value: values[i]});
      if (keys[i] !== "undefined") {
        const deviceObject = {
          device: keys[i],
          frequency: values[i]
        }
        finalData.push(deviceObject)
      }
    }
    return finalData;
  }

  const getDashboardDetails = createAsyncThunk('/dashboard', async (data, { rejectWithValue }) => {
    setLoading(true)
    try {
      const response = await axiosInstance({
        url: "/dashboard",
        method: "POST",
        data: {
          collegeId: data?._id,
          ...simpleDateRangeValue
        },
      })
      setDashboardDetails(response?.data?.data)
      const courseResponse = get(response, 'data.data.data.coursesData', [])
      const careerResponse = get(response, 'data.data.data.careersData', [])
      const pageLeaveResponse = get(response, 'data.data.data.pageLeaveData', {})
      const devicesResponse = get(response, 'data.data.data.deviceData', {})
      const pageLeaveData =  getPageLeaveData(pageLeaveResponse)
      const devicesData =  getDevicesData(devicesResponse)
      setAnalyticsData(prevData => ({
        ...prevData,
        CoursesData: courseResponse,
        CareersData: careerResponse,
        PageLeaveData: pageLeaveData,
        DevicesData: devicesData
      }))
      return response.data;
    } catch (error) {
      console.log("err", error);
      return rejectWithValue(error)
    } finally {
      setLoading(false)
    }
  })

  const hideOnEscape = (e) => {
    if (e.key === 'Escape') {
      setOpen(false);
    }
  }
  const hideOnClickOutside = (e) => {
    if (refOne.current && !refOne.current.contains(e.target)) {
      setOpen(false);
    }
  }

  useEffect(() => {
    if (dateValue) {
      if (dateValue !== 0) {
        // console.log('value==>',Math. dateValue.value);
        setDateRangeValue(
          dateRangeValue.map((Value, index) => index === 0 ? {
            // startDate: new Date(),
            // endDate: addDays(new Date(), 7),
            startDate: addDays(new Date(), dateValue.value),
            endDate: new Date(),
            key: 'selection'
          } : dateValue)
        )
      }
    }
  }, [dateValue?.value])


  useEffect(() => {
    const selected = JSON.parse(localStorage.getItem('selectedCollege'));
    if (selected && simpleDateRangeValue) {
      dispatch(getDashboardDetails(selected));
    }
    else if (simpleDateRangeValue) {
      dispatch(getDashboardDetails());
    }
  }, [selectedCollege, simpleDateRangeValue])
  useEffect(() => {
    document.addEventListener('keydown', hideOnEscape, true)
    document.addEventListener('click', hideOnClickOutside, true)
  }, [])

  useEffect(() => {
    setSimpleDateRangeValue({
      // startDate: format(dateRangeValue[0].startDate, "MM/dd/yyyy"),
      // endDate: format(dateRangeValue[0].endDate, "MM/dd/yyyy"),
      startDate: format(dateRangeValue[0].startDate, "yyyy/MM/dd"),
      endDate: format(dateRangeValue[0].endDate, "yyyy/MM/dd"),
    })
  }, [dateRangeValue])


  const handleCourseSearch = (event) => {
    const filteredCourses = CoursesData.filter(course => {
      return course?.title && course?.title?.toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.details?.toString()?.includes(event.target.value.toLowerCase()) ||
      course?.apply?.toString()?.includes(event.target.value.toLowerCase()) ||
      course?.enquiry?.toString()?.includes(event.target.value.toLowerCase())}
    )
    return filteredCourses
  };
  const handleCareerSearch = (event) => {
    const filteredCareers = CareersData.filter(course => course?.title?.toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.details?.toString().toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.history?.toString().toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.goal?.toString().toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.compared?.toString().toLowerCase().includes(event.target.value.toLowerCase()) ||
      course?.recommended?.toString().toLowerCase().includes(event.target.value.toLowerCase()) 
    )
    return filteredCareers
  };
  const handlePageLeaveSearch = (event) => {
    const filteredPageTerms = PageLeaveData.filter(pageData => pageData?.page?.toLowerCase().includes(event.target.value.toLowerCase()) || pageData?.userLeaves?.toString()?.toLowerCase()?.includes(event.target.value.toLowerCase()))
    return filteredPageTerms
  };
  const handleDeviceSearch = (event) => {
    const filteredDevices = DevicesData.filter(deviceData => deviceData?.device?.toLowerCase().includes(event.target.value.toLowerCase()) || deviceData?.frequency?.toString()?.toLowerCase()?.includes(event.target.value.toLowerCase()))
    return filteredDevices
  };
  return (
    <>
      <Helmet>
        <title> Dashboard | ThinkSkill </title>
      </Helmet>

      <Container maxWidth="xl">
        <Typography variant="h4" gutterBottom mb={3}>
          Dashboard
        </Typography>
        {/* <Backdrop
          sx={{ color: '#fff', zIndex: (theme) => theme.zIndex.drawer + 99999 }}
          open={loading}
        // onClick={handleOpenBackdrop}
        >
          <CircularProgress color="inherit" />
        </Backdrop> */}
        <Card sx={{ p: 4, pb: 8, minHeight: '800px' }}>
          <Box sx={{ display: 'flex', justifyContent: 'right' }}>
            <FormControl sx={{ width: '20%' }}>
              <SelectComponent
                menuName={"label"}
                menuValue={"value"}
                labelId="date-label"
                label="Date *"
                inputLabel="Date"
                disableNone
                menuItems={dateOptions}
                sx={{ width: '100%' }}
                name='date'
                defaultValue={-7}
                value={dateValue}
                onChange={handleDateChange}
              />
            </FormControl>
            {
              dateValue?.value === 0 &&
              <FormControl sx={{ width: '20%', marginLeft: '20px', }}>
                <DateRangeField
                  state={dateRangeValue}
                  handleChange={handleDateRangeChange}
                  open={open}
                  openCalender={setOpen}
                  dateRef={refOne}
                  label={'Select Date Range'}
                  sx={{
                    width: '100%'
                  }}
                />
              </FormControl>
            }
          </Box>
          <Grid container justifyContent='center' spacing={3} mt={2}>
            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading}  title="Regions" total={dashboardDetails?.regions || "0"} icon={'material-symbols:location-on'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading}  title="College groups" total={dashboardDetails?.collegeGroups || "0"} icon={'material-symbols:group-add'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Colleges" total={dashboardDetails?.colleges || "0"} color="info" icon={'maki:college'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Campus" total={dashboardDetails?.campuses || "0"} color="warning" icon={'fluent:building-bank-24-filled'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Active users" total={dashboardDetails?.users || "0"} color="error" icon={'mdi:users-check'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Unique visitors" total={dashboardDetails?.uniqueVisitors || "0"} color="error" icon={'mdi:users-outline'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total visitors" total={dashboardDetails?.totalVisitors || "0"} color="error" icon={'fa-solid:users'} />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Unique Landing Users" total={dashboardDetails?.uniqueLanding || "0"} color="error" icon={'mdi:users-outline'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total Landing Users" total={dashboardDetails?.totalLanding || "0"} color="error" icon={'fa-solid:users'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Unique visitors selected Upskill" total={dashboardDetails?.uniqueUpskillVisitors || "0"} color="error" icon={'mdi:users-outline'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total visitors selected Upskill" total={dashboardDetails?.upskillVisitors || "0"} color="error" icon={'mdi:users'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Unique visitors selected Reskill" total={dashboardDetails?.uniqueReskillVisitors || "0"} color="error" icon={'mdi:users-outline'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total visitors selected Reskill" total={dashboardDetails?.reskillVisitors || "0"} color="error" icon={'mdi:users'} />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Number of radars generated" total={dashboardDetails?.radarsGenerated || "0"} color="error" icon={'ant-design:radar-chart-outlined'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total skill reports sent" total={dashboardDetails?.skillReports || "0"} color="error" icon={'simple-line-icons:docs'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total skill reports sent from careers page" total={dashboardDetails?.skillReportsFromCareersPage || "0"} color="error" icon={'pajamas:doc-symlink'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total skill reports sent from result page" total={dashboardDetails?.skillReportsFromCareersResultPage || "0"} color="error" icon={'pajamas:doc-symlink'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Total skill reports sent from Comparision page" total={dashboardDetails?.skillReportsFromComparisonPage || "0"} color="error" icon={'pajamas:doc-symlink'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Users engaging with scatter graph" total={dashboardDetails?.careerGraph || "0"} color="error" icon={'fa-solid:users'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Users engaging with careers as a list" total={dashboardDetails?.careerList || "0"} color="error" icon={'fa-solid:users'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Number of times the more details/apply/enquire button has been clicked" total={(dashboardDetails?.courseApply + dashboardDetails?.courseEnquiry + dashboardDetails?.courseDetails) || "0"} color="error" icon={'icon-park-outline:click'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Number of times Course Apply button has been clicked" total={dashboardDetails?.courseApply || "0"} color="error" icon={'icon-park-outline:click'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Number of times Course Details button has been clicked" total={dashboardDetails?.courseDetails || "0"} color="error" icon={'icon-park-outline:click'} />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <AppWidgetSummary loading={loading} title="Number of times Course Enquire button has been clicked" total={dashboardDetails?.courseEnquiry || "0"} color="error" icon={'icon-park-outline:click'} />
            </Grid>

            {/* <Grid item xs={12} md={6} lg={8}>
              <AppWebsiteVisits
                title="Website Visits"
                subheader="(+43%) than last year"
                chartLabels={[
                  '01/01/2003',
                  '02/01/2003',
                  '03/01/2003',
                  '04/01/2003',
                  '05/01/2003',
                  '06/01/2003',
                  '07/01/2003',
                  '08/01/2003',
                  '09/01/2003',
                  '10/01/2003',
                  '11/01/2003',
                ]}
                chartData={[
                  {
                    name: 'Team A',
                    type: 'column',
                    fill: 'solid',
                    data: [23, 11, 22, 27, 13, 22, 37, 21, 44, 22, 30],
                  },
                  {
                    name: 'Team B',
                    type: 'area',
                    fill: 'gradient',
                    data: [44, 55, 41, 67, 22, 43, 21, 41, 56, 27, 43],
                  },
                  {
                    name: 'Team C',
                    type: 'line',
                    fill: 'solid',
                    data: [30, 25, 36, 30, 45, 35, 64, 52, 59, 36, 39],
                  },
                ]}
              />
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <AppCurrentVisits
                title="Current Visits"
                chartData={[
                  { label: 'America', value: 4344 },
                  { label: 'Asia', value: 5435 },
                  { label: 'Europe', value: 1443 },
                  { label: 'Africa', value: 4443 },
                ]}
                chartColors={[
                  theme.palette.primary.main,
                  theme.palette.info.main,
                  theme.palette.warning.main,
                  theme.palette.error.main,
                ]}
              />
            </Grid>

            <Grid item xs={12} md={6} lg={8}>
              <AppConversionRates
                title="Conversion Rates"
                subheader="(+43%) than last year"
                chartData={[
                  { label: 'Italy', value: 400 },
                  { label: 'Japan', value: 430 },
                  { label: 'China', value: 448 },
                  { label: 'Canada', value: 470 },
                  { label: 'France', value: 540 },
                  { label: 'Germany', value: 580 },
                  { label: 'South Korea', value: 690 },
                  { label: 'Netherlands', value: 1100 },
                  { label: 'United States', value: 1200 },
                  { label: 'United Kingdom', value: 1380 },
                ]}
              />
            </Grid>

            <Grid item xs={12} md={6} lg={4}>
              <AppCurrentSubject
                title="Current Subject"
                chartLabels={['English', 'History', 'Physics', 'Geography', 'Chinese', 'Math']}
                chartData={[
                  { name: 'Series 1', data: [80, 50, 30, 40, 100, 20] },
                  { name: 'Series 2', data: [20, 30, 40, 80, 20, 80] },
                  { name: 'Series 3', data: [44, 76, 78, 13, 43, 10] },
                ]}
                chartColors={[...Array(6)].map(() => theme.palette.text.secondary)}
              />
            </Grid> */}
          </Grid>
        </Card>
        <Grid item xs={12} sm={6} md={10} className='accordian-wrapper'>
          {
            dashboardDetails?.data?.coursesData?.length > 0 &&
            <AccordionComponent
              title='Which courses triggered more details/enquire/apply'
              name="courses"
            >
              <DataTable
                TableHead={COURSES_TABLE_HEAD}
                // TableData={dashboardDetails?.data?.coursesData}
                TableData={CoursesData}
                renderCells={renderCourseCell}
                disableActions={"true"}
                filterSearch={handleCourseSearch}
                pagination="true"
                rowsPerPageProp={10}
              />
            </AccordionComponent>
          }
          {
            dashboardDetails?.data?.careersData?.length > 0 &&
            <AccordionComponent
              title='Career Wise Report'
              name="careers"
            >
              <DataTable
                TableHead={CAREERS_TABLE_HEAD}
                // TableData={dashboardDetails?.data?.careersData}
                TableData={CareersData}
                renderCells={renderCareersCell}
                disableActions={"true"}
                filterSearch={handleCareerSearch}
                pagination="true"
                rowsPerPageProp={10}
              />
            </AccordionComponent>
          }
          {
            PageLeaveData?.length > 0 &&
            <AccordionComponent
              title='Users Dropping off Report'
              name="pageLeaves"
            >
              <DataTable
                TableHead={PAGE_LEAVE_HEAD}
                // TableData={dashboardDetails?.data?.careersData}
                TableData={PageLeaveData}
                renderCells={pageLeaveCell}
                disableActions={"true"}
                filterSearch={handlePageLeaveSearch}
                pagination="true"
                rowsPerPageProp={10}
              />
            </AccordionComponent>
          }
          {
            DevicesData?.length > 0 &&
            <AccordionComponent
              title='Device Usage Report'
              name="devicesType"
            >
              <DataTable
                TableHead={DEVICES_TYPE_HEAD}
                // TableData={dashboardDetails?.data?.careersData}
                TableData={DevicesData}
                renderCells={devicesRenderCells}
                disableActions={"true"}
                filterSearch={handleDeviceSearch}
                pagination="true"
                rowsPerPageProp={10}
              />
            </AccordionComponent>
          }
        </Grid>
      </Container>
    </>
  );
}
