const express = require("express");
const router = express.Router();
const jwt = require("jsonwebtoken");
const { UserRoles } = require("../models/user");
const { Country } = require("../models/country");
const forAsync = require("../tools/forAsync");
// const Tools = require("../methods/tools/tools");
const mongoose = require("mongoose");
// const isDuplicateEntry = require("../methods/database/isDuplicateEntry");
const countryController = require('../controllers/countries.controller')


router.post("/add", countryController.add);

router.get("/get", countryController.get);

router.get("/getById", countryController.getByID);

router.delete("/remove", countryController.remove);

router.put("/update", countryController.update);

// router.post("/update", async (req, res, next) => {
//   req.body.uniqueName = Tools.getUniqueName(req.body.name);

//   const isDuplicate = await isDuplicateEntry(req.body, Category, false, true);

//   if (isDuplicate) {
//     return res.status(400).json({
//       success: false,
//       msg: "An entry with the same name already exists.",
//       code: 11000
//     });
//   }

//   Category.update({ _id: req.body._id, defaultEntry: false }, req.body)
//     .catch(error => {
//       return res.status(400).json({
//         success: false,
//         msg: "An unknown error occured: " + error.code
//       });
//     })
//     .then(() => {
//       res.status(200).json({ success: true });
//     });
// });

// router.post("/getByIDs", (req, res, next) => {
//   if (!req.body.ids) {
//     return res.status(400).json({ msg: "No IDs provided" });
//   }

//   Category.find({ _id: { $in: req.body.ids } })
//     .catch(error => {
//       console.error("Error while getting categories by ID", error);
//       return res.status(400).json({
//         success: false,
//         msg: "An unknown error occured: " + error.code
//       });
//     })
//     .then(categories => {
//       res.json(categories);
//     });
// });

// const updateStatus = require("../methods/tools/updateStatus");
// router.post("/updatestatus/:id/:status", (req, res, next) => {
//   updateStatus(Category, req.params.id, req.params.status, res);
// });

// router.post("/updatestatus/", (req, res, next) =>
// {
//   updateStatus(Category, req.body.entry._id, req.body.entry.status, res);
// });

module.exports = router;

// module.exports=(app)=>{
    
//     // get data of all employees by superuser
//     app.get('/api/countries/getAll', countryController.getAll)

// }