import { Box, Button, Fade, Grid, Stack, TextField, TextareaAutosize, Typography } from '@mui/material';
import { useFormik } from 'formik';
import { useDispatch } from 'react-redux';
import { addGroup } from '../CollegeGroup/collegeGroupsSlice';
import { formButton, formStyle } from '../../utils/cssStyles';
import { groupValidationSchema } from '../../utils/validationSchemas';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';

const CareerForm = ({ Groups, setOpenModel, openModel }) => {


    const formik = useFormik({
        initialValues: {
            id: '',
            careerTitle: '',
            careerDescription: '',
            knowledge: '',
            intrest: '',
            jobZone: '',
            salary: '',
            cluster: '',
            pathway: '',
            skills: [],
            abilities: [],
        },
        onSubmit: (values) => {

        },
        // validationSchema: groupValidationSchema
    })
    const dispatch = useDispatch()
    const handleClose = () => {
        setOpenModel(false)
    }

    return (
        <Fade in={openModel}>
            <Box >
                <form onSubmit={formik.handleSubmit}>
                    <Grid container gap={2} >
                        <Grid item xs={12} md={11.8}>

                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='careerTitle'
                                label="Title"
                                onBlur={formik.handleBlur}
                                value={formik.values.careerTitle}
                                onChange={formik.handleChange}
                            // error={formik.touched.Name && Boolean(formik.errors.Name)}
                            // helperText={formik.touched.Name && formik.errors.Name}
                            />
                        </Grid>
                        <Grid item xs={12} md={11.8}>
                            <TextFIeldComponent
                                multiline
                                rows={12}
                                placeholder="Description"
                                sx={{ width: '100%' }}
                                name='careerDescription'
                                label="Description"
                                onBlur={formik.handleBlur}
                                value={formik.values.careerDescription}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                        <Grid item xs={12} md={3.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='knowledge'
                                label="Knowledge"
                                onBlur={formik.handleBlur}
                                value={formik.values.knowledge}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                        <Grid item xs={12} md={3.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='intrest'
                                label="Intrest"
                                onBlur={formik.handleBlur}
                                value={formik.values.intrest}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                        <Grid item xs={12} md={3.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='jobZone'
                                label="Job Zone"
                                onBlur={formik.handleBlur}
                                value={formik.values.jobZone}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                        <Grid item xs={12} md={3.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='salary'
                                label="Salary"
                                onBlur={formik.handleBlur}
                                value={formik.values.salary}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                        <Grid item xs={12} md={3.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='cluster'
                                label="Cluster"
                                onBlur={formik.handleBlur}
                                value={formik.values.cluster}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                        <Grid item xs={12} md={3.8}>
                            <TextFIeldComponent
                                sx={{ width: '100%' }}
                                name='pathway'
                                label="Pathway"
                                onBlur={formik.handleBlur}
                                value={formik.values.pathway}
                                onChange={formik.handleChange}
                            />
                        </Grid>
                    </Grid>
                    <Stack
                        direction="row"
                        justifyContent="flex-end"
                    >
                        <Button
                            type='submit'
                            variant='contained'
                            sx={formButton}>
                            Add
                        </Button>
                    </Stack>
                </form>
            </Box>
        </Fade >
    )
}

export default CareerForm