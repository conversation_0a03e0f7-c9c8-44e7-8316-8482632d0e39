import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Button, Container, Typography } from '@mui/material';
import { Link } from 'react-router-dom';
import TextFIeldComponent from "../../components/TextField/TextFieldComponent";

export default function HaveACode() {
    const [codeValue, setCodeValue] = useState();
    const [isDisableSubmitBtn, setIsDisableSubmitBtn] = useState(true);

    useEffect(()=>{
        if(codeValue && codeValue.length >=6){
            setIsDisableSubmitBtn(false)
        }else{
            setIsDisableSubmitBtn(true)
        }
    }, [codeValue]);

  return (
    <>
      <Helmet>
        <title>Have a code</title>
      </Helmet>
      <Box className='page-content-wrapper home'>
        <Container>
          <Box className='content'>
            <Box className="link-wrapper">
              <Typography variant='h1' color='primary.main'>UPSKILL</Typography>
              <p> / </p>
              <Typography variant='h1' color='primary.main'>RESKILL</Typography>
            </Box>
            <Box className="code-content">
              <Typography variant='h3' color='primary.black'>Do you have a code</Typography>
              <Typography variant='body' color='primary.light'>If you have already requested a Skills Report from us you will have received a unique Upskill / Reskill code.</Typography>
              <Typography variant='body' color='primary.light'>Enter that code here to pick up right where you left off.</Typography>
              <Box className="field-wrapper">
                <TextFIeldComponent                
                    id='code'
                    placeholder='Enter Code'
                    error='Please enter valide code'
                    handleChange={(e)=>setCodeValue(e.target.value)}
                    value={codeValue}
                    className="input-text-field"
                />
                <Button className='btn noUppercase' disabled={isDisableSubmitBtn} sx={!isDisableSubmitBtn ? {backgroundColor: '#7040f1 !important'} : ''} ><Typography variant='button2' color='primary.white'>Submit</Typography></Button>
              </Box>
              <Box className="box-content">
                <Typography variant='h3' color='primary.black'>Don't have a code?</Typography>
                <Typography variant='body' color='primary.light'>Use Upskill / Reskill to understand employability skills, the best courses to build them, and find which careers you could successfully transfer your skills to.  It all gets summed up in a handy report we email to you, and the code allows you to return at any time.</Typography>
                <Link className='btn noUppercase main' to='/' ><Typography variant='button2' color='primary.white'>Break out of your career box</Typography></Link>
              </Box>
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
}