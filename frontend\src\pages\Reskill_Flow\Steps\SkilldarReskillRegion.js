import { useEffect, useMemo, useRef, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Button, Container, Typography, Grid, Popover, CircularProgress } from '@mui/material';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import CloseIcon from '@mui/icons-material/Close';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import AssignmentIcon from '@mui/icons-material/Assignment';
import StepperComponent from 'src/components/stepper/Stepper';
import ApexCharts from 'src/components/apexcharts/ApexCharts';
import SkilldarExplainned1 from 'src/assets/images/skilldar-explained-1.jpg';
import SkilldarExplainned2 from 'src/assets/images/skilldar-explained-2.jpg';
import SkilldarExplainned3 from 'src/assets/images/skilldar1.svg';
import SkilldarExplainned4 from 'src/assets/images/skilldar2.svg';
import { SkilldarChartData, skillLevels } from 'src/assets/data/Dummy';
import { setAlert } from 'src/layouts/main/MainLayoutSlice';
import PolarChartComponent from 'src/components/RadarChart/PolarChartComponent';
import { useGetSkilldarChartDataMutation } from 'src/pages/Upskill_Flow/UpskillSlice';
import axios from 'axios';
import RadarChartComponent from 'src/components/RadarChart/RadarChartComponent';
import SkillCard from 'src/components/RadarChart/SkillCard';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import Cookies from 'js-cookie';
import { getRegionBySlug } from 'src/pages/CareerHistory/CareerHistorySlice';
import { isEmpty } from 'lodash';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';
import { Icon } from '@iconify/react';
import RegionStepper from 'src/components/RegionStepper';
import { ArcticonsEmojiSpiderWeb } from '../SpiderIcon';

export default function SkilldarReskillRegion() {
  const params = useParams();
  const isRegion = !!params?.rg_name;
  const dispatch = useDispatch();
  const ref = useRef(null);
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = useState(null);
  const [slideNo, setSlideNo] = useState(1);
  const [windowSize, setWindowSize] = useState(window.innerWidth);
  const [chartWidth, setChartWidth] = useState(820);
  const [chartHeight, setChartHeight] = useState('100%');
  const [ReskillSkilldarSteps, setReskillSkilldarSteps] = useState();
  const [careerHistoryId, setCareerHistoryId] = useState();
  const CareerHistoryState = useSelector((state) => state.careerHistory.CareerHistory);
  const collegeId = useSelector((state) => state.mainLayout.collegeDetails?._id);
  const [getUpskillRadar, upskillSkilldarData] = useGetSkilldarChartDataMutation();
  const [showLabel, setShowLabel] = useState(false);
  const [IP, setIp] = useState('');
  const [openPopup, setOpenPopup] = useState(false);
  const [loadedRadarData, setLoadedRadarData] = useState(false);
  const loadRadar = () => {
    setLoadedRadarData(true);
  };
  const { rg_name } = useParams();
  const regions = useSelector((state) => state.careerHistory);
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);

  useEffect(() => {
    setLoading(true);
    dispatch(getRegionBySlug(rg_name));
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(data)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, data]);

  const skillDescriptions = [
    {
      title: 'Communication',
      text: `Communication skills allow you to understand and be understood by others. 
This skills category is made up of Active Listening, Speaking, Writing and Reading Comprehension.`,
    },
    {
      title: 'Problem Solving',
      text: `Problem solving is the process of identifying and fixing a problem or obstacle to achieve a goal.
This skills category is made up of Critical Thinking, Complex Problem Solving, Judgement and Decision Making and Deductive reasoning.`,
    },
    {
      title: 'Teamwork',
      text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.`,
    },
    {
      title: 'Organisation',
      text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.`,
    },
    {
      title: 'Creativity',
      text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.`,
    },
    {
      title: 'Learning',
      text: `Lorem ipsum placeholder for Learning.`,
    },
    {
      title: 'Technical',
      text: `Lorem ipsum placeholder for Technical.`,
    },
    {
      title: 'Physical',
      text: `Lorem ipsum placeholder for Physical.`,
    },
    {
      title: 'Manipulation',
      text: `Lorem ipsum placeholder for Manipulation.`,
    },
    {
      title: 'Leadership',
      text: `Lorem ipsum placeholder for Leadership.`,
    },
  ];
  const buttonColor = data?.button?.bgColor;
  const buttonFontColor = data?.button?.color;
  const logo = data?.logo || '';
  const partnerLogos = data?.partnerLogos || [];
  const primaryColor = data?.primaryColor || '';
  const fontColor = data?.fontColor || '';
  const secondaryColor = data?.secondaryColor || '';
  const bgImage = data?.bgImage || '';
  const getIp = async () => {
    // const res = await axios.get("https://api.ipify.org/?format=json");
    // const Ip = res.data.ip
    const userIp = JSON.parse(localStorage.getItem('userIp'));
    setIp(userIp);
  };
  useEffect(() => {
    getIp();
  }, []);
  useEffect(() => {
    if (loadedRadarData) {
      setTimeout(() => {
        scrollToTop();
        // openLabel()
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadedRadarData]);
  const closeLabel = () => {
    setShowLabel(false);
    document.body.style.overflow = 'unset';
    window.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };
  const openLabel = () => {
    setShowLabel(true);
    if (typeof window !== 'undefined' && window.document) {
      document.body.style.overflow = 'unset';
      // window.scrollTo({
      //   top: 60,
      //   behavior: "smooth"
      // });
    }
  };

  const scrollToLaicie = (element) => {
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };
  const handleClick = () => {
    navigate(`/${params.cg_name}/reskill/career-courses`);
  };

  const toggleShowSkilldarPopup = (event) => {
    setAnchorEl(event.currentTarget);
    setSlideNo(1);
  };

  const HandleCloseSkilldar = () => {
    setAnchorEl(null);
  };

  const openSkilldarPopup = Boolean(anchorEl);

  const handleChangeSlide = (e) => {
    if (e === 'next') {
      setSlideNo((prev) => prev + 1);
    }
    if (e === 'prev') {
      setSlideNo((prev) => prev - 1);
    }
  };
  const scrollRef = useRef(null);
  const scrollToTop = () => {
    const element = scrollRef?.current;
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'end' });
      setOpenPopup(true);
    }
  };
  useEffect(() => {
    if (openPopup) {
      setTimeout(() => {
        openLabel();
      }, 600);
    }
  }, [openPopup]);

  useEffect(() => {
    if (CareerHistoryState && CareerHistoryState.length > 0) {
      const id = CareerHistoryState.map((item) => item.value);
      setCareerHistoryId(id);
    } else {
      navigate(`/region/${params?.rg_name}/career-history`);
      dispatch(
        setAlert({
          open: true,
          msg: 'Please select your current career again',
        })
      );
    }
  }, [CareerHistoryState]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const handleWindowResize = () => {
      if (window.innerWidth <= 1024 && window.innerWidth > 599) {
        setChartWidth('100%');
        setChartHeight('85%');
      } else if (window.innerWidth <= 599 && window.innerWidth > 399) {
        setChartWidth('100%');
        setChartHeight('60%');
      } else if (window.innerWidth <= 399) {
        setChartWidth('100%');
        setChartHeight('50%');
      } else {
        setChartWidth('65%');
        setChartHeight('100%');
      }
    };
    if (window.innerWidth <= 1024 && window.innerWidth > 599) {
      setChartWidth('100%');
      setChartHeight('85%');
    } else if (window.innerWidth <= 599 && window.innerWidth > 399) {
      setChartWidth('100%');
      setChartHeight('60%');
    } else if (window.innerWidth <= 399) {
      setChartWidth('100%');
      setChartHeight('50%');
    } else {
      setChartWidth('65%');
      setChartHeight('100%');
    }

    window.addEventListener('resize', handleWindowResize);

    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, []);

  useEffect(() => {
    // setReskillSkilldarSteps([
    //   {
    //     label: 'Career History',
    //     link: `/${params.cg_name}/career-history`
    //   },
    //   {
    //     label: 'Skilldar',
    //   },
    //   {
    //     label: 'Career and Reskill time',
    //   },
    // ])
    setReskillSkilldarSteps([
      {
        label: 'Your Skills',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/skilldar`
          : `/${params.cg_name}/reskill/skilldar`,
      },
      {
        label: 'Your Careers',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/career-courses`
          : `/${params.cg_name}/reskill/career-courses`,
      },
      {
        label: 'Colleges',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/colleges`
          : `/${params.cg_name}/reskill/colleges`,
      },
      {
        label: 'Your Region',
        link: isRegion
          ? `/region/${params.rg_name}/reskill/regional-info`
          : `/${params.cg_name}/reskill/regional-info`,
      },
    ]);
  }, [params, isRegion]);

  const SpiderIcon = () => (
    <Icon icon="fluent-emoji:spider-web" color={buttonColor} width="36" height="36" />
  );

  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    {
      icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color={buttonFontColor} />,
      label: 'Results',
    }, // Capitalized if it's a component
  ];

  return (
    <>
      <Helmet>
        <title>Reskill - Skilldar</title>
      </Helmet>

      <Box
        sx={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
        }}
        className="page-content-wrapper"
      >
        <Container maxWidth="xl">
          <Box pt={5} pb={4} className="content">
            {/* <Typography variant='h2' color='primary.black' className='page-head'>Reskill</Typography> */}
            <Box pb={2}>
              <RegionStepper
                steps={steps}
                activeStep={1} // Change to 1 to highlight "Results"
                buttonColor={buttonColor}
                buttonFontColor={buttonFontColor}
              />    
            </Box>
            <ThinkSkillsHeader fontColor="white" />
            <SecondRegionStepper steps={ReskillSkilldarSteps} activeStep={0} noIcon />
            <Box sx={{ borderRadius: '0 0 12px 12px', mt: 0.2 }} backgroundColor="white">
              {/* <Typography variant='h2' color='primary.black' >Your Skills</Typography>
                <Typography variant='body1' color='primary.light'>Click on any skill to see its attribute in more detail.</Typography> */}
              <Grid container className="skillchart-wrapper" sx={{ overflow: 'hidden' }} ref={ref}>
                <Grid
                  item
                  sm={12}
                  md={12}
                  lg={12}
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  className="chart-wrapper"
                >
                  {careerHistoryId && chartWidth ? (
                    <RadarChartComponent
                      loadRadar={loadRadar}
                      width={chartWidth}
                      height={650}
                      radarChartId={{
                        IP,
                        careerIds: careerHistoryId,
                        careerGoal: '',
                        collegeId,
                      }}
                      closeLabel={closeLabel}
                      openLabel={openLabel}
                      toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                      skilldar
                      legend
                      radarApiHook={getUpskillRadar}
                      handleClick={handleClick}
                      showlabels
                      disableTooltip
                      showLabel={showLabel}
                      chartAnimation
                      params={params}
                      regionId={data?._id}
                    />
                  ) : (
                    <CircularProgress />
                  )}
                  {/* {
                       careerHistoryId && chartWidth ?
                       <PolarChartComponent
                       loadRadar ={loadRadar}
                       reskillChart 
                       width={chartWidth}
                       height={chartHeight}
                       radarChartId={{
                         IP,
                         careerIds: careerHistoryId,
                         careerGoal: '',
                         collegeId
                       }}
                       closeLabel={closeLabel}
                       openLabel={openLabel}
                       toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                       skilldar legend 
                       radarApiHook={getUpskillRadar}
                       handleClick={handleClick}
                       showlabels
                       disableTooltip
                       showLabel={showLabel}
                       chartAnimation
                       /> :
                       <CircularProgress /> 
                      } */}
                </Grid>
              </Grid>
            </Box>
            <Box className="content-wrapper">
              <Box sx={{ py: 0 }}>
                {/* Top Section */}
                <Box
                  sx={{
                    backgroundColor: '#f3f3f3',
                    borderRadius: 2,
                    p: 3,
                    mb: 4,
                    textAlign: 'center',
                    // maxWidth: 900,
                    mx: 'auto',
                  }}
                >
                  <Grid container alignItems="center">
                  <Grid item sm={12}>
                        <Box sx={{ fontWeight: 700, fontSize: '20px !important', mb: 1 }}>
                        Your Skilldar explained
                      </Box>
                  </Grid>
                    <Grid item sm={3}>
                      {careerHistoryId && chartWidth ? (
                        <RadarChartComponent
                          loadRadar={loadRadar}
                          legend={false}
                          reskillChart
                          width={chartWidth}
                          height={200}
                          radarChartId={{
                            IP,
                            careerIds: careerHistoryId,
                            careerGoal: '',
                            collegeId,
                          }}
                          closeLabel={closeLabel}
                          openLabel={openLabel}
                          toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                          radarApiHook={getUpskillRadar}
                          handleClick={handleClick}
                          disableTooltip
                          showLabel={showLabel}
                          chartAnimation
                          params={params}
                          regionId={data?._id}
                        />
                      ) : (
                        <CircularProgress />
                      )}
                    </Grid>
                    {/* Radar Chart */}
                    <Grid item sm={9}>
                      <Typography
                        sx={{
                          fontSize: '14px !important',
                          mb: 2,
                          textAlign: 'justify',
                          whiteSpace: 'pre-line',
                        }}
                      >
                        {`Your Skilldar is made up of ten universal employability skills, labelled around the edge of the diagram.
Your skill level for each skill (high – low) is plotted on each axis.
Our Careers database contains skill level information about every career. When you entered your career history,
we know precisely how skilled you have needed to be to do these jobs and so we can use this to determine your skill level.
Now that we know your skills, we can match you to other careers in our database that require a skill set similar to yours.
This is where you truly get to realise your full potential!`}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>

                {/* Bottom Section */}
                <Box
                  sx={{
                    // backgroundColor: '#f3f3f3',
                    borderRadius: 2,
                    // maxWidth: 1400,
                    mb: 3,
                  }}
                >
                  <Box
                    sx={{
                      fontWeight: 700,
                      fontSize: '16px !important',
                      textAlign: 'center',
                      mb: 1,
                      mt: 6,
                      color: '#f3f3f3'
                    }}
                  >
                    Your Skills explained
                  </Box>
                  <Box sx={{ fontSize: '14px !important', textAlign: 'center', mb: 5, color: '#f3f3f3' }}>
                    Skills are important because they help you be more productive, effective, and
                    successful in your work. Understanding your skills ensures that you are able to
                    choose careers that matched your strengths.
                  </Box>

                  <Grid container spacing={2}>
                    {skillDescriptions.map((skill, index) => (
                      <Grid item xs={12} sm={4} md={3} key={index}>
                        <SkillCard {...skill} />
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box>
            </Box>
            <Popover
              id="skilldar-explained"
              // open={openSkilldarPopup}
              open={false}
              anchorEl={anchorEl}
              onClose={HandleCloseSkilldar}
              className="app-popup skilldar"
            >
              <Box className="popup-head">
                <Typography variant="capitalize2" color="primary.black" className="icon-text">
                  <span>?</span>Skilldar Explained
                </Typography>
                <Box className="checkbox-field">
                  <input type="checkbox" id="show-popup" />
                  <label htmlFor="show-popup">Don't show this again</label>
                </Box>
                <Button className="close-btn" onClick={HandleCloseSkilldar}>
                  <CloseIcon />
                </Button>
              </Box>
              <Box className="content-wrapper">
                {slideNo === 1 ? (
                  <Grid container className="slider-content-wrapper">
                    <Grid xs={7} sm={7} md={7} lg={8} item className="left-col content-col">
                      <img src={SkilldarExplainned1} alt="Skilldar Explainned" />
                    </Grid>
                    <Grid
                      xs={5}
                      sm={5}
                      md={5}
                      lg={4}
                      item
                      className="right-col content-col"
                      sx={{ justifyContent: 'space-between' }}
                    >
                      <Typography variant="body1" color="primary.light">
                        Your Skilldar is made up of ten universal employability skills, with each
                        one represented around its edge.
                      </Typography>
                      <Typography variant="body1" color="primary.light">
                        Your skill level is represented by the points plotted against each skill
                        axis.
                      </Typography>
                    </Grid>
                  </Grid>
                ) : (
                  ''
                )}
                {slideNo === 2 ? (
                  <Grid container className="slider-content-wrapper">
                    <Grid xs={7} sm={7} md={7} lg={8} item className="left-col content-col">
                      <img src={SkilldarExplainned2} alt="Skilldar Explainned" />
                    </Grid>
                    <Grid xs={5} sm={5} md={5} lg={4} item className="right-col content-col">
                      <Typography variant="body1" color="primary.light" className="bottom-spacing">
                        The level of each skill, from low to high, is calculated by combining the
                        values of a skill's attributes.
                      </Typography>
                      <Typography variant="body1" color="primary.light">
                        Click on any skill to see its attributes in more detail.
                      </Typography>
                    </Grid>
                  </Grid>
                ) : (
                  ''
                )}
                {slideNo === 3 ? (
                  <Grid container className="slider-content-wrapper">
                    <Grid xs={7} sm={7} md={7} lg={6} item className="left-col content-col">
                      <img src={SkilldarExplainned3} alt="Skilldar Explainned" />
                    </Grid>
                    <Grid xs={5} sm={5} md={5} lg={6} item className="right-col content-col">
                      <Typography variant="body1" color="primary.light">
                        Our careers database contains skill level information about every career, so
                        we know precisely how skilled you needed to be to perform all of the jobs
                        you included in your career history.
                      </Typography>
                      <Typography variant="body1" color="primary.light">
                        We are able to determine your proven level in each attribute and
                        employability skill by combining their skill values.
                      </Typography>
                    </Grid>
                  </Grid>
                ) : (
                  ''
                )}
                {slideNo === 4 ? (
                  <Grid container className="slider-content-wrapper" flexDirection="column">
                    <Grid
                      lg={10}
                      item
                      className="left-col content-col"
                      sx={{ marginBottom: '30px' }}
                    >
                      <img src={SkilldarExplainned4} alt="Skilldar Explainned" />
                    </Grid>
                    <Grid
                      lg={10}
                      item
                      className="right-col content-col"
                      sx={{ height: 'unset !important' }}
                    >
                      <Typography
                        variant="body1"
                        color="primary.light"
                        sx={{ textAlign: 'center !important' }}
                      >
                        Now we know your skills and strengths, we can match you to careers in our
                        database that require a skill set similar to your own.
                      </Typography>
                    </Grid>
                  </Grid>
                ) : (
                  ''
                )}
              </Box>
              <Box className="popup-slider-control">
                <Button
                  onClick={() => handleChangeSlide('prev')}
                  className={`${slideNo <= 1 ? 'disable' : ''}`}
                >
                  <ChevronLeftIcon />
                </Button>
                <Box className="dots-wrapper">
                  <span className={`dot ${slideNo === 1 ? 'active' : ''}`} />
                  <span className={`dot ${slideNo === 2 ? 'active' : ''}`} />
                  <span className={`dot ${slideNo === 3 ? 'active' : ''}`} />
                  <span className={`dot ${slideNo === 4 ? 'active' : ''}`} />
                </Box>
                <Button
                  onClick={() => handleChangeSlide('next')}
                  className={`${slideNo >= 4 ? 'disable' : ''}`}
                >
                  <ChevronRightIcon />
                </Button>
              </Box>
            </Popover>
            {/* <Box className="btn-wrapper">
              <Button className='btn noUppercase' sx={{backgroundColor: '#7040f1 !important'}} onClick={handleClick}><Typography color='primary.white'>Unlock your full potential</Typography></Button>
            </Box> */}
          </Box>
        </Container>
      </Box>
    </>
  );
}
