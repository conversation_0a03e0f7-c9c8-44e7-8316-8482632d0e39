import Cookies from 'universal-cookie';
import jwtDecode from "jwt-decode";
import { LoadingButton } from '@mui/lab';
import { Box, <PERSON>ton, Card, CardHeader, Container, FormLabel, Grid, IconButton, LinearProgress, Stack, TextField, Typography } from '@mui/material';
import CreateIcon from '@mui/icons-material/Create';
import AlternateEmailIcon from '@mui/icons-material/AlternateEmail';
import EmailIcon from '@mui/icons-material/Email';
import { useFormik } from 'formik';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { get } from 'lodash';
import PopUp from 'src/components/PopUp';
import ChangePassword from 'src/components/ChangePassword';
import { profileValidation, resetPasswordValidation } from 'src/utils/validationSchemas';
import noImagePlaceholder from '../assets/images/no-image-icon-0.jpg';
import PhoneInput from '../components/PhoneInput';
import { APP_ROUTER_BASE_URL, getUserRole } from '../utils';
import axiosInstance from '../utils/axiosInstance';
import TextFIeldComponent from '../components/TextField/TextFIeldComponent';
import { CancelButton, label } from '../utils/cssStyles';
import useAuth from '../hooks/useAuth';
import Iconify from '../components/Iconify/Iconify';
import { editUser } from './Userspage/usersSlice';
import { setSnackbar } from '../Redux/snackbarSlice';


const Profile = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [loading, setLoading] = useState(false);
    const [editing, setEditing] = useState(false);
    const [openModel, setOpenModel] = useState(false);
    const [passwordLoading, setPasswordLoading] = useState(false);
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const cookies = new Cookies();
    const jwtToken = cookies.get("token")
    const User = jwtToken && jwtDecode(jwtToken)
    const formik = useFormik({
        initialValues: {
            firstName: '',
            lastName: '',
            email: '',
            phone: '',
            password: 'password',
            role: '',
            // confirmPassword: '',
            profile: ''
        },
        validationSchema: profileValidation,
        onSubmit: (values) => {
            setIsLoading(true)
            const user = {
                firstName: values.firstName,
                lastName: values.lastName,
                email: values.email,
                id: values.id,
                contactNumber: values.phone,
                photo: values.profile
                // password: values.password,
            }
            dispatch(editUser(user)).then(res => {
                if (res?.payload?.success) {
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: "Succesfully updated User Info"
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/user`)
                } else {
                    const errorMessage = get(res, 'payload.data.msg', 'something went wrong!')
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }))
                }
            }).finally(() => setIsLoading(false))
        },
    })
    const passwordFormik = useFormik({
        initialValues: {
            oldPassword: "",
            newPassword: "",
            confirmPassword: "",
        },
        validationSchema: resetPasswordValidation,
        onSubmit: (values) => {
            setPasswordLoading(true)
            const { confirmPassword, oldPassword, newPassword } = values
            const data = {
                oldPassword,
                newPassword,
                userId: User?.id
            }
            // if (confirmPassword === newPassword) {
            axiosInstance({
                url: "users/resetPassword",
                method: "POST",
                data
            }).then(res => {
                const successMessage = res?.data?.message
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: successMessage || "Succesfully changed password "
                }))
                passwordFormik.resetForm()
                closeModelPopup()
            }).catch(error => {
                const errorMessage = error?.response?.data?.message || "Something Went Wrong"
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: errorMessage
                }))
            }).finally(() =>
                setPasswordLoading(false))

            // }
        }
    })
    const removePic = () => {
        formik.setValues({
            ...formik.values,
            profile: ''
        })
        document.getElementById("profilePic").value = ''
    }
    const closeModelPopup = () => {
        setOpenModel(false)
        passwordFormik.resetForm()
    }
    const openModelPopup = () => {
        setOpenModel(true)
    }
    const handlePhoneChange = (newValue, info) => {
        formik.setValues({
            ...formik.values,
            phone: newValue
        })
    }
    const style = {
        p: 4,
    };

    const { user } = useAuth()
    useEffect(() => {
        const getUser = async (id) => {
            setLoading(true)
            try {
                const response = await axiosInstance({
                    url: "users/getByID",
                    method: "GET",
                    params: {
                        id
                    }
                })
                // console.log("g",group.data)
                const userDetails = response.data?.data;
                formik.setValues({
                    ...formik.values,
                    firstName: userDetails.firstName,
                    lastName: userDetails.lastName,
                    email: userDetails.email,
                    phone: userDetails.contactNumber,
                    // password: userDetails.password,
                    id: userDetails._id,
                    profile: userDetails?.photo
                })
            } catch (error) {
                const errorMessage = error?.response?.data?.msg
                console.log("error get colleges", errorMessage)
            } finally {
                setLoading(false)
            }
        }
        getUser(user?._id)
    }, [])

    return (
        <Container maxWidth="lg">
            <PopUp
                open={openModel}
                onClose={closeModelPopup}
                title="Change Password"
                maxWidth='sm'
            >
                <ChangePassword
                    formik={passwordFormik}
                    loading={passwordLoading}
                />
            </PopUp>
            <Typography variant="h4" component="h2" sx={{ mb: 3 }} >
                Profile
            </Typography>
            {loading ?
                <LinearProgress /> :
                <>
                    {editing ?
                        <Grid container wrap='wrap' width={'100%'} gap={2}>
                            <Grid item xs={6} lg={2.8}  >
                                <Card sx={{ minHeight: 306 }} >
                                    <Box sx={{
                                        p: 4,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        {/* <label htmlFor='profilePic' > */}
                                        {/* <img className='user-profile' alt='logo' src={Image ? URL.createObjectURL(Image) : noImagePlaceholder} /> */}
                                        <img className='user-profile' alt='logo' src={formik.values.profile ? formik.values.profile : noImagePlaceholder} />
                                        {/* </label> */}
                                        <TextField
                                            sx={{ width: '100%', display: 'none' }}
                                            id='profilePic'
                                            name='profile'
                                            // label="Profile Image"
                                            onChange={(e) => {
                                                // setImage(e.target.files[0])
                                                const fileReader = new FileReader();
                                                fileReader.readAsDataURL(e.target.files[0]);
                                                fileReader.onload = (event) => {
                                                    formik.setValues({
                                                        ...formik.values,
                                                        profile: event.target.result
                                                    })
                                                }
                                            }}
                                            type='file'
                                        />
                                    </Box>
                                    <Stack
                                        direction={'row'}
                                        justifyContent={'center'}
                                        gap={1}
                                        alignItems={'center'}
                                    >
                                        <Button
                                            variant='contained'
                                            sx={{ minWidth: 80, px: 0, py: 0 }}
                                        >
                                            <FormLabel sx={label} htmlFor='profilePic'>
                                                {formik.values.profile ? "Change" : "Add"}
                                            </FormLabel>
                                            {/* <label htmlFor='profilePic' >
                                    {formik.values.profile ? "Change" : "Add"}
                                </label> */}
                                        </Button>
                                        {formik.values.profile &&
                                            <Button
                                                sx={{ minWidth: 80 }}
                                                variant='contained'
                                                color='error'
                                                onClick={removePic}
                                            >
                                                Remove
                                            </Button>}
                                    </Stack>
                                </Card>
                            </Grid>
                            <Grid xs={12} lg={8.8}>
                                <Card>
                                    <Box sx={style}>
                                        <form
                                            onSubmit={formik.handleSubmit}
                                        >
                                            <Grid container gap={2} >
                                                <Grid item xs={12} md={5.8}>
                                                    <TextFIeldComponent
                                                        sx={{ width: '100%' }}
                                                        name='firstName'
                                                        label="First Name"
                                                        onBlur={formik.handleBlur}
                                                        value={formik.values.firstName}
                                                        onChange={formik.handleChange}
                                                        error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                                                        helperText={formik.touched.firstName && formik.errors.firstName}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} md={5.8}>
                                                    <TextFIeldComponent
                                                        sx={{ width: '100%' }}
                                                        value={formik.values.lastName}
                                                        onBlur={formik.handleBlur}
                                                        name='lastName'
                                                        label="Last Name"
                                                        onChange={formik.handleChange}
                                                        error={formik.touched.lastName && Boolean(formik.errors.lastName)}
                                                        helperText={formik.touched.lastName && formik.errors.lastName}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} md={5.8}>
                                                    <TextFIeldComponent
                                                        sx={{ width: '100%' }}
                                                        value={formik.values.email}
                                                        name='email'
                                                        type={'email'}
                                                        label="Email"
                                                        onChange={formik.handleChange}
                                                        error={formik.touched.email && Boolean(formik.errors.email)}
                                                        helperText={formik.touched.email && formik.errors.email}
                                                        onBlur={formik.handleBlur}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} md={5.8}>
                                                    {/* <PhoneInput
                                                        sx={{ width: "100%" }}
                                                        value={formik.values.phone}
                                                        name='phone'
                                                        label="Phone"
                                                        defaultCountry="GB"
                                                        onChange={handlePhoneChange}
                                                        onBlur={formik.handleBlur}
                                                        error={formik.touched.phone && Boolean(formik.errors.phone)}
                                                        helperText={formik.touched.phone && formik.errors.phone}
                                                    /> */}
                                                    <TextFIeldComponent
                                                        sx={{ width: '100%' }}
                                                        name='phone'
                                                        label="Phone"
                                                        onBlur={formik.handleBlur}
                                                        value={formik.values.phone}
                                                        onChange={formik.handleChange}
                                                        error={formik.touched.phone && Boolean(formik.errors.phone)}
                                                        helperText={formik.touched.phone && formik.errors.phone}
                                                    />
                                                </Grid>
                                                <Grid xs={12} md={5.8}>
                                                    <Button
                                                        onClick={openModelPopup}
                                                    >
                                                        Change Password
                                                    </Button>
                                                </Grid>

                                            </Grid>
                                            <Stack direction="row" justifyContent="flex-end" >
                                                <Button
                                                    type='button'
                                                    variant='contained'
                                                    sx={CancelButton}
                                                    color='error'
                                                    onClick={() => setEditing(false)}
                                                >
                                                    Cancel
                                                </Button>
                                                <LoadingButton
                                                    loading={isLoading}
                                                    type='submit'
                                                    variant='contained'
                                                    sx={{ width: '10%', m: 1, mt: 2 }}
                                                >
                                                    Update
                                                </LoadingButton>
                                            </Stack>
                                        </form>
                                    </Box>
                                </Card>
                            </Grid>
                            {/* <Grid xs={12}>
                    <Card
                        sx={style}>
                        <Typography variant='h6' mb={2}>
                            Change Password
                        </Typography>
                        <Grid container wrap='noWrap' gap={2}>
                            <Grid xs={6}>
                                <TextFIeldComponent
                                    sx={{ width: '100%' }}
                                    name='currentPassword'
                                    label="Current Password"
                                // onBlur={formik.handleBlur}
                                // value={formik.values.firstName}
                                // onChange={formik.handleChange}
                                // error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                                // helperText={formik.touched.firstName && formik.errors.firstName}
                                />
                            </Grid>
                            <Grid xs={6}>
                                <TextFIeldComponent
                                    sx={{ width: '100%' }}
                                    name='currentPassword'
                                    label="Current Password"
                                // onBlur={formik.handleBlur}
                                // value={formik.values.firstName}
                                // onChange={formik.handleChange}
                                // error={formik.touched.firstName && Boolean(formik.errors.firstName)}
                                // helperText={formik.touched.firstName && formik.errors.firstName}
                                />
                            </Grid>
                        </Grid>
                    </Card>
                </Grid> */}
                        </Grid> :
                        <Card sx={{ pb: 8, width: '50%' }}>
                            <CardHeader
                                action={
                                    <IconButton onClick={() => setEditing(true)} aria-label="settings">
                                        <Iconify icon={'eva:edit-fill'} />
                                    </IconButton>
                                }
                            // title="Shrimp and Chorizo Paella"
                            // subheader="September 14, 2016"
                            />
                            <Grid container alignItems={'center'} justifyContent={'center'}>
                                {/* <Grid item xs={4} >
                                    <Box sx={{
                                        p: 2,
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    }}>
                                        <img className='user-profile' alt='logo' src={formik.values.profile ? formik.values.profile : noImagePlaceholder} />
                                    </Box>
                                </Grid> */}
                                <Grid item xs={4}>
                                    <Box sx={{
                                        p: 2,
                                        // display: 'flex',
                                        // flexDirection: 'column',
                                        // alignItems: 'center',
                                        // justifyContent: 'center'
                                    }}>
                                        <Typography variant='h4'>
                                            {`${formik.values.firstName} ${formik.values.lastName}`}
                                        </Typography>
                                        <Typography variant='subtitle1'>
                                            {getUserRole(user.role)}
                                        </Typography>
                                        <Stack direction={'row'} gap={0.5} mt={0.8}>
                                            <EmailIcon sx={{ width: 14, color: 'GrayText' }} />
                                            <Typography variant='body2'>
                                                {formik.values.email}
                                            </Typography>
                                        </Stack>
                                    </Box>
                                </Grid>
                            </Grid>
                        </Card>
                    }
                </>
            }
        </Container>
    )
}

export default Profile