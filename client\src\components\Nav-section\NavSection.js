import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';
import { NavLink as RouterLink } from 'react-router-dom';
// @mui
import { Box, Collapse, List, ListItemText } from '@mui/material';
//
import Cookies from 'universal-cookie';
import jwtDecode from 'jwt-decode';
import { useSelector } from 'react-redux';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import { StyledNavItem, StyledNavItemIcon } from './styles';

// ----------------------------------------------------------------------

NavSection.propTypes = {
  data: PropTypes.array,
};
// const cookies = new Cookies();
// const jwtToken = cookies.get("token")
// const Roles = jwtToken && jwtDecode(jwtToken)
// // const role = String(Role.role)
// const Role = Roles?.role?.toString() 

export default function NavSection({ data = [], ...other }) {
  const cookies = new Cookies();
  const jwtToken = cookies.get("token")
  const Roles = jwtToken && jwtDecode(jwtToken)
  const Role = Roles?.role?.toString() || '5'

  return (
    <Box {...other}>
      <List disablePadding sx={{ p: 1 }}>
        {data.map((item) => (
          item?.roles ? (item.roles.includes(Role) && <NavItem key={item.title} item={item} />) : <NavItem key={item.title} item={item} />
        ))}
      </List>
    </Box>
  );
}

// ----------------------------------------------------------------------

NavItem.propTypes = {
  item: PropTypes.object,
};

function NavItem({ item }) {
  const userPermissions = useSelector(state => state.selectedCollege?.currentUserPermissions)
  const { title, path, icon, info } = item;
  const cookies = new Cookies();
  const jwtToken = cookies.get("token")
  const Roles = jwtToken && jwtDecode(jwtToken)
  // const role = String(Role.role)
  const Role = Roles?.role?.toString()
  const CollegePermissions = JSON.parse(localStorage.getItem('currentUserPermissions'))
  const [collegePermissions, setCollegePermissions] = useState(null)
  const actualCollegePermissions = collegePermissions ? {

    // ...collegePermissions,
    app: collegePermissions?.dashboard,
    user: collegePermissions?.users,
    courses: collegePermissions?.courses,
    campuses: collegePermissions?.campuses
  } : {}
  const routesArray = path.split('/')
  const currentRoute = routesArray[routesArray.length - 1]

  const [menuName, setMenuName] = useState("")
  const handleNavClicked = () => {
    if (item.children) {
      setMenuName(menuName === title ? "" : title)
    }
  }
  useEffect(() => {
    // console.log('userPermissions in mav', userPermissions);
    if (userPermissions) {
      setCollegePermissions(userPermissions)
    }
    setCollegePermissions(CollegePermissions)
  }, [userPermissions])

  if (collegePermissions && (Role === '2' || Role === '3' || Role === '4')) {
    return (
      <>
        {(currentRoute in actualCollegePermissions ? actualCollegePermissions[currentRoute] : true) && <StyledNavItem

          onClick={handleNavClicked}
          component={RouterLink}
          to={!item.children && path}
          sx={{
            '&.active': {
              color: !item.children && 'text.primary',
              bgcolor: !item.children && 'action.selected',
              fontWeight: 'fontWeightBold',
            },
          }}
        >
          <StyledNavItemIcon>{icon && icon}</StyledNavItemIcon>

          <ListItemText disableTypography primary={title} />
          {item.children && (menuName !== item.title ? <KeyboardArrowRightIcon /> : <KeyboardArrowDownIcon />)}
          {info && info}
        </StyledNavItem>}
        {item.children && (
          <Collapse in={menuName === item.title} unmountOnExit>
            <NavSection data={item.children} />
          </Collapse>
        )}
      </>
    );
  }
  return (
    <>
      <StyledNavItem
        onClick={handleNavClicked}
        component={RouterLink}
        to={!item.children && path}
        sx={{
          '&.active': {
            color: !item.children && 'text.primary',
            bgcolor: !item.children && 'action.selected',
            fontWeight: 'fontWeightBold',
          },
        }}
      >
        <StyledNavItemIcon>{icon && icon}</StyledNavItemIcon>

        <ListItemText disableTypography primary={title} />
        {item.children && (menuName !== item.title ? <KeyboardArrowRightIcon /> : <KeyboardArrowDownIcon />)}
        {info && info}
      </StyledNavItem>
      {item.children && (
        <Collapse in={menuName === item.title} unmountOnExit>
          <NavSection data={item.children} />
        </Collapse>
      )}
    </>
  );
}

