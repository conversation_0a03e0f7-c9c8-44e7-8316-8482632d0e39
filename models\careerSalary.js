const { Schema, Model, model } = require("mongoose");


const CareerSalarySchema = new Schema({
  regionID: {
    type: Schema.Types.ObjectId,
    ref: "regions",
  },
  socCode: { // first 2 digits
    type: String,
    default: ""
  },
  meanValue: {
    type: Number,
    default: null
  },
  medianValue: {
    type: Number,
    default: null
  },
})

module.exports.CareerSalarySchema = CareerSalarySchema;

class CareerSalary extends Model {

}

model(CareerSalary, CareerSalarySchema, "careerSalary");

module.exports.CareerSalary = CareerSalary;