const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import all test suites
const CompleteSystemTester = require('./testCompleteSystem');
const PerformanceTester = require('./performanceTester');
const ModelQualityTester = require('./modelQualityTester');
const RegressionTester = require('./regressionTester');

/**
 * Master Test Runner
 * Orchestrates all testing suites and provides comprehensive system validation
 */
class MasterTestRunner {
  constructor() {
    this.testResults = {
      masterSummary: {},
      systemTests: null,
      performanceTests: null,
      qualityTests: null,
      regressionTests: null,
      startTime: null,
      endTime: null
    };
  }

  /**
   * Run all test suites
   */
  async runAllTests(options = {}) {
    const {
      includeSystemTests = true,
      includePerformanceTests = true,
      includeQualityTests = true,
      includeRegressionTests = true,
      createBaseline = false,
      quickMode = false
    } = options;

    console.log('🚀 MASTER TEST RUNNER - COMPREHENSIVE SYSTEM VALIDATION');
    console.log('========================================================');
    console.log(`📊 Test Configuration:`);
    console.log(`   System Tests: ${includeSystemTests}`);
    console.log(`   Performance Tests: ${includePerformanceTests}`);
    console.log(`   Quality Tests: ${includeQualityTests}`);
    console.log(`   Regression Tests: ${includeRegressionTests}`);
    console.log(`   Quick Mode: ${quickMode}`);
    console.log(`   Create Baseline: ${createBaseline}`);

    this.testResults.startTime = new Date();

    try {
      // Test Suite 1: Complete System Tests
      if (includeSystemTests) {
        await this.runSystemTestSuite(quickMode);
      }

      // Test Suite 2: Performance Tests
      if (includePerformanceTests) {
        await this.runPerformanceTestSuite(quickMode);
      }

      // Test Suite 3: Model Quality Tests
      if (includeQualityTests) {
        await this.runQualityTestSuite(quickMode);
      }

      // Test Suite 4: Regression Tests
      if (includeRegressionTests) {
        await this.runRegressionTestSuite(createBaseline, quickMode);
      }

      // Generate master summary
      await this.generateMasterSummary();

      this.testResults.endTime = new Date();
      const duration = this.testResults.endTime - this.testResults.startTime;

      console.log('\n🎉 MASTER TEST RUNNER COMPLETED!');
      console.log('=================================');
      console.log(`⏱️ Total Duration: ${Math.round(duration / 1000)}s`);
      console.log(`📊 Overall Status: ${this.getOverallStatus()}`);

      return this.testResults;

    } catch (error) {
      console.error('❌ Master test runner failed:', error);
      throw error;
    }
  }

  /**
   * Run system test suite
   */
  async runSystemTestSuite(quickMode) {
    console.log('\n🔧 RUNNING SYSTEM TEST SUITE');
    console.log('============================');

    try {
      const systemTester = new CompleteSystemTester();
      this.testResults.systemTests = await systemTester.runCompleteTests({
        includeDataPipeline: true,
        includeAPITests: true,
        includeServiceTests: true,
        includeIntegrationTests: true,
        includeStressTests: !quickMode, // Skip stress tests in quick mode
        cleanupAfterTests: true
      });

      console.log(`✅ System Tests: ${this.testResults.systemTests.passedTests}/${this.testResults.systemTests.totalTests} passed`);

    } catch (error) {
      console.error('❌ System test suite failed:', error);
      this.testResults.systemTests = { error: error.message };
    }
  }

  /**
   * Run performance test suite
   */
  async runPerformanceTestSuite(quickMode) {
    console.log('\n⚡ RUNNING PERFORMANCE TEST SUITE');
    console.log('=================================');

    try {
      const performanceTester = new PerformanceTester();
      this.testResults.performanceTests = await performanceTester.runPerformanceTests({
        maxConcurrentUsers: quickMode ? 5 : 10,
        testDurationSeconds: quickMode ? 30 : 60,
        warmupRequests: quickMode ? 3 : 5,
        includeLoadTests: true,
        includeResponseTimeTests: true,
        includeConcurrencyTests: true,
        includeThroughputTests: !quickMode // Skip throughput tests in quick mode
      });

      console.log(`✅ Performance Tests: Completed successfully`);

    } catch (error) {
      console.error('❌ Performance test suite failed:', error);
      this.testResults.performanceTests = { error: error.message };
    }
  }

  /**
   * Run quality test suite
   */
  async runQualityTestSuite(quickMode) {
    console.log('\n🧠 RUNNING MODEL QUALITY TEST SUITE');
    console.log('===================================');

    try {
      const qualityTester = new ModelQualityTester();
      this.testResults.qualityTests = await qualityTester.runModelQualityTests({
        includeQualityTests: true,
        includeAccuracyTests: true,
        includeConsistencyTests: !quickMode, // Skip consistency tests in quick mode
        includeEducationalTests: true,
        testIterations: quickMode ? 2 : 3
      });

      console.log(`✅ Quality Tests: Completed successfully`);

    } catch (error) {
      console.error('❌ Quality test suite failed:', error);
      this.testResults.qualityTests = { error: error.message };
    }
  }

  /**
   * Run regression test suite
   */
  async runRegressionTestSuite(createBaseline, quickMode) {
    console.log('\n🔄 RUNNING REGRESSION TEST SUITE');
    console.log('================================');

    try {
      const regressionTester = new RegressionTester();
      this.testResults.regressionTests = await regressionTester.runRegressionTests({
        createNewBaseline: createBaseline,
        performanceThreshold: 0.2,
        functionalityThreshold: 0.95,
        includePerformanceRegression: true,
        includeFunctionalityRegression: true,
        includeQualityRegression: !quickMode // Skip quality regression in quick mode
      });

      console.log(`✅ Regression Tests: Completed successfully`);

    } catch (error) {
      console.error('❌ Regression test suite failed:', error);
      this.testResults.regressionTests = { error: error.message };
    }
  }

  /**
   * Generate master summary
   */
  async generateMasterSummary() {
    console.log('\n📋 GENERATING MASTER SUMMARY');
    console.log('============================');

    const summary = {
      testDate: new Date(),
      totalDuration: this.testResults.endTime - this.testResults.startTime,
      overallStatus: this.getOverallStatus(),
      testSuiteResults: {
        systemTests: this.summarizeSystemTests(),
        performanceTests: this.summarizePerformanceTests(),
        qualityTests: this.summarizeQualityTests(),
        regressionTests: this.summarizeRegressionTests()
      },
      criticalIssues: this.identifyCriticalIssues(),
      recommendations: this.generateMasterRecommendations(),
      deploymentReadiness: this.assessDeploymentReadiness()
    };

    this.testResults.masterSummary = summary;

    // Save master report
    const reportDir = path.join(__dirname, '../test-reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const reportFile = path.join(reportDir, `master-test-report-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(this.testResults, null, 2), 'utf8');

    console.log(`📋 Master test report saved: ${reportFile}`);

    // Display summary
    this.displayMasterSummary(summary);

    return summary;
  }

  /**
   * Summarize system tests
   */
  summarizeSystemTests() {
    if (!this.testResults.systemTests || this.testResults.systemTests.error) {
      return { status: 'failed', error: this.testResults.systemTests?.error };
    }

    const results = this.testResults.systemTests;
    return {
      status: results.failedTests === 0 ? 'passed' : 'failed',
      totalTests: results.totalTests,
      passedTests: results.passedTests,
      failedTests: results.failedTests,
      successRate: `${((results.passedTests / results.totalTests) * 100).toFixed(1)}%`
    };
  }

  /**
   * Summarize performance tests
   */
  summarizePerformanceTests() {
    if (!this.testResults.performanceTests || this.testResults.performanceTests.error) {
      return { status: 'failed', error: this.testResults.performanceTests?.error };
    }

    const results = this.testResults.performanceTests;
    return {
      status: 'passed',
      averageResponseTime: this.extractAverageResponseTime(results),
      throughput: results.throughputTests?.requestsPerSecond || 'N/A',
      concurrencyHandling: 'Good' // Simplified assessment
    };
  }

  /**
   * Summarize quality tests
   */
  summarizeQualityTests() {
    if (!this.testResults.qualityTests || this.testResults.qualityTests.error) {
      return { status: 'failed', error: this.testResults.qualityTests?.error };
    }

    const results = this.testResults.qualityTests;
    const overallScore = results.summary?.overallQualityScore || 0;
    return {
      status: overallScore > 0.7 ? 'passed' : 'warning',
      overallQualityScore: `${(overallScore * 100).toFixed(1)}%`,
      qualityLevel: overallScore > 0.8 ? 'Excellent' : overallScore > 0.7 ? 'Good' : 'Needs Improvement'
    };
  }

  /**
   * Summarize regression tests
   */
  summarizeRegressionTests() {
    if (!this.testResults.regressionTests || this.testResults.regressionTests.error) {
      return { status: 'failed', error: this.testResults.regressionTests?.error };
    }

    const regressionDetected = this.testResults.regressionTests.summary?.regressionDetected;
    return {
      status: regressionDetected ? 'warning' : 'passed',
      regressionDetected: regressionDetected,
      message: regressionDetected ? 'Regression detected - review before deployment' : 'No regression detected'
    };
  }

  /**
   * Identify critical issues
   */
  identifyCriticalIssues() {
    const issues = [];

    // Check system test failures
    if (this.testResults.systemTests?.failedTests > 0) {
      issues.push({
        severity: 'high',
        category: 'functionality',
        message: `${this.testResults.systemTests.failedTests} system tests failed`
      });
    }

    // Check regression
    if (this.testResults.regressionTests?.summary?.regressionDetected) {
      issues.push({
        severity: 'high',
        category: 'regression',
        message: 'System regression detected'
      });
    }

    // Check quality
    const qualityScore = this.testResults.qualityTests?.summary?.overallQualityScore || 0;
    if (qualityScore < 0.6) {
      issues.push({
        severity: 'medium',
        category: 'quality',
        message: `Low model quality score: ${(qualityScore * 100).toFixed(1)}%`
      });
    }

    return issues;
  }

  /**
   * Generate master recommendations
   */
  generateMasterRecommendations() {
    const recommendations = [];
    const criticalIssues = this.identifyCriticalIssues();

    if (criticalIssues.length === 0) {
      recommendations.push('✅ All tests passed - System is ready for production deployment');
    } else {
      recommendations.push('⚠️ Critical issues detected - Address before deployment:');
      criticalIssues.forEach(issue => {
        recommendations.push(`   • ${issue.message} (${issue.severity} severity)`);
      });
    }

    // Add specific recommendations
    if (this.testResults.systemTests?.failedTests > 0) {
      recommendations.push('• Review and fix failed system tests');
    }

    if (this.testResults.regressionTests?.summary?.regressionDetected) {
      recommendations.push('• Investigate and resolve regression issues');
    }

    const qualityScore = this.testResults.qualityTests?.summary?.overallQualityScore || 0;
    if (qualityScore < 0.7) {
      recommendations.push('• Improve model quality through additional training data');
    }

    return recommendations;
  }

  /**
   * Assess deployment readiness
   */
  assessDeploymentReadiness() {
    const criticalIssues = this.identifyCriticalIssues();
    const highSeverityIssues = criticalIssues.filter(issue => issue.severity === 'high');

    if (highSeverityIssues.length > 0) {
      return {
        ready: false,
        status: 'not_ready',
        message: 'Critical issues must be resolved before deployment',
        blockers: highSeverityIssues.map(issue => issue.message)
      };
    }

    const systemSuccess = this.testResults.systemTests?.passedTests / this.testResults.systemTests?.totalTests || 0;
    if (systemSuccess < 0.95) {
      return {
        ready: false,
        status: 'not_ready',
        message: 'System test success rate below 95%',
        blockers: ['Low system test success rate']
      };
    }

    return {
      ready: true,
      status: 'ready',
      message: 'System is ready for production deployment',
      blockers: []
    };
  }

  /**
   * Get overall status
   */
  getOverallStatus() {
    const criticalIssues = this.identifyCriticalIssues();
    const highSeverityIssues = criticalIssues.filter(issue => issue.severity === 'high');

    if (highSeverityIssues.length > 0) {
      return 'CRITICAL_ISSUES';
    }

    if (criticalIssues.length > 0) {
      return 'WARNINGS';
    }

    return 'PASSED';
  }

  /**
   * Extract average response time
   */
  extractAverageResponseTime(performanceResults) {
    const responseTests = performanceResults.responseTimeTests || {};
    const times = Object.values(responseTests).map(test => test.averageMs).filter(t => t);
    return times.length > 0 ? Math.round(times.reduce((a, b) => a + b, 0) / times.length) : 0;
  }

  /**
   * Display master summary
   */
  displayMasterSummary(summary) {
    console.log('\n📊 MASTER TEST SUMMARY');
    console.log('======================');
    console.log(`🎯 Overall Status: ${summary.overallStatus}`);
    console.log(`🚀 Deployment Ready: ${summary.deploymentReadiness.ready ? 'YES' : 'NO'}`);
    console.log(`⏱️ Total Duration: ${Math.round(summary.totalDuration / 1000)}s`);

    console.log('\n📋 Test Suite Results:');
    Object.entries(summary.testSuiteResults).forEach(([suite, results]) => {
      const status = results.status === 'passed' ? '✅' : results.status === 'warning' ? '⚠️' : '❌';
      console.log(`   ${status} ${suite}: ${results.status.toUpperCase()}`);
    });

    if (summary.criticalIssues.length > 0) {
      console.log('\n⚠️ Critical Issues:');
      summary.criticalIssues.forEach(issue => {
        console.log(`   • ${issue.message} (${issue.severity})`);
      });
    }

    console.log('\n💡 Recommendations:');
    summary.recommendations.forEach(rec => {
      console.log(`   ${rec}`);
    });
  }
}

// Command line interface
const runMasterTests = async () => {
  const args = process.argv.slice(2);
  const runner = new MasterTestRunner();

  const options = {
    includeSystemTests: !args.includes('--skip-system'),
    includePerformanceTests: !args.includes('--skip-performance'),
    includeQualityTests: !args.includes('--skip-quality'),
    includeRegressionTests: !args.includes('--skip-regression'),
    createBaseline: args.includes('--create-baseline'),
    quickMode: args.includes('--quick')
  };

  try {
    const results = await runner.runAllTests(options);
    
    // Exit with error code if critical issues found
    if (results.masterSummary.overallStatus === 'CRITICAL_ISSUES') {
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Master test runner failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runMasterTests();
}

module.exports = MasterTestRunner;
