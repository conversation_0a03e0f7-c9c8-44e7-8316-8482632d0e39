import PropTypes from 'prop-types';
// form
import { useForm<PERSON>ontext, Controller } from 'react-hook-form';
// @mui
import {
  Box,
  Chip,
  // Select,
  Checkbox,
  MenuItem,
  TextField,
  InputLabel,
  FormControl,
  OutlinedInput,
  FormHelperText,
  Typography,
} from '@mui/material';
import Select from 'react-select'

// ----------------------------------------------------------------------

export function RHFSelect({ name, native, maxHeight = 220, helperText, children, control, ...other }) {

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          select
          fullWidth
          className='select-field'
          SelectProps={{
            native,
            MenuProps: {
              PaperProps: {
                sx: {
                  ...(!native && {
                    px: 1,
                    maxHeight: typeof maxHeight === 'number' ? maxHeight : 'unset',
                    '& .MuiMenuItem-root': {
                      px: 1,
                      borderRadius: 0.75,
                      typography: 'body2',
                      textTransform: 'capitalize',
                    },
                  }),
                },
              },
            },
            sx: { textTransform: 'capitalize' },
          }}
          error={!!error}
          helperText={error ? error?.message : helperText}
          {...other}
        >
          {children}
        </TextField>
      )}
    />
  );
}

RHFSelect.propTypes = {
  name: PropTypes.string,
  native: PropTypes.bool,
  children: PropTypes.node,
  helperText: PropTypes.node,
  maxHeight: PropTypes.number,
  control : PropTypes.object
};

// ----------------------------------------------------------------------

export function RHFMultiSelect({
  name,
  chip,
  label,
  options,
  checkbox,
  placeholder,
  helperText,
  sx,
  ...other
}) {
  const { control } = useFormContext();

  const renderValues = (selectedIds) => {
    const selectedItems = options.filter((item) => selectedIds.includes(item.value));

    if (!selectedItems.length && placeholder) {
      return (
        <Box component="em" sx={{ color: 'text.disabled' }}>
          {placeholder}
        </Box>
      );
    }

    if (chip) {
      return (
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {selectedItems.map((item) => (
            <Chip key={item.value} size="small" label={item.label} />
          ))}
        </Box>
      );
    }

    return selectedItems.map((item) => item.label).join(', ');
  };

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <FormControl sx={sx}>
          {label && <InputLabel id={name}> {label} </InputLabel>}

          <Select
            {...field}
            multiple
            displayEmpty={!!placeholder}
            labelId={name}
            input={<OutlinedInput fullWidth label={label} error={!!error} />}
            renderValue={renderValues}
            MenuProps={{
              PaperProps: {
                sx: { px: 1, maxHeight: 280 },
              },
            }}
            {...other}
          >
            {placeholder && (
              <MenuItem
                disabled
                value=""
                sx={{
                  py: 1,
                  px: 2,
                  borderRadius: 0.75,
                  typography: 'body2',
                }}
              >
                <em> {placeholder} </em>
              </MenuItem>
            )}

            {options.map((option) => {
              const selected = field.value.includes(option.value);

              return (
                <MenuItem
                  key={option.value}
                  value={option.value}
                  sx={{
                    py: 1,
                    px: 2,
                    borderRadius: 0.75,
                    typography: 'body2',
                    ...(selected && {
                      fontWeight: 'fontWeightSemiBold',
                    }),
                    ...(checkbox && {
                      p: 0.25,
                    }),
                  }}
                >
                  {checkbox && <Checkbox disableRipple size="small" checked={selected} />}

                  {option.label}
                </MenuItem>
              );
            })}
          </Select>

          {(!!error || helperText) && (
            <FormHelperText error={!!error}>{error ? error?.message : helperText}</FormHelperText>
          )}
        </FormControl>
      )}
    />
  );
}

RHFMultiSelect.propTypes = {
  name: PropTypes.string,
  chip: PropTypes.bool,
  label: PropTypes.string,
  options: PropTypes.array,
  checkbox: PropTypes.bool,
  placeholder: PropTypes.string,
  helperText: PropTypes.node,
  sx: PropTypes.object,
};

export function SelectComponent ({ name, selectOptions, isDisabled, isSearchable,defaultValue, placeholder,value,handleChange, showError, errorMessage, className,...other }) {
  return (
    <>
      <Select
      name = {name}
      value={value}
      options={selectOptions}
      onChange={handleChange}
      isDisabled={isDisabled}
      className={`select-field ${className}`}
      isSearchable={isSearchable}
      defaultValue= {defaultValue}
      placeholder={placeholder}
      isOptionDisabled={(option) => option.disabled}
    />
    {
      showError?
        <Typography variant='error' className='errorNote' color='primary.error'>{errorMessage}</Typography>
      : ''
    }
    </>
  )
}

SelectComponent.propTypes = {
  name: PropTypes.string,
  placeholder: PropTypes.string,
  handleChange: PropTypes.func,
  value: PropTypes.object,
  defaultValue:PropTypes.any,
  showError:PropTypes.bool,
  errorMessage: PropTypes.string,
  selectOptions: PropTypes.array,
  isDisabled: PropTypes.bool,
  isSearchable: PropTypes.bool,
  className: PropTypes.string,
};

export function MultiSelectComponent ({ name,defaultValue, placeholder, native, value,handleChange, maxHeight = 220, helperText, children, errorMessage, showError,...other }) {
  return (
    <>
      <Select
      name = {name}
      value={value}
      displayEmpty
      multiple
      onChange={handleChange}
      className='select-field'
      defaultValue= {defaultValue}
      input={<OutlinedInput id="select-multiple-chip" label="Chip" />}
      renderValue={(selected) => {
        if (selected.length === 0) {
          return <em>{placeholder}</em>;
        }
        return(
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {selected?.map((item) => (
              <Chip key={item} label={item} />
            ))}
          </Box>
      )}}
    >
      {placeholder?
        <MenuItem disabled value="none">
          <em>{placeholder}</em>
        </MenuItem>
        : ''
      }
      
      {children}
    </Select>
    {
      showError?
        <Typography variant='error' className='errorNote' color='primary.error'>{errorMessage}</Typography>
      : ''
    }
  </>
  )
}

MultiSelectComponent.propTypes = {
  name: PropTypes.string,
  native: PropTypes.bool,
  children: PropTypes.node,
  helperText: PropTypes.node,
  maxHeight: PropTypes.number,
  placeholder: PropTypes.string,
  handleChange: PropTypes.func,
  value: PropTypes.array,
  defaultValue:PropTypes.any,
  showError:PropTypes.bool,
  errorMessage: PropTypes.string
};