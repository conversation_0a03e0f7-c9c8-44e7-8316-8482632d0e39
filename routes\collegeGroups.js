const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const collegeGroupsController = require('../controllers/collegeGroups.controller')
const CollegeGroupAdminGuard = require("../guards/collegeGroupAdmin.guard");
const SuperUserGuard = require("../guards/super-user.guard");

router.post("/add", SuperUserGuard, collegeGroupsController.add);

router.get("/get", CollegeGroupAdminGuard, collegeGroupsController.get);

router.get("/getById", CollegeGroupAdminGuard, collegeGroupsController.getByID);

router.delete("/remove", SuperUserGuard, collegeGroupsController.remove);

router.put("/update", CollegeGroupAdminGuard, collegeGroupsController.update);

module.exports = router;