const router = require("express").Router()
const sectorsController = require('../controllers/sectors.controller')
const subsectorsController = require('../controllers/subsectors.controller')
const careersController = require('../controllers/careers.controller')
const frontendController = require('../controllers/frontend.controller')

router.get("/sectors/get", sectorsController.get);

router.get("/subsectors/get", subsectorsController.get);

router.get("/careers/get", careersController.fetchAllData);

router.get("/add/skillAndAbilities", careersController.addSkillsAndAbilities);

router.post("/get/skillDarAndRecommendedCareers", frontendController.getSkilldarAndRecommendedCareers);

module.exports = router