import { useEffect, useRef, useState } from 'react';
import AssignmentIcon from '@mui/icons-material/Assignment';
import { Helmet } from 'react-helmet-async';
import { Box, Button, Container, Typography, Grid, Popover, CircularProgress, useTheme, useMediaQuery } from '@mui/material';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import CloseIcon from '@mui/icons-material/Close';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import StepperComponent from 'src/components/stepper/Stepper';
import SkilldarExplainned1 from 'src/assets/images/skilldar-explained-1.jpg';
import SkilldarExplainned2 from 'src/assets/images/skilldar-explained-2.jpg';
import SkilldarExplainned3 from 'src/assets/images/skilldar1.svg';
import SkilldarExplainned4 from 'src/assets/images/skilldar2.svg';
import { setAlert } from 'src/layouts/main/MainLayoutSlice';
import { useGetSkilldarChartDataMutation } from 'src/pages/Upskill_Flow/UpskillSlice';
import RadarChartComponent from 'src/components/RadarChart/RadarChartComponent';
import axios from 'axios';
import PolarChartComponent from 'src/components/RadarChart/PolarChartComponent';
import SkillCard from 'src/components/RadarChart/SkillCard';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import { ArcticonsEmojiSpiderWeb } from 'src/pages/Reskill_Flow/SpiderIcon';
import RegionStepper from 'src/components/RegionStepper';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';

export default function UpskillSkilldar() {
  const params = useParams();
  const dispatch = useDispatch();
  const [anchorEl, setAnchorEl] = useState(null);
  const [slideNo, setSlideNo] = useState(1);
  const [chartWidth, setChartWidth] = useState('65%');
  const [chartHeight, setChartHeight] = useState('100%');
  const [careerGoalId, setCareerGoalId] = useState('');
  const [careerHistoryId, setCareerHistoryId] = useState();
  const [IP, setIp] = useState("");
  const [UpskillSkilldarSteps, setUpskillSkilldarSteps] = useState();
  const collegeId = useSelector(state => state.mainLayout.collegeDetails?._id);
  const CareerHistoryState = useSelector(state => state.careerHistory.CareerHistory);
  const CareerGoalState = useSelector(state => state.careerHistory.CareerGoal);
  const [getUpskillRadar, upskillSkilldarData] = useGetSkilldarChartDataMutation();
  const navigate = useNavigate();
  const ref = useRef(null);
  const [showLabel, setShowLabel] = useState(false);
  const [openPopup, setOpenPopup] = useState(false);
  const [loadedRadarData, setLoadedRadarData] = useState(false)
  const isRegion = !!params?.rg_name;
  const loadRadar = () => {
    setLoadedRadarData(true)
  }
  const skillDescriptions = [
    {
      title: 'Communication.',
      text: `Communication skills allow you to understand and be understood by others. 
This skills category is made up of Active Listening, Speaking, Writing and Reading Comprehension.`,
    },
    {
      title: 'Problem Solving.',
      text: `Problem solving is the process of identifying and fixing a problem or obstacle to achieve a goal.
This skills category is made up of Critical Thinking, Complex Problem Solving, Judgement and Decision Making and Deductive reasoning.`,
    },
    {
      title: 'Teamwork.',
      text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.`,
    },
    {
      title: 'Organisation.',
      text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.`,
    },
    {
      title: 'Creativity.',
      text: `Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore.`,
    },
    {
      title: 'Learning.',
      text: `Lorem ipsum placeholder for Learning.`,
    },
    {
      title: 'Technical.',
      text: `Lorem ipsum placeholder for Technical.`,
    },
    {
      title: 'Physical.',
      text: `Lorem ipsum placeholder for Physical.`,
    },
    {
      title: 'Manipulation.',
      text: `Lorem ipsum placeholder for Manipulation.`,
    },
    {
      title: 'Leadership.',
      text: `Lorem ipsum placeholder for Leadership.`,
    },
  ];
  const scrollRef = useRef(null)

  const scrollToTop = () => {
    const element = scrollRef?.current
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'end' })
      setOpenPopup(true)
    }
  }
  const getIp = async () => {
    // const res = await axios.get("https://api.ipify.org/?format=json");
    // const Ip = res.data.ip
    const userIp = JSON.parse(localStorage.getItem('userIp'))
    setIp(userIp)
  }
  useEffect(() => {
    getIp()
  }, [])
  useEffect(() => {
    if (openPopup) {
      setTimeout(() => {
        openLabel()
      }, 600);
    }
  }, [openPopup])
  useEffect(() => {
    if (loadedRadarData) {
      setTimeout(() => {
        scrollToTop()
        // openLabel()
      }, 500);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loadedRadarData])
  const closeLabel = () => {
    setShowLabel(false);
    document.body.style.overflow = 'unset';
    window.scrollTo({
      top: 60,
      behavior: "smooth"
    });
  }
  const openLabel = () => {
    setShowLabel(true);
    if (typeof window !== 'undefined' && window.document) {
      document.body.style.overflow = 'unset';
      // window.scrollTo({
      //   top: 60,
      //   behavior: "smooth"
      // });
    }
  }

  const toggleShowSkilldarPopup = (event) => {
    setAnchorEl(event.currentTarget);
    setSlideNo(1);
  };

  const HandleCloseSkilldar = () => {
    setAnchorEl(null);
  };

  const openSkilldarPopup = Boolean(anchorEl);

  const handleChangeSlide = (e) => {
    if (e === 'next') {
      setSlideNo(prev => prev + 1);
    }
    if (e === 'prev') {
      setSlideNo(prev => prev - 1);
    }
  }

  useEffect(() => {
    if (CareerGoalState) {
      setCareerGoalId(CareerGoalState.value ? CareerGoalState.value : '');
    }
  }, [CareerGoalState]) // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (CareerHistoryState && CareerHistoryState.length > 0) {
      const id = CareerHistoryState.map((item) => item.value);
      setCareerHistoryId(id);
    } else {
      navigate(`/${params.cg_name}/career-history`);
      dispatch(setAlert({
        open: true,
        msg: 'Please select your current career again'
      }))
    }
  }, [CareerHistoryState]) // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const handleWindowResize = () => {
      if (window.innerWidth <= 1024 && window.innerWidth > 599) {
        setChartWidth('100%');
        setChartHeight('85%');
      } else if (window.innerWidth <= 599 && window.innerWidth > 399) {
        setChartWidth('100%');
        setChartHeight('54%');
      }
      else if (window.innerWidth <= 399) {
        setChartWidth('100%');
        setChartHeight('53%');
      }
      else {
        setChartWidth('65%');
        setChartHeight('100%')
      }
    };
    if (window.innerWidth <= 1024 && window.innerWidth > 599) {
      setChartWidth('100%');
      setChartHeight('90%');
    } else if (window.innerWidth <= 599 && window.innerWidth > 399) {
      setChartWidth('100%');
      setChartHeight('54%');
    }
    else if (window.innerWidth <= 399) {
      setChartWidth('100%');
      setChartHeight('53%');
    }
    else {
      setChartWidth('65%');
      setChartHeight('100%')
    }

    window.addEventListener('resize', handleWindowResize);

    return () => {
      window.removeEventListener('resize', handleWindowResize);
    };
  }, []);

  useEffect(() => {
    if (params) {
      setUpskillSkilldarSteps([
        {
          label: 'Your Skills',
          link: isRegion ? `/region/${params.rg_name}/upskill/skilldar` : `/${params.cg_name}/upskill/skilldar`
        },
        {
          label: 'Your Careers',
          link: isRegion ? `/region/${params.rg_name}/upskill/career-courses` : `/${params.cg_name}/upskill/career-courses`
        },
        // {
        //   label: 'Colleges',
        //   link: isRegion ? `/region/${params.rg_name}/upskill/colleges`:`/${params.cg_name}/upskill/colleges`
        // },
        {
          label: 'Your Region',
          link: isRegion ? `/region/${params.rg_name}/upskill/regional-info` : `/${params.cg_name}/upskill/regional-info`
        },
      ])
    }
  }, [params, isRegion])
  const theme = useTheme();
  const isXs = useMediaQuery(theme.breakpoints.down("sm"));
  const isSm = useMediaQuery(theme.breakpoints.between("sm", "md"));
  const isMd = useMediaQuery(theme.breakpoints.between("md", "lg"));
  const isLg = useMediaQuery(theme.breakpoints.up("lg"));

  let skilldarHeight = 650;
  if (isXs) skilldarHeight = 500;
  else if (isSm) skilldarHeight = 500;
  else if (isMd) skilldarHeight = 500;
  else if (isLg) skilldarHeight = 650;

  const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
  const isRegionData = !!clgDetails?.region;
  const buttonColor = isRegionData ? clgDetails?.region?.button?.bgColor : '';
  const buttonFontColor = isRegionData ? clgDetails?.region?.button?.color : '';
  const fontColor = isRegionData ? clgDetails?.region?.fontColor : '';
  const bgImage = isRegionData ? clgDetails?.region?.bgImage : '';
  const colorTheme = useTheme();
  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    { icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color={buttonFontColor || 'white'} />, label: 'Results' }  // Capitalized if it's a component
  ];



  return (
    <>
      <Helmet>
        <title>Upskill - Skilldar</title>
      </Helmet>

      <Box
        sx={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed'
        }}
        className='page-content-wrapper'
      >
        <Container maxWidth='xl'>
          <Box pt={2} className='content'>
            {/* <Typography variant='h2' color='primary.black' className='page-head'>Upskill</Typography> */}
            <ThinkSkillsHeader fontColor={isRegionData ? 'white' : ''} />
            <Box pb={2}>
              <RegionStepper
                steps={steps}
                activeStep={1} // Change to 1 to highlight "Results"
                buttonColor={buttonColor || colorTheme.palette.primary.main}
                buttonFontColor={buttonFontColor || 'white'}
              />
            </Box>
            <SecondRegionStepper steps={UpskillSkilldarSteps} activeStep={0} noIcon />
            <Box sx={{ borderRadius: '0 0 12px 12px', mt: 0.2 }} backgroundColor='white'>
              {/* <Typography variant='h2' color='primary.black' >Your Skills</Typography>
                <Typography variant='body1' color='primary.light'>Click on any skill to see its attribute in more detail.</Typography> */}
              <Grid container className="skillchart-wrapper" sx={isRegionData ? { overflow: 'hidden' } : { border: (baseTheme) => `2px solid ${baseTheme.palette.primary.main}`, borderRadius: '15px', overflow: 'hidden' }} ref={ref}>
                <Grid item sm={12} md={12} lg={12} display='flex' justifyContent='center' alignItems='center' className='chart-wrapper'>
                  {careerHistoryId && chartWidth ?
                    <RadarChartComponent
                      width={isXs ? chartWidth : '100%'}
                      height={skilldarHeight}
                      radarChartId={{
                        collegeId,
                        IP,
                        careerIds: careerHistoryId,
                        careerGoal: careerGoalId,
                      }}
                      toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                      skilldar legend
                      radarApiHook={getUpskillRadar}
                      params={params}
                      showlabels
                      disableTooltip
                      loadRadar={loadRadar}
                      closeLabel={closeLabel}
                      openLabel={openLabel}
                      showLabel={showLabel}
                      chartAnimation
                    />
                    :
                    <CircularProgress />
                  }
                  {/* {
                       careerHistoryId && chartWidth ?
                       <PolarChartComponent
                       loadRadar ={loadRadar}
                       reskillChart 
                       width={chartWidth}
                       height={chartHeight}
                       radarChartId={{
                         IP,
                         careerIds: careerHistoryId,
                         careerGoal: '',
                         collegeId
                       }}
                       closeLabel={closeLabel}
                       openLabel={openLabel}
                       toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                       skilldar legend 
                       radarApiHook={getUpskillRadar}
                       handleClick={handleClick}
                       showlabels
                       disableTooltip
                       showLabel={showLabel}
                       chartAnimation
                       /> :
                       <CircularProgress /> 
                      } */}
                </Grid>
              </Grid>
            </Box>
            <Box className="content-wrapper">
              <Box sx={{ py: 0 }}>
                {/* Top Section */}
                <Box
                  sx={{
                    backgroundColor: '#f3f3f3',
                    borderRadius: 2,
                    p: 3,
                    mb: 4,
                    textAlign: 'center',
                    // maxWidth: 900,
                    mx: 'auto',
                  }}
                >
                  <Grid container alignItems='center'>
                    <Grid item sm={4}>
                      {careerHistoryId && chartWidth ?
                        <RadarChartComponent
                          width={isXs ? chartWidth : '100%'}
                          height={200}
                          legend={false}
                          radarChartId={{
                            collegeId,
                            IP,
                            careerIds: careerHistoryId,
                            careerGoal: careerGoalId,
                          }}
                          toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                          // skilldar legend
                          radarApiHook={getUpskillRadar}
                          params={params}
                          disableTooltip
                          loadRadar={loadRadar}
                          closeLabel={closeLabel}
                          openLabel={openLabel}
                          showLabel={showLabel}
                          chartAnimation
                        />
                        :
                        <CircularProgress />
                      }
                    </Grid>
                    {/* Radar Chart */}
                    <Grid item sm={8}>
                      <Box sx={{ fontWeight: 'bold', fontSize: '14px !important', mb: 1 }}>
                        Your Skilldar explained
                      </Box>
                      <Box sx={{ fontSize: '12px !important', mb: 2 }}>
                        Your Skilldar is made up of ten universal employability skills, labelled around the edge of the diagram.
                        Your skill level for each skill (high – low) is plotted on each axis.
                        <br /><br />
                        Our Careers database contains skill level information about every career. When you entered your career history,
                        we know precisely how skilled you have needed to be to do these jobs and so we can use this to determine your skill level.
                        <br /><br />
                        Now that we know your skills, we can match you to other careers in our database that require a skill set similar to yours.
                        <br /><br />
                        <b>This is where you truly get to realise your full potential!</b>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>

                {/* Bottom Section */}
                <Box
                  sx={{
                    backgroundColor: '#f3f3f3',
                    borderRadius: 2,
                    p: 3,
                    // maxWidth: 1400,
                    // mx: 'auto',
                  }}
                >
                  <Box sx={{ fontWeight: 'bold', fontSize: '14px !important', textAlign: 'center', mb: 1 }}>
                    Your Skills explained
                  </Box>
                  <Box sx={{ fontSize: '12px !important', textAlign: 'center', mb: 3 }}>
                    Skills are important because they help you be more productive, effective, and successful in your work.
                    Understanding your skills ensures that you are able to choose careers that matched your strengths.
                  </Box>

                  <Grid container spacing={2}>
                    {skillDescriptions.map((skill, index) => (
                      <Grid item xs={12} sm={4} md={3} key={index}>
                        <SkillCard {...skill} />
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box>
            </Box>







            {/* <Box className="col" ref={ref}>
              <Box className='flex' sx={{ justifyContent: 'center', minHeight: !isXs && 650, flexDirection: { md: 'row', sm: 'column', xs: 'column' } }}>
                {careerHistoryId && chartWidth ?
                  <RadarChartComponent
                    width={isXs ? chartWidth : '100%'}
                    height={skilldarHeight}
                    radarChartId={{
                      collegeId,
                      IP,
                      careerIds: careerHistoryId,
                      careerGoal: careerGoalId,
                    }}
                    toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                    skilldar legend
                    radarApiHook={getUpskillRadar}
                    params={params}
                    showlabels
                    disableTooltip
                    loadRadar={loadRadar}
                    closeLabel={closeLabel}
                    openLabel={openLabel}
                    showLabel={showLabel}
                    chartAnimation
                  />
                  :
                  <CircularProgress />
                }
              </Box>
            </Box>
            <Box className="content-wrapper">
            
              <Box sx={{ px: 2, py: 4 }}>
                <Box
                  sx={{
                    backgroundColor: '#f3f3f3',
                    borderRadius: 2,
                    p: 3,
                    mb: 4,
                    textAlign: 'center',
                    maxWidth: 900,
                    mx: 'auto',
                  }}
                >
                  <Grid container alignItems='center' justifyContent='center'>
                    <Grid textAlign='center' item sm={4}>
                      {careerHistoryId && chartWidth ?
                        <RadarChartComponent
                          width={isXs ? chartWidth : '100%'}
                          height={200}
                          legend={false}
                          radarChartId={{
                            collegeId,
                            IP,
                            careerIds: careerHistoryId,
                            careerGoal: careerGoalId,
                          }}
                          toggleShowSkilldarPopup={toggleShowSkilldarPopup}
                          // skilldar legend
                          radarApiHook={getUpskillRadar}
                          params={params}
                          disableTooltip
                          loadRadar={loadRadar}
                          closeLabel={closeLabel}
                          openLabel={openLabel}
                          showLabel={showLabel}
                          chartAnimation
                        />
                        :
                        <CircularProgress />
                      }
                    </Grid>
                    <Grid item sm={8}>
                      <Box sx={{ fontWeight: 'bold', fontSize: '14px !important', mb: 1 }}>
                        Your Skilldar explained
                      </Box>
                      <Box sx={{ fontSize: '12px !important', mb: 2 }}>
                        Your Skilldar is made up of ten universal employability skills, labelled around the edge of the diagram.
                        Your skill level for each skill (high – low) is plotted on each axis.
                        <br /><br />
                        Our Careers database contains skill level information about every career. When you entered your career history,
                        we know precisely how skilled you have needed to be to do these jobs and so we can use this to determine your skill level.
                        <br /><br />
                        Now that we know your skills, we can match you to other careers in our database that require a skill set similar to yours.
                        <br /><br />
                        <b>This is where you truly get to realise your full potential!</b>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>

                <Box
                  sx={{
                    backgroundColor: '#f3f3f3',
                    borderRadius: 2,
                    p: 3,
                    maxWidth: 1400,
                    mx: 'auto',
                  }}
                >
                  <Box sx={{ fontWeight: 'bold', fontSize: '14px !important', textAlign: 'center', mb: 1 }}>
                    Your Skills explained
                  </Box>
                  <Box sx={{ fontSize: '12px !important', textAlign: 'center', mb: 3 }}>
                    Skills are important because they help you be more productive, effective, and successful in your work.
                    Understanding your skills ensures that you are able to choose careers that matched your strengths.
                  </Box>

                  <Grid container spacing={2}>
                    {skillDescriptions.map((skill, index) => (
                      <Grid item xs={12} sm={4} md={3} key={index}>
                        <SkillCard {...skill} />
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              </Box>
            </Box> */}
            <Popover
              id='skilldar-explained'
              // open={openSkilldarPopup}
              open={false}
              anchorEl={anchorEl}
              onClose={HandleCloseSkilldar}
              className='app-popup skilldar'
            >
              <Box className='popup-head'>
                <Typography variant='capitalize2' color='primary.black' className='icon-text'><span>?</span>Skilldar Explained</Typography>
                <Box className='checkbox-field'>
                  <input type='checkbox' id='show-popup' />
                  <label htmlFor='show-popup'>Don't show this again</label>
                </Box>
                <Button className='close-btn' onClick={HandleCloseSkilldar}><CloseIcon /></Button>
              </Box>
              <Box className='content-wrapper'>
                {
                  slideNo === 1 ?
                    <Grid container className='slider-content-wrapper'>
                      <Grid xs={7} sm={7} md={7} lg={8} item className='left-col content-col'>
                        <img src={SkilldarExplainned1} alt='Skilldar Explainned' />
                      </Grid>
                      <Grid xs={5} sm={5} md={5} lg={4} item className='right-col content-col' sx={{ justifyContent: 'space-between' }}>
                        <Typography variant='body1' color='primary.light'>
                          Your Skilldar is made up of ten universal employability skills, with each one represented around its edge.
                        </Typography>
                        <Typography variant='body1' color='primary.light'>
                          Career skill level is represented by the points plotted against each skill axis.
                        </Typography>
                      </Grid>
                    </Grid>
                    : ''
                }
                {
                  slideNo === 2 ?
                    <Grid container className='slider-content-wrapper'>
                      <Grid xs={7} sm={7} md={7} lg={8} item className='left-col content-col'>
                        <img src={SkilldarExplainned2} alt='Skilldar Explainned' />
                      </Grid>
                      <Grid xs={5} sm={5} md={5} lg={4} item className='right-col content-col'>
                        <Typography variant='body1' color='primary.light' className='bottom-spacing'>
                          The level of each skill, from low to high, is calculated by combining the values of a skill's attributes.
                        </Typography>
                        <Typography variant='body1' color='primary.light'>
                          Click on any skill to see its attributes in more detail.
                        </Typography>
                      </Grid>
                    </Grid>
                    : ''
                }
                {
                  slideNo === 3 ?
                    <Grid container className='slider-content-wrapper'>
                      <Grid xs={7} sm={7} md={7} lg={6} item className='left-col content-col'>
                        <img src={SkilldarExplainned3} alt='Skilldar Explainned' />
                      </Grid>
                      <Grid xs={5} sm={5} md={5} lg={6} item className='right-col content-col'>
                        <Typography variant='body1' color='primary.light'>
                          Our careers database contains skill level information about every career, so we know the precise skill requirements needed to perform your current role.
                        </Typography>
                      </Grid>
                    </Grid>
                    : ''
                }
                {
                  slideNo === 4 ?
                    <Grid container className='slider-content-wrapper' flexDirection='column'>
                      <Grid lg={10} item className='left-col content-col' sx={{ marginBottom: '30px' }}>
                        <img src={SkilldarExplainned4} alt='Skilldar Explainned' />
                      </Grid>
                      <Grid lg={10} item className='right-col content-col' sx={{ height: 'unset !important' }}>
                        <Typography variant='body1' color='primary.light' sx={{ textAlign: 'center !important' }}>
                          Now we know your required career skills, and those of your career goal (if you selected one), we can look for courses that will help you upskill.
                        </Typography>
                      </Grid>
                    </Grid>
                    : ''
                }
              </Box>
              <Box className='popup-slider-control' >
                <Button onClick={() => handleChangeSlide('prev')} className={`${slideNo <= 1 ? 'disable' : ''}`}><ChevronLeftIcon /></Button>
                <Box className='dots-wrapper'>
                  <span className={`dot ${slideNo === 1 ? 'active' : ''}`} />
                  <span className={`dot ${slideNo === 2 ? 'active' : ''}`} />
                  <span className={`dot ${slideNo === 3 ? 'active' : ''}`} />
                  <span className={`dot ${slideNo === 4 ? 'active' : ''}`} />
                </Box>
                <Button onClick={() => handleChangeSlide('next')} className={`${slideNo >= 4 ? 'disable' : ''}`}><ChevronRightIcon /></Button>
              </Box>
            </Popover>
            {/* <Box className="btn-wrapper">
              <Button className='btn noUppercase' sx={{backgroundColor: '#7040f1 !important'}} onClick={()=>navigate(`/${params.cg_name}/upskill/career-courses`)}><Typography color='primary.white'>Unlock your full potential</Typography></Button>
            </Box> */}
          </Box>
        </Container>
      </Box>
    </>
  );
}