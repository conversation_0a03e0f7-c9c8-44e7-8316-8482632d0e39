import { Box, CircularProgress, Container } from '@mui/material';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useParams } from 'react-router';
import { useSearchParams } from 'react-router-dom';
import AuthWrapper from 'src/components/AuthWrapper';
import BarsComponent from 'src/components/bar/BarsComponent';
import { setAlert, useGetClgBySlugQuery } from 'src/layouts/main/MainLayoutSlice';
import axiosInstance from 'src/utils/axiosInstance';
import { getWidgetCareers } from '../CareerHistory/CareerHistorySlice';
import { useGetCareersReskillTimeMutation } from '../Reskill_Flow/ReskillSlice';
import ChatBot from './ChatBot';
import VideoComponent from './VideoComponent';
import WidgetStepper from './WidgetStepper';

const careerHistoryId = ['64ad551627a3095a17a94970', '64957bc29d014f9c6ffd6d05'];

const Widget = () => {
  const widgetsSteps = [
    {
      label: 'Think Careers',
      stepIndex: 0,
    },
    {
      label: 'Watch Our Imersive Video',
      stepIndex: 1,
    },
    {
      label: 'Chat with our AI BOT',
      stepIndex: 2,
    },
  ];
  const [searchParams, setSearchParams] = useSearchParams();
  const stepFromURL = parseInt(searchParams.get('step'), 10) || 0;
  const [currentStep, setCurrentStep] = useState(stepFromURL);

  const collegeName = searchParams.get('collegeName');
  const courseId = searchParams.get('courseId');
  const widgetType = searchParams.get('widgetType');
  const handleStepChange = (stepIndex) => {
    setCurrentStep(stepIndex);
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev);
      newParams.set('step', stepIndex);
      return newParams;
    });
  };
  const staticURL = 'https://player.vimeo.com/video/669377715';
  const staticData = [
    {
      name: 'Technical Production',
      color: '#F04E66',
      careers: [
        {
          id: '6491c32999f80a080a403826',
          name: 'Audio and Video Technician',
          salary: 31200,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 7,
            },
            {
              id: '1.B.1.b',
              name: 'Investigative',
              value: 5,
            },
          ],
          coursesAvailable: 4,
          careerType: 'NORMAL',
        },
        {
          id: '6491c8c499f80a080a4038d5',
          name: 'Camera Operator - Television, Video, & Film',
          salary: 31200,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 6.33,
            },
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 6,
            },
          ],
          coursesAvailable: 4,
          careerType: 'NORMAL',
        },
        {
          id: '6491c96f99f80a080a403987',
          name: 'Film and Video Editor',
          salary: 50440,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 6.67,
            },
            {
              id: '1.B.1.b',
              name: 'Investigative',
              value: 3.67,
            },
          ],
          coursesAvailable: 4,
          careerType: 'NORMAL',
        },
        {
          id: '6491ca6099f80a080a403a32',
          name: 'Media Technical Manager or Director',
          salary: 50440,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.e',
              name: 'Enterprising',
              value: 7,
            },
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 4,
            },
          ],
          coursesAvailable: 4,
          careerType: 'NORMAL',
        },
        {
          id: '6491cafc99f80a080a403adb',
          name: 'Sound Engineering Technician',
          salary: 31200,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 6.67,
            },
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 5,
            },
          ],
          coursesAvailable: 4,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Editorial Production',
      color: '#F5AE1B',
      careers: [
        {
          id: '6491bcdc99f80a080a403258',
          name: 'Producer or Director',
          salary: 50440,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.e',
              name: 'Enterprising',
              value: 6.67,
            },
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 6,
            },
          ],
          coursesAvailable: 3,
          careerType: 'NORMAL',
        },
        {
          id: '6491bd5199f80a080a4032f3',
          name: 'Talent Director',
          salary: 50440,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.e',
              name: 'Enterprising',
              value: 7,
            },
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 5.67,
            },
          ],
          coursesAvailable: 3,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Dance',
      color: '#25B681',
      careers: [
        {
          id: '6491b15f99f80a080a403105',
          name: 'Choreographer',
          salary: 39520,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 7,
            },
            {
              id: '1.B.1.d',
              name: 'Social',
              value: 5,
            },
          ],
          coursesAvailable: 1,
          careerType: 'NORMAL',
        },
        {
          id: '6491bbdb99f80a080a4031b8',
          name: 'Dancer',
          salary: 39520,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 7,
            },
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 5.33,
            },
          ],
          coursesAvailable: 1,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Broadcasting',
      color: '#F04E66',
      careers: [
        {
          id: '6491af8699f80a080a402f8d',
          name: 'Broadcast Technician',
          salary: 31200,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 7,
            },
            {
              id: '1.B.1.f',
              name: 'Conventional',
              value: 5.33,
            },
          ],
          coursesAvailable: 1,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Acting',
      color: '#25B681',
      careers: [
        {
          id: '6491ae0499f80a080a402ef7',
          name: 'Actor',
          salary: 58240,
          transferWindowMin: 0,
          transferWindowMax: 1,
          transferWindow: 'up to 1 year',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 6.67,
            },
            {
              id: '1.B.1.e',
              name: 'Enterprising',
              value: 4.67,
            },
          ],
          coursesAvailable: 2,
          careerType: 'CURRENT_ROLE',
        },
      ],
    },
    {
      name: 'Photography',
      color: '#3585C7',
      careers: [
        {
          id: '6491c0b799f80a080a40360b',
          name: 'Photographer',
          salary: 31200,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 6,
            },
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 4.67,
            },
          ],
          coursesAvailable: 5,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Set Design',
      color: '#D1227C',
      careers: [
        {
          id: '6491c28299f80a080a403767',
          name: 'Set and Exhibit Designer',
          salary: 31200,
          transferWindowMin: 4,
          transferWindowMax: 5,
          transferWindow: '4+ years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 7,
            },
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 5,
            },
          ],
          coursesAvailable: 2,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Costume & Makeup',
      color: '#8EB34C',
      careers: [
        {
          id: '6491b0e999f80a080a403056',
          name: 'Makeup Artist - Theatrical or Performance',
          salary: 18720,
          transferWindowMin: 1,
          transferWindowMax: 2,
          transferWindow: '1 - 2 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 7,
            },
            {
              id: '1.B.1.a',
              name: 'Realistic',
              value: 5,
            },
          ],
          coursesAvailable: 1,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Music',
      color: '#204497',
      careers: [
        {
          id: '6491bf7299f80a080a40355e',
          name: 'Musician or Singer',
          salary: 42120,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 6.83,
            },
            {
              id: '1.B.1.e',
              name: 'Enterprising',
              value: 4.17,
            },
          ],
          coursesAvailable: 2,
          careerType: 'NORMAL',
        },
      ],
    },
    {
      name: 'Presenting',
      color: '#3585C7',
      careers: [
        {
          id: '6491c17199f80a080a4036bb',
          name: 'Broadcast Announcer or Radio Disc Jockey',
          salary: 58240,
          transferWindowMin: 2,
          transferWindowMax: 4,
          transferWindow: '2 - 4 years',
          compare: false,
          interests: [
            {
              id: '1.B.1.c',
              name: 'Artistic',
              value: 5.67,
            },
            {
              id: '1.B.1.e',
              name: 'Enterprising',
              value: 5.33,
            },
          ],
          coursesAvailable: 2,
          careerType: 'NORMAL',
        },
      ],
    },
  ];
  const [hasError, setHasError] = useState(false);
  const [careersReskillTime, setCareersReskillTime] = useState([]);
  const params = useParams();
  const cg_name = params?.cg_name;
  const hasErrorRef = useRef(false);
  const [getCareersReskillTime, status] = useGetCareersReskillTimeMutation();

  const {
    data: clgBySlug,
    error: slugError,
    isLoading: slugLoading,
    isError,
  } = useGetClgBySlugQuery(cg_name, {
    skip: !cg_name || hasErrorRef.current,
    retry: 0, // Important!
    refetchOnMountOrArgChange: false,
  });
  const dispatch = useDispatch();
  useEffect(() => {
    if ((isError || slugError?.status === 'FETCH_ERROR') && !hasErrorRef.current) {
      hasErrorRef.current = true;
    }
  }, [isError, slugError, hasErrorRef]);
  const careerIds = useMemo(() => {
    const widgetSearchParams = new URLSearchParams(window.location.search);
    return widgetSearchParams.get('career_ids')?.split(',') || [];
  }, []);
  const careerurl = useMemo(() => {
    const widgetSearchParams = new URLSearchParams(window.location.search);
    return widgetSearchParams.get('url') || '';
  }, []);
  const [careerLoading, setCareerLoading] = useState(false);
  useEffect(() => {
    if (careerurl) {
      const url = careerurl || 'https://www.howcollege.ac.uk/courses/business-and-management/4657/level-5-hnd-in-leadership-and-management-htq-full-time/';
      const fetchCareers = async () => {
        try {
          setCareerLoading(true);
          const response = await axiosInstance({
            url: `frontend/getWidgetCareers?url=${url}`,
            method: "POST",
          })
          setCareersReskillTime(response.data?.data)
          // localdata set
          return response.data
        } catch (error) {
          console.log("error get regions", error)
          dispatch(
            setAlert({
              open: true,
              msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong',
            })
          )
          return error;
        }finally{
          setCareerLoading(false);
        }
      }

      // getCareersReskillTime(data)
      fetchCareers(url);
      // .then((payload) => setCareersReskillTime(payload))
      //   .catch((error) =>
      //     dispatch(
      //       setAlert({
      //         open: true,
      //         msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong',
      //       })
      //     )
      //   );
    }
  }, [clgBySlug, careerurl, dispatch]);

  return (
    <AuthWrapper title="Widget">
      <Box className="page-content-wrapper">
        <Container>
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            // mt={3}
            height="100vh"
          >
            <Box
              sx={{
                border: '1px solid #ccc',
                borderRadius: '0 0 8px 8px !important',
                p: 0,
                mt: 0,
                width: 600,
                maxHeight: 800,
                height: 'auto',
              }}
            >
              <WidgetStepper currentStep={currentStep} onStepChange={handleStepChange} />
              <Box sx={{ height: '600px', mt: 0, display: currentStep === 0 ? 'block' : 'none' }}>
                {status?.isLoading || careerLoading && (
                  <Box
                    display="flex"
                    flexDirection="column"
                    alignItems="center"
                    justifyContent="center"
                    // mt={3}
                    height="600px"
                  >
                    <CircularProgress />
                  </Box>
                )}
                <>
                  {careersReskillTime?.sectorOrSubsector?.length > 0 && (
                    <BarsComponent
                      data={careersReskillTime?.sectorOrSubsector}
                      // data={careersUpskillTime?.sectorOrSubsector}
                      // addToCompare={addToCompare}
                      // compareCareersState={compareCareersState}
                      // showCareerDetailsPopup={showCareerDetailsPopup}
                      widthClass="bar-wrapper"
                      isWidget
                    />
                  )}
                </>
                <>
                  {careerIds?.length <= 0 && (
                    <BarsComponent
                      data={[]}
                      widthClass="bar-wrapper"
                      isWidget
                    />
                  )}
                </>
              </Box>
              <Box sx={{ mt: 0, display: currentStep === 1 ? 'block' : 'none' }}>
                <VideoComponent url={staticURL} />
              </Box>
              <Box sx={{ mt: 0, display: currentStep === 2 ? 'block' : 'none' }}>
                <ChatBot clgBySlug={clgBySlug} />
              </Box>
            </Box>
          </Box>
        </Container>
      </Box>
    </AuthWrapper>
  );
};

export default Widget;
