export const SkilldarChartData = {
    series: [
        {
            "name": "Computer Programmers",
            "data": [
                43,
                50,
                53,
                39,
                37,
                51,
                35,
                0,
                45,
                0
            ],
            "color": "#F9B5D7"
        }
    ],
    options: {
      
        "xaxis": {
            "categories": [
                "Leadership",
                "Communication",
                "Problem Solving",
                "Teamwork",
                "Organisation",
                "Creativity",
                "Learning/Adaptability Skills",
                "Physical Skills",
                "Technical Skills",
                "Manipulation Skills"
            ]
        },
        grid: {
            borderColor: '#3067DE'
        },
        plotOptions: {
            radar: {
            polygons: {
                strokeColors: ['#FD4645', '#FFB55B', '#2AA24A', '#933DA9', '#3067DE'],
            }
            }
        },
    // legend: {
    //     position: 'right',
    //     horizontalAlign: 'right',
    //     floating: true,
    // }
}}

export const skillLevels = [
    {color: '#FD4645', title: 'High'},
    {color: '#FFB55B', title: 'Advanced'},
    {color: '#2AA24A', title: 'Intermediate'},
    {color: '#933DA9', title: 'Basic'},
    {color: '#3067DE', title: 'Low'},
]

export const CareerCoursesChartData = {
    series: [
        {
            "name": "Computer Programmers",
            "data": [
                [
                    27,
                    50440,
                    30
                ]
            ],
            "color": "#24D92B",
            "careers": [
                {
                    "id": "647dbca3d846cec5ff610a72",
                    "name": "",
                    "noOfCourse": 2,
                    "transferWindow": "2 - 4 years"
                }
            ],
            "isCurrentRole": true
        },
        {
            "name": "Network Systems",
            "data": [
                [
                    36,
                    35360,
                    30
                ],
                [
                    42,
                    37960,
                    30
                ],
                [
                    31,
                    48360,
                    30
                ],
                [
                    16,
                    50440,
                    30
                ]
            ],
            "color": "#F916E4",
            "careers": [
                {
                    "id": "647dbf6ad846cec5ff610bd3",
                    "name": "Computer Network Support Specialists",
                    "noOfCourse": 1,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dbf95d846cec5ff610c7f",
                    "name": "Database Administrators",
                    "noOfCourse": 1,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dbff2d846cec5ff610d30",
                    "name": "Information Security Analysts",
                    "noOfCourse": 1,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dc066d846cec5ff610de1",
                    "name": "Telecommunications Engineering Specialists",
                    "noOfCourse": 1,
                    "transferWindow": "1 - 2 years"
                }
            ]
        },
        {
            "name": "Information Management",
            "data": [
                [
                    47,
                    48880,
                    30
                ],
                [
                    41,
                    55640,
                    30
                ]
            ],
            "color": "#17BDFA",
            "careers": [
                {
                    "id": "647dc08bd846cec5ff610e95",
                    "name": "Business Intelligence Analysts",
                    "noOfCourse": 0,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dc993827db749d5a77444",
                    "name": "Computer & Information Systems Managers",
                    "noOfCourse": 0,
                    "transferWindow": "2 - 4 years"
                }
            ]
        },
        {
            "name": "Programming and Software Development",
            "data": [
                [
                    45,
                    50440,
                    30
                ],
                [
                    27,
                    40560,
                    30
                ]
            ],
            "color": "#FF4D1B",
            "careers": [
                {
                    "id": "647dbca3d846cec5ff610a72",
                    "name": "Computer Programmers",
                    "noOfCourse": 2,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dbd93d846cec5ff610b24",
                    "name": "Health Informatics Specialists",
                    "noOfCourse": 2,
                    "transferWindow": "2 - 4 years"
                }
            ]
        },
        {
            "name": "IT Support and Services",
            "data": [
                [
                    7,
                    23400,
                    30
                ],
                [
                    26,
                    35360,
                    30
                ],
                [
                    37,
                    49920,
                    30
                ],
                [
                    44,
                    61880,
                    30
                ],
                [
                    47,
                    33800,
                    30
                ],
                [
                    24,
                    35360,
                    30
                ]
            ],
            "color": "#24265A",
            "careers": [
                {
                    "id": "647dc14cd846cec5ff610f47",
                    "name": "Data Entry Keyers",
                    "noOfCourse": 0,
                    "transferWindow": "up to 1 year"
                },
                {
                    "id": "647dc18ad846cec5ff610fed",
                    "name": "Data Scientists",
                    "noOfCourse": 0,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dc2a7d846cec5ff61106c",
                    "name": "Document Management Specialists",
                    "noOfCourse": 0,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dc2f1d846cec5ff611101",
                    "name": "Information Technology Project Managers",
                    "noOfCourse": 0,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "647dc317d846cec5ff61119b",
                    "name": "Search Marketing Strategists",
                    "noOfCourse": 0,
                    "transferWindow": "2 - 4 years"
                },
                {
                    "id": "64871c1629f8571eec27185c",
                    "name": "Computer User Support Specialists",
                    "noOfCourse": 0,
                    "transferWindow": "1 - 2 years"
                }
            ]
        }
    ],
    // series: [
    //     {
    //         name: 'Registered Nurse',
    //         data: [[6,20000,15]],
    //         color: '#251660',
    //         careerName: ['Health & Social Care']
    //     },
    //     {
    //         name: 'Acute Care Nurse',
    //         data: [[24,70000,12], [30,80000,18]],
    //         color: '#7A33FA',
    //         careerName: ['Health & Social Care', 'Nursing']
    //     },
    //     {
    //         name: 'Cardiology',
    //         data: [[12,50000,20], [42,20000,40]],
    //         color: '#FD1F97',
    //         careerName: ['Health & Social Care', 'Nursing']
    //     },
    //     {
    //         name: 'Dermatology',
    //         data: [[36,60000,22], [12,80000,20]],
    //         color: '#FFAB06',
    //         careerName: ['Health & Social Care', 'Nursing']
    //     },
    //     {
    //         name: 'Orthodontics',
    //         data: [[18,50000,15], [24,30000,23]],
    //         color: '#36B449',
    //         careerName: ['Health & Social Care', 'Nursing']
    //     },
    //     {
    //         name: 'Nursing & Midwifery',
    //         data: [[48,60000,25], [18,20000,16]],
    //         color: '#00ADEF',
    //         careerName: ['Health & Social Care', 'Nursing']
    //     },
    //     {
    //         name: 'Opthamology',
    //         data: [[24,60000,25], [50,20000,16]],
    //         color: '#0055A6',
    //         careerName: ['Health & Social Care', 'Nursing']
    //     },
    // ],
    options: {
        xaxis: {
            tickAmount: 20,
            type: 'category',
            min: 3,
            max: 60,
            title: {
                text: 'Transfer Window',
                style: {
                    color: '#1f1f1f',
                    fontSize: '16px',
                    fontFamily: 'Public Sans,sans-serif'
                },
            },
            subTitle: {
                text: 'Average Salary',
                style: {
                    color: '#1f1f1f',
                    fontSize: '16px',
                    fontFamily: 'Public Sans,sans-serif'
                },
            }
        },
        yaxis: {
            tickAmount: 20,
            max: 100000,
            min: 15000,
            title: {
                text: 'Average Salary',
                style: {
                    color: '#1f1f1f',
                    fontSize: '16px',
                    fontFamily: 'Public Sans,sans-serif'
                },
            }
        },
        stroke: {
            show: true,
            curve: 'smooth',
            colors: ['#fff'],
            width: 3,
            dashArray: 0, 
        },
        fill: {
            opacity: 0.6
        },
        dataLabels: {
            enabled: false
        },
        legend: {
            position: 'right',
            horizontalAlign: 'center',
            offsetX: -40,
            offsetY: 120,
            width: 320
        },
}}

export const CareerCoursesListData = [
    {
        id:1,
        title: 'Resgistered Nurse',
        currentRole: true,
        subSectorColor: '#FD1F97',
        subSector: 'Health & Social Care',
        price: '£22,000',
        timePeriod: '1 year',
        coursesAvailable: 5,
        addToComparison: false,
        averageWeeklyHours: 35,
        overallGrowth: 'Growth',
        regionalGrowth: 'Growth',
        levelRange: 'Level 2 - HND',
        courseLength: '3 months - 2 years'
    },
    {
        id:2,
        title: 'Head Teacher',
        subSectorColor: '#5C93E9',
        subSector: 'Education Sector',
        price: '£120,000',
        timePeriod: '6 years',
        coursesAvailable: 3,
        addToComparison: false,
        averageWeeklyHours: 20,
        overallGrowth: 'Decline',
        regionalGrowth: 'Decline',
        levelRange: 'Level 2 - HND',
        courseLength: '9 months - 5 years'
    },
    {
        id:3,
        title: 'Cabin Crew',
        subSectorColor: '#5C93E9',
        subSector: 'Sports, Leisure & Tourism Sector',
        price: '£47,000',
        timePeriod: '3 weeks',
        coursesAvailable: 8,
        addToComparison: false,
        averageWeeklyHours: 25,
        overallGrowth: 'Growth',
        regionalGrowth: 'Growth',
        levelRange: 'Level 2 - HND',
        courseLength: '3 months - 2 years'
    },
]

export const filters = [
    {color: '#FD1F97', title: 'Cardiology'},
    {color: '#FFAB04', title: 'Dermatology'},
    {color: '#36B449', title: 'Orthodontics'},
    {color: '#00ADEF', title: 'Nursing & Midwifery'},
    {color: '#0055A6', title: 'Opthalmology'},
    {color: '#EE1D26', title: 'Pharmaceutical'},
];

export const CareerHistorySteps = [
    {
        label: 'Career History',
    },
    {
        label: 'Skilldar',
    },
    {
        label: 'Career and Upskill time',
    },
]

export const UpskillSteps = {
    skilldar: [
        {
            label: 'Career History',
            link: '/lincoln-college/career-history'
        },
        {
            label: 'Skilldar',
        },
        {
            label: 'Career and Upskill time',
        },
    ],
    CareerCourses : [
        {
            label: 'Career History',
            link: '/lincoln-college/career-history'
        },
        {
            label: 'Skilldar',
            link: '/lincoln-college/upskill/skilldar'
        },
        {
            label: 'Career and Upskill time',
        },
    ]
};

export const ReskillSteps = {
    skilldar:[
        {
            label: 'Career History',
            link: '/lincoln-college/career-history'
        },
        {
            label: 'Skilldar',
        },
        {
            label: 'Career and Upskill time',
        },
    ],
    CareerCourses: [
        {
            label: 'Career History',
            link: '/lincoln-college/career-history'
        },
        {
            label: 'Skilldar',
            link: '/lincoln-college/reskill/skilldar'
        },
        {
            label: 'Career and Upskill time',
        },
    ]
}

export const careerDetailsPopupData = {
    careerName: 'Acute Care Nurses',
    supportingCourses: [
        {
            name: 'FDSC Nursing Associate',
            level: 'Undergraduate',
            duration: '2 years full-time',
            desc: 'Nursing Associates work with registered Nurses to care for patients and service users of different ages and in a range of settings. Nursing Associates are members of the nursing team and are accountable for their own practice. This full-time 2 year nursing associate degree FdSc, with a 50/50 split between theory and practice learning, will prepare you to be a highly skilled autonomous and reflective practitioner who is able to deliver high quality, compassionate, professional and safe care.'
        },
        {
            name: 'Access to Higher Education Health Professions',
            level: 'Undergraduate',
            duration: '2 years full-time',
            desc: 'Nursing Associates work with registered Nurses to care for patients and service users of different ages and in a range of settings. Nursing Associates are members of the nursing team and are accountable for their own practice. This full-time 2 year nursing associate degree FdSc, with a 50/50 split between theory and practice learning, will prepare you to be a highly skilled autonomous and reflective practitioner who is able to deliver high quality, compassionate, professional and safe care.'
        },
        {
            name: 'A Level Psychology',
            level: 'Undergraduate',
            duration: '2 years full-time',
            desc: 'Nursing Associates work with registered Nurses to care for patients and service users of different ages and in a range of settings. Nursing Associates are members of the nursing team and are accountable for their own practice. This full-time 2 year nursing associate degree FdSc, with a 50/50 split between theory and practice learning, will prepare you to be a highly skilled autonomous and reflective practitioner who is able to deliver high quality, compassionate, professional and safe care.'
        },
        {
            name: 'Adult Nursing BSc (Hons)',
            level: 'Undergraduate',
            duration: '2 years full-time',
            desc: 'Nursing Associates work with registered Nurses to care for patients and service users of different ages and in a range of settings. Nursing Associates are members of the nursing team and are accountable for their own practice. This full-time 2 year nursing associate degree FdSc, with a 50/50 split between theory and practice learning, will prepare you to be a highly skilled autonomous and reflective practitioner who is able to deliver high quality, compassionate, professional and safe care.'
        },
    ],
    averageSalary : '£30,000 per year, £576 per week',
    desc : 'Nurses provide general and/or specialised nursing care for the sick, injured and others in need of such care, assist medical doctors with their tasks and work with other healthcare professionals and within teams of healthcare workers.',
    tasks: [
        'Administer medications to patients and monitor patients for reactions or side effects.',
        'Maintain accurate, detailed reports and records',
        "Monitor, record, and report symptoms or changes in patients' conditions.",
        "Provide health care, first aid, immunizations, or assistance in convalescence or rehabilitation in locations such as schools, hospitals, or industry.",
        "Consult and coordinate with healthcare team members to assess, plan, implement, or evaluate patient care plans.",
        "Direct or supervise less-skilled nursing or healthcare personnel or supervise a Monitor of all aspects of patient care, including diet and physical activity.",
        "Instruct individuals, families, or other groups on topics such as health education, disease prevention, or childbirth and develop health improvement programs.",
        "Modify patient treatment plans as indicated by patients' responses and conditions.",
        "Prescribe or recommend drugs, medical devices, or other forms of treatment, such as physical therapy, inhalation therapy, or related therapeutic procedures."
    ],
    averageWeeklyHours: '36 hours',
    transferWindow: {
        duration:'1-2 years',
        disc:'Typical training to become a nurse takes 1-2 years to attain reuired qualifications & experience.'
    },
    growthData:{
        overallGrowth:{
            duration: '2012-2027',
            overallGrowthNumber: '219,172',
            overallGrowthPercentage: 36.2,
        },
        regionalGrowth:{
            year: '2027',
            chartData: {
                series: [{
                    name: 'Regional Growth',
                    data: [80000, 95000, 60000, 30000, 50000, 65000, 70000, 100000, 50000, 40000, 30000]
                  }],
                options: {
                    chart: {
                        type: 'bar',
                        height: 350
                    },
                    plotOptions: {
                        bar: {
                            columnWidth: '70%',
                        },
                    },
                    dataLabels: {
                        enabled: false,
                    },
                    xaxis: {
                        categories: ['FeNW (Eng)', 'Scotland', 'East Midlands', 'Northern Ireland', 'East of England', 'NE (Eng)', 'SW (Eng)', 'Yorkshire & the Humber', 'London', 'SE (Eng)', 'W. Midlands (Eng)'],
                    },
                    yaxis: {
                    min: 20000,
                    max: 120000
                    },
                }
            }
        },
        QualificationGrowth:{
            year: '2027',
            chartData: {
                series: [{
                name: 'Qualificational Growth',
                data: [8000, 9500, 16000, 23000, 20000, 25000, 10000, 18000, 22000]
              }],
              options: {
                chart: {
                    type: 'bar',
                    height: 350
                  },
                  plotOptions: {
                    bar: {
                      columnWidth: '70%',
                    },
                  },
                  dataLabels: {
                    enabled: false
                  },
                  xaxis: {
                    categories: [0, 1, 2, 3, 4, 5, 6, 7, 8],
                  },
                  yaxis: {
                    min: 0,
                    max: 30000
                  },
              }
            }
        },
        workTypeGrowth:{
            year: '2027',
            fullTime: '494,481',
            partTime: '305,410',
            selfEmployeed: '24,892',
        },
    }
}