import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
  Container,
  Grid,
  Typography
} from '@mui/material';
// @mui
import { useFormik } from 'formik';
import { createAsyncThunk } from '@reduxjs/toolkit';
import useLoading from '../../hooks/useLoading';
import { skillAbilitiesRadarCategoryValidationSchema, skillAbilitiesSubRadarCategoryValidationSchema } from '../../utils/validationSchemas';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import AbilitiesForm from './AbilitiesForm';
import { getAbilities, getAbilityById, getSubRadarCat, updateAbility } from './abilitySlice';
import axiosInstance from '../../utils/axiosInstance';
// import { getSectors, postSector, removeSector, updateSector } from './SectorSlice';

// ----------------------------------------------------------------------
export const ABILITY_TABLE_HEAD = [
  { id: 'lmiName', label: 'Name', alignRight: false },
  { id: 'radarCategory', label: 'Radar Category', alignRight: false },
  { id: 'radarSubcategory', label: 'Radar Sub Category', alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];
export const renderRadarCategoryCells = ['lmiName','radarCategory', 'radarSubcategory']

const Abilities = () => {
  const dispatch = useDispatch()
  const abilitiesData = useSelector(state => state.abilities.abilities)
  const abilityId = useSelector(state => state.abilities.abilityId)
  const subRadarCategoryId = useSelector(state => state.abilities.subRadarCat)
  const { status } = useSelector(state => state.abilities)
  const [openModel, setOpenModel] = useState(false);
  const [ability, setAbility] = useState([]);
  const [abilityIdDetails, setAbilityIdDetails] = useState({})
  const [detailsData, setDetailsData] = useState({})
  const loading = useLoading(status)
  // const [loadingHeader,setLoadingHeader] =
  const [radarDetails, setRadarDetails] = useState([])
  const [subRadarDetails, setSubRadarDetails] = useState([])
  const [updateSubRadarDetails, setUpdateSubRadarDetails] = useState([])

  const getRadarDetails = createAsyncThunk('radar-category/get', async (data, { rejectWithValue }) => {
    try {
      const response = await axiosInstance({
        url: "radar-category/get",
        method: "GET",
      })
      setRadarDetails(response.data.data)
      return response.data;
    } catch (error) {
      console.log("err", error);
      return rejectWithValue(error)
    }
  })
  const getSubRadarDetails = createAsyncThunk('sub-radar-category/get', async (data, { rejectWithValue }) => {
    try {
      const response = await axiosInstance({
        url: "sub-radar-category/get",
        method: "GET",
      })
      setSubRadarDetails(response.data.data)
      return response.data;
    } catch (error) {
      console.log("err", error);
      return rejectWithValue(error)
    }
  })

  const formik = useFormik({
    initialValues: {
      id: '',
      lmiId: '',
      lmiName: '',
      radarCategory: '',
      radarSubCategory: '',
    },
    onSubmit: (values) => {
      // setLoading(true)
      const ability = {
        id: values?.id,
        radarCategoryId: values?.radarCategory,
        radarSubcategoryId: values?.radarSubCategory
      }
      dispatch(updateAbility(ability))
        .then(response => {
          if (response?.payload?.success) {
            dispatch(getAbilities());
            dispatch(setSnackbar({
              snackbarOpen: true,
              snackbarType: 'success',
              snackbarMessage: "Succesfully updated ability"
            }))
          } else {
            // error occoured
            const errorMessage = response?.payload?.response?.data?.msg
            dispatch(setSnackbar({
              snackbarOpen: true,
              snackbarType: 'error',
              snackbarMessage: errorMessage || "Something went wrong!"
            }))
          }
        }).finally(() => {
          // setLoading(false)
        })
      handleClose()
    },
    validationSchema: updateSubRadarDetails && updateSubRadarDetails.length ? skillAbilitiesRadarCategoryValidationSchema : skillAbilitiesSubRadarCategoryValidationSchema

  })

  useEffect(() => {
    dispatch(getAbilities());
    dispatch(getRadarDetails())
    dispatch(getSubRadarDetails())

  }, [])
  useEffect(() => {
    setAbility(abilitiesData)
  }, [abilitiesData])

  const handleFilterSearch = (event) => {
    const filteredAbilities = ability.filter(ability => ability.lmiName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
    ability.radarCategory?.toLowerCase().includes(event.target.value.toLowerCase()) ||
    ability.radarSubcategory?.toLowerCase().includes(event.target.value.toLowerCase())
    )
    return filteredAbilities
  };

  const editAbilities = (ability) => {
    dispatch(getAbilityById({ id: ability?._id }))
    setOpenModel(true)
  }

  useEffect(() => {
    const radarCId = formik.values.radarCategory;
    if(radarCId){
      dispatch(getSubRadarCat({id:radarCId}))
    }
    const updateRadarId = []
    if (radarCId) {
      const id = subRadarDetails && subRadarDetails.map((item, id) => {
        if (radarCId === item.radarCategoryId) {
          updateRadarId.push(item)
        }
        return item
      })
    }
    setUpdateSubRadarDetails(updateRadarId)
  }, [formik.values.radarCategory])

  useEffect(()=>{
    if(subRadarCategoryId && subRadarCategoryId.radarSubcategories?.length){
      setUpdateSubRadarDetails(subRadarCategoryId.radarSubcategories)
    }
  },[subRadarCategoryId])

  useEffect(() => {
    setAbilityIdDetails(abilityId)
  }, [abilityId])

  useEffect(() => {
    const data = {
      id: abilityIdDetails && abilityIdDetails?._id,
      lmiId: abilityIdDetails?.lmiId,
      lmiName: abilityIdDetails?.lmiName,
      radarCategory: abilityIdDetails?.radarCategoryId && abilityIdDetails.radarCategoryId,
      radarSubCategory: abilityIdDetails?.radarSubcategoryId && abilityIdDetails.radarSubcategoryId,
    }
    setDetailsData(data)
  }, [abilityIdDetails])


  useEffect(() => {
    if (detailsData.id !== undefined)
      formik.setValues(detailsData)
  }, [detailsData])
  const handleClose = () => {
    setOpenModel(false)
    formik.resetForm()
  };

  return (
    <>
      <Helmet>
        <title> Abilities | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        <PopUp
          open={openModel}
          onClose={handleClose}
          title={"Edit Abilities"}
          maxWidth="md"
        >
          <AbilitiesForm
            formik={formik}
            Groups={radarDetails}
            Groups1={updateSubRadarDetails || subRadarDetails}
            setOpenModel={setOpenModel}
            openModel={openModel}
          />
        </PopUp>
        <Grid container justifyContent={'space-between'}>
          <Grid item lg={12}>
            <Typography variant="h4" gutterBottom mb={3}>
              Abilities
            </Typography>
            <DataTable
              loading={loading}
              TableHead={ABILITY_TABLE_HEAD}
              TableData={ability}
              filterSearch={handleFilterSearch}
              searchLable={"Search..."}
              handleEdit={editAbilities}
              renderCells={renderRadarCategoryCells}
              // handleDelete={handleDeleteSector}
              pagination
              disableDelete={'true'}
            />
          </Grid>
        </Grid>
      </Container>
    </>
  )
}

export default Abilities






