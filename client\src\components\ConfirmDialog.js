import { Button, Dialog, DialogActions, DialogContent, DialogTitle } from "@mui/material";
import { useEffect, useState } from "react";
import { CancelButton, formButton } from "../utils/cssStyles";

const ConfirmDialog = (props) => {
  const { title, children, setOpen, onConfirm, open, selectedField } = props;
  useEffect(() => {
    //  setDeleteDialog(false)

  }, [])
  const handleClose = () => {
    setOpen(false)
  }
  //    const [open,setOpen] = useState(open)
  return (
    <Dialog
      open={open}
      onClose={handleClose}
      fullWidth
      maxWidth={"sm"}
    >
      <DialogTitle id="confirm-dialog">{title}</DialogTitle>
      <DialogContent>{children}</DialogContent>
      <DialogActions>
        <Button
          style={CancelButton}
          variant="contained"
          onClick={handleClose}
          color="error"
          size="small"
        >
          Cancel
        </Button>
        <Button
          style={formButton}
          variant="contained"
          onClick={() => {
            // setOpen(false);
            onConfirm(selectedField);
          }}
          color="secondary"
          size="small"
        >
          Yes
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ConfirmDialog;