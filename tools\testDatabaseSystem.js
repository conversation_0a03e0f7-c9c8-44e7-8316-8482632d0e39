const mongoose = require('mongoose');
const TrainingDataset = require('../models/trainingDataset');
const FineTunedModel = require('../models/fineTunedModel');

/**
 * Comprehensive Database System Test
 * Tests all database functionality without requiring authentication
 */

const testDatabaseSystem = async () => {
  console.log('🧪 Starting Database System Test...');
  console.log('=====================================');

  try {
    // Connect to database if not already connected
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/horizon-ai');
      console.log('✅ Connected to MongoDB');
    }

    let testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };

    // Test 1: Database Models Loading
    console.log('\n📋 Test 1: Database Models Loading');
    try {
      const datasetCount = await TrainingDataset.countDocuments();
      const modelCount = await FineTunedModel.countDocuments();
      console.log(`✅ TrainingDataset model loaded - ${datasetCount} records found`);
      console.log(`✅ FineTunedModel model loaded - ${modelCount} records found`);
      testResults.passed += 2;
      testResults.tests.push({ name: 'Database Models Loading', status: 'PASSED' });
    } catch (error) {
      console.log(`❌ Database models test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Database Models Loading', status: 'FAILED', error: error.message });
    }

    // Test 2: Create Training Dataset
    console.log('\n📋 Test 2: Create Training Dataset');
    try {
      const testDataset = new TrainingDataset({
        name: 'Test Dataset',
        version: 'v1.0.0-test',
        description: 'Test dataset for database validation',
        generationConfig: {
          maxExamplesPerType: 100,
          validationSplit: 0.1,
          examplesPerRecord: 3,
          validateData: true
        },
        files: {
          trainingFile: {
            filename: 'test_training.jsonl',
            path: '/test/path/training.jsonl',
            size: 1024,
            examples: 90,
            checksum: 'test-checksum-123'
          },
          validationFile: {
            filename: 'test_validation.jsonl',
            path: '/test/path/validation.jsonl',
            size: 256,
            examples: 10,
            checksum: 'test-checksum-456'
          }
        },
        statistics: {
          trainingExamples: 90,
          validationExamples: 10,
          totalRecordsProcessed: 50,
          qualityScore: 0.95
        },
        validation: {
          warnings: 2,
          errors: 0,
          warningRate: 0.02,
          qualityScore: 0.95
        },
        tags: ['test', 'validation']
      });

      await testDataset.save();
      console.log(`✅ Training dataset created: ${testDataset._id}`);
      
      // Test virtual properties
      console.log(`✅ Total examples: ${testDataset.totalExamples}`);
      console.log(`✅ File count: ${testDataset.fileCount}`);
      console.log(`✅ Ready for training: ${testDataset.isReadyForTraining()}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Create Training Dataset', status: 'PASSED', id: testDataset._id });

      // Test 3: Create Fine-Tuned Model
      console.log('\n📋 Test 3: Create Fine-Tuned Model');
      const testModel = new FineTunedModel({
        modelName: 'Test Model',
        openAIModelId: 'ft:gpt-4o-mini:test:model:123',
        openAIJobId: 'ftjob-test123',
        baseModel: 'gpt-4o-mini',
        trainingDatasetId: testDataset._id,
        trainingConfig: {
          suffix: 'test-model',
          epochs: 3,
          hyperparameters: {}
        },
        trainingResults: {
          status: 'succeeded',
          trainingTokens: 100000,
          validationLoss: 0.15,
          trainingLoss: 0.12,
          completedAt: new Date()
        },
        version: 'v1.0.0',
        description: 'Test model for database validation',
        tags: ['test', 'validation']
      });

      await testModel.save();
      console.log(`✅ Fine-tuned model created: ${testModel._id}`);
      
      // Test virtual properties and methods
      console.log(`✅ Success rate: ${testModel.successRate}`);
      console.log(`✅ Ready for deployment: ${testModel.isReadyForDeployment()}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Create Fine-Tuned Model', status: 'PASSED', id: testModel._id });

      // Test 4: Model Deployment
      console.log('\n📋 Test 4: Model Deployment');
      const deployedModel = await FineTunedModel.deployModel(testModel._id, 'test-user');
      console.log(`✅ Model deployed successfully: ${deployedModel.modelName}`);
      console.log(`✅ Deployment status: ${deployedModel.deploymentStatus}`);
      console.log(`✅ Deployed at: ${deployedModel.deployedAt}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Model Deployment', status: 'PASSED' });

      // Test 5: Find Deployed Model
      console.log('\n📋 Test 5: Find Deployed Model');
      const foundDeployed = await FineTunedModel.findDeployed();
      if (foundDeployed && foundDeployed._id.toString() === testModel._id.toString()) {
        console.log(`✅ Deployed model found correctly: ${foundDeployed.modelName}`);
        testResults.passed += 1;
        testResults.tests.push({ name: 'Find Deployed Model', status: 'PASSED' });
      } else {
        console.log(`❌ Deployed model not found correctly`);
        testResults.failed += 1;
        testResults.tests.push({ name: 'Find Deployed Model', status: 'FAILED' });
      }

      // Test 6: Model Performance Update
      console.log('\n📋 Test 6: Model Performance Update');
      await testModel.updatePerformance(1200, true, 150);
      await testModel.updatePerformance(1100, true, 120);
      await testModel.updatePerformance(1300, false, 0);
      
      const updatedModel = await FineTunedModel.findById(testModel._id);
      console.log(`✅ Total requests: ${updatedModel.performance.totalRequests}`);
      console.log(`✅ Successful requests: ${updatedModel.performance.successfulRequests}`);
      console.log(`✅ Failed requests: ${updatedModel.performance.failedRequests}`);
      console.log(`✅ Success rate: ${updatedModel.successRate.toFixed(2)}`);
      console.log(`✅ Average response time: ${updatedModel.performance.averageResponseTime}ms`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Model Performance Update', status: 'PASSED' });

      // Test 7: Database Queries
      console.log('\n📋 Test 7: Database Queries');
      
      // Test dataset queries
      const activeDatasets = await TrainingDataset.findActive();
      console.log(`✅ Active datasets found: ${activeDatasets.length}`);
      
      const qualityDatasets = await TrainingDataset.findByQuality(0.9);
      console.log(`✅ High quality datasets found: ${qualityDatasets.length}`);
      
      // Test model queries
      const activeModels = await FineTunedModel.findActive();
      console.log(`✅ Active models found: ${activeModels.length}`);
      
      const succeededModels = await FineTunedModel.findByTrainingStatus('succeeded');
      console.log(`✅ Succeeded models found: ${succeededModels.length}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Database Queries', status: 'PASSED' });

      // Test 8: Cleanup Test Data
      console.log('\n📋 Test 8: Cleanup Test Data');
      await FineTunedModel.findByIdAndDelete(testModel._id);
      await TrainingDataset.findByIdAndDelete(testDataset._id);
      console.log(`✅ Test data cleaned up successfully`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Cleanup Test Data', status: 'PASSED' });

    } catch (error) {
      console.log(`❌ Database operations test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Database Operations', status: 'FAILED', error: error.message });
    }

    // Test Results Summary
    console.log('\n📊 TEST RESULTS SUMMARY');
    console.log('========================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📊 Total: ${testResults.passed + testResults.failed}`);
    console.log(`🎯 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

    console.log('\n📋 Detailed Results:');
    testResults.tests.forEach((test, index) => {
      const status = test.status === 'PASSED' ? '✅' : '❌';
      console.log(`${status} ${index + 1}. ${test.name}: ${test.status}`);
      if (test.error) {
        console.log(`   Error: ${test.error}`);
      }
      if (test.id) {
        console.log(`   ID: ${test.id}`);
      }
    });

    if (testResults.failed === 0) {
      console.log('\n🎉 ALL TESTS PASSED! Database system is working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }

    return testResults;

  } catch (error) {
    console.error('❌ Database system test failed:', error);
    return { passed: 0, failed: 1, tests: [{ name: 'Database System Test', status: 'FAILED', error: error.message }] };
  }
};

// Run test if called directly
if (require.main === module) {
  // Load environment variables
  require('dotenv').config();
  
  testDatabaseSystem()
    .then((results) => {
      console.log('\n🏁 Database system test completed');
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testDatabaseSystem;
