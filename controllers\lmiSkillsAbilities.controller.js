const { default: mongoose } = require("mongoose");
const { getAddedBy, getEditedBy } = require('../tools/database');
const commonHelper = require("../helpers/commonHelper");
const { messageResponse } = require("../helpers/commonHelper");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS } = require("../config/messages");
const { RadarCategory } = require("../models/radarCategory");
const { LMISkillAbility } = require("../models/lmiSkillAbility");
const { RadarSubcategory } = require("../models/radarSubcategory");

const validate = async(req, action) => {
  try {
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }
    }

    if (!req.body.radarCategoryId) {
      return messageResponse(INVALID_MISSING, "Radar Category ID", false, 400, null)
    }

    const selectedParent = await RadarCategory.findById(new mongoose.Types.ObjectId(req.body.radarCategoryId));
    if (!selectedParent) {
      return messageResponse(INVALID_MISSING, "Radar Category", false, 400, null)
    }

    const RadarSubcategories = await RadarSubcategory.find({ radarCategoryId: req.body.radarCategoryId });
    if (RadarSubcategories.length) {
      if (!req.body.radarSubcategoryId) {
        return messageResponse(INVALID_MISSING, "Radar Subcategory ID", false, 400, null)
      }

      const selectedParent1 = RadarSubcategories.find(subcat => subcat._id == req.body.radarSubcategoryId)
      if (!selectedParent1) {
        return messageResponse(INVALID_MISSING, "Radar Subcategory", false, 400, null)
      }
    }

    // let query;
    // if(action == 'edit') {
    //   query = { $and: [ { name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } } ] };
    // }
    // else {
    //   query = { name: req.body.name };
    // }  

    // const existingSubsector = await Subsector.findOne(query);
    // if(existingSubsector != null) {
    //   return messageResponse(DUPLICATE, "", false, 400, null);
    // }

    return { success: true };
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == 'add') {
      // const addedBy = getAddedBy(req);
      // req.body.addedBy = addedBy;

      // const newSubsector = await Subsector.create(req.body)

      // if (!newSubsector) return messageResponse(ADD_ERROR, "Sub Sector", false, 400, null, res)

      // res.status(200).json({ success: true, id: newSubsector._id })
    }
    else {
      const editedBy = getEditedBy(req, 'edit');
      const entry = {
        radarCategoryId: req.body.radarCategoryId,
        radarSubcategoryId: req.body.radarSubcategoryId,
        editedBy: editedBy
      };

      const updateResult = await LMISkillAbility.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, entry, { returnOriginal: false })

      if (!updateResult) return messageResponse(EXIST_PERMISSION, "Skill/Ability", false, 404, null, res)

      return messageResponse(UPDATE_SUCCESS, "Skill/Ability", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

// const add = async(req, res, next=> )
// {
//   return await addOrEdit(req, res, 'add');
// }
// module.exports.add=add;

const get = async(req, res) => {
  try {
    if (!req.query.category) {
      return messageResponse(REQUIRED, "Category", false, 400, null, res)
    }

    const pipeline = [
      { $match: { category: req.query.category } },
      {
        $lookup: {
          from: "radarCategories",
          localField: "radarCategoryId",
          foreignField: "_id",
          as: "radarCategory"
        }
      },
      {
        $lookup: {
          from: "radarSubcategories",
          localField: "radarSubcategoryId",
          foreignField: "_id",
          as: "radarSubcategory"
        }
      },
      { $set: { radarCategory: { $arrayElemAt: ["$radarCategory.name", 0] } } },
      { $set: { radarSubcategory: { $arrayElemAt: ["$radarSubcategory.name", 0] } } },
      { $sort: { 'addedBy.date': -1 } },
    ]
    const aggrResult = await LMISkillAbility.aggregate(pipeline);

    if (!aggrResult.length) return messageResponse(NOT_FOUND, "Skill/Ability", false, 404, null, res)

    return messageResponse(null, "", true, 200, aggrResult, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.get = get;

const getByID = async(req, res) => {
  try {
    // if (!req.query.category) {
    //   return messageResponse(REQUIRED, "Category", false, 400, null, res)
    // }

    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const pipeline = [
      // { $match: { $and: [{ category: { $eq: req.query.category } }, { _id: { $eq: new mongoose.Types.ObjectId(req.query.id) } }] } },
      { $match: { $and: [{ _id: { $eq: new mongoose.Types.ObjectId(req.query.id) } }] } },
      {
        $lookup: {
          from: "radarCategories",
          localField: "radarCategoryId",
          foreignField: "_id",
          as: "radarCategory"
        }
      },
      {
        $lookup: {
          from: "radarSubcategories",
          localField: "radarSubcategoryId",
          foreignField: "_id",
          as: "radarSubcategory"
        }
      },
      { $set: { radarCategory: { $arrayElemAt: ["$radarCategory.name", 0] } } },
      { $set: { radarSubcategory: { $arrayElemAt: ["$radarSubcategory.name", 0] } } },
    ]
    let aggrResult = await LMISkillAbility.aggregate(pipeline);

    if (!aggrResult.length) return messageResponse(NOT_FOUND, "Skill/Ability", false, 404, null, res)

    return messageResponse(null, "", true, 200, aggrResult[0], res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID = getByID;

// const remove = async(req, res) => {
//   try {
//     if (!mongoose.isValidObjectId(req.body.id)) {
//       return messageResponse(REQUIRED, "ID", false, 400, null, res);
//     }

//     const subsector = await Subsector.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

//     if (!subsector) return messageResponse(NOT_FOUND, "Sub Sector", false, 404, null, res);

//     return messageResponse(REMOVE_SUCCESS, "Sub Sector", true, 200, null, res)
//   } catch (error) {
//     commonHelper.doReqActionOnError(error)
//     return messageResponse(SERVER_ERROR, "", false, 500, error, res)
//   }
// };
// module.exports.remove = remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit')
};
module.exports.update = update;