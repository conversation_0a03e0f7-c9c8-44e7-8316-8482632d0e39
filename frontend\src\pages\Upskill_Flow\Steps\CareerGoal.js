import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { Box, Button, Container, Typography, Select, FormControl, MenuItem, InputLabel } from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import StepperComponent from 'src/components/stepper/Stepper';
import { SelectComponent } from 'src/components/hook-form';
import { UpskillSteps } from 'src/assets/data/Dummy';

export default function UpskillCareerGoal() {
    const navigate = useNavigate();
    
    const CareerGoals = [
        {label: 'Accountants & Auditors', value: 1},
        {label: 'Actors', value: 2},
        {label: 'Barbers', value: 3},
        {label: 'Biologists', value: 4},
        {label: 'Carpenters', value: 5},
        {label: 'Chemist', value: 6},
    ];
    const [currentRole, setCurrentRole] = useState();
    const [showError,setShowError] = useState(false);

    const handleClick = () => {
      if(currentRole){
        navigate('/upskill/skilldar');
      }
      else{
        setShowError(true);
      }
    }
    useEffect(()=>{
      if(currentRole){
        setShowError(false);
      }
    }, [currentRole])
  return (
    <>
      <Helmet>
        <title>Upskill - Current Career</title>
      </Helmet>

      <Box className='page-content-wrapper'>
        <Container>
          <Box className='content'>
            <Typography variant='h2' color='primary.black' className='page-head'>Upskill</Typography>
            <StepperComponent steps={UpskillSteps.careerGoals} activeStep={0}/>
            <Box className='content-wrapper career'>
                <Typography variant='h2' color='primary.black' >What is your current role?</Typography>
                <Typography variant='body1' color='primary.light'>Select the closest match from our database, starting with your current role or the one you held most recently if you are unemployed</Typography>
                <SelectComponent native
                name="currentCareer" 
                placeholder="Start typing to find a job title..."
                defaultValue='none'
                value={currentRole} handleChange={(e)=>setCurrentRole(e)}
                showError={showError} errorMessage = 'Please select current role to analyse career skills'
                >
                  {CareerGoals.map((career) => (
                    <MenuItem key={career.value} value={career.label}>
                      {career.label}
                    </MenuItem>
                  ))}
                </SelectComponent>
            </Box>
            <Box className='content-wrapper career'>
                <Typography variant='overline' color='primary.black' >Optional</Typography>
                <Typography variant='h2' color='primary.black' >Do you have career goal?</Typography>
                <Typography variant='body1' color='primary.light'>Are you aiming for a particular role in the future? If so, you can enter that here to see where upskilling may be required to help you achieve it successfully.<br/> This UPSKILL search is limited to roles in the same sector as your current role. If the role you are aiming for is in a different sector you should use our RESKILL tool.</Typography>
                <SelectComponent native
                name="currentCareer" 
                placeholder="Start typing to find a job title..."
                value={currentRole} handleChange={(e)=>setCurrentRole(e)}
                defaultValue='none'
                >
                  {CareerGoals.map((career) => (
                    <MenuItem key={career.value} value={career.label}>
                      {career.label}
                    </MenuItem>
                  ))}
                </SelectComponent>
            </Box>
            <Box className="btn-wrapper">
              <Button className='btn noUppercase' sx={{backgroundColor: '#7040f1 !important'}} onClick={handleClick}><Typography color='primary.white'>Analyse career skills</Typography></Button>
            </Box>
          </Box>
        </Container>
      </Box>
    </>
  );
}