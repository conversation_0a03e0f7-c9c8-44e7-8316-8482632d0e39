import {
  Box,
  Tabs,
  Tab,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { useState } from 'react';

const categoryKeys = ['unitOps', 'broadOps', 'roleOps'];
const categoryLabels = ['Unit', 'Broad', 'Role'];

export default function StatsTabs({ data }) {
  const [tabIndex, setTabIndex] = useState(0);
  const [expandedIndex, setExpandedIndex] = useState(null); // index of expanded accordion in current tab

  const handleChange = (event, newValue) => {
    setTabIndex(newValue);
    setExpandedIndex(null); // reset expanded index when switching tabs
  };

  if (!data || typeof data !== 'object') {
    return (
      <Box sx={{ width: '100%', height: 560, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography variant="h6">No Data Found</Typography>
      </Box>
    );
  }

  const currentKey = categoryKeys[tabIndex];
  const categoryData = data?.[currentKey] || {};
  const valid = categoryData.validEntries || [];
  const invalid = categoryData.invalidEntries || [];

  return (
    <Box sx={{ width: '100%', height: 560 }}>
      <Tabs value={tabIndex} onChange={handleChange} centered>
        {categoryLabels.map((label) => (
          <Tab key={label} label={label} />
        ))}
      </Tabs>

      <Box sx={{ p: 2 }}>
        <Typography variant="h6">Valid Entries</Typography>
        {valid.length > 0 ? (
          <List>
            {valid.map((item, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <CheckCircleIcon color="success" />
                </ListItemIcon>
                <ListItemText primary={item.title} />
              </ListItem>
            ))}
          </List>
        ) : (
          <Typography variant="body2" color="text.secondary">
            No valid entries
          </Typography>
        )}

        <Typography variant="h6" sx={{ mt: 3 }}>
          Invalid Entries
        </Typography>
        {invalid.length > 0 ? (
          invalid.map((item, index) => (
            <Accordion
              key={index}
              expanded={expandedIndex === index}
              onChange={() => setExpandedIndex(expandedIndex === index ? null : index)}
              sx={{ mb: 1 }}
            >
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <ErrorIcon color="error" sx={{ mr: 1 }} />
                <Typography>{item.title}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Alert severity="error">{item.error}</Alert>
              </AccordionDetails>
            </Accordion>
          ))
        ) : (
          <Typography variant="body2" color="text.secondary">
            No invalid entries
          </Typography>
        )}
      </Box>
    </Box>
  );
}
