const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * Performance Testing Suite
 * Tests system performance, load handling, and response times
 */
class PerformanceTester {
  constructor() {
    this.apiBaseURL = process.env.API_BASE_URL || 'http://localhost:3000';
    this.testResults = {
      loadTests: {},
      responseTimeTests: {},
      concurrencyTests: {},
      throughputTests: {},
      startTime: null,
      endTime: null
    };
  }

  /**
   * Run complete performance test suite
   */
  async runPerformanceTests(options = {}) {
    const {
      maxConcurrentUsers = 10,
      testDurationSeconds = 60,
      warmupRequests = 5,
      includeLoadTests = true,
      includeResponseTimeTests = true,
      includeConcurrencyTests = true,
      includeThroughputTests = true
    } = options;

    console.log('⚡ Starting Performance Testing Suite');
    console.log('====================================');
    console.log(`📊 Configuration:`);
    console.log(`   Max Concurrent Users: ${maxConcurrentUsers}`);
    console.log(`   Test Duration: ${testDurationSeconds}s`);
    console.log(`   Warmup Requests: ${warmupRequests}`);

    this.testResults.startTime = new Date();

    try {
      // Warmup phase
      await this.warmupSystem(warmupRequests);

      // Test 1: Response Time Tests
      if (includeResponseTimeTests) {
        await this.runResponseTimeTests();
      }

      // Test 2: Load Tests
      if (includeLoadTests) {
        await this.runLoadTests(maxConcurrentUsers);
      }

      // Test 3: Concurrency Tests
      if (includeConcurrencyTests) {
        await this.runConcurrencyTests(maxConcurrentUsers);
      }

      // Test 4: Throughput Tests
      if (includeThroughputTests) {
        await this.runThroughputTests(testDurationSeconds);
      }

      // Generate performance report
      await this.generatePerformanceReport();

      this.testResults.endTime = new Date();
      const duration = this.testResults.endTime - this.testResults.startTime;

      console.log('\n🎉 Performance Testing Completed!');
      console.log('=================================');
      console.log(`⏱️ Total Duration: ${Math.round(duration / 1000)}s`);

      return this.testResults;

    } catch (error) {
      console.error('❌ Performance testing failed:', error);
      throw error;
    }
  }

  /**
   * Warmup system with initial requests
   */
  async warmupSystem(warmupRequests) {
    console.log(`\n🔥 Warming up system with ${warmupRequests} requests...`);
    
    for (let i = 0; i < warmupRequests; i++) {
      try {
        await this.makeAPICall('GET', '/api/chatbot/admin/status');
        await new Promise(resolve => setTimeout(resolve, 200));
      } catch (error) {
        console.warn(`⚠️ Warmup request ${i + 1} failed:`, error.message);
      }
    }
    
    console.log('✅ System warmup completed');
  }

  /**
   * Test response times for different endpoints
   */
  async runResponseTimeTests() {
    console.log('\n⏱️ Running Response Time Tests...');
    
    const endpoints = [
      { method: 'GET', path: '/api/chatbot/admin/status', name: 'Admin Status' },
      { method: 'POST', path: '/api/chatbot/admin/test-model', name: 'Model Test', data: { testMessage: 'Performance test' } }
    ];

    const responseTimeResults = {};

    for (const endpoint of endpoints) {
      console.log(`   Testing: ${endpoint.name}`);
      const times = [];
      const errors = [];

      for (let i = 0; i < 10; i++) {
        try {
          const startTime = Date.now();
          await this.makeAPICall(endpoint.method, endpoint.path, endpoint.data);
          const endTime = Date.now();
          times.push(endTime - startTime);
        } catch (error) {
          errors.push(error.message);
        }
        
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      if (times.length > 0) {
        responseTimeResults[endpoint.name] = {
          averageMs: Math.round(times.reduce((a, b) => a + b, 0) / times.length),
          minMs: Math.min(...times),
          maxMs: Math.max(...times),
          medianMs: this.calculateMedian(times),
          p95Ms: this.calculatePercentile(times, 95),
          successRate: `${((times.length / (times.length + errors.length)) * 100).toFixed(1)}%`,
          totalRequests: times.length + errors.length,
          errors: errors.length
        };

        console.log(`   ✅ ${endpoint.name}: ${responseTimeResults[endpoint.name].averageMs}ms avg`);
      } else {
        console.log(`   ❌ ${endpoint.name}: All requests failed`);
      }
    }

    this.testResults.responseTimeTests = responseTimeResults;
  }

  /**
   * Test system under increasing load
   */
  async runLoadTests(maxUsers) {
    console.log('\n📈 Running Load Tests...');
    
    const loadLevels = [1, 2, 5, Math.min(10, maxUsers)];
    const loadResults = {};

    for (const userCount of loadLevels) {
      console.log(`   Testing with ${userCount} concurrent users...`);
      
      const startTime = Date.now();
      const promises = [];
      const results = [];

      for (let i = 0; i < userCount; i++) {
        promises.push(this.simulateUserSession(`LoadTest-User-${i + 1}`));
      }

      const sessionResults = await Promise.allSettled(promises);
      const endTime = Date.now();

      const successful = sessionResults.filter(r => r.status === 'fulfilled').length;
      const failed = sessionResults.filter(r => r.status === 'rejected').length;

      loadResults[`${userCount}_users`] = {
        concurrentUsers: userCount,
        totalDurationMs: endTime - startTime,
        successfulSessions: successful,
        failedSessions: failed,
        successRate: `${((successful / userCount) * 100).toFixed(1)}%`,
        averageSessionDurationMs: Math.round((endTime - startTime) / userCount)
      };

      console.log(`   ✅ ${userCount} users: ${loadResults[`${userCount}_users`].successRate} success rate`);
      
      // Wait between load levels
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    this.testResults.loadTests = loadResults;
  }

  /**
   * Test concurrent request handling
   */
  async runConcurrencyTests(maxConcurrency) {
    console.log('\n🔀 Running Concurrency Tests...');
    
    const concurrencyLevels = [5, 10, Math.min(20, maxConcurrency)];
    const concurrencyResults = {};

    for (const concurrency of concurrencyLevels) {
      console.log(`   Testing ${concurrency} concurrent requests...`);
      
      const startTime = Date.now();
      const promises = [];

      for (let i = 0; i < concurrency; i++) {
        promises.push(this.makeAPICall('GET', '/api/chatbot/admin/status'));
      }

      const results = await Promise.allSettled(promises);
      const endTime = Date.now();

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      concurrencyResults[`${concurrency}_concurrent`] = {
        concurrentRequests: concurrency,
        totalDurationMs: endTime - startTime,
        successfulRequests: successful,
        failedRequests: failed,
        successRate: `${((successful / concurrency) * 100).toFixed(1)}%`,
        requestsPerSecond: Math.round((successful / (endTime - startTime)) * 1000)
      };

      console.log(`   ✅ ${concurrency} concurrent: ${concurrencyResults[`${concurrency}_concurrent`].successRate} success, ${concurrencyResults[`${concurrency}_concurrent`].requestsPerSecond} req/s`);
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.testResults.concurrencyTests = concurrencyResults;
  }

  /**
   * Test system throughput over time
   */
  async runThroughputTests(durationSeconds) {
    console.log(`\n🚀 Running Throughput Tests (${durationSeconds}s)...`);
    
    const startTime = Date.now();
    const endTime = startTime + (durationSeconds * 1000);
    let requestCount = 0;
    let successCount = 0;
    let errorCount = 0;
    const responseTimes = [];

    console.log('   Sending continuous requests...');

    while (Date.now() < endTime) {
      try {
        const reqStartTime = Date.now();
        await this.makeAPICall('GET', '/api/chatbot/admin/status');
        const reqEndTime = Date.now();
        
        responseTimes.push(reqEndTime - reqStartTime);
        successCount++;
      } catch (error) {
        errorCount++;
      }
      
      requestCount++;
      
      // Small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 50));
    }

    const actualDuration = Date.now() - startTime;

    this.testResults.throughputTests = {
      testDurationMs: actualDuration,
      totalRequests: requestCount,
      successfulRequests: successCount,
      failedRequests: errorCount,
      requestsPerSecond: Math.round((requestCount / actualDuration) * 1000),
      successfulRequestsPerSecond: Math.round((successCount / actualDuration) * 1000),
      averageResponseTimeMs: responseTimes.length > 0 ? Math.round(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length) : 0,
      successRate: `${((successCount / requestCount) * 100).toFixed(1)}%`
    };

    console.log(`   ✅ Throughput: ${this.testResults.throughputTests.requestsPerSecond} req/s, ${this.testResults.throughputTests.successRate} success rate`);
  }

  /**
   * Simulate a complete user session
   */
  async simulateUserSession(userAgent) {
    try {
      // Step 1: Get system status (user checking if system is available)
      await this.makeAPICall('GET', '/api/chatbot/admin/status');
      
      // Step 2: Test model (simulating admin checking model health)
      await this.makeAPICall('POST', '/api/chatbot/admin/test-model', {
        testMessage: `Performance test from ${userAgent}`
      });
      
      return { success: true, userAgent };
    } catch (error) {
      throw new Error(`User session failed for ${userAgent}: ${error.message}`);
    }
  }

  /**
   * Make API call with timing
   */
  async makeAPICall(method, endpoint, data = null, timeout = 30000) {
    const url = `${this.apiBaseURL}${endpoint}`;
    
    try {
      let response;
      const config = { timeout };
      
      if (method === 'GET') {
        response = await axios.get(url, config);
      } else if (method === 'POST') {
        response = await axios.post(url, data, config);
      } else {
        throw new Error(`Unsupported method: ${method}`);
      }
      
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`HTTP ${error.response.status}: ${error.response.statusText}`);
      } else if (error.code === 'ECONNABORTED') {
        throw new Error('Request timeout');
      } else {
        throw new Error(`Network error: ${error.message}`);
      }
    }
  }

  /**
   * Calculate median of array
   */
  calculateMedian(arr) {
    const sorted = [...arr].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 !== 0 ? sorted[mid] : Math.round((sorted[mid - 1] + sorted[mid]) / 2);
  }

  /**
   * Calculate percentile of array
   */
  calculatePercentile(arr, percentile) {
    const sorted = [...arr].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * Generate performance report
   */
  async generatePerformanceReport() {
    console.log('\n📋 Generating Performance Report...');
    
    const report = {
      summary: {
        testDate: new Date(),
        apiBaseURL: this.apiBaseURL,
        totalDuration: this.testResults.endTime - this.testResults.startTime
      },
      responseTimeTests: this.testResults.responseTimeTests,
      loadTests: this.testResults.loadTests,
      concurrencyTests: this.testResults.concurrencyTests,
      throughputTests: this.testResults.throughputTests,
      recommendations: this.generatePerformanceRecommendations()
    };
    
    // Save report to file
    const reportDir = path.join(__dirname, '../test-reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const reportFile = path.join(reportDir, `performance-test-${Date.now()}.json`);
    fs.writeFileSync(reportFile, JSON.stringify(report, null, 2), 'utf8');
    
    console.log(`📋 Performance report saved: ${reportFile}`);
    return report;
  }

  /**
   * Generate performance recommendations
   */
  generatePerformanceRecommendations() {
    const recommendations = [];
    
    // Check response times
    const responseTests = this.testResults.responseTimeTests;
    Object.entries(responseTests).forEach(([endpoint, results]) => {
      if (results.averageMs > 2000) {
        recommendations.push(`${endpoint} has slow response time (${results.averageMs}ms avg) - consider optimization`);
      }
      if (parseFloat(results.successRate) < 95) {
        recommendations.push(`${endpoint} has low success rate (${results.successRate}) - investigate errors`);
      }
    });
    
    // Check load test results
    const loadTests = this.testResults.loadTests;
    Object.entries(loadTests).forEach(([level, results]) => {
      if (parseFloat(results.successRate) < 90) {
        recommendations.push(`System struggles under ${results.concurrentUsers} concurrent users (${results.successRate} success rate)`);
      }
    });
    
    // Check throughput
    const throughput = this.testResults.throughputTests;
    if (throughput && throughput.requestsPerSecond < 10) {
      recommendations.push(`Low throughput detected (${throughput.requestsPerSecond} req/s) - consider scaling`);
    }
    
    if (recommendations.length === 0) {
      recommendations.push('System performance is within acceptable limits');
    }
    
    return recommendations;
  }
}

// Command line interface
const runPerformanceTests = async () => {
  const args = process.argv.slice(2);
  const tester = new PerformanceTester();

  const options = {
    maxConcurrentUsers: parseInt(args[0]) || 10,
    testDurationSeconds: parseInt(args[1]) || 30,
    warmupRequests: parseInt(args[2]) || 5
  };

  try {
    await tester.runPerformanceTests(options);
  } catch (error) {
    console.error('❌ Performance testing failed:', error);
    process.exit(1);
  }
};

// Run if called directly
if (require.main === module) {
  runPerformanceTests();
}

module.exports = PerformanceTester;
