const mongoose = require('mongoose');
const { cacheService, CACHE_KEYS } = require('../services/cacheService');
const { performanceMonitor } = require('../services/performanceMonitorService');
const MessageProcessingService = require('../services/messageProcessingService');
const FineTunedModel = require('../models/fineTunedModel');
const TrainingDataset = require('../models/trainingDataset');

/**
 * Performance Optimization Test Suite
 * Tests caching, performance monitoring, and response times
 */

const testPerformanceOptimizations = async () => {
  console.log('⚡ Starting Performance Optimization Tests...');
  console.log('===============================================');

  try {
    // Connect to database if not already connected
    if (mongoose.connection.readyState !== 1) {
      await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/horizon-ai');
      console.log('✅ Connected to MongoDB');
    }

    let testResults = {
      passed: 0,
      failed: 0,
      tests: []
    };

    // Test 1: Cache Service Performance
    console.log('\n📋 Test 1: Cache Service Performance');
    try {
      const testKey = 'performance_test_key';
      const testValue = { data: 'test_value', timestamp: Date.now() };
      
      // Test cache set/get
      const startTime = Date.now();
      cacheService.set(testKey, testValue, 60000);
      const cachedValue = cacheService.get(testKey);
      const cacheTime = Date.now() - startTime;
      
      console.log(`✅ Cache operation completed in ${cacheTime}ms`);
      console.log(`✅ Cache value matches: ${JSON.stringify(cachedValue) === JSON.stringify(testValue)}`);
      
      // Test cache stats
      const stats = cacheService.getStats();
      console.log(`✅ Cache size: ${stats.size} entries`);
      console.log(`✅ Memory usage: ${Math.round(stats.memoryUsage / 1024)}KB`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Cache Service Performance', status: 'PASSED', time: cacheTime });
    } catch (error) {
      console.log(`❌ Cache service test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Cache Service Performance', status: 'FAILED', error: error.message });
    }

    // Test 2: Performance Monitor
    console.log('\n📋 Test 2: Performance Monitor');
    try {
      // Reset metrics for clean test
      performanceMonitor.reset();
      
      // Test timing functionality
      const endTiming = performanceMonitor.startTiming('test_operation');
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate 100ms operation
      const duration = endTiming({ success: true, testData: 'sample' });
      
      console.log(`✅ Performance timing recorded: ${duration}ms`);
      
      // Test cache hit/miss recording
      performanceMonitor.recordCacheHit();
      performanceMonitor.recordCacheHit();
      performanceMonitor.recordCacheMiss();
      
      const stats = performanceMonitor.getStats();
      console.log(`✅ Cache hit rate: ${(stats.cache.hitRate * 100).toFixed(1)}%`);
      console.log(`✅ Response time average: ${stats.responseTime.average.toFixed(1)}ms`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Performance Monitor', status: 'PASSED' });
    } catch (error) {
      console.log(`❌ Performance monitor test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Performance Monitor', status: 'FAILED', error: error.message });
    }

    // Test 3: Database Query Caching
    console.log('\n📋 Test 3: Database Query Caching');
    try {
      // Clear cache first
      cacheService.clear();
      
      // First query (should hit database)
      const startTime1 = Date.now();
      const models1 = await FineTunedModel.find({ status: 'active' }).limit(5).lean();
      const dbTime = Date.now() - startTime1;
      
      // Cache the result manually for testing
      cacheService.set('test_models', models1, 60000);
      
      // Second query (should hit cache)
      const startTime2 = Date.now();
      const models2 = cacheService.get('test_models');
      const cacheTime = Date.now() - startTime2;
      
      console.log(`✅ Database query time: ${dbTime}ms`);
      console.log(`✅ Cache retrieval time: ${cacheTime}ms`);
      console.log(`✅ Performance improvement: ${Math.round((dbTime - cacheTime) / dbTime * 100)}%`);
      
      testResults.passed += 1;
      testResults.tests.push({ 
        name: 'Database Query Caching', 
        status: 'PASSED', 
        dbTime: dbTime,
        cacheTime: cacheTime,
        improvement: Math.round((dbTime - cacheTime) / dbTime * 100)
      });
    } catch (error) {
      console.log(`❌ Database caching test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Database Query Caching', status: 'FAILED', error: error.message });
    }

    // Test 4: Cache Wrap Function
    console.log('\n📋 Test 4: Cache Wrap Function Performance');
    try {
      let dbCallCount = 0;
      
      // Simulate expensive database operation
      const expensiveOperation = async () => {
        dbCallCount++;
        await new Promise(resolve => setTimeout(resolve, 200)); // Simulate 200ms DB query
        return { data: 'expensive_result', callCount: dbCallCount };
      };
      
      // First call (should execute function)
      const startTime1 = Date.now();
      const result1 = await cacheService.wrap('expensive_op', expensiveOperation, 60000);
      const firstCallTime = Date.now() - startTime1;
      
      // Second call (should use cache)
      const startTime2 = Date.now();
      const result2 = await cacheService.wrap('expensive_op', expensiveOperation, 60000);
      const secondCallTime = Date.now() - startTime2;
      
      console.log(`✅ First call (DB): ${firstCallTime}ms, DB calls: ${result1.callCount}`);
      console.log(`✅ Second call (Cache): ${secondCallTime}ms, DB calls: ${result2.callCount}`);
      console.log(`✅ Cache prevented ${dbCallCount - 1} unnecessary DB calls`);
      console.log(`✅ Speed improvement: ${Math.round((firstCallTime - secondCallTime) / firstCallTime * 100)}%`);
      
      testResults.passed += 1;
      testResults.tests.push({ 
        name: 'Cache Wrap Function', 
        status: 'PASSED',
        firstCall: firstCallTime,
        secondCall: secondCallTime,
        dbCallsSaved: dbCallCount - 1
      });
    } catch (error) {
      console.log(`❌ Cache wrap test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Cache Wrap Function', status: 'FAILED', error: error.message });
    }

    // Test 5: Message Processing Service Performance
    console.log('\n📋 Test 5: Message Processing Service Performance');
    try {
      const messageService = new MessageProcessingService();
      
      // Test session info caching
      const testSessionId = new mongoose.Types.ObjectId().toString();
      
      // First call (should hit database)
      const startTime1 = Date.now();
      const session1 = await messageService.getSessionInfo(testSessionId);
      const dbTime = Date.now() - startTime1;
      
      // Second call (should hit cache)
      const startTime2 = Date.now();
      const session2 = await messageService.getSessionInfo(testSessionId);
      const cacheTime = Date.now() - startTime2;
      
      console.log(`✅ Session lookup (DB): ${dbTime}ms`);
      console.log(`✅ Session lookup (Cache): ${cacheTime}ms`);
      
      if (cacheTime < dbTime) {
        console.log(`✅ Cache improved performance by ${Math.round((dbTime - cacheTime) / dbTime * 100)}%`);
      }
      
      testResults.passed += 1;
      testResults.tests.push({ 
        name: 'Message Processing Performance', 
        status: 'PASSED',
        dbTime: dbTime,
        cacheTime: cacheTime
      });
    } catch (error) {
      console.log(`❌ Message processing test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Message Processing Performance', status: 'FAILED', error: error.message });
    }

    // Test 6: Cache TTL and Cleanup
    console.log('\n📋 Test 6: Cache TTL and Cleanup');
    try {
      const shortTTL = 1000; // 1 second
      const testKey = 'ttl_test_key';
      const testValue = 'ttl_test_value';
      
      // Set cache with short TTL
      cacheService.set(testKey, testValue, shortTTL);
      
      // Immediately check (should exist)
      const immediate = cacheService.get(testKey);
      console.log(`✅ Immediate retrieval: ${immediate === testValue ? 'SUCCESS' : 'FAILED'}`);
      
      // Wait for expiry
      await new Promise(resolve => setTimeout(resolve, shortTTL + 100));
      
      // Check after expiry (should be null)
      const expired = cacheService.get(testKey);
      console.log(`✅ After expiry: ${expired === null ? 'SUCCESS' : 'FAILED'}`);
      
      testResults.passed += 1;
      testResults.tests.push({ name: 'Cache TTL and Cleanup', status: 'PASSED' });
    } catch (error) {
      console.log(`❌ Cache TTL test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Cache TTL and Cleanup', status: 'FAILED', error: error.message });
    }

    // Test 7: Performance Recommendations
    console.log('\n📋 Test 7: Performance Recommendations');
    try {
      // Generate some test metrics
      performanceMonitor.recordMetric('response', 5000, { slow: true }); // Slow response
      performanceMonitor.recordMetric('database', 1500, { slow: true }); // Slow query
      
      const recommendations = performanceMonitor.getRecommendations();
      console.log(`✅ Generated ${recommendations.length} recommendations`);
      
      recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. [${rec.severity.toUpperCase()}] ${rec.type}: ${rec.message}`);
      });
      
      testResults.passed += 1;
      testResults.tests.push({ 
        name: 'Performance Recommendations', 
        status: 'PASSED',
        recommendations: recommendations.length
      });
    } catch (error) {
      console.log(`❌ Performance recommendations test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Performance Recommendations', status: 'FAILED', error: error.message });
    }

    // Test Results Summary
    console.log('\n📊 PERFORMANCE OPTIMIZATION TEST RESULTS');
    console.log('==========================================');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    console.log(`📊 Total: ${testResults.passed + testResults.failed}`);
    console.log(`🎯 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

    console.log('\n📋 Detailed Results:');
    testResults.tests.forEach((test, index) => {
      const status = test.status === 'PASSED' ? '✅' : '❌';
      console.log(`${status} ${index + 1}. ${test.name}: ${test.status}`);
      if (test.time) console.log(`   Time: ${test.time}ms`);
      if (test.improvement) console.log(`   Improvement: ${test.improvement}%`);
      if (test.dbCallsSaved) console.log(`   DB calls saved: ${test.dbCallsSaved}`);
      if (test.error) console.log(`   Error: ${test.error}`);
    });

    // Final performance stats
    const finalStats = performanceMonitor.getStats();
    console.log('\n📈 Final Performance Statistics:');
    console.log(`   Cache Hit Rate: ${(finalStats.cache.hitRate * 100).toFixed(1)}%`);
    console.log(`   Average Response Time: ${finalStats.responseTime.average.toFixed(1)}ms`);
    console.log(`   Total Cache Hits: ${finalStats.cache.hits}`);
    console.log(`   Total Cache Misses: ${finalStats.cache.misses}`);

    if (testResults.failed === 0) {
      console.log('\n🎉 ALL PERFORMANCE TESTS PASSED! Optimizations are working correctly.');
    } else {
      console.log('\n⚠️ Some tests failed. Please review the errors above.');
    }

    return testResults;

  } catch (error) {
    console.error('❌ Performance test failed:', error);
    return { passed: 0, failed: 1, tests: [{ name: 'Performance Test', status: 'FAILED', error: error.message }] };
  }
};

// Run test if called directly
if (require.main === module) {
  // Load environment variables
  require('dotenv').config();
  
  testPerformanceOptimizations()
    .then((results) => {
      console.log('\n🏁 Performance optimization test completed');
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testPerformanceOptimizations;
