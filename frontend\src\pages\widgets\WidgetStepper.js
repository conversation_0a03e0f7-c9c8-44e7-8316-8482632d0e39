import { Box, Button } from '@mui/material';
import PropTypes from 'prop-types';

const steps = [
  'Think Careers',
  'Watch Our Imersive Video',
  'Chat with our AI BOT',
];

const WidgetStepper = ({ currentStep, onStepChange }) => {
  let a;
  return (
    <Box sx={{ display: 'flex', borderBottom: '1px solid #ccc', }}>
      {steps.map((label, index) => (
        <Button
          key={label}
          onClick={() => onStepChange(index)}
          sx={{
            width: '100%',
            borderRadius: index === steps.length - 1 ? '0 8px 0 0' : '0',
            borderTopLeftRadius: index === 0 ? '8px' : '0',
            borderTopRightRadius: index === steps.length - 1 ? '8px' : '0',
            textTransform: 'none',
            backgroundColor: currentStep === index ? '#2E2E2E' : 'transparent',
            color: currentStep === index ? '#fff' : '#333',
            fontWeight: currentStep === index ? 'bold' : 400,
            border: '1px solid #ccc',
            borderBottom: currentStep === index ? 'none' : '1px solid #ccc',
            px: 2,
            py: 1,
            '&:hover': {
              backgroundColor: currentStep === index ? '#2E2E2E' : '#f5f5f5',
            },
          }}
        >
          {label}
        </Button>
      ))}
    </Box>
  );
};

export default WidgetStepper;
WidgetStepper.propTypes = {
  currentStep: PropTypes.number,
  onStepChange: PropTypes.func
};