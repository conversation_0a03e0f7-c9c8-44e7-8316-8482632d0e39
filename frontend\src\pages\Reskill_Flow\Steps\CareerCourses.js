import { yupResolver } from '@hookform/resolvers/yup';
import AssignmentIcon from '@mui/icons-material/Assignment';
import CloseIcon from '@mui/icons-material/Close';
import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
import KeyboardArrowRightOutlinedIcon from '@mui/icons-material/KeyboardArrowRightOutlined';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Checkbox, Container, Grid, Popover, TextField, Typography, useTheme } from '@mui/material';
import { PDFExport } from "@progress/kendo-react-pdf";
import PropTypes from 'prop-types';
import { useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import { skillLevels } from 'src/assets/data/Dummy';
import CompareCareerComponent from 'src/components/CompareCareer';
import GraphsData from 'src/components/GraphsData';
import Report from 'src/components/HTMLtoPdf/Report';
import RadarChartComponent from 'src/components/RadarChart/RadarChartComponent';
import { analytics, currencyFormat, exportPDFWithMethod } from 'src/utils';
import * as Yup from 'yup';

import DekstopPoup from 'src/components/CareerDetailsPopup/DekstopPoup';
import MobilePopup from 'src/components/CareerDetailsPopup/MobilePopup';
import RegionStepper from 'src/components/RegionStepper';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import { addCompareCareer, setAlert, useGetRegionBySlugQuery, usePostPdfMutation } from 'src/layouts/main/MainLayoutSlice';
import { addSkillReport, useCareerNCoursesDetailsMutation, useCareerNCoursesDetailsRegionMutation, useGetCareersUpskillTimeMutation, useGetCompareCareersDataMutation, useGetSkillsReportDataMutation } from 'src/pages/Upskill_Flow/UpskillSlice';
import { useGetCareersReskillTimeMutation, useGetCareersReskillTimeRegionMutation } from '../ReskillSlice';
import { ArcticonsEmojiSpiderWeb } from '../SpiderIcon';


function TabPanel(props) {
    const { children, value, index, ...other } = props;
    return (
        <div
            role="tabpanel"
            hidden={value !== index}
            id={`simple-tabpanel-${index}`}
            aria-labelledby={`simple-tab-${index}`}
            {...other}
        >
            {value === index && (
                <Box sx={{ p: 3 }}>
                    {children}
                </Box>
            )}
        </div>
    );
}

TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.number.isRequired,
    value: PropTypes.number.isRequired,
};

function a11yProps(index) {
    return {
        id: `simple-tab-${index}`,
        'aria-controls': `simple-tabpanel-${index}`,
    };
}

export default function ReskillCareerCourses() {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const params = useParams();
    const { cg_name, rg_name } = useParams();
    const {
        data: clgByRegion,
        error: regionError,
        isLoading: regionLoading,
    } = useGetRegionBySlugQuery(rg_name, { skip: !rg_name, refetchOnMountOrArgChange: true, });
    const isLoading = regionLoading;
    const errorss = regionError;
    const popupRef = useRef(null);
    const chartRef = useRef(null);
    const reportRef = useRef(null);
    const ref = useRef(null);
    const pdfReportData = useSelector(state => state.upskill.SkillsReport)
    const [compareCareersState, setCompareCareersState] = useState([]);
    const [listFilterValue, setListFilterValue] = useState();
    const [uploadPdf, pdfStatus] = usePostPdfMutation();
    const [IP, setIp] = useState("");
    const [anchorElCompareCareer, setAnchorElCompareCareer] = useState(null);
    const [anchorElChartData, setAnchorElChartData] = useState(null);
    const [getCareersReskillTime, status] = useGetCareersReskillTimeMutation();
    const [getCareersReskillTimeRegion, regionStatus] = useGetCareersReskillTimeRegionMutation();
    const [getCareersUpskillTime, upskillStatus = status] = useGetCareersUpskillTimeMutation();
    const [reskillcareersListData, setReskillCareersListData] = useState();
    const [careerChartNListData, setcareerChartNListData] = useState();
    const [currentCareerId, setCurrentCareerId] = useState();
    const [windowSize, setWindowSize] = useState(window.innerWidth);
    const [chartSize, setChartSize] = useState();
    const [expanded, setExpanded] = useState(0);
    const [getSkillPopupLocation, setGetSkillPopupLocation] = useState();
    const [onClickChartData, setOnClickChartData] = useState();
    const [ReskillCareerCoursesSteps, setReskillCareerCoursesSteps] = useState();
    const [bubblechartData, setBubblechartData] = useState();
    const [careerGoalId, setCareerGoalId] = useState('');
    const [careerHistoryId, setCareerHistoryId] = useState();
    const [careerNCourseData, setCareerNCourseData] = useState();
    const [careersReskillTime, setCareersReskillTime] = useState();
    const CareerHistoryState = useSelector(state => state.careerHistory.CareerHistory);
    const collegeDetails = useSelector(state => state.mainLayout)
    const regionDetails = useSelector(state => state.mainLayout)
    const [RegionDetails, setRegionDetails] = useState({})
    const collegeId = useSelector(state => state.mainLayout.collegeDetails?._id);
    const [getCompareCareersData] = useGetCompareCareersDataMutation();
    const [careerNCoursesDetails, careerNCoursesDetailsStatus] = useCareerNCoursesDetailsMutation({});
    const [careerNCoursesDetailsRegion] = useCareerNCoursesDetailsRegionMutation({})
    const [value, setChangeValue] = useState(0);
    const [careerNCoursesPopDetails, setCareerNCoursesPopDetails] = useState();
    const [chartWidth, setChartWidth] = useState();
    const [skillReportDetails, setSkillReportDetails] = useState(null);
    const [report, setReport] = useState({});
    const [termsAndCondition, setTermsAndCondition] = useState(false);
    const [regionalChartData, setRegionalChartData] = useState({})
    const [qualificationsChartData, setQualificationsChartData] = useState({})
    const [sendSkillReport] = useGetSkillsReportDataMutation({});
    const [getSkillReportPopup, setGetSkillReportPopup] = useState(false);
    const [skillRadarPopup, setSkillRadarPopup] = useState(false);
    const [openCareerDetailsPopup, setOpenCareerDetailsPopup] = useState(false);
    const [popupChartWidth, setPopupChartWidth] = useState('100%');
    const [compareCareersData, setCompareCareersData] = useState([]);
    const [readMore, setReadMore] = useState(false)
    const [showCompare, setShowCompare] = useState(false)
    const [pdfLoading, setPdfLoading] = useState(false)
    const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
    const isRegionData = !!clgDetails?.region;
    const buttonColor = isRegionData ? clgDetails?.region?.button?.bgColor : '';
    const buttonFontColor = isRegionData ? clgDetails?.region?.button?.color : '';
    const fontColor = isRegionData ? clgDetails?.region?.fontColor : '';
    const bgImage = isRegionData ? clgDetails?.region?.bgImage : '';
    const getIp = async () => {
        // const res = await axios.get("https://api.ipify.org/?format=json");
        // const Ip = res.data.ip
        const userIp = JSON.parse(localStorage.getItem('userIp'))
        setIp(userIp)
    }
    useEffect(() => {
        getIp()
    }, [])
    const darkBlue = "#000c3b"
    const schema = Yup.object().shape({
        firstName: Yup.string().required(`First name is required`),
        lastName: Yup.string().required(`Last name is required`),
        email: Yup.string().required(`Email is required`).email('Email must be a valid email').matches(/^(?!.*@[^,]*,)/),
    });

    const compareCareers = useSelector(state => state.mainLayout.compareCareers)
    useEffect(() => {
        setCompareCareersState(compareCareers)
    }, [compareCareers])

    useEffect(() => {
        if (skillReportDetails !== null) {
            exportPDFWithMethod(postPdf)
        }
    }, [skillReportDetails]) // eslint-disable-line react-hooks/exhaustive-deps


    const {
        register,
        handleSubmit,
        formState: { errors },
        reset,
        getValues,
        watch,
        setValue,
    } = useForm({
        resolver: yupResolver(schema),
    });
    const chartDataDetails1 = {
        series: [],
        options: {
            chart: {
                type: 'bar',
                height: 350
            },
            plotOptions: {
                bar: {
                    columnWidth: '70%',
                },
            },
            dataLabels: {
                enabled: false
            },
            colors: [`${collegeDetails.collegeDetails.primaryColor}`],
            yaxis: {
                min: 0,
            },
        }
    }
    const chartDataDetails2 = {
        series: [],
        options: {
            chart: {
                type: 'bar',
                height: 350
            },
            plotOptions: {
                bar: {
                    columnWidth: '70%',
                },
            },
            dataLabels: {
                enabled: false
            },
            colors: [`${collegeDetails.collegeDetails.primaryColor}`],
            yaxis: {
                min: 0,
            },
        }
    }

    const handleChangeTab = (event, newValue) => {
        setChangeValue(newValue);
    };


    const showCompareCareersPopup = (event) => {
        // if (compareCareersState.length > 0) {
        const apiData = isRegion ? {
            "regionId": clgByRegion?.data?._id,
            "currentCareerIds": careerHistoryId,
            "careerGoal": careerGoalId,
            "compareCareerIds": compareCareersState,
            isRegion
        } : {
            "collegeId": collegeId,
            "currentCareerIds": careerHistoryId,
            "careerGoal": careerGoalId,
            "compareCareerIds": compareCareersState,
            isRegion
        }
        getCompareCareersData(apiData)
            .unwrap()
            .then((payload) => setCompareCareersData(payload))
            .catch((error) => dispatch(setAlert({
                open: true,
                msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
            })))
        setAnchorElCompareCareer(true);
        setOpenCareerDetailsPopup(false);
        // }
    };
    const showCareerDetailsPopup = (id, event) => {
        setOpenCareerDetailsPopup(true);
        setAnchorElCompareCareer(null);
        setCompareCareersData('');
        setAnchorElChartData(null);
        const data = isRegion ? {
            "regionId": clgByRegion?.data?._id,
            "selectedCareerId": id,
            "currentCareerIds": careerHistoryId,
            isRegion
        } : {
            IP,
            "collegeId": collegeId,
            "selectedCareerId": id,
            "currentCareerIds": careerHistoryId,
            isRegion
        }
        // const rg_data = {
        //     IP,
        //     "collegeId": '64be5db6bb7ba446ec807e66',
        //     "selectedCareerId": id,
        //     "currentCareerIds": careerHistoryId,
        //     "regionId" : RegionDetails._id
        // }
        // careerNCoursesDetailsRegion(rg_data)
        careerNCoursesDetails(data)
            .unwrap()
            .then((payload) => {
                setCareerNCourseData(payload)
            })
            .catch((error) => {
                dispatch(setAlert({
                    open: true,
                    msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
                }))
                if (error === 500) {
                    setOpenCareerDetailsPopup(false);
                }
            })
    };
    const showChartDataPopup = (event) => {
        setAnchorElChartData(true);
    }
    const HandleCloseSkillReportPopup = () => {
        setGetSkillReportPopup(false)
        setTermsAndCondition(false)
        reset({
            firstName: "",
            lastName: "",
            email: "",
        })
    }
    const getSkillsReport = (page) => {
        // if(compareCareersState && compareCareersState.length ){
        //     setGetSkillReportPopup(true)
        // }else{
        // setGetSkillReportPopup(false)
        // }
        setGetSkillPopupLocation(page);
        setGetSkillReportPopup(true)
    }

    const HandleCloseCompareCareersPopup = () => {
        setAnchorElCompareCareer(null);
        setCompareCareersData('');
        setShowCompare(false)
    };
    const HandleCloseCareerDetailsPopup = () => {
        setOpenCareerDetailsPopup(false);
        setCareerNCoursesPopDetails(null);
        setExpanded(0);
        setReadMore(false);
    };

    const HandleOpenRadarPopup = () => {
        setSkillRadarPopup(true)
    }

    const handleFilterChange = (e) => {
        setListFilterValue(e);
        if (e.value === 1) {
            setReskillCareersListData(reskillcareersListData.sort((a, b) => b.avgSalary - a.avgSalary))
        }
        else if (e.value === 2) {
            setReskillCareersListData(reskillcareersListData.sort((a, b) => b.noOfCourse - a.noOfCourse))
        }
        else if (e.value === 3) {
            setReskillCareersListData(reskillcareersListData.sort((a, b) => a.transferWindow - b.transferWindow))
        }
    }

    const openCompareCareersPopup = Boolean(anchorElCompareCareer);

    const removeFromComparison = (id) => {
        const newCompareCareerState = compareCareersState?.filter((career) => career !== id);
        setCompareCareersState(newCompareCareerState);
        dispatch(addCompareCareer(id))
        const newCareerChartNListState = careerChartNListData?.map((item) => ({
            ...item,
            careers: item.careers.map((career) => ({
                ...career,
                addToComparison: career.id === id ? false : career.addToComparison
            }))
        }))
        setcareerChartNListData(newCareerChartNListState);
    }

    const HandleCloseRadarPopup = () => {
        setSkillRadarPopup(false)
    }

    const handleChange = (e) => (event, isExpanded) => {
        setExpanded(isExpanded ? e : false);
    };
    const [isReskill, setIsReskill] = useState(true);
    const handleToggle = (event) => {
        setIsReskill(event.target.checked);
    };
    const downloadHandler = (page) => {
        // analytics({
        //     collegeId: params?.cg_name,
        //     event: 'SKILL_REPORT',
        //     from: typeof page === 'string' ?  page : 'career-courses',
        //   })
        if (reportRef.current) {
            // reportRef.current.save();
        }
    }
    const postPdf = (pdfUrl) => {
        const { firstName, lastName, email } = getValues()
        const pdfData = {
            firstName,
            lastName,
            email,
            collegeId,
            IP,
            pdfB64: pdfUrl,
            callFrom: "career-courses"
        }
        uploadPdf(pdfData)
            .unwrap()
            .then(() => {
                dispatch(setAlert({
                    type: "success",
                    open: true,
                    msg: `Report generated successfully.`
                }))
                reset({
                    firstName: "",
                    lastName: "",
                    email: "",
                })
                setSkillReportDetails(null)
            })
            .catch((error) => dispatch(setAlert({
                open: true,
                msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
            }))).finally(() => {
                setPdfLoading(false)
                setGetSkillReportPopup(false)
                setTermsAndCondition(false)
                setSkillReportDetails(null)
                reset({
                    firstName: "",
                    lastName: "",
                    email: "",
                })
            })
    }
    const onSubmit = (data) => {
        setPdfLoading(true)
        const body = {
            IP,
            ...data,
            collegeId,
            callFrom: 'career-courses',
            currentCareerIds: careerHistoryId,
            compareCareerIds: compareCareersState
        }
        sendSkillReport(body)
            .unwrap()
            .then((payload) => {
                setSkillReportDetails(payload)
                // exportPDFWithMethod(postPdf)
                dispatch(addSkillReport(payload))
                // setGetSkillReportPopup(false)
                // setTermsAndCondition(false)
                // analytics({
                //     collegeId,
                //     event: 'SKILL_REPORT',
                //     from: getSkillPopupLocation,
                //     // from: openCompareCareersPopup ? 'comparision' : 'career-courses',
                //   })
            })
            .catch((error) => {
                setPdfLoading(false)
                dispatch(setAlert({
                    open: true,
                    msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
                }))
            })
    };

    useEffect(() => {
        if (CareerHistoryState && CareerHistoryState.length > 0) {
            const id = CareerHistoryState?.map((item) => item.value);
            setCareerHistoryId(id);
        } else {
            navigate(`/${params.cg_name}/career-history`);
            dispatch(setAlert({
                open: true,
                msg: 'Please select your current career again'
            }))
        }
    }, [CareerHistoryState]) // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (ref.current) {
            setChartWidth(ref.current.offsetWidth);
        }
    }, [ref.current]); // eslint-disable-line react-hooks/exhaustive-deps

    const scrollRef = useRef(null)

    useEffect(() => {
        let data
        // console.log('get', getValues())
    }, [watch('email'), watch('firstName'), watch('lastName')]) // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        const Qualification = careerNCoursesPopDetails?.growthData?.QualificationGrowth?.chartData?.xaxis
        const Regional = careerNCoursesPopDetails?.growthData?.regionalGrowth?.chartData?.xaxis

        if (careerNCoursesPopDetails && careerNCoursesPopDetails.growthData) {
            chartDataDetails1.series = careerNCoursesPopDetails?.growthData?.regionalGrowth?.chartData?.series
            chartDataDetails1.options.xaxis = { ...Regional, convertedCatToNumeric: false }
            setRegionalChartData(chartDataDetails1)

            chartDataDetails2.series = careerNCoursesPopDetails?.growthData?.QualificationGrowth?.chartData?.series
            chartDataDetails2.options.xaxis = { ...Qualification, convertedCatToNumeric: false }
            setQualificationsChartData(chartDataDetails2)
        }
    }, [careerNCoursesPopDetails]) // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (careerNCourseData) {
            let newState;
            if (compareCareersState?.length > 0) {
                const isIdInArray = compareCareersState.includes(careerNCourseData.id);
                if (isIdInArray) {
                    newState = {
                        ...careerNCourseData,
                        addToComparison: true
                    };
                } else {
                    newState = {
                        ...careerNCourseData,
                        addToComparison: false
                    };
                }
            }
            else {
                newState = {
                    ...careerNCourseData,
                    addToComparison: false
                };
            }
            setCareerNCoursesPopDetails(newState);
            if (newState?.courses?.length) {
                // setExpanded(newState?.courses[0].title)
            }
        }
    }, [careerNCourseData]) // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        const handleWindowResize = () => {
            setChartSize(chartRef?.current?.offsetWidth);
            setChartWidth(ref?.current?.offsetWidth);
            // setPopupChartWidth(popupRef?.current?.offsetWidth);
        };
        window.addEventListener('resize', handleWindowResize);

        return () => {
            window.removeEventListener('resize', handleWindowResize);
        };
    }, []);

    // console.log(regionId)
    const isRegion = !!params?.rg_name;
    useEffect(() => {
        if (careerHistoryId && collegeId) {
            const data = {
                "collegeId": collegeId,
                "currentCareerIds": careerHistoryId,
            }
            if (isReskill) {
                getCareersReskillTime(data)
                    .unwrap()
                    .then((payload) => setCareersReskillTime(payload))
                    .catch((error) => dispatch(setAlert({
                        open: true,
                        msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
                    })));
            } else {
                getCareersUpskillTime({ ...data })
                    .unwrap()
                    .then((response) => setCareersReskillTime(response))
                    .catch((error) => dispatch(setAlert({
                        open: true,
                        msg: error.data?.msg ? `${error.status}, ${error.data.msg}` : 'Something went wrong'
                    })))
            }
        }
    }, [careerGoalId, careerHistoryId, collegeId, isReskill]) // eslint-disable-line react-hooks/exhaustive-deps


    useEffect(() => {
        if (careersReskillTime?.sectorOrSubsector?.length > 0) {
            careersReskillTime?.sectorOrSubsector?.map(sector =>
                sector?.careers?.map(career => career?.careerType === 'CURRENT_ROLE' ?
                    setCurrentCareerId(career?.id)
                    : career)
            )
        }
    }, [careersReskillTime])

    useEffect(() => {
        if (chartRef.current) {
            setChartSize(chartRef.current.offsetWidth);
        }
    }, [chartRef.current]); // eslint-disable-line react-hooks/exhaustive-deps

    // useEffect(() => {
    //     if (popupRef.current) {
    //         setPopupChartWidth(popupRef.current.offsetWidth);
    //     }
    // }, [popupRef.current]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (careerChartNListData) {
            const data = {
                series: careerChartNListData,
                options: {
                    xaxis: {
                        tickAmount: 20,
                        type: 'category',
                        min: 3,
                        max: 60,
                        title: {
                            text: 'Transfer Window',
                            style: {
                                color: '#1f1f1f',
                                fontSize: '16px',
                                fontFamily: 'Public Sans,sans-serif'
                            },
                        },
                        subTitle: {
                            text: 'Average Salary',
                            style: {
                                color: '#1f1f1f',
                                fontSize: '16px',
                                fontFamily: 'Public Sans,sans-serif'
                            },
                        }
                    },
                    yaxis: {
                        tickAmount: 20,
                        max: 100000,
                        min: 15000,
                        title: {
                            text: 'Average Salary',
                            style: {
                                color: '#1f1f1f',
                                fontSize: '16px',
                                fontFamily: 'Public Sans,sans-serif'
                            },
                        }
                    },
                    stroke: {
                        show: true,
                        curve: 'smooth',
                        colors: ['#fff'],
                        width: 3,
                        dashArray: 0,
                    },
                    fill: {
                        opacity: 0.6
                    },
                    dataLabels: {
                        enabled: false
                    },
                    legend: {
                        position: 'right',
                        horizontalAlign: 'center',
                        offsetX: -40,
                        offsetY: 90,
                        width: 320
                    },
                    tooltip: {
                        enabled: false,
                    },
                    chart: {
                        events: {
                            dataPointSelection(event, chartContext, config) {
                                let chartDetails = {};
                                chartDetails = {
                                    id: config?.w.config.series[config?.seriesIndex]?.careers[config?.dataPointIndex].id,
                                    title: config?.w.config.series[config?.seriesIndex].name,
                                    courses: config?.w.config.series[config?.seriesIndex]?.careers[config?.dataPointIndex].noOfCourse,
                                    duration: config?.w.config.series[config?.seriesIndex]?.careers[config?.dataPointIndex].transferWindow,
                                    avgSalary: currencyFormat(config?.w.config.series[config?.seriesIndex]?.data[config?.dataPointIndex][1]).toString(),
                                    careerName: config?.w.config.series[config?.seriesIndex]?.careers[config?.dataPointIndex].name,
                                    popoverPosition: {
                                        offsetX: event.offsetX,
                                        offsetY: event.offsetY,
                                    },
                                    careerType: config?.w.config.series[config?.seriesIndex].careerType,
                                    addToComparison: config?.w.config.series[config?.seriesIndex]?.careers[config?.dataPointIndex].addToComparison
                                }
                                careerChartNListData?.map((state) => {
                                    state.careers.map((career) => {
                                        if (chartDetails?.id === career.id) {
                                            chartDetails.addToComparison = career.addToComparison
                                        }
                                        return ''
                                    })
                                    return ''
                                })
                                setOnClickChartData(chartDetails);
                            }
                        }
                    }

                }
            }
            setBubblechartData(data);
            const careerDataList = [];

            for (let i = 0; i < careerChartNListData?.length; i += 1) {
                for (let j = 0; j < careerChartNListData[i].careers?.length; j += 1) {
                    careerDataList.push({
                        name: careerChartNListData[i].name,
                        careerType: careerChartNListData[i].careerType,
                        color: careerChartNListData[i].color,
                        id: careerChartNListData[i].careers[j].id,
                        careerName: careerChartNListData[i].careers[j].name,
                        noOfCourse: careerChartNListData[i].careers[j].noOfCourse,
                        transferWindowRange: careerChartNListData[i].careers[j].transferWindow,
                        transferWindow: careerChartNListData[i].data[j][0],
                        avgSalary: careerChartNListData[i].data[j][1],
                        addToComparison: careerChartNListData[i].careers[j].addToComparison
                    })
                }
            }
            if (onClickChartData) {
                const newChartData = onClickChartData;
                careerChartNListData.map((item) => {
                    item.careers.map((career) => {
                        if (career.id === newChartData.id) {
                            newChartData.addToComparison = career.addToComparison
                        }
                        return ''
                    })
                    return ''
                })
                setOnClickChartData(newChartData);
            }
            setReskillCareersListData(careerDataList);
        }
    }, [careerChartNListData])  // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (onClickChartData) {
            showChartDataPopup();
        }
    }, [onClickChartData])

    useEffect(() => {
        if (params) {
            // setReskillCareerCoursesSteps([
            //     {
            //         label: 'Career History',
            //         link: `/${params.cg_name}/career-history`
            //     },
            //     {
            //         label: 'Skilldar',
            //         link: `/${params.cg_name}/reskill/skilldar`
            //     },
            //     {
            //         label: 'Career and Reskill time',
            //     },
            // ])
            setReskillCareerCoursesSteps([
                {
                    label: 'Your Skills',
                    link: isRegion ? `/region/${params.rg_name}/reskill/skilldar` : `/${params.cg_name}/reskill/skilldar`
                },
                {
                    label: 'Your Careers',
                    link: isRegion ? `/region/${params.rg_name}/reskill/career-courses` : `/${params.cg_name}/reskill/career-courses`
                },
                // {
                //     label: 'Colleges',
                //     link: isRegion ? `/region/${params.rg_name}/reskill/colleges` : `/${params.cg_name}/reskill/colleges`
                // },
                {
                    label: 'Your Region',
                    link: isRegion ? `/region/${params.rg_name}/reskill/regional-info` : `/${params.cg_name}/reskill/regional-info`
                },
            ])
        }
    }, [params, isRegion])

    useEffect(() => {
        if (skillReportDetails && skillReportDetails.reportForVisitorName) {
            // dispatch(setAlert({
            //     type:"success",
            //     open: true,
            //     msg: `Report generated successfully.`
            // }))
            setReport(skillReportDetails)
            // setGetSkillReportPopup(false)
            downloadHandler()

        }
    }, [skillReportDetails]) // eslint-disable-line react-hooks/exhaustive-deps

    const addToAnalytics = ({ event, course }) => {
        analytics({
            collegeId,
            event,
            data: {
                selectedCourseId: course
            },
            from: 'career-result',
            // from: 'career-courses',
        })
    }
    const addToCompare = (data) => {

        if (compareCareers.every(currentCareer => currentCareer !== data)) {
            if (compareCareers?.length > 4) {
                if (compareCareers.some(currentCareer => currentCareer !== data)) {
                    dispatch(setAlert({
                        open: true,
                        // msg: "You can't add more than 4 careers for comparison"
                        msg: "You have the maximum of 6 careers selected for comparison.  Please deselect careers to change your selection."
                    }))
                }
            } else {
                dispatch(addCompareCareer(data))
                analytics({
                    collegeId,
                    // event: 'SELECTED_CAREER',
                    event: 'CAREER_COMPARISON',
                    from: 'career-courses',
                    data: {
                        // selectedCareerId : data
                        careerComparisonId: data
                    }
                })
            }
        } else {
            dispatch(addCompareCareer(data))
        }
    }


    const redirectUrl = (data, type) => {
        let renderLink = ''
        if (data && data.length > 0) {
            const httpCheck = data.toLocaleLowerCase().startsWith("http")
            if (httpCheck) {
                renderLink = data
            } else {
                renderLink = `http://${data}`
            }
        }
        return (
            <a href={renderLink} rel="noreferrer" target='_blank'> <Typography variant='medium' color="white" sx={{ textTransform: 'none', display: 'flex', alignItems: 'center', fontSize: '1.05rem', padding: '3px' }} >{type}<KeyboardArrowRightIcon sx={{ color: 'white' }} /></Typography></a>
        )
    }

    const showComparePopup = (id) => {

        // if(compareCareersState.find(currentCareer => currentCareer === id)){
        HandleCloseCareerDetailsPopup()
        setShowCompare(true)
        // }else{
        //     addToCompare(id)
        //     HandleCloseCareerDetailsPopup()
        //     setShowCompare(true) 
        // }
    }

    useEffect(() => {
        if (showCompare) {
            showCompareCareersPopup()
        }
    }, [showCompare, compareCareersState])  // eslint-disable-line react-hooks/exhaustive-deps
    const colorTheme = useTheme();
    const steps = [
        { icon: <AssignmentIcon />, label: 'Career History' },
        { icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color={buttonFontColor || 'white'} />, label: 'Results' }  // Capitalized if it's a component
    ];
    return (
        <>
            <Helmet>
                <title>Careers & Courses</title>
            </Helmet>

            <Box
                sx={{
                    backgroundImage: `url(${bgImage})`,
                    backgroundSize: 'cover',
                    backgroundRepeat: 'no-repeat',
                    backgroundPosition: 'center',
                    minHeight: '100vh',
                    backgroundAttachment: 'fixed'
                }}
                className='page-content-wrapper'
            >
                <Container maxWidth='xl'>
                    <Box ref={scrollRef} pt={5} pb={4} className='content'>
                        <Box pb={2}>
                            <RegionStepper
                                steps={steps}
                                activeStep={1} // Change to 1 to highlight "Results"
                                buttonColor={buttonColor || colorTheme.palette.primary.main}
                                buttonFontColor={buttonFontColor || 'white'}
                            />
                        </Box>
                        <ThinkSkillsHeader fontColor={isRegionData ? 'white' : ''} />
                        <SecondRegionStepper steps={ReskillCareerCoursesSteps} activeStep={1} noIcon />
                        <GraphsData
                            showCompareCareersPopup={showCompareCareersPopup}
                            careersUpskillTime={careersReskillTime}
                            addToCompare={addToCompare}
                            isLoading={isRegion ? regionStatus?.isLoading : (status?.isLoading || upskillStatus?.isLoading)}
                            status={isRegion ? regionStatus : status}
                            scrollRef={scrollRef}
                            isRegion={isRegionData}
                            regionData={{
                                buttonColor: isRegionData ? clgDetails?.region?.button?.bgColor : '',
                                buttonFontColor: isRegionData ? clgDetails?.region?.button?.color : '',
                                bgImage: isRegionData ? clgDetails?.region?.bgImage : '',
                                fontColor: isRegionData ? clgDetails?.region?.fontColor : ''
                            }}
                            compareCareersState={compareCareersState}
                            showCareerDetailsPopup={showCareerDetailsPopup}
                            careerHistoryId={careerHistoryId}
                            getSkillReport={getSkillsReport}
                            reskillBar
                            collegeId={collegeId}
                            handleToggle={handleToggle}
                            isReskill={isReskill}
                        />
                        {
                            anchorElCompareCareer ?
                                <CompareCareerComponent
                                    openPopup={openCompareCareersPopup}
                                    anchorEl={anchorElCompareCareer}
                                    HandleClosePopup={HandleCloseCompareCareersPopup}
                                    downloadHandler={downloadHandler}
                                    showCareerDetailsPopup={showCareerDetailsPopup}
                                    removeFromComparison={removeFromComparison}
                                    getSkillsReport={getSkillsReport}
                                    compareCareersData={compareCareersData}
                                    setCompareCareersData={setCompareCareersData}
                                    stepType='Reskill'
                                />
                                : ''
                        }
                        <Popover
                            open={skillRadarPopup}
                            onClose={HandleCloseRadarPopup}
                            className='chart-popup'
                        >
                            <Button className='close-btn' onClick={HandleCloseRadarPopup}><CloseIcon /></Button>

                            <Box className="col" >
                                <Typography variant='h4' className='heading' color='primary.dark'>Skilldar</Typography>
                                <Grid container gap={2} flexWrap='nowrap' className='popup-chart-wrapper'>
                                    <Grid item lg={3}>
                                        <Box className='skilldar-keys' >
                                            <Box sx={{ width: '100%' }}>
                                                <Typography className='title' variant='capitalize' textAlign='left' color={darkBlue} sx={{ marginBottom: '8px' }}>Skill Level</Typography>
                                                {
                                                    skillLevels.map((skill) => (
                                                        <Typography key={skill.title} sx={{ marginBottom: '0px !important' }} className='skill-level' color={darkBlue}><span className='line' style={{ borderColor: skill.color, width: '15%', display: 'inline-block' }} />{skill.title}</Typography>
                                                    ))
                                                }
                                            </Box>
                                        </Box>
                                    </Grid>
                                    <Grid item lg={9} ref={popupRef}>
                                        <RadarChartComponent
                                            width={popupChartWidth || 400}
                                            legend
                                            radarApiDetails={careerNCoursesPopDetails?.skilldarChartData}
                                            height='100%'
                                            careersDetails='true'
                                            params={params}
                                            showlabels
                                            disableTooltip
                                            chartAnimation
                                        />
                                    </Grid>
                                </Grid>
                            </Box>
                        </Popover>

                        <DekstopPoup
                            isLoading={careerNCoursesDetailsStatus?.isLoading}
                            careerNCoursesPopDetails={careerNCoursesPopDetails}
                            openCareerDetailsPopup={openCareerDetailsPopup}
                            HandleCloseCareerDetailsPopup={HandleCloseCareerDetailsPopup}
                            showComparePopup={showComparePopup}
                            addToCompare={addToCompare}
                            compareCareersState={compareCareersState}
                            getSkillsReport={getSkillsReport}
                            addToAnalytics={addToAnalytics}
                            redirectUrl={redirectUrl}
                            currentNGoalCareerId={careerHistoryId}
                            expanded={expanded}
                            readMore={readMore}
                            setReadMore={setReadMore}
                            ref={ref}
                            HandleOpenRadarPopup={HandleOpenRadarPopup}
                            regionalChartData={regionalChartData}
                            qualificationsChartData={qualificationsChartData}
                            handleChange={handleChange}
                            regionData={clgDetails?.region}
                        />
                        <MobilePopup
                            careerNCoursesPopDetails={careerNCoursesPopDetails}
                            openCareerDetailsPopup={openCareerDetailsPopup}
                            HandleCloseCareerDetailsPopup={HandleCloseCareerDetailsPopup}
                            showComparePopup={showComparePopup}
                            addToCompare={addToCompare}
                            compareCareersState={compareCareersState}
                            getSkillsReport={getSkillsReport}
                            addToAnalytics={addToAnalytics}
                            redirectUrl={redirectUrl}
                            currentNGoalCareerId={careerHistoryId}
                            expanded={expanded}
                            readMore={readMore}
                            setReadMore={setReadMore}
                            ref={ref}
                            HandleOpenRadarPopup={HandleOpenRadarPopup}
                            regionalChartData={regionalChartData}
                            qualificationsChartData={qualificationsChartData}
                            handleChange={handleChange}
                        />

                        <Popover
                            open={getSkillReportPopup}
                            onClose={HandleCloseSkillReportPopup}
                            className='app-popup skilldar getskill-report'
                        >
                            <Button className='close-btn' onClick={HandleCloseSkillReportPopup}><CloseIcon sx={{ color: 'primary.main', fontWeight: '900' }} /></Button>
                            <Box className="col" ref={ref}>
                                <Typography variant='h4' color='primary.dark' sx={{ fontWeight: 700, fontSize: '25px !important', textAlign: 'left' }} >Get Skills Report</Typography>
                                <Button className='close-btn' onClick={HandleCloseSkillReportPopup}><CloseIcon sx={{ color: 'primary.dark', fontWeight: '900' }} /></Button>
                                <Box className="col">
                                    {/* <Typography variant='h4'  color='primary.dark' sx={{fontWeight:700,fontSize:'25px !important',textAlign:'left'}} >Get Skills Report</Typography> */}
                                    <Box className='flex' sx={{ justifyContent: "center", pt: 2 }}>
                                        <Typography variant='body1' color='primary.dark' sx={{ textAlign: "left", pb: 3, fontWeight: '600' }}>Get a copy of your unique Skilldar to keep, and a summary of any careers and associated courses you have saved to your comparison.  We will also send this information to the college so they can help you find the perfect course for your needs.</Typography>
                                    </Box>
                                    <Box>
                                        <form onSubmit={handleSubmit(onSubmit)}>
                                            <Grid container spacing={2} pr={4}>
                                                <Grid item xs={12} md={6}>
                                                    <TextField
                                                        // label="First Name"
                                                        name="firstName"
                                                        type='text'
                                                        placeholder="First Name"
                                                        variant="outlined"
                                                        {...register('firstName')}
                                                        focused='true'
                                                        sx={{
                                                            width: "100%",
                                                            "& input::placeholder": { fontSize: "18px", color: 'primary.dark', fontWeight: '500' },
                                                            "& .MuiOutlinedInput-root": {
                                                                "&.Mui-focused fieldset": {
                                                                    borderColor: "primary.dark"
                                                                }
                                                            }
                                                        }}
                                                        inputProps={{ style: { fontSize: 18 } }}
                                                        helperText={errors.firstName && errors.firstName.message}
                                                        error={errors.firstName && errors.firstName.message}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} md={6}>
                                                    <TextField
                                                        // label="Last Name"
                                                        name="lastName"
                                                        type='text'
                                                        placeholder="Last Name"
                                                        variant="outlined"
                                                        {...register('lastName')}
                                                        focused='true'
                                                        sx={{
                                                            width: "100%",
                                                            "& input::placeholder": { fontSize: "18px", fontWeight: '500', color: 'primary.dark' },
                                                            "& .MuiOutlinedInput-root": {
                                                                "&.Mui-focused fieldset": {
                                                                    borderColor: "primary.dark"
                                                                }
                                                            }
                                                        }}
                                                        inputProps={{ style: { fontSize: 18 } }}
                                                        helperText={errors.lastName && errors.lastName.message}
                                                        error={errors.lastName && errors.lastName.message}
                                                    />
                                                </Grid>
                                                <Grid item xs={12}>
                                                    <TextField
                                                        // label="Email"
                                                        name="email"
                                                        type='text'
                                                        placeholder="Email Address"
                                                        variant="outlined"
                                                        {...register('email')}
                                                        focused='true'
                                                        sx={{
                                                            width: "100%",
                                                            "& input::placeholder": { fontSize: "18px", fontWeight: '500', color: 'primary.dark' },
                                                            "& .MuiOutlinedInput-root": {
                                                                "&.Mui-focused fieldset": {
                                                                    borderColor: "primary.dark"
                                                                }
                                                            }
                                                        }}
                                                        inputProps={{ style: { fontSize: 18 } }}
                                                        helperText={errors.email && errors.email.message}
                                                        error={errors.email && errors.email.message}
                                                    />
                                                </Grid>
                                                <Grid item xs={12} className='tc-checkbox-wrapper'>
                                                    <Checkbox onClick={(e) => { setTermsAndCondition(e.target.checked) }} sx={{ color: 'primary.dark', transform: "scale(1.5)", mr: 1 }} />
                                                    <Typography sx={{ color: 'primary.dark', display: 'inline', fontWeight: '600' }} >I accept the
                                                        <Link target='_blank' rel="noreferrer" to={`/${params.cg_name}/terms-conditions`}  >terms and conditions</Link>
                                                    </Typography>
                                                </Grid>
                                            </Grid>
                                            <LoadingButton
                                                loading={pdfLoading}
                                                disabled={!termsAndCondition}
                                                endIcon={<KeyboardArrowRightOutlinedIcon sx={{ fontSize: '30px !important' }} />}
                                                variant='contained'
                                                sx={{ minWidth: "15%", mt: 1, bgcolor: 'secondary.main', fontWeight: '600', padding: '10px', borderRadius: '6px' }} type='submit'>SUBMIT</LoadingButton>
                                        </form>
                                    </Box>
                                </Box>
                            </Box>
                        </Popover>

                        <Box sx={{ height: '0px', overflow: 'hidden', width: '0px' }}>
                            <PDFExport
                                ref={reportRef}
                                paperSize='A4'
                                fileName='Report'>
                                <Report roleType='reskill' />
                            </PDFExport>
                        </Box>
                    </Box>
                </Container>
            </Box>
        </>
    )
}