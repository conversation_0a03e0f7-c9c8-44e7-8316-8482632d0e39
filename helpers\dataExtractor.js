const mongoose = require('mongoose');

// Import all data models
const { Region } = require('../models/region');
const { CollegeGroup } = require('../models/collegeGroup');
const { College } = require('../models/college');
const { Campus } = require('../models/campus');
const { Course } = require('../models/course');
const { Career } = require('../models/career');
const { LMISkillAbility } = require('../models/lmiSkillAbility');
const { Sector } = require('../models/sector');
const { Subsector } = require('../models/subsector');

/**
 * Data Extraction Helper
 * Provides comprehensive data extraction from all models with relationships
 * Optimized for fine-tuning data preparation
 */
class DataExtractor {
  constructor() {
    this.extractionStats = {
      regions: 0,
      collegeGroups: 0,
      colleges: 0,
      campuses: 0,
      courses: 0,
      careers: 0,
      skills: 0,
      abilities: 0,
      sectors: 0,
      subsectors: 0,
      totalRecords: 0,
      extractionTime: 0
    };
  }

  /**
   * Extract all data with relationships populated
   * @param {Object} options - Extraction options
   * @returns {Promise<Object>} - Extracted data with relationships
   */
  async extractAllData(options = {}) {
    const {
      includeInactive = true,
      populateRelationships = true,
      maxRecordsPerType = null,
      includeMetadata = true
    } = options;

    console.log('🔍 Starting comprehensive data extraction...');
    const startTime = Date.now();

    try {
      this.resetStats();

      // Extract all data types in parallel for performance
      const [
        regions,
        collegeGroups,
        colleges,
        campuses,
        courses,
        careers,
        skills,
        abilities,
        sectors,
        subsectors
      ] = await Promise.all([
        this.extractRegions({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractCollegeGroups({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractColleges({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractCampuses({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractCourses({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractCareers({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractSkills({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractAbilities({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractSectors({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType }),
        this.extractSubsectors({ includeInactive, populateRelationships, maxRecords: maxRecordsPerType })
      ]);

      // Calculate final statistics
      this.extractionStats.extractionTime = Date.now() - startTime;
      this.extractionStats.totalRecords = Object.values(this.extractionStats)
        .filter(val => typeof val === 'number' && val > 0)
        .reduce((sum, val) => sum + val, 0) - this.extractionStats.extractionTime;

      console.log('✅ Data extraction completed');
      console.log(`📊 Total records extracted: ${this.extractionStats.totalRecords}`);
      console.log(`⏱️ Extraction time: ${this.extractionStats.extractionTime}ms`);

      const extractedData = {
        regions,
        collegeGroups,
        colleges,
        campuses,
        courses,
        careers,
        skills,
        abilities,
        sectors,
        subsectors
      };

      if (includeMetadata) {
        extractedData.metadata = {
          extractionDate: new Date(),
          statistics: this.extractionStats,
          options: options
        };
      }

      return extractedData;

    } catch (error) {
      console.error('❌ Data extraction failed:', error);
      throw error;
    }
  }

  /**
   * Extract regions with relationships
   */
  async extractRegions(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = Region.find({}).select("-logo -partnerLogos -bannerImages -bgImage");
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('collegeGroupIds');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const regions = await query.lean();
      this.extractionStats.regions = regions.length;
      
      console.log(`📍 Extracted ${regions.length} regions`);
      return regions;

    } catch (error) {
      console.error('❌ Error extracting regions:', error);
      return [];
    }
  }

  /**
   * Extract college groups with relationships
   */
  async extractCollegeGroups(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = CollegeGroup.find({});
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('countryId adminUserId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const collegeGroups = await query.lean();
      this.extractionStats.collegeGroups = collegeGroups.length;
      
      console.log(`🏢 Extracted ${collegeGroups.length} college groups`);
      return collegeGroups;

    } catch (error) {
      console.error('❌ Error extracting college groups:', error);
      return [];
    }
  }

  /**
   * Extract colleges with relationships
   */
  async extractColleges(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = College.find({}).select("-logo -image");
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('collegeGroupId adminUserId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const colleges = await query.lean();
      this.extractionStats.colleges = colleges.length;
      
      console.log(`🏫 Extracted ${colleges.length} colleges`);
      return colleges;

    } catch (error) {
      console.error('❌ Error extracting colleges:', error);
      return [];
    }
  }

  /**
   * Extract campuses with relationships
   */
  async extractCampuses(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = Campus.find({});
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('collegeId countryId adminUserId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const campuses = await query.lean();
      this.extractionStats.campuses = campuses.length;
      
      console.log(`🏛️ Extracted ${campuses.length} campuses`);
      return campuses;

    } catch (error) {
      console.error('❌ Error extracting campuses:', error);
      return [];
    }
  }

  /**
   * Extract courses with relationships
   */
  async extractCourses(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = Course.find({});
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate([
          { path: 'campusId', populate: { path: 'collegeId' } },
          { path: 'sectorIds' },
          { path: 'subsectorIds' }
        ]);
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const courses = await query.lean();
      this.extractionStats.courses = courses.length;
      
      console.log(`📚 Extracted ${courses.length} courses`);
      return courses;

    } catch (error) {
      console.error('❌ Error extracting courses:', error);
      return [];
    }
  }

  /**
   * Extract careers with relationships
   */
  async extractCareers(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = Career.find({});
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('sectorIds subsectorIds unitGroupId broadCareerId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const careers = await query.lean();
      this.extractionStats.careers = careers.length;
      
      console.log(`💼 Extracted ${careers.length} careers`);
      return careers;

    } catch (error) {
      console.error('❌ Error extracting careers:', error);
      return [];
    }
  }

  /**
   * Extract skills with relationships
   */
  async extractSkills(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = LMISkillAbility.find({ category: 'skill' });
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('radarCategoryId radarSubcategoryId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const skills = await query.lean();
      this.extractionStats.skills = skills.length;
      
      console.log(`🧠 Extracted ${skills.length} skills`);
      return skills;

    } catch (error) {
      console.error('❌ Error extracting skills:', error);
      return [];
    }
  }

  /**
   * Extract abilities with relationships
   */
  async extractAbilities(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = LMISkillAbility.find({ category: 'ability' });
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('radarCategoryId radarSubcategoryId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const abilities = await query.lean();
      this.extractionStats.abilities = abilities.length;
      
      console.log(`💪 Extracted ${abilities.length} abilities`);
      return abilities;

    } catch (error) {
      console.error('❌ Error extracting abilities:', error);
      return [];
    }
  }

  /**
   * Extract sectors with relationships
   */
  async extractSectors(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = Sector.find({});
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const sectors = await query.lean();
      this.extractionStats.sectors = sectors.length;
      
      console.log(`🏭 Extracted ${sectors.length} sectors`);
      return sectors;

    } catch (error) {
      console.error('❌ Error extracting sectors:', error);
      return [];
    }
  }

  /**
   * Extract subsectors with relationships
   */
  async extractSubsectors(options = {}) {
    const { includeInactive = true, populateRelationships = true, maxRecords = null } = options;
    
    try {
      let query = Subsector.find({});
      
      if (!includeInactive) {
        query = query.where({ status: { $ne: 'inactive' } });
      }
      
      if (populateRelationships) {
        query = query.populate('sectorId');
      }
      
      if (maxRecords) {
        query = query.limit(maxRecords);
      }

      const subsectors = await query.lean();
      this.extractionStats.subsectors = subsectors.length;
      
      console.log(`🔧 Extracted ${subsectors.length} subsectors`);
      return subsectors;

    } catch (error) {
      console.error('❌ Error extracting subsectors:', error);
      return [];
    }
  }

  /**
   * Reset extraction statistics
   */
  resetStats() {
    Object.keys(this.extractionStats).forEach(key => {
      this.extractionStats[key] = 0;
    });
  }

  /**
   * Get extraction statistics
   */
  getStats() {
    return { ...this.extractionStats };
  }

  /**
   * Extract data for specific college only
   * @param {string} collegeId - College ID to extract data for
   * @returns {Promise<Object>} - College-specific data
   */
  async extractCollegeSpecificData(collegeId) {
    console.log(`🎯 Extracting data for college: ${collegeId}`);
    
    try {
      // Get college info
      const college = await College.findById(collegeId)
        .populate('collegeGroupId')
        .lean();
      
      if (!college) {
        throw new Error('College not found');
      }

      // Get campuses for this college
      const campuses = await Campus.find({ collegeId })
        .populate('countryId')
        .lean();

      // Get courses for this college's campuses
      const campusIds = campuses.map(c => c._id);
      const courses = await Course.find({ campusId: { $in: campusIds } })
        .populate('sectorIds subsectorIds')
        .lean();

      console.log(`✅ Extracted college-specific data:`);
      console.log(`   College: ${college.name}`);
      console.log(`   Campuses: ${campuses.length}`);
      console.log(`   Courses: ${courses.length}`);

      return {
        college,
        campuses,
        courses,
        metadata: {
          collegeId,
          extractionDate: new Date(),
          recordCounts: {
            college: 1,
            campuses: campuses.length,
            courses: courses.length
          }
        }
      };

    } catch (error) {
      console.error('❌ Error extracting college-specific data:', error);
      throw error;
    }
  }
}

module.exports = DataExtractor;
