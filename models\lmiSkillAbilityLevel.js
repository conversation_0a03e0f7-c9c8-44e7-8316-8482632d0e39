const mongoose = require("mongoose");

const LMISkillAbilityLevelSchema = new mongoose.Schema({
  lmiSkillAbilityId: { type: mongoose.Schema.Types.ObjectId, ref:"lmiSkillAbility" },
  careerId: { type: mongoose.Schema.Types.ObjectId, ref:"career" },
  level:Number,

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.LMISkillAbilityLevelSchema = LMISkillAbilityLevelSchema;

class LMISkillAbilityLevel extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await LMISkillAbilityLevel.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await LMISkillAbilityLevel.create(entry);
    }
  }
}

mongoose.model(LMISkillAbilityLevel, LMISkillAbilityLevelSchema, "lmiSkillAbilityLevels");

module.exports.LMISkillAbilityLevel = LMISkillAbilityLevel;
