const jwt = require("jsonwebtoken");
const keys = require("../config/keys");
const { Campus } = require("../models/campus");
const { College } = require("../models/college");
const { CollegeGroup } = require("../models/collegeGroup");
const { User, UserRoles } = require("../models/user");
const mongoose = require("mongoose");

const frontendRoutes = [
  "/getCareerHistory",
  "/getSkilldarChartData",
  "/getCompareCareersData",
  "/getCareersNUpskillTime",
  "/getCareersNReskillTime",
  "/getCompareCareersDataRegion",
  "/getCareersNUpskillTimeRegion",
  "/getCareersNReskillTimeRegion",
]

const checkToken = (req, res, next) => {
  if (
    req.url === "/register" ||
    req.url === "/authenticate" ||
    req.url === "/authenticate/app" ||
    req.url === "/resetPassword" ||
    req.url === "/forgotPassword" ||
    req.url === "/requestPasswordReset"
  )
  {
    next();
    return;
  }

  let token = req.headers["authorization"];

  if (!token) {
    if(frontendRoutes.includes(req.url)) {
      next();
      return;
    }

    return res
      .status(401)
      .send({ success: false, msg: "No token provided." });
  }

  token = token.toString().substr(7);

  jwt.verify(token, keys.JWT_SECRET_KEY, async function(err, decoded)
  {
    if (err)
    {
      return res.status(401).send({
        success: false,
        msg: "Failed to authenticate token.",
      });
    }
    req.token = token;

    // const pipeline = [
    //   //decoded.user._id
    //   {
    //     $lookup: {
    //       from: "users",
    //       localField: "adminUserId",
    //       foreignField: "_id",
    //       as: "adminUser"
    //     }
    //   },
    //   {
    //     $lookup: {
    //       from: "colleges",
    //       localField: "collegeId",
    //       foreignField: "_id",
    //       as: "college"
    //     }
    //   },
    //   {
    //     $unwind: {
    //       path: "$college",
    //     }
    //   },
    //   {
    //     $lookup: {
    //       from: "collegeGroups",
    //       localField: "college.collegeGroupId",
    //       foreignField: "_id",
    //       as: "collegeGroup"
    //     }
    //   },
    //   {
    //     $project: {
    //       "college.logo":0,
    //       "adminUser.password": 0,
    //     }
    //   },
    //   {
    //     $unwind: {
    //       path: "$adminUser",
    //     }
    //   },
    //   {
    //     $unwind: {
    //       path: "$collegeGroup",
    //     }
    //   },
    // ]

    // const user = await User.aggregate(pipeline);
    const user = await User.findById(decoded.user._id);

    // if(!user) {
    //   return res
    //       .status(401)
    //       .json({ msg: "Couldn't find the user" });
    // }
    
    if (!user) {
      return res
        .status(401)
        .json({ success:false, msg: "User not found, Please login again.", code: 996 });
    }

    if (user.role != decoded.role) {
      return res
        .status(401)
        .json({ success:false, msg: "User is modified, Please login again.", code: 996 });
    }

    req.user = user;
    // req.viewingAs = user.customerID;

    // // only apply the viewingAs parameter if it's a ReTime user
    // if (user.customerID.toString() == ReTimeCustomerID)
    // { 
    //   req.viewingAs = viewingAs || user.customerID;
    // }

    if(user.role == UserRoles.NOT_AVAILABLE && !frontendRoutes.includes(req.route.path)) {
      return res
        .status(401)
        .json({ success:false, msg: "Role not assigned" });
    }

    // if(user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
    //   if(!mongoose.isValidObjectId(user.collegeGroupId)) {
    //     return res
    //       .status(401)
    //       .json({ success:false, msg: "College group not tagged" });
    //   }
    //   else {
    //     const collegeGroup = CollegeGroup.findById(user.collegeGroupId);
    //     if(!collegeGroup) {
    //       return res
    //       .status(401)
    //       .json({ success:false, msg: "Couldn't find the College group" });
    //     }
    //   }
    // }
    // else if(user.role == UserRoles.COLLEGE_ADMIN) {
    //   // if(!mongoose.isValidObjectId(user.collegeId)) {
    //   if(!user.collegeId) {
    //     return res
    //       .status(401)
    //       .json({ success:false, msg: "College not tagged" });
    //   }
    //   else {
    //     const college = College.findById(user.collegeId);
    //     if(!college) {
    //       return res
    //         .status(401)
    //         .json({ success:false, msg: "Couldn't find the College" });
    //     }
    //   }
    // }
    // else if(user.role == UserRoles.CAMPUS_ADMIN) {
    //   // if(!mongoose.isValidObjectId(user.campusId)) {
    //   if(!user.campusId) {
    //     return res
    //       .status(401)
    //       .json({ success:false, msg: "Campus not tagged" });
    //   }
    //   else {
    //     const campus = Campus.findById(user.campusId);
    //     if(!campus) {
    //       return res
    //         .status(401)
    //         .json({ success:false, msg: "Couldn't find the Campus" });
    //     }
    //   }
    // }

    next();

    // // TODO: think if this is correct
    // // Allows global users to access every app
    // if (user.canBeUsedByEveryCustomer)
    // {
    //   next();
    //   return;
    // }

    // // Check if the user can use the current app
    // Customer.find({
    //   $or: [{ _id: user.customerID }, { _id: user.canBeUsedByCustomers }],
    // })
    //   .catch((error) =>
    //   {
    //     console.error("Couldn't find the customer", error);
    //     return res
    //       .status(401)
    //       .json({ msg: "Couldn't find the customer", code: error.code });
    //   })
    //   .then((customers) =>
    //   {
    //     if (!customers.length)
    //     {
    //       return res
    //         .status(401)
    //         .json({ msg: "Couldn't find the customer" });
    //     }

    //     let hasAccess = false;
    //     customers.forEach((customer) =>
    //     {
    //       if (customer.studyTypes.indexOf(studyType) !== -1)
    //       {
    //         hasAccess = true;
    //       }
    //     });

    //     if (!hasAccess)
    //     {
    //       return res.status(401).json({
    //         msg: "You are not allowed to access this App",
    //         code: 996,
    //       });
    //     }

    //     next();
    //   });
  });
};

module.exports = checkToken;