import React, { useState } from 'react'
import PropTypes from 'prop-types';
import { Box, Collapse, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import CustomBar from './CustomBar';


const BarsComponent = ({ data, addToCompare, compareCareersState, showCareerDetailsPopup, widthClass, isWidget }) => {
  
  const isCareers = (sectors) => sectors && sectors?.find(sector => sector?.careers?.length > 0)
  const maxLength = data?.reduce((prev, current) => (prev?.careers?.length > current?.careers?.length) ? prev : current, 1).careers?.length
  return (
    <Box 
    sx={{
      height: widthClass === "bar-wrapper-mob" ? 'auto !important': '',
      maxHeight: widthClass === "bar-wrapper-mob" ? '540px !important': '',
      mb: widthClass === "bar-wrapper-mob" ? '16PX !important': '',
    }} className={isWidget ? "bar-wrapper widget" : "bar-wrapper"}>
      {data?.length >0 ?
        <>
          {data?.map((careersData, index) =>
            <CustomBar
              addToCompare={addToCompare}
              data={careersData}
              maxLength={maxLength}
              compareCareersState={compareCareersState}
              showCareerDetailsPopup={showCareerDetailsPopup}
              index={index}
              isWidget={isWidget}
            />
          )
          }
        </> :
        <Box sx={{
          display:'flex', justifyContent:'center', textAlign:'center', alignItems:'center', height:'100%'
        }}>
          <Typography>
            No Careers Found
          </Typography>
        </Box>
      }
    </Box>
  )
}

export default BarsComponent

BarsComponent.propTypes = {
  data: PropTypes.array,
  isWidget: PropTypes.bool,
  addToCompare: PropTypes.func,
  compareCareersState: PropTypes.array,
  showCareerDetailsPopup: PropTypes.func,
  widthClass: PropTypes.string,
}