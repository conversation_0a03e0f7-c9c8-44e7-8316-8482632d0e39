import PropTypes from 'prop-types';
import { useEffect, useRef, useState } from 'react';
// @mui
import { alpha } from '@mui/material/styles';
import { Box, CircularProgress, Dialog, IconButton } from '@mui/material';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';

//
import Iconify from '../iconify';
//
import { StyledReactPlayer } from './styles';
import thumbnail from "../../assets/images/background-video.png"

// ----------------------------------------------------------------------

export default function PlayerDialog({ videoPath, open, onClose, ...other }) {
  const [resHeight,setResHeight] = useState(false)
  const playIcon = <Box
        // onClick={handleOpenVideo}
        sx={{
            width: 54,
            height: 54,
            display: 'flex',
            justifyContent: 'center',
            position:'unset',
            alignItems: 'center',
            backgroundColor: 'primary.main',
            borderRadius: '50%',
            boxShadow: "0px 0px 12px rgba(0,0,0,0.4)",
            cursor: 'pointer',
        }}
    >
        <PlayArrowIcon sx={{ color: 'white' }} />

    </Box>
  const ref = useRef()
  useEffect(()=>{
      if(ref?.current?.offsetWidth < 950){
        setResHeight(true)
      }else{
        setResHeight(false)
      }
  },[ref.current]) // eslint-disable-line react-hooks/exhaustive-deps


  const [loading, setLoading] = useState(true);
  const onReady = () => {
    setLoading(false);
  };

  return (
    <div ref={ref}>
    <Dialog
      fullScreen
      open={open}
      PaperProps={{
        sx: { bgcolor: 'unset',
        width:'100%',
        padding: "unset",
        height: resHeight ? 'auto' : '50%'
      },
      }}
    >
      <IconButton
        className='close-icon'
        size="large"
        onClick={onClose}
        sx={{
          padding:0.5,
          top: '0%',
          right: '30%',
          zIndex: 9,
          position: 'absolute',
          // color: (theme) => alpha(theme.palette.common.white, 1),
          // bgcolor: (theme) => alpha(theme.palette.common.white, 0.01),
          // color: (theme) => alpha(theme.palette.primary.main, 1),
          color: (theme) => alpha(theme.palette.error.main, 0.9),
          '&:hover': {
            // bgcolor: (theme) => alpha(theme.palette.common.white, 0.86),
          },
        }}
      >
        {/* <Iconify icon="carbon:close" width={24} /> */}
        <Iconify icon="mdi:close-circle" width={28} />
        
      </IconButton>

      {loading && (
        <CircularProgress
          sx={{
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            m: 'auto',
            position: 'absolute',
          }}
        />
      )}

      <StyledReactPlayer
        url={videoPath}
        controls
        config={{
          file: {
            attributes: {
              controlsList: "nofullscreen nodownload noremoteplayback ",
              disablepictureinpicture :'true',
              
            },
          },
        }}
        // playIcon = { <PlayArrowIcon sx={{ color: 'red' }} />}
        playing={!loading}
        onReady={onReady}
        // light= {<img style={{width:"100%", height:'100%'}} src={thumbnail} alt='Thumbnail' />}
        {...other} />
    </Dialog>
    </div>

  );
}

PlayerDialog.propTypes = {
  onClose: PropTypes.func,
  open: PropTypes.bool,
  videoPath: PropTypes.string,
};
