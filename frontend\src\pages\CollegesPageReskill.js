import { useTheme } from '@emotion/react';
import AssignmentIcon from '@mui/icons-material/Assignment';
import { Box, Container, Grid, Link, Typography } from '@mui/material';
import { useMemo } from 'react';
import { useParams } from 'react-router';
import AuthWrapper from 'src/components/AuthWrapper';
import RegionStepper from 'src/components/RegionStepper';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import { ArcticonsEmojiSpiderWeb } from './Reskill_Flow/SpiderIcon';

const CollegesPageReskill = () => {
  const colleges = [
    {
      name: 'Heart of Worcestershire College (HoW College)',
      description: `We have a wide range of full and part-time courses in a variety of different subject areas; 
      from Engineering, Accounting and Marketing, to NVQs, ESOL and Teacher Training, the possibilities are endless.
  
      Whether you’re looking to progress your career, learn a new skill or try a new hobby, we have a course for you.`,
      location: 'Various',
      website: 'www.linktocollegewebsite.org.uk',
    },
    {
      name: 'Kidderminster College',
      description: `It's always possible to make a change. Whether you're considering a career shift, planning to rejoin the workforce, 
      or aiming to develop the skills to kickstart your business venture, Kidderminster College is here for you. 
      We offer various part-time courses tailored to empower adults like you in their educational journey. 
      Make a fresh start with Kidderminster College and embrace the opportunities that await you.`,
      location: 'Market Street, Kidderminster, DY10 1AB',
      website: 'www.linktocollegewebsite.org.uk',
    },
    {
      name: 'College group WCG',
      description: `From part-time leisure courses to professional qualifications including degrees, 
      we have something for everyone at WCG.
  
      We know that our range of lifelong learning programmes is essential to strengthening our communities and 
      making sure that local people can succeed in their personal goals and career aspirations.`,
      location: 'Various',
      website: 'www.linktocollegewebsite.org.uk',
    },
  ];
  const params = useParams();
  const isRegion = !!params?.rg_name;
  const stepperSteps = useMemo(() => ([
    {
      label: 'Your Skills',
      link: isRegion ? `/region/${params.rg_name}/reskill/skilldar` : `/${params.cg_name}/reskill/skilldar`
    },
    {
      label: 'Your Careers',
      link: isRegion ? `/region/${params.rg_name}/reskill/career-courses` : `/${params.cg_name}/reskill/career-courses`
    },
    {
      label: 'Colleges',
      link: isRegion ? `/region/${params.rg_name}/reskill/colleges` : `/${params.cg_name}/reskill/colleges`
    },
    {
      label: 'Your Region',
      link: isRegion ? `/region/${params.rg_name}/reskill/regional-info` : `/${params.cg_name}/reskill/regional-info`
    },
  ]), [params, isRegion])
  const colorTheme = useTheme();
  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    { icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color='white' />, label: 'Results' }  // Capitalized if it's a component
  ];
  return (
    <>
      <AuthWrapper title="Colleges">
        <Box className="page-content-wrapper">
          <Container maxWidth="xl">
            <Box pt={5} pb={4} className='content'>
              <ThinkSkillsHeader />
              <Box pb={2}>
                <RegionStepper
                  steps={steps}
                  activeStep={1} // Change to 1 to highlight "Results"
                  buttonColor={colorTheme.palette.primary.main}
                  buttonFontColor='white'
                />
              </Box>
              <SecondRegionStepper steps={stepperSteps} activeStep={2} noIcon />
            </Box>
            <Box sx={{ backgroundColor: 'white', p: 2 }}>
              <Typography sx={{ fontSize: '22px !important', fontWeight: 700, mt: 1 }} textAlign="start">
                We recommend the following Colleges
              </Typography>
              <Grid container spacing={4} mt={1}>
                {colleges.map((college, index) => (
                  <Grid item xs={12} md={4} key={index}>
                    <Box sx={{ textAlign: 'center' }}>
                      {/* Image placeholder */}
                      {/* <Box
                                                sx={{
                                                    width: '100%',
                                                    height: 180,
                                                    bgcolor: '#ccc',
                                                    mb: 2,
                                                }}
                                            /> */}

                      <Box
                        component="img"
                        src={`https://picsum.photos/400/180?random=${index}`} // Unique per card
                        alt={college.name}
                        sx={{
                          width: '100%',
                          height: 180,
                          objectFit: 'cover',
                          borderRadius: 1,
                          mb: 2,
                        }}
                      />

                      <Typography sx={{ fontSize: '22px !important', fontWeight: 700 }} gutterBottom>
                        {college.name}
                      </Typography>

                      <Typography
                        sx={{
                          fontSize: '16px !important',
                          fontWeight: 400,
                          textAlign: 'left',
                          whiteSpace: 'pre-line',
                        }}
                      >
                        {college.description}
                      </Typography>

                      <Typography
                        sx={{
                          fontSize: '16px !important',
                          fontWeight: 400,
                          textAlign: 'left',
                          whiteSpace: 'pre-line',
                          mt: 1,
                        }}
                      >
                        <strong>Location:</strong> {college.location}
                      </Typography>

                      <Link
                        href={`https://${college.website}`}
                        target="_blank"
                        underline="hover"
                        sx={{ display: 'block', mt: 1, textAlign: 'left' }}
                      >
                        {college.website}
                      </Link>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Container>
        </Box>
      </AuthWrapper>
    </>
  );
};

export default CollegesPageReskill;
