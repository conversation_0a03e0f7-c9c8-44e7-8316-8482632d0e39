{"name": "@minimal/material-kit-react", "author": "minimals.cc", "licence": "MIT", "version": "1.7.0", "private": false, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx ./src", "clear-all": "rm -rf build node_modules", "re-start": "rm -rf build node_modules && yarn install && yarn start", "re-build": "rm -rf build node_modules && yarn install && yarn build"}, "eslintConfig": {"extends": ["react-app"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@faker-js/faker": "^7.6.0", "@iconify/react": "^4.1.0", "@mui/icons-material": "^5.14.0", "@mui/lab": "^5.0.0-alpha.103", "@mui/material": "^5.14.0", "@reduxjs/toolkit": "^1.9.3", "apexcharts": "^3.37.0", "autosuggest-highlight": "^3.3.4", "axios": "^1.3.6", "change-case": "^4.1.2", "date-fns": "^2.29.3", "formik": "^2.2.9", "history": "^5.3.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "mui-tel-input": "^3.1.3", "numeral": "^2.0.6", "prop-types": "^15.8.1", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-csv": "^2.2.2", "react-data-table-component": "^7.5.3", "react-date-range": "^1.4.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.43.1", "react-redux": "^8.0.5", "react-router-dom": "^6.8.1", "react-scripts": "^5.0.1", "redux": "^4.2.1", "simplebar-react": "^3.2.1", "styled-components": "^5.3.11", "sweetalert2": "^11.7.5", "universal-cookie": "^4.0.4", "web-vitals": "^3.1.1", "xlsx": "^0.18.5", "yup": "^1.1.0"}, "devDependencies": {"@babel/core": "^7.21.0", "@babel/eslint-parser": "^7.19.1", "eslint": "^8.34.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.6.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.4"}, "overrides": {"nth-check": "2.1.1"}}