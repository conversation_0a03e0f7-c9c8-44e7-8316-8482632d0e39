import { createSlice } from '@reduxjs/toolkit';
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import Cookies from 'js-cookie';
import { Api_Base_URL } from 'src/config-global';

export const UpskillApi = createApi({
  reducerPath: 'UpskillApi',
  baseQuery: fetchBaseQuery({
    baseUrl: Api_Base_URL,
    prepareHeaders: (headers) => {
      const token = Cookies.get('feToken');
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getSkilldarChartData: builder.mutation({
      query: (data) => ({
        url: '/getSkilldarChartData',
        method: 'POST',
        body: data,
      }),
      transformResponse: (response, meta, arg) => response.data,
      transformErrorResponse: (response, meta, arg) => response,
    }),
    getCareersUpskillTime: builder.mutation({
      query: (data) => {
        const baseUrl = data?.isRegion
          ? '/getCareersNUpskillTimeRegion'
          : '/getCareersNUpskillTime';
        return {
          url: baseUrl,
          method: 'POST',
          body: data,
        };
      },
      transformResponse: (response, meta, arg) => response.data,
      transformErrorResponse: (response, meta, arg) => response,
    }),
    careerNCoursesDetails: builder.mutation({
      query: (data) => {
        const baseUrl = data?.isRegion
          ? '/getCareersNCoursesDetailsRegion'
          : '/getCareersNCoursesDetails';
        return {
          url: baseUrl,
          method: 'POST',
          body: data,
        };
      },
      transformResponse: (response, meta, arg) => response.data,
      transformErrorResponse: (response, meta, arg) => response,
    }),
    careerNCoursesDetailsRegion: builder.mutation({
      query: (data) => ({
        url: '/getCareersNCoursesDetailsRegion',
        method: 'POST',
        body: data,
      }),
      transformResponse: (response, meta, arg) => response.data,
      transformErrorResponse: (response, meta, arg) => response,
    }),
    getCompareCareersData: builder.mutation({
      query: (data) => {
        const baseUrl = data?.isRegion ? '/getCompareCareersDataRegion' : '/getCompareCareersData';
        return {
          url: baseUrl,
          method: 'POST',
          body: data,
        };
      },
      transformResponse: (response, meta, arg) => response.data,
      transformErrorResponse: (response, meta, arg) => response,
    }),
    getSkillsReportData: builder.mutation({
      query: (data) => {
        let baseUrl = '/getSkillsReportData';
        if(data?.isRegion) {
          baseUrl = '/getSkillsReportDataRegion';
        }
        return {
          url: baseUrl,
          method: 'POST',
          body: data,
        };
      },
      transformResponse: (response, meta, arg) => response.data,
      transformErrorResponse: (response, meta, arg) => response,
    }),
  }),
});

const UpskillSlice = createSlice({
  name: 'Upskill',
  initialState: {
    CompareCareers: [],
    SkillsReport: {},
  },
  reducers: {
    addCompareCareers: (state, action) => {
      state.CompareCareers.push(action.payload);
    },
    addSkillReport: (state, action) => {
      // console.log('pay',action.payload);
      state.SkillsReport = action.payload;
    },
  },
});

export const {
  useGetSkilldarChartDataMutation,
  useGetCareersUpskillTimeMutation,
  useCareerNCoursesDetailsMutation,
  useGetCompareCareersDataMutation,
  useGetSkillsReportDataMutation,
  useCareerNCoursesDetailsRegionMutation,
} = UpskillApi;
export const { addCompareCareers, addSkillReport } = UpskillSlice.actions;
export default UpskillSlice.reducer;
