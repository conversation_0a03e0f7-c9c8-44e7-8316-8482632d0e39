import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from 'src/utils/axiosInstance';

export const getRegions = createAsyncThunk('regions/getregions', async (isSystem, { rejectWithValue }) => {
  try {
    const baseUrl = isSystem ? 'regions/get?system=true' : 'regions/get';
    const response = await axiosInstance({
      url: baseUrl,
      method: 'GET',
    });
    // localdata set
    return response.data;
  } catch (error) {
    console.log('error get regions', error);
    return rejectWithValue(error);
  }
});
export const getRegionByID = createAsyncThunk('regions/getRegionByID', async (data, { rejectWithValue }) => {
  try {
    const response = await axiosInstance({
      url: `regions/getByID?id=${data}`,
      method: 'GET',
    });
    // localdata set
    return response.data;
  } catch (error) {
    console.log('error get regions', error);
    return rejectWithValue(error);
  }
});

export const getRegionBySlug = createAsyncThunk('regions/getRegionBySlug', async (data, { rejectWithValue }) => {
  try {
    const response = await axiosInstance({
      url: `regions/getBySlug?slug=${data?.slug}`,
      method: 'GET',
    });
    // localdata set
    return response.data;
  } catch (error) {
    console.log('error get regions', error);
    return rejectWithValue(error);
  }
});

export const postRegions = createAsyncThunk('regions/postRegions', async (data, { rejectWithValue }) => {
  try {
    const response = await axiosInstance({
      url: 'regions/add',
      method: 'POST',
      data,
    });
    // response.data.name = data.name
    response.data.name = data?.name;
    response.data.groupName = data?.groupName;
    return response.data;
  } catch (error) {
    return rejectWithValue(error);
  }
});
export const updateRegion = createAsyncThunk('regions/update', async (data, { rejectWithValue }) => {
  try {
    const response = await axiosInstance({
      url: 'regions/update',
      method: 'PUT',
      data,
    });
    // response.data.name = data.name
    // response.data.name = data?.name;
    // response.data.groupName = data?.groupName ;
    return response.data;
  } catch (error) {
    return rejectWithValue(error);
  }
});

export const removeRegion = createAsyncThunk('region/removeRegion', async (data) => {
  try {
    const response = await axiosInstance({
      url: 'regions/remove',
      method: 'DELETE',
      data,
    });
    response.data.id = data.id;
    return response.data;
  } catch (error) {
    return error;
  }
});

const regionsSlice = createSlice({
  name: 'regions',
  initialState: {
    // colleges: [...colleges],
    regions: [],
    region: {},
    status: 'idle', // 'idle' | 'pending' | 'succeeded' | 'failed'
    error: null,
  },
  reducers: {
    // addCollege: (state, action) => {
    //     state.colleges.push(action.payload)
    // },
    // updateColleges: (state, action) => {
    // },
    // deleteCollege: (state, action) => {
    //     state.colleges = state.colleges.filter(college => college.id !== action.payload.id)
    // },
  },
  extraReducers: {
    [getRegions.pending]: (state) => {
      state.status = 'pending';
    },
    [getRegions.fulfilled]: (state, action) => {
      state.status = 'succeeded';
      const data = action?.payload?.data;
      if (!Array.isArray(data)) {
        console.warn('Expected array but got:', data);
        state.regions = [];
        return;
      }
      state.regions = data.map((region) => ({
        name: region?.name,
        _id: region?._id,
        slug: region?.slug,
      }));
    },
    [getRegions.rejected]: (state, action) => {
      state.status = 'rejected';
      const error = action.payload;
      console.log('region err', error);
    },

    [getRegionByID.fulfilled]: (state, action) => {
      state.region = action.payload.data[0];
      state.status = 'success';
    },

    [postRegions.fulfilled]: (state, action) => {
      const data = action.payload;
      state.regions.push(data);
      // console.log("post data",data)
    },
    [postRegions.rejected]: (state, action) => {
      const error = action.payload;
      console.log('error post region', error);
    },
    [updateRegion.fulfilled]: (state, action) => {},
    [removeRegion.fulfilled]: (state, action) => {
      // const = action.payload
      const data = action.payload;
      // console.log("remove college",data)
      state.regions = state.regions.filter((college) => college._id !== data.id);
    },
    [removeRegion.rejected]: (state, action) => {
      // const = action.payload
      const error = action.payload;
      console.log('remove region error', error);
    },
  },
});
// export const { addCollege, deleteCollege } = regionsSlice.actions
export default regionsSlice.reducer;
