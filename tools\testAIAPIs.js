const axios = require('axios');
const fs = require('fs');
const path = require('path');

/**
 * AI API Testing Script
 * Tests all AI management endpoints
 * Note: This requires a valid auth token for protected routes
 */

const BASE_URL = 'http://localhost:3000';
const AI_BASE = `${BASE_URL}/api/ai`;

// Mock auth token (replace with real token for actual testing)
const AUTH_TOKEN = 'your-auth-token-here';

const headers = {
  'Content-Type': 'application/json',
  'Authorization': `Bearer ${AUTH_TOKEN}`
};

const testAIAPIs = async () => {
  console.log('🧪 Starting AI API Test Suite...');
  console.log('==================================');

  let testResults = {
    passed: 0,
    failed: 0,
    skipped: 0,
    tests: []
  };

  // Test 1: Health Check (No Auth Required)
  console.log('\n📋 Test 1: Health Check');
  try {
    const response = await axios.get(`${AI_BASE}/health`);
    if (response.status === 401) {
      console.log('⚠️ Health endpoint requires authentication (expected)');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'Health Check', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`✅ Health check passed: ${response.data.message}`);
      testResults.passed += 1;
      testResults.tests.push({ name: 'Health Check', status: 'PASSED' });
    }
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ Health endpoint requires authentication (expected)');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'Health Check', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ Health check failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Health Check', status: 'FAILED', error: error.message });
    }
  }

  // Test 2: List Training Datasets (Auth Required)
  console.log('\n📋 Test 2: List Training Datasets');
  try {
    const response = await axios.get(`${AI_BASE}/training-datasets`, { headers });
    console.log(`✅ Training datasets listed: ${response.data.data.datasets.length} found`);
    testResults.passed += 1;
    testResults.tests.push({ name: 'List Training Datasets', status: 'PASSED' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ Training datasets endpoint requires authentication');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'List Training Datasets', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ List training datasets failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'List Training Datasets', status: 'FAILED', error: error.message });
    }
  }

  // Test 3: List Models (Auth Required)
  console.log('\n📋 Test 3: List Models');
  try {
    const response = await axios.get(`${AI_BASE}/models`, { headers });
    console.log(`✅ Models listed: ${response.data.data.models.length} found`);
    testResults.passed += 1;
    testResults.tests.push({ name: 'List Models', status: 'PASSED' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ Models endpoint requires authentication');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'List Models', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ List models failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'List Models', status: 'FAILED', error: error.message });
    }
  }

  // Test 4: Get Deployed Model (Auth Required)
  console.log('\n📋 Test 4: Get Deployed Model');
  try {
    const response = await axios.get(`${AI_BASE}/models/deployed`, { headers });
    if (response.data.data.deployed) {
      console.log(`✅ Deployed model found: ${response.data.data.model.modelName}`);
    } else {
      console.log(`✅ No model currently deployed (valid state)`);
    }
    testResults.passed += 1;
    testResults.tests.push({ name: 'Get Deployed Model', status: 'PASSED' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ Deployed model endpoint requires authentication');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'Get Deployed Model', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ Get deployed model failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Get Deployed Model', status: 'FAILED', error: error.message });
    }
  }

  // Test 5: System Overview (Auth Required)
  console.log('\n📋 Test 5: System Overview');
  try {
    const response = await axios.get(`${AI_BASE}/overview`, { headers });
    const overview = response.data.data.overview;
    console.log(`✅ System overview retrieved:`);
    console.log(`   - Total datasets: ${overview.datasets.total}`);
    console.log(`   - Total models: ${overview.models.total}`);
    console.log(`   - Deployed models: ${overview.models.deployed}`);
    console.log(`   - Running jobs: ${overview.training.runningJobs}`);
    testResults.passed += 1;
    testResults.tests.push({ name: 'System Overview', status: 'PASSED' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ System overview endpoint requires authentication');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'System Overview', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ System overview failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'System Overview', status: 'FAILED', error: error.message });
    }
  }

  // Test 6: Training Data Status (Auth Required)
  console.log('\n📋 Test 6: Training Data Status');
  try {
    const response = await axios.get(`${AI_BASE}/training-data/status`, { headers });
    console.log(`✅ Training data status retrieved: ${response.data.data.ready ? 'Ready' : 'Not Ready'}`);
    testResults.passed += 1;
    testResults.tests.push({ name: 'Training Data Status', status: 'PASSED' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ Training data status endpoint requires authentication');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'Training Data Status', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ Training data status failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Training Data Status', status: 'FAILED', error: error.message });
    }
  }

  // Test 7: List Fine-Tuning Jobs (Auth Required)
  console.log('\n📋 Test 7: List Fine-Tuning Jobs');
  try {
    const response = await axios.get(`${AI_BASE}/fine-tuning/jobs`, { headers });
    console.log(`✅ Fine-tuning jobs listed: ${response.data.data.jobs.length} found`);
    testResults.passed += 1;
    testResults.tests.push({ name: 'List Fine-Tuning Jobs', status: 'PASSED' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log('⚠️ Fine-tuning jobs endpoint requires authentication');
      testResults.skipped += 1;
      testResults.tests.push({ name: 'List Fine-Tuning Jobs', status: 'SKIPPED', reason: 'Auth required' });
    } else {
      console.log(`❌ List fine-tuning jobs failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'List Fine-Tuning Jobs', status: 'FAILED', error: error.message });
    }
  }

  // Test 8: Route Protection Validation
  console.log('\n📋 Test 8: Route Protection Validation');
  try {
    // Test without auth token
    const response = await axios.get(`${AI_BASE}/models`);
    console.log(`❌ Route protection failed - endpoint accessible without auth`);
    testResults.failed += 1;
    testResults.tests.push({ name: 'Route Protection', status: 'FAILED', error: 'Endpoint accessible without auth' });
  } catch (error) {
    if (error.response && error.response.status === 401) {
      console.log(`✅ Route protection working - 401 Unauthorized returned`);
      testResults.passed += 1;
      testResults.tests.push({ name: 'Route Protection', status: 'PASSED' });
    } else {
      console.log(`❌ Route protection test failed: ${error.message}`);
      testResults.failed += 1;
      testResults.tests.push({ name: 'Route Protection', status: 'FAILED', error: error.message });
    }
  }

  // Test Results Summary
  console.log('\n📊 API TEST RESULTS SUMMARY');
  console.log('============================');
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`⚠️ Skipped: ${testResults.skipped} (Auth required)`);
  console.log(`📊 Total: ${testResults.passed + testResults.failed + testResults.skipped}`);
  
  const totalTests = testResults.passed + testResults.failed;
  if (totalTests > 0) {
    console.log(`🎯 Success Rate: ${((testResults.passed / totalTests) * 100).toFixed(1)}%`);
  }

  console.log('\n📋 Detailed Results:');
  testResults.tests.forEach((test, index) => {
    let status = '';
    if (test.status === 'PASSED') status = '✅';
    else if (test.status === 'FAILED') status = '❌';
    else if (test.status === 'SKIPPED') status = '⚠️';
    
    console.log(`${status} ${index + 1}. ${test.name}: ${test.status}`);
    if (test.error) {
      console.log(`   Error: ${test.error}`);
    }
    if (test.reason) {
      console.log(`   Reason: ${test.reason}`);
    }
  });

  console.log('\n💡 Notes:');
  console.log('- Skipped tests require valid authentication token');
  console.log('- Route protection is working correctly (401 responses expected)');
  console.log('- To test authenticated endpoints, provide a valid AUTH_TOKEN');

  if (testResults.failed === 0) {
    console.log('\n🎉 ALL TESTABLE ENDPOINTS WORKING! API system is functioning correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the errors above.');
  }

  return testResults;
};

// Run test if called directly
if (require.main === module) {
  testAIAPIs()
    .then((results) => {
      console.log('\n🏁 AI API test completed');
      process.exit(results.failed === 0 ? 0 : 1);
    })
    .catch((error) => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = testAIAPIs;
