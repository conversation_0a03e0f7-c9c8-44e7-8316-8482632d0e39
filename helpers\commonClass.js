const { default: mongoose } = require("mongoose");
const { User, UserRoles } = require("../models/user");
const { CollegeGroup } = require("../models/collegeGroup");
const { College } = require("../models/college");
const { Campus } = require("../models/campus");
const { isEmpty } = require("./commonHelper");
const { Region } = require("../models/region");

class commonClass {
  static allowedUsers;

  static async getAllowedUsers(req) {

    // if(!commonClass.allowedUsers) {

    let query = {};
    let users;
    let listUsers = [];

    if (req.user.role == UserRoles.SUPER_ADMIN) {
      const pipeline = [
        { $match: { _id: { $ne: new mongoose.Types.ObjectId(req.user._id) } } },
        {
          $lookup: {
            from: "collegeGroups",
            localField: "collegeGroupIds",
            foreignField: "_id",
            as: "collegeGroups"
          }
        },
        // {$unwind: {
        //   path: "$collegeGroups",
        // }},
        {
          $lookup: {
            from: "colleges",
            localField: "collegeIds",
            foreignField: "_id",
            as: "colleges"
          }
        },
        {
          $lookup: {
            from: "campuses",
            localField: "campusIds",
            foreignField: "_id",
            as: "campuses"
          }
        },
        // College Group of College
        {
          $lookup: {
            from: "collegeGroups",
            localField: "colleges.collegeGroupId",
            foreignField: "_id",
            as: "college_collegeGroups"
          }
        },
        // College of campus
        {
          $lookup: {
            from: "colleges",
            localField: "campuses.collegeId",
            foreignField: "_id",
            as: "campus_colleges"
          }
        },
        // College group of College of campus
        {
          $lookup: {
            from: "collegeGroups",
            localField: "campus_colleges.collegeGroupId",
            foreignField: "_id",
            as: "campus_collegeGroups"
          }
        },
        // {$unwind: {
        //     path: "$college_collegeGroup",
        // }},
        {
          $project: {
            "password": 0,

            "collegeGroups.addedBy": 0,
            "collegeGroups.status": 0,
            "collegeGroups.defaultEntry": 0,
            "collegeGroups.editedBy": 0,

            "colleges.logo": 0,
            "colleges.addedBy": 0,
            "colleges.status": 0,
            "colleges.defaultEntry": 0,
            "colleges.editedBy": 0,

            "campuses.addedBy": 0,
            "campuses.status": 0,
            "campuses.defaultEntry": 0,
            "campuses.editedBy": 0,

            "college_collegeGroups.addedBy": 0,
            "college_collegeGroups.status": 0,
            "college_collegeGroups.defaultEntry": 0,
            "college_collegeGroups.editedBy": 0,

            "campus_colleges.logo": 0,
            "campus_colleges.addedBy": 0,
            "campus_colleges.status": 0,
            "campus_colleges.defaultEntry": 0,
            "campus_colleges.editedBy": 0,

            "campus_collegeGroups.addedBy": 0,
            "campus_collegeGroups.status": 0,
            "campus_collegeGroups.defaultEntry": 0,
            "campus_collegeGroups.editedBy": 0,
          }
        },
        // {$unwind: {
        //     path: "$college.collegeGroup",
        // }},
      ]
      listUsers = await User.aggregate(pipeline);

      listUsers.forEach(user => {
        if (user.role == UserRoles.COLLEGE_ADMIN) {
          user.collegeGroups = user.college_collegeGroups;
        }
        else if (user.role == UserRoles.CAMPUS_ADMIN) {
          user.colleges = user.campus_colleges;
          user.collegeGroups = user.campus_collegeGroups;
        }
        user.collegeGroups = user.collegeGroups[0];
        user.colleges = user.colleges[0];
        user.campuses = user.campuses[0];

        delete user["college_collegeGroups"];
        delete user["campus_colleges"];
        delete user["campus_collegeGroups"];
      });
    }
    else if (req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
      const pipeline = [
        { $match: { adminUserId: new mongoose.Types.ObjectId(req.user._id) } },
        {
          $lookup: {
            from: "colleges",
            localField: "_id",
            foreignField: "collegeGroupId",
            as: "colleges"
          }
        },
        {
          $lookup: {
            from: "campuses",
            localField: "colleges._id",
            foreignField: "collegeId",
            as: "campuses"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "colleges.adminUserId",
            foreignField: "_id",
            as: "college_users"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "campuses.adminUserId",
            foreignField: "_id",
            as: "campus_users"
          }
        },
        {
          $project: {
            "password": 0,

            "college_users.password": 0,
            "campus_users.password": 0,

            "colleges.logo": 0,
            "colleges.addedBy": 0,
            "colleges.status": 0,
            "colleges.defaultEntry": 0,
            "colleges.editedBy": 0,

            "campuses.addedBy": 0,
            "campuses.status": 0,
            "campuses.defaultEntry": 0,
            "campuses.editedBy": 0
          }
        },
        // {$unwind: {
        //     path: "$college.collegeGroup",
        // }},
      ]
      const cgs = await CollegeGroup.aggregate(pipeline);
      // console.log(cgs)

      let userIdsForNewlyCreatedUsers = [];
      userIdsForNewlyCreatedUsers.push(req.user._id);
      for (let cg of cgs) {
        userIdsForNewlyCreatedUsers.push(cg.adminUserId);
        if (cg.college_users && cg.college_users.length > 0) {
          // console.log('in college users condition');
          for (let collegeUser of cg.college_users) {

            const userIndex = listUsers.findIndex(user => user._id.toString() == collegeUser._id.toString());
            if (userIndex != -1) {
              continue;
            }

            const user = collegeUser;
            let collegesOfCollegeUser = [];
            if (cg.colleges && cg.colleges.length > 0) {
              // collegeOfCollegeUser = cg.colleges.find(college => college._id.toString() == collegeUser.collegeIds[0].toString())
              collegesOfCollegeUser = cg.colleges.filter(college => college.adminUserId && college.adminUserId.toString() == collegeUser._id.toString())
            }
            user.colleges = collegesOfCollegeUser[0];
            userIdsForNewlyCreatedUsers.push(user.colleges.adminUserId);
            if (user.colleges) {
              const { _id, name } = cg;
              const newCG = { _id, name };
              user.collegeGroups = newCG;
            }
            // console.log('assigned college users', collegesOfCollegeUser);
            // .
            listUsers.push(user);
          }
        }

        if (cg.campus_users && cg.campus_users.length > 0) {
          // console.log('in college users condition');
          for (let campusUser of cg.campus_users) {

            const userIndex = listUsers.findIndex(user => user._id.toString() == campusUser._id.toString());
            if (userIndex != -1) {
              continue;
            }

            const user = campusUser;
            let campusesOfCampusUser = [];
            if (cg.campuses && cg.campuses.length > 0) {
              // collegeOfCollegeUser = cg.colleges.find(college => college._id.toString() == collegeUser.collegeIds[0].toString())
              campusesOfCampusUser = cg.campuses.filter(campus => campus.adminUserId && campus.adminUserId.toString() == campusUser._id.toString())
            }
            user.campuses = campusesOfCampusUser[0];
            if (user.campuses) {
              if (cg.colleges && cg.colleges.length > 0) {
                const collegeOfCampus = cg.colleges.find(college => college._id.toString() == user.campuses.collegeId)
                user.colleges = collegeOfCampus;
              }
              const { _id, name } = cg;
              const newCG = { _id, name };
              user.collegeGroups = newCG;
            }
            // console.log('assigned college users', campusesOfCampusUser);
            // .
            listUsers.push(user);
          }
        }
      }

      const userPipeline = [
        { $match: { $and: [{ role: { "$eq": 5 } }, { adminUserId: { $in: userIdsForNewlyCreatedUsers } }] } },
        {
          $project: {
            "password": 0,
          }
        },
      ]
      const users = await User.aggregate(userPipeline);
      listUsers.push(...users);
    }
    else if (req.user.role == UserRoles.COLLEGE_ADMIN) {
      const pipeline = [
        { $match: { adminUserId: new mongoose.Types.ObjectId(req.user._id) } },
        // {
        //   $lookup: {
        //     from: "colleges",
        //     localField: "_id",
        //     foreignField: "collegeGroupId",
        //     as: "colleges"
        //   }
        // },
        {
          $lookup: {
            from: "campuses",
            localField: "_id",
            foreignField: "collegeId",
            as: "campuses"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "colleges.adminUserId",
            foreignField: "_id",
            as: "college_users"
          }
        },
        {
          $lookup: {
            from: "users",
            localField: "campuses.adminUserId",
            foreignField: "_id",
            as: "campus_users"
          }
        },
        {
          $lookup: {
            from: "collegeGroups",
            localField: "collegeGroupId",
            foreignField: "_id",
            as: "collegeGroups"
          }
        },
        {
          $project: {
            "name": 1,
            "collegeGroupId": 1,
            "adminUserId": 1,

            "college_users._id": 1,
            "college_users.firstName": 1,
            "college_users.lastName": 1,
            "college_users.email": 1,
            "college_users.role": 1,
            "college_users.adminUserId": 1,
            "college_users.addedBy": 1,
            "college_users.editedBy": 1,
            "college_users.contactNumber": 1,
            "college_users.photo": 1,
            "college_users.collegeGroupIds": 1,
            "college_users.collegeIds": 1,
            "college_users.campusIds": 1,

            "campus_users._id": 1,
            "campus_users.firstName": 1,
            "campus_users.lastName": 1,
            "campus_users.email": 1,
            "campus_users.role": 1,
            "campus_users.adminUserId": 1,
            "campus_users.addedBy": 1,
            "campus_users.editedBy": 1,
            "campus_users.contactNumber": 1,
            "campus_users.photo": 1,
            "campus_users.collegeGroupIds": 1,
            "campus_users.collegeIds": 1,
            "campus_users.campusIds": 1,

            "campuses._id": 1,
            "campuses.name": 1,
            "campuses.adminUserId": 1,
            "campuses.collegeId": 1,

            "collegeGroups._id": 1,
            "collegeGroups.name": 1,
            "collegeGroups.adminUserId": 1,
          }
        },
        // {$unwind: {
        //     path: "$college.collegeGroup",
        // }},
      ]
      const colleges = await College.aggregate(pipeline);
      // console.log(colleges)

      for (let college of colleges) {

        if (college.campus_users && college.campus_users.length > 0) {
          // console.log('in college users condition');
          for (let campusUser of college.campus_users) {

            const userIndex = listUsers.findIndex(user => user._id.toString() == campusUser._id.toString());
            if (userIndex != -1) {
              continue;
            }

            const user = campusUser;
            let campusesOfCampusUser = [];
            if (college.campuses && college.campuses.length > 0) {
              // collegeOfCollegeUser = cg.colleges.find(college => college._id.toString() == collegeUser.collegeIds[0].toString())
              campusesOfCampusUser = college.campuses.filter(campus => campus.adminUserId && campus.adminUserId.toString() == campusUser._id.toString())
            }
            user.campuses = campusesOfCampusUser[0];
            if (user.campuses) {
              // if (cg.colleges && cg.colleges.length > 0) {
              //   const collegeOfCampus = cg.colleges.find(college => college._id.toString() == user.campuses[0].collegeId)
              //   user.college = collegeOfCampus;
              // }

              const { _id, name, collegeGroups } = college;
              const newCollege = { _id, name };
              user.colleges = newCollege;
              user.collegeGroups = collegeGroups[0];
            }
            // console.log('assigned college users', campusesOfCampusUser);
            // .
            listUsers.push(user);
          }
        }
      }

      // console.log('listUsers', listUsers);

      const userPipeline = [
        { $match: { $and: [{ role: { "$eq": 5 } }, { adminUserId: { $eq: new mongoose.Types.ObjectId(req.user._id) } }] } },
        {
          $project: {
            "password": 0,
          }
        },
      ]
      const users = await User.aggregate(userPipeline);
      listUsers.push(...users);
    }
    listUsers.sort((a, b) => {
      return a.role - b.role
    })
    this.allowedUsers = listUsers;
    // }

    return this.allowedUsers;
  }

  static allowedRegions;

  static async getAllowedRegions(req) {
    const regions = await Region.find({});
    this.allowedRegions = regions;

    return this.allowedRegions;
  }

  static allowedCollegeGroups;

  static async getAllowedCollegeGroups(req, editingCollege) {

    // if(!commonClass.allowedUsers) {

    let query = {};
    let users;
    let listCGs = [];

    if (req.user.role != UserRoles.SUPER_ADMIN && req.user.role != UserRoles.COLLEGE_GROUP_ADMIN &&
      req.user.role != UserRoles.COLLEGE_ADMIN) {
      return [];
    }

    if (req.user.role == UserRoles.COLLEGE_ADMIN && !editingCollege) {
      return [];
    }

    const pipeline = [
      {
        $lookup: {
          from: "users",
          localField: "adminUserId",
          foreignField: "_id",
          as: "adminUser"
        }
      },
      {
        $project: {
          "adminUser.password": 0,
        }
      },
      {
        $unwind: {
          path: "$adminUser",
        }
      },
      {$sort: {'addedBy.date': -1}}
    ]
    if (req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
      pipeline.push({ $match: { adminUserId: new mongoose.Types.ObjectId(req.user._id) } });
    }
    else if (req.user.role == UserRoles.COLLEGE_ADMIN) {
      pipeline.push({ $match: { _id: new mongoose.Types.ObjectId(editingCollege.collegeGroupId) } });
    }
    const cgs = await CollegeGroup.aggregate(pipeline);
    this.allowedCollegeGroups = cgs;
    // }

    return this.allowedCollegeGroups;
  }


  static allowedColleges;

  static async getAllowedColleges(req, editingCampus) {

    // if(!commonClass.allowedUsers) {

    let query = {};
    let users;
    let listCGs = [];

    if (req.user.role != UserRoles.SUPER_ADMIN && req.user.role != UserRoles.COLLEGE_GROUP_ADMIN &&
      req.user.role != UserRoles.COLLEGE_ADMIN && req.user.role != UserRoles.CAMPUS_ADMIN) {
      return [];
    }

    if (req.user.role == UserRoles.CAMPUS_ADMIN && !editingCampus) {
      return [];
    }

    const pipeline = [
      {
        $lookup: {
          from: "users",
          localField: "adminUserId",
          foreignField: "_id",
          as: "adminUser"
        }
      },
      //overWriting adminUser now its give adminUser as object
      {
        $addFields: {
          adminUser: { $arrayElemAt: ["$adminUser",0] }
        }
      },
      {
        $addFields: {
          adminUser: {
            $ifNull: ["$adminUser", null]
          },
          hasApiKey: {
            $cond: {
              if: {
                $and: [
                  { $ifNull: ["$cleverHubApiKey", false] },
                  { $ne: ["$cleverHubApiKey", ""] }
                ]
              },
              then: true,
              else: false
            }
          }
        }
      },
      // {
      //   $unwind: {
      //    path: "$adminUser",
      //     }
      //    },
      {
        $lookup: {
          from: "collegeGroups",
          localField: "collegeGroupId",
          foreignField: "_id",
          as: "collegeGroup"
        }
      },
      {
        $project: {
          // "logo":0,
          "adminUser.password": 0,
        }
      },
   
      {
        $unwind: {
          path: "$collegeGroup",
        }
      },
      {$sort: {'addedBy.date': -1}}
    ]
    if (req.user.role == UserRoles.COLLEGE_ADMIN) {
      pipeline.push({ $match: { adminUserId: new mongoose.Types.ObjectId(req.user._id) } });
    }
    else if (req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
      pipeline.push({ $match: { collegeGroupId: new mongoose.Types.ObjectId(req.user.collegeGroupIds[0]) } });
    }
    else if (req.user.role == UserRoles.CAMPUS_ADMIN) {
      pipeline.push({ $match: { _id: new mongoose.Types.ObjectId(editingCampus.collegeId) } });
    }
    const colleges = await College.aggregate(pipeline);
    this.allowedColleges = colleges;
    // }

    return this.allowedColleges;
  }

  static allowedCampuses;

  static async getAllowedCampuses(req) {

    if (req.user.role != UserRoles.SUPER_ADMIN && req.user.role != UserRoles.COLLEGE_GROUP_ADMIN &&
      req.user.role != UserRoles.COLLEGE_ADMIN && req.user.role != UserRoles.CAMPUS_ADMIN) {
      return [];
    }
    // check collegeId validity if provided
    if (!isEmpty(req.query.collegeId) && !mongoose.isValidObjectId(req.query.collegeId)) {
      return messageResponse(INVALID_MISSING, "ID", false, 400, null, res)
    }

    const pipeline = [
      {
        $lookup: {
          from: "users",
          localField: "adminUserId",
          foreignField: "_id",
          as: "adminUser"
        }
      },
      {
        $addFields: {
          adminUser: { $arrayElemAt: ["$adminUser",0] }
        }
      },
      {
        $addFields: {
          adminUser: {
            $ifNull: ["$adminUser", null]
          }
        }
      },
      {
        $lookup: {
          from: "colleges",
          localField: "collegeId",
          foreignField: "_id",
          as: "college"
        }
      },
      {
        $unwind: {
          path: "$college",
        }
      },
      {
        $lookup: {
          from: "collegeGroups",
          localField: "college.collegeGroupId",
          foreignField: "_id",
          as: "collegeGroup"
        }
      },
      {
        $project: {
          "college.logo": 0,
          "adminUser.password": 0,
        }
      },
      // {
      //   $unwind: {
      //     path: "$adminUser",
      //   }
      // },
      {
        $unwind: {
          path: "$collegeGroup",
        }
      },
      {$sort: {'addedBy.date': -1}}
    ]
    if (!isEmpty(req.query.collegeId)) {
      pipeline.unshift({ $match: { collegeId: new mongoose.Types.ObjectId(req.query.collegeId) } });
    }
    if (req.user.role == UserRoles.CAMPUS_ADMIN) {
      pipeline.push({ $match: { adminUserId: new mongoose.Types.ObjectId(req.user._id) } });
    }
    else if (req.user.role == UserRoles.COLLEGE_ADMIN) {
      pipeline.push({ $match: { collegeId: new mongoose.Types.ObjectId(req.user.collegeIds[0]) } });
    }
    else if (req.user.role == UserRoles.COLLEGE_GROUP_ADMIN) {
      pipeline.push({ $match: { 'college.collegeGroupId': new mongoose.Types.ObjectId(req.user.collegeGroupIds[0]) } });
    }
    const campuses = await Campus.aggregate(pipeline);
    this.allowedCampuses = campuses;
    // }

    return this.allowedCampuses;
  }
}

module.exports = commonClass;