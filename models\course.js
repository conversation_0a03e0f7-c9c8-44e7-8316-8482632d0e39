const mongoose = require("mongoose");
const { Campus, CampusSchema } = require("./campus");
const { Sector, SectorSchema } = require("./sector");
const { Subsector, SubsectorSchema } = require("./subsector");

const CourseSchema = new mongoose.Schema({
  code: String,
  title: String,
  description: String,
  level: String,
  campusId: { type: mongoose.Schema.Types.ObjectId, ref:mongoose.model(Campus, CampusSchema) },    
  duration : String,
  pageURL: String,
  applyURL: String,
  enquiryURL: String,

  sectors: { type: [Object], default: [] },

  sectorIds: { type: [mongoose.Schema.Types.ObjectId], default: [], ref: mongoose.model(Sector, SectorSchema) },
  subsectorIds: { type: [mongoose.Schema.Types.ObjectId], default: [], ref: mongoose.model(Subsector, SubsectorSchema) },

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.CourseSchema = CourseSchema;

class Course extends mongoose.Model {
  static async createDefaultEntries() {
    for (const entry of defaultEntries) {
      const count = await Course.count({ _id: entry._id });
      if (count) {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await Course.create(entry);
    }
  }
}

mongoose.model(Course, CourseSchema, "courses");

module.exports.Course = Course;