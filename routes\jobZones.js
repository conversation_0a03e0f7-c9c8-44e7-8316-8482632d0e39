const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const jobZonesController = require('../controllers/jobZones.controller')

const AuthGuard = require("../guards/auth.guard");
const SuperUserGuard = require("../guards/super-user.guard");

router.post("/createJobZones", [AuthGuard, SuperUserGuard], jobZonesController.createJobZones);

module.exports = router;