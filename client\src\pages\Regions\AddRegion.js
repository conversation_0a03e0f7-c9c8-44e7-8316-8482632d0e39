import PhotoLibraryIcon from '@mui/icons-material/PhotoLibrary';
import { LoadingButton } from '@mui/lab';
import {
  Box,
  Button,
  Card,
  Container,
  FormControl,
  FormLabel,
  Grid,
  IconButton,
  Modal,
  Stack,
  TextField,
  Typography,
} from '@mui/material';
import * as Yup from 'yup';
import CancelIcon from '@mui/icons-material/Cancel';
import { useFormik } from 'formik';
import jwtDecode from 'jwt-decode';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import Cookies from 'universal-cookie';
import Iconify from 'src/components/Iconify';
import { setSnackbar } from '../../Redux/snackbarSlice';
import noImagePlaceholder from '../../assets/images/no-image-icon-0.jpg';
import SelectComponent from '../../components/SelectComponent';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import UrlComponent from '../../components/UrlComponent';
import { APP_ROUTER_BASE_URL } from '../../utils';
import { CancelButton, label } from '../../utils/cssStyles';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getAvailableUsers } from '../Userspage/usersSlice';
import { postRegions } from './regionsSlice';

// groups,

const AddRegion = () => {
  const [Groups, setGroups] = useState([]);
  const [loading, setLoading] = useState(false);
  const [BluredOnce, setBluredOnce] = useState(false);
  const [bannerModalOpen, setBannerModalOpen] = useState(false);
  const groups = useSelector((state) => state.collegeGroups.groups);
  const dispatch = useDispatch();
  const { availableUsers } = useSelector((state) => state.users);
  const cookies = new Cookies();
  const jwtToken = cookies.get('token');
  const Role = jwtToken && jwtDecode(jwtToken);
  const role = String(Role?.role);
  useEffect(() => {
    setGroups(groups);
  }, [groups]);
  useEffect(() => {
    const currentUrl = window.location.host;
    dispatch(getCollegeGroups());
    const params = {
      role: '2',
      includeIds: '',
    };
    dispatch(getAvailableUsers(params));
  }, []);

  const validationSchema = Yup.object({
    name: Yup.string().required('Name is required'),
    slug: Yup.string().required('Slug is required'),
    collegeGroupIds: Yup.array().min(1, 'At least one College Group is required'),
  });

  const navigate = useNavigate();
  const removePic = () => {
    formik.setValues({
      ...formik.values,
      logo: '',
    });
    document.getElementById('logo').value = '';
  };
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      slug: '',
      collegeGroupIds: [],
      button: {
        color: '#211e21',
        bgColor: '#f1a853',
      },
      fontColor: '#fbfbfb',
      primaryColor: '#00376f',
      secondaryColor: '#f1a853',
      logo: '',
      bgImage: '',
      partnerLogos: [],
      bannerImages: [],
    },
    validationSchema,
    onSubmit: (values) => {
      setLoading(true);
      const data = { ...values /* ...other fields... */ };
      dispatch(postRegions(data))
        .then((res) => {
          console.log(res);
          if (res?.payload?.success) {
            dispatch(
              setSnackbar({
                snackbarOpen: true,
                snackbarType: 'success',
                snackbarMessage: 'Successfully added Region',
              })
            );
            setTimeout(() => {
              formik.resetForm();
              navigate(`${APP_ROUTER_BASE_URL}dashboard/regions`);
            }, 100);
          } else {
            // Error is in res.payload.response.data.message
            const errorMessage = get(res, 'payload.response.data.message', 'Something went wrong!');
            dispatch(
              setSnackbar({
                snackbarOpen: true,
                snackbarType: 'error',
                snackbarMessage: errorMessage,
              })
            );
          }
        })
        .finally(() => setLoading(false));
    },
    // validationSchema: collegeValidationSchema
  });
  const handlePhoneChange = (newValue, info) => {
    formik.setValues({
      ...formik.values,
      collegeTelNumber: newValue,
    });
  };
  const style = {
    p: 4,
  };
  const handleCollegeNameBlur = () => {
    // formik.handleBlur()
    if (!BluredOnce && formik.values.name) {
      const slug = formik.values.name.split(' ').join('-').toLocaleLowerCase();
      formik.setValues({
        ...formik.values,
        slug,
      });
      setBluredOnce(true);
    }
  };
  const handleBannerUpload = (files) => {
    const readers = files.map((file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
      });
    });

    Promise.all(readers).then((images) => {
      formik.setValues({
        ...formik.values,
        bannerImages: [...(formik.values.bannerImages || []), ...images],
      });
    });
  };
  const handlePartnerUpload = (files) => {
    const readers = files.map((file) => {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = reject;
      });
    });

    Promise.all(readers).then((images) => {
      formik.setValues({
        ...formik.values,
        partnerLogos: [...formik.values.partnerLogos, ...images],
      });
    });
  };
  const handleBackgroundUpload = (file) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      formik.setValues({
        ...formik.values,
        bgImage: reader.result,
      });
    };
  };
  const handleLogoUpload = (file) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      formik.setValues({
        ...formik.values,
        logo: reader.result,
      });
    };
  };

  return (
    <Container maxWidth="xl">
      <Modal open={bannerModalOpen} onClose={() => setBannerModalOpen(false)}>
        <Box
          sx={{
            width: 700,
            bgcolor: 'background.paper',
            borderRadius: 2,
            p: 4,
            mx: 'auto',
            mt: '10vh',
            outline: 'none',
          }}
        >
          <Typography variant="h6" mb={2}>
            Upload Banner Images
          </Typography>

          <Box
            onDrop={(e) => {
              e.preventDefault();
              const files = Array.from(e.dataTransfer.files);
              handleBannerUpload(files);
            }}
            onDragOver={(e) => e.preventDefault()}
            sx={{
              border: '2px dashed #aaa',
              borderRadius: 2,
              textAlign: 'center',
              py: 4,
              cursor: 'pointer',
            }}
          >
            <Typography>Drag & drop banner images here</Typography>
            <Typography variant="body2" color="text.secondary">
              or
            </Typography>
            <Button variant="outlined" component="label" sx={{ mt: 1 }}>
              Browse
              <input
                type="file"
                accept="image/*"
                multiple
                hidden
                onChange={(e) => {
                  const files = Array.from(e.target.files);
                  handleBannerUpload(files);
                }}
              />
            </Button>
          </Box>
          <Box
            sx={{
              maxHeight: 300,
              overflowY: 'auto',
              display: 'flex',
              flexWrap: 'wrap',
              gap: 1,
              mt: 2,
              pr: 1,
            }}
          >
            {formik.values.bannerImages?.map((img, i) => (
              <Box
                key={i}
                sx={{
                  position: 'relative',
                  width: 80,
                  height: 80,
                  borderRadius: 2,
                  overflow: 'hidden',
                  '&:hover .delete-icon': {
                    opacity: 1,
                  },
                }}
              >
                <img
                  src={img}
                  alt={`banner-${i}`}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: 6,
                    border: '1px solid #ccc',
                  }}
                />
                <Box
                  className="delete-icon"
                  sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: '100%',
                    height: '100%',
                    bgcolor: 'rgba(0,0,0,0.4)',
                    display: 'flex',
                    justifyContent: 'flex-end',
                    alignItems: 'flex-start',
                    opacity: 0,
                    transition: 'opacity 0.2s ease-in-out',
                  }}
                >
                  <IconButton
                    size="small"
                    sx={{ color: 'white', m: 0.5 }}
                    onClick={() => {
                      const updatedImages = formik.values.bannerImages.filter((_, idx) => idx !== i);
                      formik.setValues({
                        ...formik.values,
                        bannerImages: updatedImages,
                      });
                    }}
                  >
                    <CancelIcon />
                    {/* <Iconify icon="proicons:cancel" sx={{ color: 'red', width: 20, height: 20 }} /> */}
                  </IconButton>
                </Box>
              </Box>
            ))}
          </Box>

          {/* <Stack direction="row" gap={1} mt={2} flexWrap="wrap">
                        {formik.values.banerImages?.map((img, i) => (
                            <img
                                key={i}
                                src={img}
                                alt={`banner-${i}`}
                                style={{ width: 80, height: 80, objectFit: 'cover', borderRadius: 6, border: '1px solid #ccc' }}
                            />
                        ))}
                    </Stack> */}

          <Stack direction="row" justifyContent="flex-end" mt={3}>
            <Button onClick={() => setBannerModalOpen(false)}>Close</Button>
          </Stack>
        </Box>
      </Modal>

      <Typography variant="h4" component="h2" sx={{ pb: 3 }}>
        Add Region
      </Typography>
      <Grid container wrap="wrap" width={'100%'} justifyContent="flex-end" gap={1.5}>
        <Grid item xs={12} lg={2.8} mx="auto">
          <Typography variant="subtitle1" mb={1}>
            Upload Logo
          </Typography>

          <Box
            onClick={() => {
              // Only trigger file picker if logo is not selected
              if (!formik.values.logo) {
                document.getElementById('logo').click();
              }
            }}
            onDrop={(e) => {
              e.preventDefault();
              const file = e.dataTransfer.files[0];
              if (file) {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = (event) => {
                  formik.setValues({
                    ...formik.values,
                    logo: event.target.result,
                  });
                };
              }
            }}
            onDragOver={(e) => e.preventDefault()}
            sx={{
              border: '2px dashed #ccc',
              borderRadius: 2,
              textAlign: 'center',
              position: 'relative',
              height: 200,
              width: '100%',
              cursor: 'pointer',
              bgcolor: '#fafafa',
              overflow: 'hidden',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              px: 1, // reduced horizontal padding
            }}
          >
            <input
              type="file"
              accept="image/*"
              hidden
              id="logo"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.readAsDataURL(file);
                  reader.onload = (event) => {
                    formik.setValues({
                      ...formik.values,
                      logo: event.target.result,
                    });
                  };
                }
              }}
            />

            {formik.values.logo ? (
              <>
                <img
                  src={formik.values.logo}
                  alt="Logo"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    padding: '8px',
                  }}
                />
                <IconButton
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation(); // prevent re-opening file picker
                    formik.setValues({ ...formik.values, logo: '' });
                    document.getElementById('logo').value = '';
                  }}
                  sx={{
                    position: 'absolute',
                    top: 4,
                    right: 4,
                    bgcolor: 'rgba(0,0,0,0.6)',
                    color: '#fff',
                    zIndex: 1,
                  }}
                >
                  <CancelIcon fontSize="small" />
                </IconButton>
              </>
            ) : (
              <Box>
                <Typography variant="body2">Click or drag to upload logo</Typography>
                <Typography variant="caption" color="text.secondary">
                  (only image files)
                </Typography>
              </Box>
            )}
          </Box>
        </Grid>

        <Grid xs={12} lg={8.8}>
          <Card sx={{ height: '100%' }}>
            <Box sx={style}>
              <form
                style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}
                onSubmit={formik.handleSubmit}
              >
                <Grid container gap={2}>
                  <Grid item xs={12} md={11.8}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      name="name"
                      label="Name"
                      onBlur={handleCollegeNameBlur}
                      value={formik.values.name}
                      onChange={formik.handleChange}
                      error={formik.touched.name && Boolean(formik.errors.name)}
                      helperText={formik.touched.name && formik.errors.name}
                    />
                  </Grid>
                  <Grid item xs={12} md={11.8}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      name="description"
                      label="Description"
                      onBlur={handleCollegeNameBlur}
                      value={formik.values.description}
                      onChange={formik.handleChange}
                      error={formik.touched.description && Boolean(formik.errors.description)}
                      helperText={formik.touched.description && formik.errors.description}
                    />
                  </Grid>
                  <Grid item xs={12} md={11.8}>
                    <FormControl sx={{ minWidth: '100%' }}>
                      <SelectComponent
                        menuName={'Name'}
                        menuValue={'id'}
                        menuItems={Groups}
                        name="collegeGroupIds"
                        labelId="group-label"
                        multiple
                        value={formik.values.collegeGroupIds}
                        label="College Groups *"
                        inputLabel="College Groups"
                        onChange={formik.handleChange}
                        error={formik.touched.collegeGroupIds && Boolean(formik.errors.collegeGroupIds)}
                        labelColor={formik.touched.collegeGroupIds && formik.errors.collegeGroupIds && 'error'}
                        labelError={formik.touched.collegeGroupIds && formik.errors.collegeGroupIds}
                        helperText={formik.touched.collegeGroupIds && formik.errors.collegeGroupIds}
                      />
                    </FormControl>
                  </Grid>

                  {BluredOnce && (
                    <Grid xs={11.8}>
                      <UrlComponent
                        isRegion
                        url={formik.values.slug}
                        onInputChange={(value) =>
                          formik.setValues({
                            ...formik.values,
                            slug: value,
                          })
                        }
                        formik={formik}
                        error={formik.errors.slug}
                      />
                    </Grid>
                  )}

                  <Grid item xs={11.8} md={5.8} lg={3.8}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      type="color"
                      name="primaryColor"
                      label="Primary Color"
                      value={formik.values.primaryColor}
                      onChange={formik.handleChange}
                    />
                  </Grid>
                  <Grid item xs={11.8} md={5.8} lg={3.8}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      type="color"
                      name="secondaryColor"
                      label="Secondary Color"
                      value={formik.values.secondaryColor}
                      onChange={formik.handleChange}
                    />
                  </Grid>

                  <Grid item xs={11.8} md={5.8} lg={3.8}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      type="color"
                      name="fontColor"
                      label="Font Color"
                      value={formik.values.fontColor}
                      onChange={formik.handleChange}
                    />
                  </Grid>

                  <Grid item xs={11.8} md={5.8} lg={5}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      type="color"
                      name="button.color"
                      label="Button Font Color"
                      value={formik.values.button.color}
                      onChange={formik.handleChange}
                    />
                  </Grid>
                  <Grid item xs={11.8} md={5.8} lg={5}>
                    <TextFIeldComponent
                      sx={{ width: '100%' }}
                      type="color"
                      name="button.bgColor"
                      label="Button Background Color"
                      value={formik.values.button.bgColor}
                      onChange={formik.handleChange}
                    />
                  </Grid>

                  <Grid item xs={11.8} md={5.8} lg={4}>
                    {/* BG Image Upload Card */}
                    <Card sx={{ minHeight: 240, p: 2 }}>
                      <Typography variant="subtitle1" mb={1}>
                        Background Image
                      </Typography>

                      <Box
                        onDrop={(e) => {
                          e.preventDefault();
                          const file = e.dataTransfer.files?.[0];
                          if (file && file.type.startsWith('image/')) {
                            handleBackgroundUpload(file);
                          }
                        }}
                        onDragOver={(e) => e.preventDefault()}
                        sx={{
                          border: '2px dashed #ccc',
                          borderRadius: 2,
                          p: 2,
                          textAlign: 'center',
                          cursor: 'pointer',
                          position: 'relative',
                          minHeight: 150,
                        }}
                      >
                        {formik.values.bgImage ? (
                          <Box
                            sx={{
                              position: 'relative',
                              width: '100%',
                              height: 150,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                            }}
                          >
                            <img
                              src={formik.values.bgImage}
                              alt="Background"
                              style={{
                                maxHeight: '100%',
                                maxWidth: '100%',
                                objectFit: 'contain',
                                borderRadius: 8,
                              }}
                            />
                            <IconButton
                              size="small"
                              onClick={() => {
                                formik.setValues({ ...formik.values, bgImage: '' });
                              }}
                              sx={{
                                position: 'absolute',
                                top: 8,
                                right: 8,
                                bgcolor: 'rgba(0,0,0,0.6)',
                                color: '#fff',
                              }}
                            >
                              <CancelIcon />
                            </IconButton>
                          </Box>
                        ) : (
                          <>
                            <Typography variant="body2" color="text.secondary">
                              Drag & drop background image here
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              or
                            </Typography>
                            <Button variant="outlined" component="label" sx={{ mt: 1 }}>
                              Browse
                              <input
                                type="file"
                                accept="image/*"
                                hidden
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) handleBackgroundUpload(file);
                                }}
                              />
                            </Button>
                          </>
                        )}
                      </Box>
                    </Card>
                  </Grid>
                  <Grid item xs={11.8} md={5.8} lg={4}>
                    {/* Partner Logos Multi-Select Upload Card */}
                    <Card sx={{ minHeight: 240, p: 2 }}>
                      <Typography variant="subtitle1" mb={1}>
                        Partner Logos
                      </Typography>

                      <Box
                        onDrop={(e) => {
                          e.preventDefault();
                          const files = Array.from(e.dataTransfer.files);
                          handlePartnerUpload(files);
                        }}
                        onDragOver={(e) => e.preventDefault()}
                        sx={{
                          border: '2px dashed #ccc',
                          borderRadius: 2,
                          p: 3,
                          textAlign: 'center',
                          cursor: 'pointer',
                        }}
                      >
                        <Typography variant="body2">Click or drag to upload partner logos</Typography>
                        <Button variant="outlined" component="label" sx={{ mt: 1 }}>
                          Browse
                          <input
                            type="file"
                            accept="image/*"
                            multiple
                            hidden
                            onChange={(e) => {
                              const files = Array.from(e.target.files);
                              handlePartnerUpload(files);
                            }}
                          />
                        </Button>
                      </Box>

                      <Box
                        sx={{
                          display: 'flex',
                          flexWrap: 'wrap',
                          mt: 2,
                          gap: 1,
                          maxHeight: 150,
                          overflowY: 'auto',
                        }}
                      >
                        {formik.values.partnerLogos.map((logo, idx) => (
                          <Box
                            key={idx}
                            sx={{
                              position: 'relative',
                              width: 60,
                              height: 60,
                              borderRadius: 1,
                              overflow: 'hidden',
                              '&:hover .remove-icon': { opacity: 1 },
                            }}
                          >
                            <img
                              src={logo}
                              alt={`logo-${idx}`}
                              style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                                border: '1px solid #ccc',
                                borderRadius: 4,
                              }}
                            />
                            <IconButton
                              className="remove-icon"
                              size="small"
                              onClick={() => {
                                const newImages = formik.values.partnerLogos.filter((_, i) => i !== idx);
                                formik.setValues({ ...formik.values, partnerLogos: newImages });
                              }}
                              sx={{
                                position: 'absolute',
                                top: 0,
                                right: 0,
                                bgcolor: 'rgba(0,0,0,0.5)',
                                color: '#fff',
                                opacity: 0,
                                transition: 'opacity 0.2s ease-in-out',
                              }}
                            >
                              <CancelIcon />
                            </IconButton>
                          </Box>
                        ))}
                      </Box>
                    </Card>
                  </Grid>
                  <Grid item xs={11.8} md={5.8} lg={5}>
                    <Stack direction="row" alignItems="center" spacing={2} mt={2}>
                      <Button
                        startIcon={<PhotoLibraryIcon />}
                        sx={{ minWidth: 210 }}
                        variant="contained"
                        onClick={() => setBannerModalOpen(true)}
                      >
                        {formik?.values?.bannerImages && formik.values.bannerImages?.length > 0 ? 'Edit' : 'Add'} Banner
                        Images
                      </Button>

                      <Stack direction="row" spacing={1}>
                        {formik.values.bannerImages?.slice(0, 3).map((img, i) => (
                          <Box
                            key={i}
                            component="img"
                            src={img}
                            alt={`thumb-${i}`}
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: 1,
                              objectFit: 'cover',
                              border: '1px solid #ccc',
                            }}
                          />
                        ))}
                        {formik.values.bannerImages?.length > 3 && (
                          <Typography variant="body2" sx={{ mt: 0.5 }}>
                            +{formik.values.bannerImages.length - 3} more
                          </Typography>
                        )}
                      </Stack>
                    </Stack>
                  </Grid>
                </Grid>

                {/* {role !== '1' && ( */}
                <Stack direction="row" justifyContent="flex-end">
                  <Button
                    type="button"
                    variant="contained"
                    sx={CancelButton}
                    color="error"
                    onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/regions`)}
                  >
                    Cancel
                  </Button>
                  <LoadingButton loading={loading} type="submit" variant="contained" sx={{ width: '10%', m: 1, mt: 2 }}>
                    Add
                  </LoadingButton>
                </Stack>
                {/* )} */}
              </form>
            </Box>
          </Card>
          {/* <Stack direction="row" justifyContent="flex-end" >
                        <Button
                            type='button'
                            variant='contained'
                            sx={CancelButton}
                            color='error'
                            onClick={() => navigate(`${APP_ROUTER_BASE_URL}dashboard/regions`)}

                        >
                            Cancel
                        </Button>
                        <LoadingButton
                            onClick={formik.handleSubmit}
                            loading={loading}
                            type='submit'
                            variant='contained'
                            sx={{ m: 1, mt: 2 }}
                        >
                            Add
                        </LoadingButton>
                    </Stack> */}
        </Grid>
        {/* { role === '1' && <Grid item xs={12} lg={8.8}>
                    <Card>
                        <Box sx={style}>
                            <Typography variant='h6'>
                                Permissions
                            </Typography>
                            <FormGroup sx={{display:'flex', flexWrap:'wrap', flexDirection:'row', gap:4}}>
                                <FormControlLabel control={<Checkbox name='permissions.dashboard' checked={formik.values?.permissions?.dashboard} onChange={formik.handleChange} />} label="Dashboard" />
                                <FormControlLabel control={<Checkbox name='permissions.campuses' checked={formik.values?.permissions?.campuses} onChange={formik.handleChange} />} label="Campuses" />
                                <FormControlLabel control={<Checkbox name='permissions.users' checked={formik.values?.permissions?.users} onChange={formik.handleChange} />} label="Users" />
                                <FormControlLabel control={<Checkbox name='permissions.courses' checked={formik.values?.permissions?.courses} onChange={formik.handleChange} />} label="Courses" />
                            </FormGroup>
                                
                        </Box>
                    </Card>
                </Grid>} */}
      </Grid>
    </Container>
  );
};

export default AddRegion;
