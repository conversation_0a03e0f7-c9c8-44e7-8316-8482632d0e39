import { Box } from '@mui/material';
import PropTypes from 'prop-types';

const SkillCard = ({ title, text }) => (
  <Box
    sx={{
      border: '1px solid #ccc',
      borderRadius: 2,
      p: 2,
      minHeight: 170,
      backgroundColor: '#fff',
      pt:3
    }}
  >
    <Box sx={{ fontWeight: 700, fontSize: '16px !important', mb: 1, textAlign:'center' }}>{title}</Box>
    <Box sx={{ fontSize: '12px !important', whiteSpace: 'pre-line', textAlign:'center' }}>{text}</Box>
  </Box>
);
SkillCard.propTypes = {
  title: PropTypes.string,
  text: PropTypes.string,
};
export default SkillCard;