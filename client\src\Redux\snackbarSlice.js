import { createSlice } from '@reduxjs/toolkit'


const snackBarSlice = createSlice({
    name: 'snackBar',
    initialState: {
        snackbarOpen: false,
        snackbarType: "",
        snackbarMessage: "",
        snackbarPosition: {
            vertical: 'top',
            horizontal: 'center',
        }
    },
    reducers: {
        setSnackbar: (state, action) => {
            const { snackbarOpen, snackbarMessage, snackbarType } = action.payload;
            state.snackbarOpen = snackbarOpen;
            state.snackbarType = snackbarType;
            state.snackbarMessage = snackbarMessage;
            if (action.payload.snackbarPosition && action.payload.snackbarPosition !== null ) {
                const {vertical , horizontal} = action.payload?.snackbarPosition
                state.snackbarPosition.vertical  = vertical;
                state.snackbarPosition.horizontal  = horizontal;
            }
        }
        // addCollege: (state, action) => {
        //     state.colleges.push(action.payload)
        // },
        // updateColleges: (state, action) => {

        // },
        // deleteCollege: (state, action) => {
        //     state.colleges = state.colleges.filter(college => college.id !== action.payload.id)
        // },
    },
})
export const { setSnackbar } = snackBarSlice.actions
export default snackBarSlice.reducer