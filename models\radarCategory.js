const mongoose = require("mongoose");

const RadarCategorySchema = new mongoose.Schema({
  name:String,
  priority:String,
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.RadarCategorySchema = RadarCategorySchema;

class RadarCategory extends mongoose.Model
{
  
}

mongoose.model(RadarCategory, RadarCategorySchema, "radarCategories");

module.exports.RadarCategory = RadarCategory;
