const mongoose = require("mongoose");

const RadarSubcategorySchema = new mongoose.Schema({
  name: String,
  radarCategoryId: { type: mongoose.Schema.Types.ObjectId, ref: "radarCategory" },
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.RadarSubcategorySchema = RadarSubcategorySchema;

class RadarSubcategory extends mongoose.Model {
  
}

mongoose.model(RadarSubcategory, RadarSubcategorySchema, "radarSubcategories");

module.exports.RadarSubcategory = RadarSubcategory;