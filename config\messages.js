module.exports = {
    REQUIRED: ":name is required.",
    ENGAGED: ":name is already engaged.",
    NOT_FOUND: ":name not found.",
    INVALID_MISSING: "Invalid/Missing :name.",
    INVALID: "Invalid :name.",
    DUPLICATE: "Entry with same name exists.",
    IN_USE: ":name is already in use.",
    NO_ACCESS: "You don't have access to this platform.",
    SERVER_ERROR: "Something went wrong. Please try again later.",
    EXIST_PERMISSION: "Cannot find :name because it does not exist or you do not have permission.",
    ACCESS_DENIED: "Access Denied.",
    ADD_SUCCESS: ":name added successfully",
    ADD_ERROR: "Couldn't add :name",
    UPDATE_SUCCESS: ":name updated successfully",
    UPDATE_ERROR: "Couldn't update :name",
    REVOKE_ERROR: "Couldn't revoke :name",
    REMOVE_SUCCESS: ":name removed successfully",
    REMOVE_ERROR: "Couldn't remove :name",
    REMOVE_ERROR_ENGAGED: "Couldn't remove :name, It is already engaged.",
    INVALID_UID: "Invalid Admin User Id.",
    UNAUTHORIZED: "Invalid :name. You are not authorize to update :name",
    INVALID_USER_ROLE: "Invalid Admin user, Role is not compatible.",
    INVALID_USER_EXISTS: "Invalid Admin user, User is already an admin of another :name.",
    INVALID_USER_BELONG: "Invalid Admin user, User not belong to selected :name.",
    DATA_NOT_FOUND: "Data not found. Please try again later.",
    MATCHING_WITH_OLDER : "New Password cannot be same as Old Password",
    SEND_EMAIL_SUCCESS:"Email submitted successfully",
    CUSTOM: ":name",

}