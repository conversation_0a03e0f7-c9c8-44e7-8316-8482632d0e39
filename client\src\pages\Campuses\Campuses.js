import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
// @mui
import { Container, Typography } from '@mui/material';
import { get } from 'lodash';
import { useNavigate } from 'react-router-dom';
import { setSnackbar } from '../../Redux/snackbarSlice';
import DataTable from '../../components/DataTable/DataTable';
import useAuth from '../../hooks/useAuth';
import useLoading from '../../hooks/useLoading';
import { APP_ROUTER_BASE_URL, allowedRoles } from '../../utils';
import { getCampuses, removeCampus } from './campusesSlice';
import PopUp from '../../components/PopUp';
import FilterForm from '../FilterForm';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import { getColleges } from '../Colleges/collegesSlice';
// ----------------------------------------------------------------------

export const CAMPUS_TABLE_HEAD = [
    { id: 'name', label: 'Campus', alignRight: false },
    { id: 'groupName', label: 'Group', alignRight: false },
    { id: 'collegeName', label: 'College', alignRight: false },
    { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];

export const renderCells = ["name", "groupName", "collegeName"]

const Campuses = () => {
    const campuses = useSelector(state => state.campuses.campuses)
    const { status } = useSelector(state => state.campuses)
    const dispatch = useDispatch()
    const navigate = useNavigate()
    const [Campuses, setCampuses] = useState([]);
    const [FilterCampuses, setFilterCampuses] = useState([]);
    const [Colleges, setColleges] = useState([]);
    const groups = useSelector(state => state.collegeGroups.groups)
    const [selectedGroups, setSelectedGroups] = useState([]);
    const [selectedColleges, setSelectedColleges] = useState([]);
    const [openFilter, setOpenFilter] = useState(false);
    const [Groups, setGroups] = useState([]);
    const loading = useLoading(status)
    const { selectedCollege } = useSelector(state => state.selectedCollege)
    const collegeStates = useSelector(state => state.colleges.colleges)
    const deleteTitle = "Delete Campus?"
    const deleteDescription = "Are you sure you want to delete this campus"
    const { role } = useAuth()
    useEffect(() => {
        setCampuses(campuses)
        setFilterCampuses(campuses)
    }, [campuses])
    useEffect(() => {
        const selected = JSON.parse(localStorage.getItem('selectedCollege'));
        if (selected) {
            dispatch(getCampuses(selected))
        } else {
            dispatch(getCampuses())
        }
    }, [selectedCollege])

    useEffect(() => {
        if (!!collegeStates) {
            setColleges([...collegeStates])
        }
    }, [collegeStates])


    const handleFilterSearch = (event) => {
        const filteredCampuses = Campuses.filter(campus => campus.name?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            campus.groupName?.toLowerCase().includes(event.target.value.toLowerCase()) ||
            campus.collegeName?.toLowerCase().includes(event.target.value.toLowerCase())
        )
        return filteredCampuses
    };

    useEffect(() => {
        if (openFilter) {
            if (role === '1' || role === '2') {
                dispatch(getCollegeGroups())
            }
            if (role === '1' || role === '2' || role === '3') {
                dispatch(getColleges())
            }
        }
    }, [openFilter])

    useEffect(() => {
        if (!!groups) {
            setGroups([...groups])
        }
    }, [groups])
    const handleCloseFilter = () => {
        // setSelectedColleges([])
        // setSelectedGroups([])
        // setUsers(Users)
        setOpenFilter(false)
    }

    const handleFilter = () => {
        setOpenFilter(true)
    }

    // const handleOpen = () => setOpenModel(true);
    const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses/add`);


    const handleDeleteCampus = (campus, handleOpenBackdrop, handleCloseBackdrop) => {
        // dispatch(deleteCampus(campus))
        handleOpenBackdrop()
        dispatch(removeCampus(campus)).then(response => {
            if (response?.payload?.data?.success) {
                const responseMessage = get(response, 'payload.data.message')
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'success',
                    snackbarMessage: responseMessage || "Campus Deleted Succesfully"
                }))
            } else {
                const errorMessage = get(response, 'payload.response.data.message', "Something went wrong")
                dispatch(setSnackbar({
                    snackbarOpen: true,
                    snackbarType: 'error',
                    snackbarMessage: errorMessage
                }))
                console.log("error ==>", errorMessage)
            }
        }).finally(() => {
            handleCloseBackdrop()
        })
    }
    const editCampus = (campus) => {
        navigate(`${APP_ROUTER_BASE_URL}dashboard/campuses/edit/${campus?.id}`)
    }

    return (
        <>
            <Helmet>
                <title> Campuses | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                {/* <Container> */}
                <PopUp
                    open={openFilter}
                    onClose={handleCloseFilter}
                    title={"Filter"}
                >
                    <FilterForm
                        selectedGroups={selectedGroups}
                        setSelectedGroups={setSelectedGroups}
                        openModel={openFilter}
                        setOpenModel={setOpenFilter}
                        setColleges={setColleges}
                        Groups={Groups}
                        Campuses={Campuses}
                        setCampuses={setFilterCampuses}
                        Colleges={Colleges}
                        selectedColleges={selectedColleges}
                        setSelectedColleges={setSelectedColleges}
                        filterName={"campuses"}
                    />
                </PopUp>
                <Typography variant="h4" gutterBottom mb={3}>
                    Campuses
                </Typography>
                <DataTable
                    loading={loading}
                    deleteTitle={deleteTitle}
                    deleteDescription={deleteDescription}
                    TableHead={CAMPUS_TABLE_HEAD}
                    TableData={FilterCampuses || Campuses}
                    filterSearch={handleFilterSearch}
                    searchLable={"Search by Campus..."}
                    buttonText={allowedRoles(role, ['1', '2', '3']) && "New Campus"}
                    buttonHandler={handleOpen}
                    handleEdit={editCampus}
                    renderCells={renderCells}
                    handleDelete={handleDeleteCampus}
                    disableDelete={role !== '1' && role !== '2' && role !== '3'}
                    filter="true"
                    handleFilter={handleFilter}
                    pagination="true"
                    rowsPerPageProp={10}
                />
                {/* </Container> */}
            </Container>
        </>
    )
}

export default Campuses






