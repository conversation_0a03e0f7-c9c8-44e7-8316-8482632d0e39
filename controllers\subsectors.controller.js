const { default: mongoose } = require("mongoose");
const { User, UserRoles } = require("../models/user");
const { Subsector } = require("../models/subsector");
const { getAddedBy, getEditedBy } = require('../tools/database');
const { Sector } = require("../models/sector");
const commonHelper = require("../helpers/commonHelper");
const {messageResponse} = require("../helpers/commonHelper");
const { Career } = require("../models/career");
const { Course } = require("../models/course");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS, REMOVE_ERROR_ENGAGED } = require("../config/messages");

const validate = async(req, action) => {
  try {
    if(action == 'edit') {
      if(!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }
    }
    
    if(!req.body.name) {
      return messageResponse(INVALID_MISSING, "Name", false, 400, null)
    }
    
    if(!mongoose.isValidObjectId(req.body.sectorId)) {
      return messageResponse(INVALID_MISSING, "Sector ID", false, 400, null)
    }
    
    const selectedSector = await Sector.findById(new mongoose.Types.ObjectId(req.body.sectorId));
    if(!selectedSector) {
      return messageResponse(INVALID_MISSING, "Sector", false, 400, null)
    }
    
    let query;
    if(action == 'edit') {
      query = { $and: [ { name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } } ] };
    }
    else {
      query = { name: req.body.name };
    }  
    
    const existingSubsector = await Subsector.findOne(query);
    if(existingSubsector != null) {
      return messageResponse(DUPLICATE, "", false, 400, null);
    }
    
    return { success: true };
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == 'add') {
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newSubsector = await Subsector.create(req.body)

      if(!newSubsector) return messageResponse(ADD_ERROR, "Sub Sector", false, 400, null, res)

      res.status(200).json({ success: true, id: newSubsector._id })
    }
    else {
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedsubsector = await Subsector.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updatedsubsector) return messageResponse(EXIST_PERMISSION, "Sub Sector", false, 404, null, res) 

      return messageResponse(UPDATE_SUCCESS, "Sub Sector", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async(req, res, next) => {
  return await addOrEdit(req, res, 'add');
}
module.exports.add=add;

const get = async(req, res) => {
  try {
    const pipeline = [
      {$lookup: {
        from: "sectors",
        localField: "sectorId",
        foreignField: "_id",
        as: "sectorName"
      }},
      {$set: {sectorName: {$arrayElemAt: ["$sectorName.name", 0]}}},
      {$sort: {'addedBy.date': -1}},
    ]
    const subsectors = await Subsector.aggregate(pipeline);

    if(!subsectors.length) return messageResponse(NOT_FOUND, "Sub Sectors", false, 404, null, res) 

    return messageResponse(null, "", true, 200, subsectors, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.get=get;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const pipeline = [
      {$match: { _id: new mongoose.Types.ObjectId(req.query.id) }},
      {$lookup: {
        from: "sectors",
        localField: "sectorId",
        foreignField: "_id",
        as: "sectorName"
      }},
      {$set: {sectorName: {$arrayElemAt: ["$sectorName.name", 0]}}},
    ]
    let subSector = await Subsector.aggregate(pipeline);
    subSector = subSector[0]

    if (!subSector) return messageResponse(NOT_FOUND, "Sub Sector", false, 404, null, res) 

    return messageResponse(null, "", true, 200, subSector, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.getByID=getByID;

const remove = async(req, res) => {
  try {
    if(!mongoose.isValidObjectId(req.body.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const career = await Career.findOne({sectorIds: {$in: [req.body.id]}})
    if (career) return messageResponse(REMOVE_ERROR_ENGAGED, "Sector", false, 400, null, res);

    const course = await Course.findOne({sectorIds: {$in: [req.body.id]}})
    if (course) return messageResponse(REMOVE_ERROR_ENGAGED, "Sector", false, 400, null, res);

    const subsector = await Subsector.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

    if(!subsector) return messageResponse(NOT_FOUND, "Sub Sector", false, 404, null, res);

    return messageResponse(REMOVE_SUCCESS, "Sub Sector", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove=remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit')
};
module.exports.update=update;