import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance'
import { postRadarSubCategory } from '../RadarSubCategory/RadarSubCategorySlice';

export const getRadarCategories = createAsyncThunk('radar-category/getRadarCategories', async () => {
    try {
        const response = await axiosInstance({
            url: "radar-category/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        console.log("error get users", error)
        return error
    }
})

export const postRadarCategory = createAsyncThunk("radar-category/add", async (data) => {
    try {
        const response = await axiosInstance({
            url: "radar-category/add",
            method: "POST",
            data
        })
        response.data.name = data?.name;
        response.data._id = response.data.id;
        return response
    } catch (error) {
        console.log("error post users", error)
        return error
    }
})

export const updateRadarCategory = createAsyncThunk('radar-category/update', async (data, {rejectWithValue}) => {
    try {
        const response = await axiosInstance({
            url: "radar-category/update",
            method: "PUT",
            data
        })
        response.data.name = data?.name;
        response.data._id = data?.id;
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

export const removeRadarCategory = createAsyncThunk('radar-category/remove', async (data) => {

    try {
        const response = await axiosInstance({
            url: "radar-category/remove",
            method: "DELETE",
            data
        })
        response.data._id = data.id
        return response.data;
    } catch (error) {
        return error
    }
})

const radarCategorySlice = createSlice({
    name:'radarCategories',
    initialState: {
        status:'idle',
        radarCategories: []
    },
    reducers:{
        addRadarCategory : (state, action) =>{
            state.radarCategories.push(action.payload)
        },
        updateRadarCategory : (state, action) =>{

        },
        deleteRadarCategory : (state, action) =>{
            state.radarCategories = state.radarCategories.filter(category => category.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getRadarCategories.pending]: (state) => {
            state.status = 'pending'
        },
        [getRadarCategories.fulfilled]: (state, action) => {
            state.radarCategories = action.payload.data
            state.status = 'succeded'
        },
        [getRadarCategories.rejected]: (state, action) => {
            console.log("error",action.payload)
            state.status = 'rejected'
        },
        [postRadarCategory.fulfilled]: (state, action) => {
            const data = action.payload.data
            if(data){
                state.radarCategories.push(data)
            }
        },
        [postRadarCategory.rejected]: (state, action) => {
            const error = action.payload
            console.log("error post sector", error)
        },
        [updateRadarCategory.fulfilled]: (state, action) => {
            state.radarCategories = state.radarCategories.map((item)=>{
                if(item._id === action.payload._id){
                    item.name = action.payload.name
                    return item
                }
                return item
            })
        },
        [updateRadarCategory.rejected]: (state, action) => {
            const error = action.payload
            console.log("error update sector", error)
        },
        [removeRadarCategory.fulfilled]: (state, action) => {
            const data = action.payload
            state.radarCategories = state.radarCategories?.filter(item => item._id !== data._id)
        },
        [removeRadarCategory.rejected]: (state, action) => {
            // const = action.payload
            const error = action.payload
            console.log("remove sector error", error)
        },
        // [postRadarSubCategory.fulfilled]: (state, action) => {
        //     const {name,id,radarCategoryId,radarCategoryName} = action.payload.data
        //     const data = {radarCategoryId, name, radarCategoryName, _id:id};
        //     if(data){
        //         state.radarCategories.find(radar => radar?._id === radarCategoryId).radarSubcategories.push(data)
        //         state.radarSubCategories.push(data)
        //     }
        // },
    }
})
export const { addRadarCategory, deleteRadarCategory } = radarCategorySlice.actions;
export default radarCategorySlice.reducer