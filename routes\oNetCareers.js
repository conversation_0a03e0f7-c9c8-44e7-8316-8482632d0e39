const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const oNetCareersController = require('../controllers/oNetCareers.controller')

const AuthGuard = require("../guards/auth.guard");
const SuperUserGuard = require("../guards/super-user.guard");

router.post("/createONetCareers", [AuthGuard, SuperUserGuard], oNetCareersController.createONetCareers);

module.exports = router;