import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';
// @mui
import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import HomeIcon from '@mui/icons-material/Home';
import TableRowsRoundedIcon from '@mui/icons-material/TableRowsRounded';
import {
  AppBar,
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Grid,
  Stack,
  Toolbar,
  Typography,
} from '@mui/material';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Logo from 'src/assets/images/whiteUpskill_ReskillLogo.png';
import useOffSetTop from 'src/hooks/useOffSetTop';
//
import Cookies from 'js-cookie';
import { jwtDecode } from 'jwt-decode';
import { get } from 'lodash';
import main_logo from 'src/assets/images/thinkskill.png';
import ModalComponent from 'src/components/Modal/Modal';
import HeaderShadow from '../../components/HeaderShadow';

// ----------------------------------------------------------------------

export default function Header({ headerOnDark, clgDetails, isLoading }) {
  const params = useParams();
  const navigate = useNavigate();
  const isOffset = useOffSetTop();
  const [open, setOpen] = useState(false);
  const [showHeader, setShowHeader] = useState(false);
  const [userInfo, setUserInfo] = useState({});
  const navigateHome = () => {
    if (params?.cg_name) {
      navigate(`${params?.cg_name}`);
    }
  };

  const token = Cookies.get('feToken');
  useEffect(() => {
    if (token) {
      try {
        const userData = jwtDecode(token);
        setUserInfo(userData);
      } catch (err) {
        console.error('Invalid token', err);
      }
    }
  }, [token]);
  const isRegion = !!params?.rg_name;
  useEffect(() => {
    if (clgDetails) {
      setShowHeader(true);
    } else {
      setShowHeader(false);
    }
  }, [clgDetails]);

  const redirectUrl = (data, type) => {
    let renderLink = '';
    if (data && data.length > 0) {
      const httpCheck = data.toLocaleLowerCase().startsWith('http');
      if (httpCheck) {
        renderLink = data;
      } else {
        renderLink = `http://${data}`;
      }
    }
    return (
      // <a href={renderLink} rel="noreferrer" target='_blank'> <Typography variant='medium' color="white" sx={{textTransform:'none',display:'flex',alignItems:'center',fontSize:'1.05rem'}} >{type}<KeyboardArrowRightIcon sx={{color:'white'}} /></Typography></a>
      renderLink
    );
  };

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const [isLogoutOpen, setIsLogoutOpen] = useState(false);
  const handleLogout = () => {
    Cookies.remove('feToken', { path: '/' });
    setIsLogoutOpen(false);
    if (isRegion) {
      navigate(`/region/${params.rg_name}/career-history`);
    } else {
      navigate(`/${params.cg_name}/career-history`);
    }
  };

  if (showHeader && clgDetails) {
    return isRegion ? (
      <>
        <Dialog open={isLogoutOpen} onClose={() => setIsLogoutOpen(false)}>
          <DialogTitle>Confirm Logout</DialogTitle>
          <DialogContent>
            <DialogContentText>Are you sure you want to logout?</DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsLogoutOpen(false)} color="primary">
              Cancel
            </Button>
            <Button onClick={handleLogout} color="error" autoFocus>
              Logout
            </Button>
          </DialogActions>
        </Dialog>
        {/* <AppBar position="static" sx={{ bgcolor: 'white', color: 'white' }}>
          <Toolbar
            sx={{ justifyContent: 'space-between', alignItems: 'center', py: 1, flexDirection: { sm: 'row', xs: 'column' } }}
          >
            <Box sx={{height: 1, width: '28%'}} />
            <Box display="flex" alignItems="center">
              <img
                src={clgDetails?.data?.logo || main_logo}
                alt="College Logo"
                style={{ height: 68, marginRight: 12 }}
              />
            </Box>
              <Box
            sx={{
              display: 'flex',
              justifyContent: 'end',
              alignItems: 'center',
              // bgcolor: '#f5f5f5',
              columnGap: 2,
              px: 2,
              // pbs: 0.6,
              // borderTop: '2px solid #000', // Top black line
            }}
          >
            <Typography
              component={Link}
              to={`/region/${params.rg_name}/career-history`}
              sx={{
                fontSize: '12px !important',
                fontWeight: 600,
                color: '#000',
                textDecoration: 'none',
                '&:hover': {
                  textDecoration: 'underline',
                },
              }}
            >
              Your Career History
            </Typography>

            {token ? (
              <Stack direction="row" alignItems="center" gap={2}>
                <Typography
                  component={Link}
                  sx={{
                    fontSize: '12px !important',
                    fontWeight: 600,
                    color: '#000',
                    textDecoration: 'none',
                    '&:hover': {
                      textDecoration: 'underline',
                    },
                  }}
                >
                  Logged in{' '}
                  {get(userInfo, 'user.firstName') && `as ${get(userInfo, 'user.firstName')}`}
                </Typography>
                <Button
                  onClick={() => setIsLogoutOpen(true)}
                  sx={{ fontSize: '12px !important', fontWeight: 600 }}
                >
                  Logout
                </Button>
              </Stack>
            ) : (
              <Button
                component={Link}
                to={`/region/${params.rg_name}/sign-up`}
                variant="outlined"
                sx={{
                  fontSize: '12px !important',
                  px: 2,
                  py: 0.5,
                  borderRadius: '20px',
                  textTransform: 'none',
                  whiteSpace: 'nowrap',
                }}
              >
                Create a free account or sign in
              </Button>
            )}
          </Box>
          </Toolbar>
        
        </AppBar> */}
        <AppBar position="static" sx={{ bgcolor: 'white', color: 'black' }}>
          <Toolbar sx={{ py: 1 }}>
            <Grid container alignItems="center">
              <Grid item xs={12} sm={4} display="flex" justifyContent="flex-start" />

              <Grid item xs={12} sm={4} display="flex" justifyContent="center">
                <img
                  src={clgDetails?.data?.logo || main_logo}
                  alt="College Logo"
                  style={{ height: 68 }}
                />
              </Grid>

              <Grid
                item
                xs={12}
                sm={4}
                display="flex"
                justifyContent="flex-end"
                alignItems="center"
                px={2}
                columnGap={2}
              >
                {token ? (
                  <Typography
                    component={Link}
                    to={`/region/${params.rg_name}/career-history`}
                    sx={{
                      fontSize: '12px !important',
                      fontWeight: 600,
                      color: '#000',
                      textDecoration: 'none',
                      '&:hover': { textDecoration: 'underline' },
                    }}
                  >
                    Your Career History
                  </Typography>
                ) : null}

                {token ? (
                  <Stack direction="row" alignItems="center" gap={2}>
                    <Typography
                      sx={{
                        fontSize: '12px !important',
                        fontWeight: 600,
                        color: '#000',
                      }}
                    >
                      Logged in{' '}
                      {get(userInfo, 'user.firstName') && `as ${get(userInfo, 'user.firstName')}`}
                    </Typography>
                    <Button
                      onClick={() => setIsLogoutOpen(true)}
                      sx={{ fontSize: '12px !important', fontWeight: 600 }}
                    >
                      Logout
                    </Button>
                  </Stack>
                ) : (
                  <Button
                    component={Link}
                    to={`/region/${params.rg_name}/sign-up`}
                    variant="outlined"
                    sx={{
                      fontSize: '12px !important',
                      px: 2,
                      py: 0.5,
                      borderRadius: '20px',
                      textTransform: 'none',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    Create a free account or sign in
                  </Button>
                )}
              </Grid>
            </Grid>
          </Toolbar>
        </AppBar>
      </>
    ) : (
      <>
        <Dialog open={isLogoutOpen} onClose={() => setIsLogoutOpen(false)}>
          <DialogTitle>Confirm Logout</DialogTitle>
          <DialogContent>
            <DialogContentText>Are you sure you want to logout?</DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setIsLogoutOpen(false)} color="primary">
              Cancel
            </Button>
            <Button onClick={handleLogout} color="error" autoFocus>
              Logout
            </Button>
          </DialogActions>
        </Dialog>
        <AppBar position="static" sx={{ bgcolor: 'white', color: 'black' }}>
          <Toolbar sx={{ py: 1 }}>
            <Grid container alignItems="center">
              <Grid item xs={12} sm={4} display="flex" justifyContent="flex-start" />

              <Grid item xs={12} sm={4} display="flex" justifyContent="center">
                <img
                  src={clgDetails?.data?.region?.logo || clgDetails?.data?.logo || main_logo}
                  alt="College Logo"
                  style={{ height: 68 }}
                />
              </Grid>

              <Grid
                item
                xs={12}
                sm={4}
                display="flex"
                justifyContent="flex-end"
                alignItems="center"
                px={2}
                columnGap={2}
              >
                {token ? (
                  <Typography
                    component={Link}
                    to={`/${params.cg_name}/career-history`}
                    sx={{
                      fontSize: '12px !important',
                      fontWeight: 600,
                      color: '#000',
                      textDecoration: 'none',
                      '&:hover': { textDecoration: 'underline' },
                    }}
                  >
                    Your Career History
                  </Typography>
                ) : null}

                {token ? (
                  <Stack direction="row" alignItems="center" gap={2}>
                    <Typography
                      sx={{
                        fontSize: '12px !important',
                        fontWeight: 600,
                        color: '#000',
                      }}
                    >
                      Logged in{' '}
                      {get(userInfo, 'user.firstName') && `as ${get(userInfo, 'user.firstName')}`}
                    </Typography>
                    <Button
                      onClick={() => setIsLogoutOpen(true)}
                      sx={{ fontSize: '12px !important', fontWeight: 600 }}
                    >
                      Logout
                    </Button>
                  </Stack>
                ) : (
                  <Button
                    component={Link}
                    to={`/${params.cg_name}/sign-up`}
                    variant="outlined"
                    sx={{
                      fontSize: '12px !important',
                      px: 2,
                      py: 0.5,
                      borderRadius: '20px',
                      textTransform: 'none',
                      whiteSpace: 'nowrap',
                    }}
                  >
                    Create a free account or sign in
                  </Button>
                )}
              </Grid>
            </Grid>
          </Toolbar>
        </AppBar>
      </>
    );
  }
  if (false || showHeader) {
    return (
      <AppBar color="transparent" className="AppHeader">
        <Toolbar disableGutters>
          <Box className="nav-wrapper">
            {window.location.pathname !== `/${params?.cg_name}` && (
              <Button
                aria-describedby="nav-menu"
                variant="text"
                className="nav-open"
                onClick={navigateHome}
              >
                <Typography variant="button">
                  <HomeIcon />
                </Typography>
              </Button>
            )}
            <Button
              aria-describedby="nav-menu"
              variant="text"
              className="nav-open"
              onClick={handleOpen}
            >
              <Typography variant="button">
                <TableRowsRoundedIcon />
              </Typography>
            </Button>

            <ModalComponent
              open={open}
              handleClose={handleClose}
              className="sidebar-nav"
              type="sidebar"
            >
              <Button
                aria-describedby="nav-menu"
                variant="text"
                onClick={handleClose}
                className="close-btn"
              >
                <Typography variant="button">
                  <CloseRoundedIcon />
                </Typography>
              </Button>
              {!isLoading ? (
                <Box className="sidebar-wrapper">
                  <a target="_blank" rel="noreferrer" href={redirectUrl(clgDetails.data.website)}>
                    <img
                      className="clg-logo"
                      src={clgDetails?.data?.logo ? clgDetails?.data?.logo : main_logo}
                      alt="College Logo"
                    />
                  </a>
                  <Box className="clg-info-wrapper">
                    <Box className="clg-info">
                      <Typography variant="bold">Address</Typography>
                      <Typography variant="body1">
                        {clgDetails?.data?.name ? clgDetails?.data?.name : ''}
                        <br /> {clgDetails?.data?.address1 ? clgDetails?.data?.address1 : '-'}
                        <br /> {clgDetails?.data?.city ? clgDetails?.data?.city : ''}
                        <br /> {clgDetails?.data?.state ? clgDetails?.data?.state : ''}{' '}
                        {clgDetails?.data?.zip ? clgDetails?.data?.zip : ''}
                      </Typography>
                    </Box>
                    <Box className="clg-info">
                      <Typography variant="bold">Phone</Typography>
                      {clgDetails?.data?.contactNumber ? (
                        <Typography variant="body1" onClick={handleClose}>
                          <a href={`tel:${clgDetails?.data?.contactNumber}`}>
                            {clgDetails?.data?.contactNumber}
                          </a>
                        </Typography>
                      ) : (
                        <Typography variant="body1">
                          <a>-</a>
                        </Typography>
                      )}
                    </Box>
                    <Box className="clg-info">
                      <Typography variant="bold">Email</Typography>
                      {clgDetails?.data?.email ? (
                        <Typography variant="body1" onClick={handleClose}>
                          <a href={`mailto:${clgDetails.data.email}`}>{clgDetails.data.email}</a>
                        </Typography>
                      ) : (
                        <Typography variant="body1">
                          <a>-</a>
                        </Typography>
                      )}
                    </Box>
                    <Box className="clg-info">
                      <Typography variant="bold">Website</Typography>
                      {clgDetails?.data?.website ? (
                        <Typography variant="body1" onClick={handleClose}>
                          <a
                            target="_blank"
                            rel="noreferrer"
                            href={redirectUrl(clgDetails.data.website)}
                          >
                            {clgDetails.data.website}
                          </a>
                        </Typography>
                      ) : (
                        <Typography variant="body1">
                          <a>-</a>
                        </Typography>
                      )}
                    </Box>
                  </Box>
                  <Box className="nav-links">
                    {/* <Link to={`${params.cg_name}/privacy-policy`} onClick={handleClose}>Privacy Policy</Link> */}
                    <Link to={`${params.cg_name}/terms-conditions`} onClick={handleClose}>
                      Terms & Condition
                    </Link>
                    {/* <Link to={`${params.cg_name}/disclaimer`} onClick={handleClose}>Disclaimer</Link> */}
                  </Box>
                  <Box className="nav-footer">
                    <Box className="logo">
                      <Typography variant="medium">powered by</Typography>
                      <img src={Logo} alt="Logo" />
                    </Box>
                    <Typography>©2023 Horizon AI Limited</Typography>
                  </Box>
                </Box>
              ) : (
                <CircularProgress />
              )}
            </ModalComponent>
          </Box>
        </Toolbar>

        {isOffset && <HeaderShadow />}
      </AppBar>
    );
  }
}

Header.propTypes = {
  headerOnDark: PropTypes.bool,
  clgDetails: PropTypes.string,
  isLoading: PropTypes.bool,
};
