const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const lmiSkillsAbilities = require('../controllers/lmiSkillsAbilities.controller')
const SuperUserGuard = require('../guards/super-user.guard')

// router.post("/add", SuperUserGuard, lmiSkillsAbilities.add);

router.get("/get", lmiSkillsAbilities.get);

router.get("/getById", lmiSkillsAbilities.getByID);

// router.delete("/remove", SuperUserGuard, lmiSkillsAbilities.remove);

router.put("/update", SuperUserGuard, lmiSkillsAbilities.update);

module.exports = router;