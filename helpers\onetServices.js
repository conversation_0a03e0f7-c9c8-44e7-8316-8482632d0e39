const axios = require('axios');
const keys = require("../config/keys");
const { ONetCareer } = require('../models/oNetCareer');

class OnetServices {

  async getCareerByKeyword(keyword) {

    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `https://api-v2.onetcenter.org/mnm/search?keyword=${keyword}&start=1&end=923`,
      headers: {
        'X-API-Key': keys.ONET_KEY_V2
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getJobZones() {

    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `https://api-v2.onetcenter.org/mnm/job_preparation/`,
      headers: {
        'X-API-Key': keys.ONET_KEY_V2
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getONetCareers(jobZoneValue) {

    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: `https://api-v2.onetcenter.org/mnm/job_preparation/${jobZoneValue}?start=1&end=999`,
      headers: {
        'X-API-Key': keys.ONET_KEY_V2
      }
    };

    const response = await axios.request(config);
    return response;
  }

  async getJobZone(oNetCode) {

    const pipeline = [
      { $match: { code: oNetCode } },
      {
        $lookup: {
          from: "jobZones",
          localField: "jobZone",
          foreignField: "value",
          as: "jobZone"
        }
      },
      {
        $project: {
          "jobZone.value": 1,
          "jobZone.title": 1,
        }
      },
      { $limit: 1 }
    ]
    const jobZone = await ONetCareer.aggregate(pipeline);

    if(jobZone && jobZone.length > 0) {
      return jobZone[0];
    }
    return null;
  }
}

module.exports = OnetServices;
