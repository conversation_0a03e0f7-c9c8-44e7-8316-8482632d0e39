const { default: mongoose } = require("mongoose");
const { getAddedBy } = require('../tools/database');
const commonHelper = require("../helpers/commonHelper");
const OnetServices = require("../helpers/onetServices");
const { JobZone } = require("../models/jobZone");
const { ONetCareer } = require("../models/oNetCareer");
const { Career } = require("../models/career");

const createONetCareers = async(req, res, next) => {
  try {
    if (req.body.deleteAll) {
      const deletedONetCareers = await ONetCareer.deleteMany({});
    }

    let onet = new OnetServices();
    const careersOfJobZone1 = await onet.getONetCareers(1);
    if (!careersOfJobZone1 || !careersOfJobZone1.data) {
        return res.status(500).json({ success: false, msg: 'Problem in get job zone "1" data, please try again later' });
    }

    const careersOfJobZone2 = await onet.getONetCareers(2);
    if (!careersOfJobZone2 || !careersOfJobZone2.data) {
        return res.status(500).json({ success: false, msg: 'Problem in get job zone "2" data, please try again later' });
    }

    const careersOfJobZone3 = await onet.getONetCareers(3);
    if (!careersOfJobZone3 || !careersOfJobZone3.data) {
        return res.status(500).json({ success: false, msg: 'Problem in get job zone "3" data, please try again later' });
    }

    const careersOfJobZone4 = await onet.getONetCareers(4);
    if (!careersOfJobZone4 || !careersOfJobZone4.data) {
        return res.status(500).json({ success: false, msg: 'Problem in get job zone "4" data, please try again later' });
    }

    const careersOfJobZone5 = await onet.getONetCareers(5);
    if (!careersOfJobZone5 || !careersOfJobZone5.data) {
        return res.status(500).json({ success: false, msg: 'Problem in get job zone "5" data, please try again later' });
    }

    const careers = [];
    const addedBy = getAddedBy(req);
    careersOfJobZone1.data.career.forEach(career => {
        career.jobZone = 1
        career.addedBy = addedBy;
        careers.push(career);
    });
    careersOfJobZone2.data.career.forEach(career => {
        career.jobZone = 2
        career.addedBy = addedBy;
        careers.push(career);
    });
    careersOfJobZone3.data.career.forEach(career => {
        career.jobZone = 3
        career.addedBy = addedBy;
        careers.push(career);
    });
    careersOfJobZone4.data.career.forEach(career => {
        career.jobZone = 4
        career.addedBy = addedBy;
        careers.push(career);
    });
    careersOfJobZone5.data.career.forEach(career => {
        career.jobZone = 5
        career.addedBy = addedBy;
        careers.push(career);
    });

    let newCareers;
    if (careers && careers.length > 0) {
      newCareers = await ONetCareer.create(careers);
    }
    else {
      return res.status(500).json({ success: false, msg: "Problem in get data, please try again later" });
    }
    
    res.status(200).json({ success: true });
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return res.status(500).json({ success: false, msg: "Something went wrong, please try again later", error:error.message });
  }
};
module.exports.createONetCareers = createONetCareers;