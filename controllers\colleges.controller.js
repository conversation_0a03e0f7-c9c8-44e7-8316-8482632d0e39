const { default: mongoose } = require("mongoose");
const axios = require('axios');
const qs = require('qs');
const { College } = require("../models/college");
const { Campus } = require("../models/campus");
const { UserRoles, User } = require("../models/user");
const { Country } = require("../models/country");
const { getAddedBy, getEditedBy } = require('../tools/database')
const commonHelper = require("../helpers/commonHelper");
const {messageResponse, checkSlug} = require("../helpers/commonHelper");
const commonClass = require("../helpers/commonClass");
// const commonClass = require("../helpers/commonClass");
const { updateUserRole, revokeUserRole } = require("../controllers/users.controller");
const { UNAUTHORIZED, INVALID, ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, INVALID_UID, INVALID_USER_ROLE, DUPLICATE, UPDATE_SUCCESS, REMOVE_ERROR_ENGAGED, REMOVE_SUCCESS, INVALID_USER_BELONG, IN_USE } = require("../config/messages");
const { toObjectId } = require("../helpers/misc");
const { Sector } = require("../models/sector");
const { Subsector } = require("../models/subsector");
const { Course } = require("../models/course");

const getCoursesFromCleverHub = async (apiKey) => {
  try {
    let data = qs.stringify({
      'API_KEY': apiKey,
      'retrieve': 'course'
    });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: 'https://cleverhub.co.uk/v2/api',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data
    };

    const response = await axios.request(config);
    return {
      success: true,
      data: response?.data?.posts
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return {
      success: false,
      errorMsg: error.message,
    }
  }
}

const validate = async(req, res, action) => {
  try {
    let college;
    if (action == 'edit') {
      if (!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }

      college = await College.findById(new mongoose.Types.ObjectId(req.body.id), { logo: 0 });
      if (!college) {
        return messageResponse(NOT_FOUND, "College", false, 404, null);
      }
      else if (req.user.role == UserRoles.COLLEGE_ADMIN && college.adminUserId.toString() != req.user._id.toString()) {
        return messageResponse(EXIST_PERMISSION, "College", false, 404, null)
      }
    }

    if (!req.body.name) {
      return messageResponse(INVALID_MISSING, "Name", false, 400, null)
    }

    const result = await checkSlug(College, req.body)
    if (!result.isValid) {
      return messageResponse(INVALID_MISSING, "Slug", false, 400, null)
    } else if (result.isDuplicate) {
      return messageResponse(IN_USE, "Slug", false, 400, null)
    }

    if (!mongoose.isValidObjectId(req.body.collegeGroupId)) {
      return messageResponse(INVALID_MISSING, "College Group ID", false, 400, null)
    }

    const allowedCGs = await commonClass.getAllowedCollegeGroups(req, college);
    const CG = allowedCGs.find(collegeGroup => collegeGroup._id == req.body.collegeGroupId);
    if (!CG) {
      return messageResponse(INVALID_MISSING, "College Group", false, 400, null)
    }

    // const selectedCG = await CollegeGroup.findById(new mongoose.Types.ObjectId(req.body.collegeGroupId));
    // if(!selectedCG) {
    //   return { statusCode:400, success: false, msg: "Invalid/missing College Group." };
    // }

    if (req.body.countryId) {
      if (!mongoose.isValidObjectId(req.body.countryId)) {
        return messageResponse(INVALID_MISSING, "Country ID", false, 400, null)
      }

      const selectedCountry = await Country.findById(new mongoose.Types.ObjectId(req.body.countryId));
      if (!selectedCountry) {
        return messageResponse(INVALID_MISSING, "Country", false, 400, null)
      }
    }

    if (req.body.adminUserId) {
      if (!mongoose.isValidObjectId(req.body.adminUserId)) {
        return messageResponse(INVALID_UID, "", false, 400, null)
      }

      if (action == 'edit' && req.user.role == UserRoles.COLLEGE_ADMIN) {
        if (college.adminUserId != req.body.adminUserId) {
          return messageResponse(UNAUTHORIZED, "Admin User", false, 400, null)
        }
        if (college.collegeGroupId != req.body.collegeGroupId) {
          return messageResponse(UNAUTHORIZED, "College Group", false, 400, null)
        }
      }
      else {
        const allowedUsers = await commonClass.getAllowedUsers(req);
        const user = allowedUsers.find(user => user._id == req.body.adminUserId);
        if (user) {
          if (user.role == UserRoles.COLLEGE_ADMIN) {
            let collegeIdOtherThanCurrentId = user.collegeIds[0];
            if (action == 'edit') {
              collegeIdOtherThanCurrentId = user.collegeIds.find(cid => cid != req.body.id);
            }
            if (collegeIdOtherThanCurrentId) {
              const collegeOfSelectedUser = await College.findById(collegeIdOtherThanCurrentId);
              if (collegeOfSelectedUser && collegeOfSelectedUser.collegeGroupId != req.body.collegeGroupId) {
                return messageResponse(INVALID_USER_BELONG, "College Group", false, 400, null)
              }
            }
          }
          else if (user.role != UserRoles.NOT_AVAILABLE) {
            return messageResponse(INVALID_USER_ROLE, "", false, 400, null);
          }
        }
        else {
          return messageResponse(INVALID, "Admin User", false, 400, null)
        }
      }
    }

    let query = { name: req.body.name };
    if (action == 'edit') {
      query = { $and: [{ name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } }] };
    }
    const existingCollege = await College.findOne(query);
    if (existingCollege != null) {
      return messageResponse(DUPLICATE, "", false, 400, null);
    }

    return { success: true, college };
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addOrEdit = async(req, res, action) => {
  try {
    const validateResult = await validate(req, res, action);

    if (!validateResult.success) {

      const statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];
      // res.status(validateResult.statusCode ? statusCode : 400).json(validateResult);
      if (statusCode == 400) {
        return res.status(400).json(validateResult);
      }
      else if (statusCode == 404) {
        return res.status(404).json(validateResult);
      }
      else {
        return res.status(500).json(validateResult);
      }
    }

    if (action == 'add') {
      const addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      const newCollege = await College.create(req.body);

      if(!newCollege) return messageResponse(ADD_ERROR, "College", false, 400, null, res)

      await updateUserRole(req.body.adminUserId, 3, newCollege._id);
      res.status(200).json({ success: true, id: newCollege._id })
    }
    else {
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedCollege = await College.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updatedCollege) return messageResponse(EXIST_PERMISSION, "College", false, 404, null, res) 

      await updateUserRole(req.body.adminUserId, 3, req.body.id, validateResult.college);
      return messageResponse(UPDATE_SUCCESS, "College", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}

const add = async(req, res, next) => {
  return await addOrEdit(req, res, 'add');
}
module.exports.add = add;

const get = async(req, res) => {
  try {
    const pipeline = [
      {
        $lookup: {
          from: "collegeGroups",
          localField: "collegeGroupId",
          foreignField: "_id",
          as: "collegeGroup"
        }
      },
      {
        $lookup: {
          from: "users",
          localField: "adminUserId",
          foreignField: "_id",
          as: "adminUser"
        }
      },
      {
        $project: {
          "logo": 0,

          "collegeGroup.addedBy": 0,
          "collegeGroup.editedBy": 0,

          "adminUser.password": 0,
          "adminUser.collegeGroupIds": 0,
          "adminUser.collegeIds": 0,
          "adminUser.campusIds": 0,
        }
      },
      { $unwind: { path: "$collegeGroup" } },
      { $unwind: { path: "$adminUser" } }
    ]

    // const colleges = await College.find({}, {logo:0});
    // const colleges = await College.aggregate(pipeline);

    let campusId = req.user.campusIds;

    let editingCampus = await Campus.findById(campusId).select({collegeId:1,_id:0})
    
    
    const allowedColleges = await commonClass.getAllowedColleges(req,editingCampus);

    if(!allowedColleges.length) return messageResponse(NOT_FOUND, "Colleges", false, 404, null, res) 

    return messageResponse(null, "", true, 200, allowedColleges, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.get = get;

const getByID = async(req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const allowedColleges = await commonClass.getAllowedColleges(req);

    if(!allowedColleges.length) return messageResponse(NOT_FOUND, "College", false, 404, null, res)

    const college = allowedColleges.find(college => college._id == req.query.id);
    if (!college) return messageResponse(EXIST_PERMISSION, "College", false, 404, null, res) 

    return messageResponse(null, "", true, 200, college, res)

    // const pipeline = [
    //   { $match: { _id: new mongoose.Types.ObjectId(req.query.id) } },
    //   {
    //     $lookup: {
    //       from: "collegeGroups",
    //       localField: "collegeGroupId",
    //       foreignField: "_id",
    //       as: "collegeGroup"
    //     }
    //   },
    //   {
    //     $lookup: {
    //       from: "users",
    //       localField: "adminUserId",
    //       foreignField: "_id",
    //       as: "adminUser"
    //     }
    //   },
    //   {
    //     $project: {
    //       "collegeGroup.addedBy": 0,
    //       "collegeGroup.editedBy": 0,
    //       "collegeGroup.status": 0,
    //       "collegeGroup.defaultEntry": 0,

    //       "adminUser.password": 0,
    //       "adminUser.collegeGroupIds": 0,
    //       "adminUser.collegeIds": 0,
    //       "adminUser.campusIds": 0,
    //     }
    //   },
    //   { $unwind: { path: "$collegeGroup" } },
    //   { $unwind: { path: "$adminUser" } }
    // ]
    // College.aggregate(pipeline)
    //   .catch(error => {
    //     console.log("Couldn't find this College", error);
    //     return res.status(400).json({ msg: "Couldn't find this College." });
    //   })
    //   .then(entry => {
    //     if (!entry) {
    //       return res.status(404).json({ msg: "College not found." });
    //     }
    //     res.json(entry[0]);
    //   });
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
}
module.exports.getByID = getByID;

const syncCourses = async(req, res) => {
  try {
    const { id } = req.query;

    const existingCollege = await College.findById(id).select({cleverHubApiKey: 1});
    if(!existingCollege) {
      return messageResponse(NOT_FOUND, "College", false, 404, null, res);
    }
    if(!existingCollege.cleverHubApiKey) {
      return messageResponse(NOT_FOUND, "Clever Hub API Key", false, 404, null, res);
    }

    const cleverHubCourses = await getCoursesFromCleverHub(existingCollege.cleverHubApiKey);
    if(!cleverHubCourses.success) {
      return messageResponse(null, "", false, 400, cleverHubCourses.errorMsg, res);
    }

    const [collegeCampuses, sectors, subsectors] = await Promise.all([
      Campus.find({collegeId: toObjectId(id)}, {name: 1}),
      Sector.find({}, {name: 1}),
      Subsector.find({}, {name: 1, sectorId: 1})
    ]);

    const campusMap = new Map(collegeCampuses.map(campus => 
      [campus.name.trim().toLowerCase(), campus._id]
    ));
    const sectorMap = new Map(sectors.map(sector => 
      [sector.name.trim().toLowerCase(), sector._id]
    ));
    const subsectorMap = new Map();
    subsectors.forEach(subsector => {
      const key = `${subsector.name.trim().toLowerCase()}-${subsector.sectorId}`;
      subsectorMap.set(key, subsector._id);
    });

    const validEntries = [];
    const invalidEntries = [];
    const bulkOps = [];

    const courses = cleverHubCourses.data;

    for (const course of courses) {
      try {
        if (course.campuses?.length) {
          const [campusKey, campusList] = course.campuses[0];

          for (const campus of campusList.split(',')) {
            const campusId = campusMap.get(campus.trim().toLowerCase());

            if (!campusId) {
              invalidEntries.push({
                title: course.title,
                error: `Campus not found: ${campus}`
              });
              continue;
            }

            const sectors = [];
            if (Array.isArray(course.sector)) {
              for (const sector of course.sector) {
                const sectorId = sectorMap.get(sector[1].trim().toLowerCase());
                if (!sectorId) continue;

                const subsectorIds = [];
                if (Array.isArray(course["sub-sector"])) {
                  for (const subsector of course["sub-sector"]) {
                    const key = `${subsector[1].trim().toLowerCase()}-${sectorId}`;
                    const subsectorId = subsectorMap.get(key);
                    if (subsectorId) subsectorIds.push(subsectorId.toString());
                  }
                }

                sectors.push({
                  sectorId: sectorId.toString(),
                  subsectorIds: subsectorIds.length
                    ? subsectorIds
                    : Array.from(subsectorMap)
                        .filter(([key]) => key.includes(sectorId.toString()))
                        .map(([, value]) => value.toString())
                });
              }
            }

            const courseData = {
              title: course.title,
              description: course.description,
              code: course["course-code"],
              level: course.level,
              duration: course.duration,
              pageURL: course["course-page-url"],
              applyURL: course["apply-url"],
              enquiryURL: course["enquire-url"],
              status: "active",
              campusId,
              sectors,
              sectorIds: sectors.map(s => s.sectorId),
              subsectorIds: sectors.flatMap(s => s.subsectorIds)
            };

            if(!validEntries.find(e => e.title === courseData.title && e.campusId === courseData.campusId)) {
              bulkOps.push({
                updateOne: {
                  filter: {
                    title: courseData.title,
                    campusId: toObjectId(courseData.campusId)
                  },
                  update: { $set: courseData },
                  upsert: true
                }
              });
  
              validEntries.push({ title: course.title, campusId: courseData.campusId });
            }
          }
        } else {
          invalidEntries.push({
            title: course.title,
            error: "Campus not found"
          });
        }
      } catch (err) {
        console.log(err);
        invalidEntries.push({
          title: course.title,
          error: err.message
        });
      }
    }


    if(bulkOps.length) {
      await Course.bulkWrite(bulkOps);
    }

    return messageResponse(null, "", true, 200, {
      uniqueCourses: cleverHubCourses.data.length,
      totalCourses: validEntries.length + invalidEntries.length,
      totalValidCourses: validEntries.length,
      totalInvalidCourses: invalidEntries.length,
      validEntries,
      invalidEntries
    }, res);
  } catch (error) { 
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.syncCourses = syncCourses;

const remove = async(req, res) => {
  try {
    if (!req.body.id) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res);
    }

    const allowedColleges = await commonClass.getAllowedColleges(req);
    // console.log('allowedColleges', allowedColleges);
    const reqCollege = allowedColleges.find(c => c._id == req.body.id);
    if (!reqCollege) return messageResponse(EXIST_PERMISSION, "College", false, 404, null, res);

    const campus = await Campus.findOne({ collegeId: req.body.id });
    if (campus) return messageResponse(REMOVE_ERROR_ENGAGED, "College", false, 400, null, res);

    const college = await College.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

    if(!college) return messageResponse(EXIST_PERMISSION, "College", false, 404, null, res);

    revokeUserRole(3, req.body.id, college)
    return messageResponse(REMOVE_SUCCESS, "College", true, 200, null, res)
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.remove = remove;

const update = async(req, res, next) => {
  return await addOrEdit(req, res, 'edit');
};
module.exports.update = update;

const permissions = async(req, res) => {
  try {
    const {collegeId} = req.query
    if (!mongoose.isValidObjectId(collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, res);
    }

    const colleges = await College.find({collegeGroupId: {$in: req.user.collegeGroupIds}})
    if(colleges.length){
      const foundCollege = colleges.find(c => c._id.toString() === collegeId.toString())
      if(!foundCollege) {
        return messageResponse(EXIST_PERMISSION, "College", false, 404, null, res);
      }
      if(foundCollege.permissions){
        return messageResponse(null, "", true, 200, foundCollege.permissions, res)
      } else {
        return messageResponse(NOT_FOUND, "College Permissions", false, 404, null, res)
      }
    } else {
      return messageResponse(NOT_FOUND, "College", false, 404, null, res);
    }

  } catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};
module.exports.permissions = permissions;