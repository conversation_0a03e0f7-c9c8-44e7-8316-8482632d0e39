import PropTypes from 'prop-types';
import { useState } from 'react';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import { Box, Button, Stack, Typography } from '@mui/material';
import { skillLevels } from 'src/assets/data/Dummy';
import RadarChartComponent from '../RadarChart/RadarChartComponent';


const CareerCard = ( {item, chartWidth, currencyFormat, showCareerDetailsPopup, removeFromComparison, removeCareer,darkBlue,ShowFullRadar} ) => (
        <>  
           
            <Box className='head' sx={{backgroundColor: 'secondary.main'}}>
                <Box>
                    <Typography variant='capitalize' sx={{lineHeight:'1.4', fontWeight: 600}}  color='primary.white'>{item.title}</Typography>
                    {item.careerType === "CURRENT_ROLE" ?
                        <Typography variant='small' sx={{fontWeight: 500,color: 'primary.white'}} className='role-label'>Current role</Typography>
                        : ''
                    }
                    {item.careerType === "CAREER_GOAL" ?
                        <Typography variant='small' sx={{fontWeight: 500,color: 'primary.white'}} className='role-label'>Career Goal</Typography>
                        : ''
                    }
                </Box>
            </Box>
            <Box className="content">
                <Box className="chart-wrapper" onClick={()=>ShowFullRadar(item.skilldarChartData)}>  
                        <RadarChartComponent
                            width={chartWidth}
                            legend={false} 
                            radarApiDetails={item.skilldarChartData}
                            height='100%'
                            compareCareer
                            chartAnimation
                        />
                </Box>
                <Box className="item-wrapper">
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{currencyFormat(item.estimatePayYear)}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.transferWindow || '-'}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.estimateHours || '-'}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.overallGrowth || '-'}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.regionalGrowth || '-'}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.noOfCourse}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.levelRange || '-'}</Typography>
                    <Typography className='compare-data' variant='bold' color='primary.dark'>{item.courseDuration || '-'}</Typography>
                </Box>
                <Box
                    sx={{backgroundColor:'#f6f6f6',width:'100%',paddingBottom:'40px'}}
                >
                    <Button sx={{backgroundColor:(theme)=>`${theme.palette.secondary.main} !important`,borderRadius:'0px',color:'white',width:'80%',p:2}} onClick={(e)=>showCareerDetailsPopup(item.id, e)}><Typography variant='capitalizeSmall'>Course Details</Typography></Button>
                    <Button className='icon-btn' sx={{width:'100%',backgroundColor:'#f6f6f6'}} onClick={
                        ()=>{(item?.careerType !== "CURRENT_ROLE" && item?.careerType !== "CAREER_GOAL") && removeFromComparison(item.id); // eslint-disable-line no-unused-expressions
                         removeCareer(item.id)}}
                        ><span><DeleteOutlineIcon/></span><Typography color='primary.light'>Remove</Typography></Button>
                    {/* <Button className='icon-btn' sx={{width:'100%',backgroundColor:'#f6f6f6'}} onClick={()=>{itemremoveCareer(item.id)}}><span><DeleteOutlineIcon/></span><Typography color='primary.light'>Remove</Typography></Button> */}
                </Box>
            </Box>
        </>
    )

export default CareerCard

CareerCard.propTypes = {
    item: PropTypes.object,
    chartWidth:  PropTypes.number,
    showCareerDetailsPopup: PropTypes.func,
    removeFromComparison: PropTypes.func,
    removeCareer: PropTypes.func,
    currencyFormat: PropTypes.func,
    ShowFullRadar: PropTypes.func,
    darkBlue: PropTypes.string
};