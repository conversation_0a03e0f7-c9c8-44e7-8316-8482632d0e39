import { configureStore } from '@reduxjs/toolkit'
import usersReducer from "../pages/Userspage/usersSlice"
import collegesReducer from '../pages/Colleges/collegesSlice'
import regionsReducer from '../pages/Regions/regionsSlice'
import newCareerReducer from '../pages/NewCareers/newCareerSlice'
import collegeGroupsReducer from '../pages/CollegeGroup/collegeGroupsSlice'
import campusReducer from '../pages/Campuses/campusesSlice'
import careerReducer from '../pages/CareerPage/careerSlice'
import coursesReducer from '../pages/Courses/coursesSlice'
import sectorReducer from '../pages/Sector/SectorSlice'
import subSectorReducer from '../pages/SubSector/SubSectorSlice'
import authReducer from '../pages/Auth/loginSlice'
import snackbarReducer from './snackbarSlice'
import RadarCategoryReducer from '../pages/RadarCategory/RadarCategorySlice'
import RadarSubCategoryReducer from '../pages/RadarSubCategory/RadarSubCategorySlice'
import skillReducer from '../pages/Skills/skillSlice'
import abilityReducer from '../pages/Abilities/abilitySlice'
import selectedCollegeReducer from './selectedCollegeSlice'

const store = configureStore({
    reducer: {
        users: usersReducer,
        colleges: collegesReducer,
        regions: regionsReducer,
        newCareers: newCareerReducer,
        collegeGroups: collegeGroupsReducer,
        campuses: campusReducer,
        courses: coursesReducer,
        skills: skillReducer,
        abilities: abilityReducer,
        career: careerReducer,
        auth: authReducer,
        snackbar: snackbarReducer,
        sectors: sectorReducer,
        subSectors: subSectorReducer,
        radarCategories: RadarCategoryReducer,
        radarSubCategories: RadarSubCategoryReducer,
        selectedCollege: selectedCollegeReducer
    },
    middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware({
            serializableCheck: false,
        }),
})

export default store