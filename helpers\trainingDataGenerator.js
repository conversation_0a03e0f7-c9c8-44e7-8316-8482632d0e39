const { careerTypes } = require("../models/career");

/**
 * Training Data Generator Helper
 * Creates diverse, high-quality training examples for fine-tuning
 * Generates multiple question types and conversation patterns
 */
class TrainingDataGenerator {
  constructor() {
    this.generatedExamples = [];
    this.questionTemplates = this.initializeQuestionTemplates();
    this.conversationPatterns = this.initializeConversationPatterns();
    this.responseStyles = this.initializeResponseStyles();
  }

  /**
   * Generate comprehensive training data from extracted data
   * @param {Object} extractedData - Data from DataExtractor
   * @param {Object} options - Generation options
   * @returns {Promise<Array>} - Training examples
   */
  async generateTrainingData(extractedData, options = {}) {
    const {
      examplesPerRecord = 3,
      includeMultiTurn = true,
      includeVariations = true,
      responseStyle = 'helpful',
      maxExamplesTotal = 10000
    } = options;

    console.log('🎯 Generating comprehensive training data...');
    console.log(`📊 Options: ${examplesPerRecord} examples per record, style: ${responseStyle}`);

    this.generatedExamples = [];
    let totalGenerated = 0;

    try {
      // Generate examples for each data type
      const generators = [
        () => this.generateCollegeExamples(extractedData.colleges, examplesPerRecord),
        () => this.generateCampusExamples(extractedData.campuses, examplesPerRecord),
        () => this.generateCourseExamples(extractedData.courses, examplesPerRecord),
        () => this.generateCareerExamples(extractedData.careers, examplesPerRecord),
        () => this.generateSkillExamples(extractedData.skills, examplesPerRecord),
        () => this.generateAbilityExamples(extractedData.abilities, examplesPerRecord),
        () => this.generateSectorExamples(extractedData.sectors, examplesPerRecord),
        () => this.generateSubsectorExamples(extractedData.subsectors, examplesPerRecord),
        () => this.generateRegionExamples(extractedData.regions, examplesPerRecord),
        () => this.generateCollegeGroupExamples(extractedData.collegeGroups, examplesPerRecord)
      ];

      // Generate examples for each type
      for (const generator of generators) {
        const examples = await generator();
        this.generatedExamples.push(...examples);
        totalGenerated += examples.length;

        // Check if we've reached the maximum
        if (totalGenerated >= maxExamplesTotal) {
          console.log(`⚠️ Reached maximum examples limit: ${maxExamplesTotal}`);
          break;
        }
      }

      // Generate multi-turn conversations if requested
      if (includeMultiTurn && totalGenerated < maxExamplesTotal) {
        const multiTurnExamples = this.generateMultiTurnConversations(extractedData, {
          maxExamples: Math.min(500, maxExamplesTotal - totalGenerated)
        });
        this.generatedExamples.push(...multiTurnExamples);
        totalGenerated += multiTurnExamples.length;
      }

      // Generate variations if requested
      if (includeVariations && totalGenerated < maxExamplesTotal) {
        const variations = this.generateQuestionVariations({
          maxExamples: Math.min(300, maxExamplesTotal - totalGenerated)
        });
        this.generatedExamples.push(...variations);
        totalGenerated += variations.length;
      }

      // Shuffle examples for better training
      this.shuffleArray(this.generatedExamples);

      console.log(`✅ Generated ${totalGenerated} training examples`);
      console.log(`📊 Final count: ${this.generatedExamples.length} examples`);

      return this.generatedExamples;

    } catch (error) {
      console.error('❌ Training data generation failed:', error);
      throw error;
    }
  }

  /**
   * Generate college-specific examples
   */
  generateCollegeExamples(colleges, examplesPerRecord) {
    const examples = [];
    
    colleges.forEach(college => {
      const collegeName = college.name || 'the college';
      const systemPrompt = `You are a helpful assistant for ${collegeName}. You provide information about ${collegeName}'s programs, courses, campuses, and services.`;

      // Basic information
      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Tell me about ${collegeName}.` },
          { role: 'assistant', content: this.generateCollegeDescription(college) }
        ]
      });

      // Contact information
      if (college.contactNumber || college.email || college.website) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `How can I contact ${collegeName}?` },
            { role: 'assistant', content: this.generateContactInfo(college) }
          ]
        });
      }

      // Location information
      if (college.city || college.address1) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `Where is ${collegeName} located?` },
            { role: 'assistant', content: this.generateLocationInfo(college) }
          ]
        });
      }

      // Additional variations
      if (examplesPerRecord > 3) {
        examples.push(...this.generateCollegeVariations(college, examplesPerRecord - 3));
      }
    });

    console.log(`🏫 Generated ${examples.length} college examples`);
    return examples;
  }

  /**
   * Generate course-specific examples
   */
  generateCourseExamples(courses, examplesPerRecord) {
    const examples = [];
    
    courses.forEach(course => {
      const collegeName = course.campusId?.collegeId?.name || 'the college';
      const systemPrompt = `You are a helpful assistant for ${collegeName}. You provide information about courses, programs, and academic offerings.`;

      // Basic course information
      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Tell me about the ${course.title} course.` },
          { role: 'assistant', content: this.generateCourseDescription(course, collegeName) }
        ]
      });

      // Application information
      if (course.applyURL || course.enquiryURL) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `How can I apply for ${course.title}?` },
            { role: 'assistant', content: this.generateApplicationInfo(course, collegeName) }
          ]
        });
      }

      // Duration and level
      if (course.duration || course.level) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `What is the duration and level of ${course.title}?` },
            { role: 'assistant', content: this.generateDurationLevelInfo(course) }
          ]
        });
      }
    });

    console.log(`📚 Generated ${examples.length} course examples`);
    return examples;
  }

  /**
   * Generate career-specific examples
   */
  generateCareerExamples(careers, examplesPerRecord) {
    const examples = [];
    
    careers.forEach(career => {
      const systemPrompt = 'You are a helpful career guidance assistant providing information about careers and job opportunities.';

      // Basic career information
      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Tell me about the ${career.title} career.` },
          { role: 'assistant', content: this.generateCareerDescription(career) }
        ]
      });

      // Salary information
      if (career.salaryMean || career.salaryMedian) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `What is the salary for ${career.title}?` },
            { role: 'assistant', content: this.generateSalaryInfo(career) }
          ]
        });
      }

      // Tasks and responsibilities
      if (career.tasks) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `What tasks are involved in ${career.title}?` },
            { role: 'assistant', content: `As a ${career.title}, typical tasks include: ${career.tasks}` }
          ]
        });
      }
    });

    console.log(`💼 Generated ${examples.length} career examples`);
    return examples;
  }

  /**
   * Generate skill and ability examples
   */
  generateSkillExamples(skills, examplesPerRecord) {
    const examples = [];
    
    skills.forEach(skill => {
      const systemPrompt = 'You are a helpful career development assistant providing information about professional skills and competencies.';

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `What is the ${skill.lmiName} skill?` },
          { role: 'assistant', content: this.generateSkillDescription(skill) }
        ]
      });

      if (skill.radarCategoryId) {
        examples.push({
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: `What category does ${skill.lmiName} belong to?` },
            { role: 'assistant', content: this.generateSkillCategoryInfo(skill) }
          ]
        });
      }
    });

    console.log(`🧠 Generated ${examples.length} skill examples`);
    return examples;
  }

  /**
   * Generate ability examples
   */
  generateAbilityExamples(abilities, examplesPerRecord) {
    const examples = [];
    
    abilities.forEach(ability => {
      const systemPrompt = 'You are a helpful career development assistant providing information about professional abilities and competencies.';

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `What is the ${ability.lmiName} ability?` },
          { role: 'assistant', content: this.generateAbilityDescription(ability) }
        ]
      });
    });

    console.log(`💪 Generated ${examples.length} ability examples`);
    return examples;
  }

  /**
   * Generate sector examples
   */
  generateSectorExamples(sectors, examplesPerRecord) {
    const examples = [];
    
    sectors.forEach(sector => {
      const systemPrompt = 'You are a helpful career guidance assistant providing information about industry sectors and career opportunities.';

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Tell me about the ${sector.name} sector.` },
          { role: 'assistant', content: `The ${sector.name} sector is an important industry area that offers various career opportunities and job roles. This sector includes multiple specializations and career paths for professionals with different skill sets and educational backgrounds.` }
        ]
      });
    });

    console.log(`🏭 Generated ${examples.length} sector examples`);
    return examples;
  }

  /**
   * Generate subsector examples
   */
  generateSubsectorExamples(subsectors, examplesPerRecord) {
    const examples = [];
    
    subsectors.forEach(subsector => {
      const systemPrompt = 'You are a helpful career guidance assistant providing information about industry specializations.';
      const sectorName = subsector.sectorId?.name || subsector.sector || 'related sector';

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `What is ${subsector.name}?` },
          { role: 'assistant', content: `${subsector.name} is a specialized area within the ${sectorName} sector. This specialization focuses on specific aspects of the industry and offers targeted career opportunities.` }
        ]
      });
    });

    console.log(`🔧 Generated ${examples.length} subsector examples`);
    return examples;
  }

  /**
   * Generate region examples
   */
  generateRegionExamples(regions, examplesPerRecord) {
    const examples = [];
    
    regions.forEach(region => {
      const systemPrompt = 'You are a helpful educational assistant providing information about educational regions and institutions.';

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Tell me about the ${region.name} region.` },
          { role: 'assistant', content: `The ${region.name} region is an educational area that includes multiple institutions and programs. ${region.description || 'This region provides quality educational opportunities for students.'} The region serves students across different locations.` }
        ]
      });
    });

    console.log(`📍 Generated ${examples.length} region examples`);
    return examples;
  }

  /**
   * Generate college group examples
   */
  generateCollegeGroupExamples(collegeGroups, examplesPerRecord) {
    const examples = [];
    
    collegeGroups.forEach(group => {
      const systemPrompt = 'You are a helpful educational assistant providing information about college groups and educational institutions.';

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `What is ${group.name}?` },
          { role: 'assistant', content: `${group.name} is a college group that operates educational institutions. They provide quality education and various programs to students across their network of colleges.` }
        ]
      });
    });

    console.log(`🏢 Generated ${examples.length} college group examples`);
    return examples;
  }

  /**
   * Generate campus examples
   */
  generateCampusExamples(campuses, examplesPerRecord) {
    const examples = [];
    
    campuses.forEach(campus => {
      const collegeName = campus.collegeId?.name || 'the college';
      const systemPrompt = `You are a helpful assistant for ${collegeName}. You provide information about campuses and facilities.`;

      examples.push({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Tell me about the ${campus.name} campus.` },
          { role: 'assistant', content: this.generateCampusDescription(campus, collegeName) }
        ]
      });
    });

    console.log(`🏛️ Generated ${examples.length} campus examples`);
    return examples;
  }

  // Helper methods for generating descriptions
  generateCollegeDescription(college) {
    const parts = [];
    parts.push(`${college.name} is ${college.collegeGroupId ? `part of the ${college.collegeGroupId.name} college group and ` : ''}a quality educational institution`);
    
    if (college.city || college.address1) {
      parts.push(`located at ${college.address1 ? `${college.address1}, ` : ''}${college.city || 'various locations'}${college.state ? `, ${college.state}` : ''}`);
    }
    
    if (college.description) {
      parts.push(college.description);
    } else {
      parts.push('offering various educational programs and services to students');
    }
    
    if (college.website) {
      parts.push(`You can visit their website at ${college.website} for more information`);
    }
    
    return parts.join('. ') + '.';
  }

  generateContactInfo(college) {
    const contacts = [];
    if (college.contactNumber) contacts.push(`Phone: ${college.contactNumber}`);
    if (college.email) contacts.push(`Email: ${college.email}`);
    if (college.website) contacts.push(`Website: ${college.website}`);
    
    return `You can contact ${college.name} through: ${contacts.join(', ')}. They will be happy to help with your inquiries about programs and admissions.`;
  }

  generateLocationInfo(college) {
    return `${college.name} is located at ${college.address1 ? `${college.address1}, ` : ''}${college.city}${college.state ? `, ${college.state}` : ''}${college.zip ? ` ${college.zip}` : ''}. This location provides convenient access to the college's programs and facilities.`;
  }

  generateCourseDescription(course, collegeName) {
    const parts = [];
    parts.push(`${course.title} ${course.code ? `(${course.code})` : ''} is offered at ${collegeName}`);
    
    if (course.campusId?.name) {
      parts.push(`at the ${course.campusId.name} campus`);
    }
    
    if (course.description) {
      parts.push(course.description);
    }
    
    if (course.level) {
      parts.push(`This is a ${course.level} level course`);
    }
    
    if (course.duration) {
      parts.push(`with a duration of ${course.duration}`);
    }
    
    return parts.join('. ') + '.';
  }

  generateApplicationInfo(course, collegeName) {
    const parts = [];
    if (course.applyURL) {
      parts.push(`you can apply directly at ${course.applyURL}`);
    }
    if (course.enquiryURL) {
      parts.push(`${course.applyURL ? 'or make an enquiry at' : 'you can make an enquiry at'} ${course.enquiryURL}`);
    }
    
    return `To apply for ${course.title} at ${collegeName}, ${parts.join(' ')}.`;
  }

  generateCareerDescription(career) {
    const parts = [];
    switch (career.type) {
      case careerTypes.UNIT_GROUP:
        parts.push(`${career.title} is a Unit Group`);
        break;
        
      case careerTypes.BROAD_CAREER:
        parts.push(`${career.title} is a Broad Career`);
        break;

      case careerTypes.SPECIALISED_ROLE:
        parts.push(`${career.title} is a Specialised Role`);
        break;
    
      default:
        break;
    }
    
    if (career.onetCode) {
      parts.push(`with O*NET code ${career.onetCode}`);
    }
    
    if (career.description) {
      parts.push(career.description);
    }
    
    if (career.jobZone) {
      parts.push(`This career typically requires job zone ${career.jobZone} level preparation`);
    }
    
    return parts.join('. ') + '.';
  }

  // change salary here
  generateSalaryInfo(career) {
    const salaryParts = [];
    const weeklySalary = career.salaryMean / 52 || career.salaryMedian / 52 || career.estimatePay;
    const annualSalary = career.salaryMean || career.salaryMedian || career.estimatePay * 52;
    
    salaryParts.push(`weekly salary of £${weeklySalary.toLocaleString()}`);
    salaryParts.push(`annual salary of £${annualSalary.toLocaleString()}`);
    
    return `The ${career.title} career offers competitive compensation with ${salaryParts.join(' and ')}. Salary can vary based on experience, location, and specific industry sector.`;
  }

  generateSkillDescription(skill) {
    return `${skill.lmiName} is an important professional skill ${skill.radarCategoryId?.name ? `in the ${skill.radarCategoryId.name} category` : ''}. This skill is valuable for career development and professional success across various industries and roles.`;
  }

  generateAbilityDescription(ability) {
    return `${ability.lmiName} is a key professional ability ${ability.radarCategoryId?.name ? `in the ${ability.radarCategoryId.name} category` : ''}. This ability is essential for effective performance in various career roles and professional situations.`;
  }

  generateSkillCategoryInfo(skill) {
    return `The ${skill.lmiName} skill belongs to the ${skill.radarCategoryId.name} category${skill.radarSubcategoryId ? `, specifically in the ${skill.radarSubcategoryId.name} subcategory` : ''}. This categorization helps identify related skills and career development paths.`;
  }

  generateCampusDescription(campus, collegeName) {
    return `The ${campus.name} campus is part of ${collegeName} and is located at ${campus.address1 ? `${campus.address1}, ` : ''}${campus.city || 'the campus location'}${campus.state ? `, ${campus.state}` : ''}. ${campus.contactNumber ? `You can contact this campus at ${campus.contactNumber}` : ''} for specific information about programs and services available at this location.`;
  }

  generateDurationLevelInfo(course) {
    const parts = [];
    if (course.level) parts.push(`Level: ${course.level}`);
    if (course.duration) parts.push(`Duration: ${course.duration}`);
    return `${course.title} details: ${parts.join(', ')}.`;
  }

  generateCollegeVariations(college, count) {
    // Generate additional variations for colleges
    const variations = [];
    const templates = [
      `What programs does ${college.name} offer?`,
      `Is ${college.name} accredited?`,
      `What are the admission requirements for ${college.name}?`,
      `Does ${college.name} offer online courses?`,
      `What facilities does ${college.name} have?`
    ];

    for (let i = 0; i < Math.min(count, templates.length); i++) {
      variations.push({
        messages: [
          { role: 'system', content: `You are a helpful assistant for ${college.name}.` },
          { role: 'user', content: templates[i] },
          { role: 'assistant', content: `For specific information about ${templates[i].toLowerCase()}, please contact ${college.name} directly${college.website ? ` at ${college.website}` : ''}${college.contactNumber ? ` or call ${college.contactNumber}` : ''}. They will provide you with the most current and detailed information.` }
        ]
      });
    }

    return variations;
  }

  generateMultiTurnConversations(extractedData, options) {
    // Generate multi-turn conversation examples
    const conversations = [];
    // Implementation would create realistic multi-turn conversations
    // This is a placeholder for the concept
    console.log(`🔄 Generated multi-turn conversations (placeholder)`);
    return conversations;
  }

  generateQuestionVariations(options) {
    // Generate question variations and paraphrases
    const variations = [];
    // Implementation would create question variations
    // This is a placeholder for the concept
    console.log(`🔄 Generated question variations (placeholder)`);
    return variations;
  }

  shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [array[i], array[j]] = [array[j], array[i]];
    }
  }

  initializeQuestionTemplates() {
    return {
      basic: ['Tell me about', 'What is', 'Describe', 'Explain'],
      specific: ['What are the requirements for', 'How can I apply for', 'Where is', 'When does'],
      comparative: ['What is the difference between', 'How does X compare to', 'Which is better'],
      procedural: ['How do I', 'What steps', 'What is the process', 'How can I']
    };
  }

  initializeConversationPatterns() {
    return {
      informational: 'Direct information request and response',
      guidance: 'Seeking advice and receiving guidance',
      procedural: 'Step-by-step process explanation'
    };
  }

  initializeResponseStyles() {
    return {
      helpful: 'Friendly, informative, and supportive',
      professional: 'Formal, accurate, and comprehensive',
      conversational: 'Natural, engaging, and approachable'
    };
  }
}

module.exports = TrainingDataGenerator;
