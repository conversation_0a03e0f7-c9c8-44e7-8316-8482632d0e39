const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const careersController = require('../controllers/careers.controller')
const upload = require("../config/multer.middleware");

const AuthGuard = require("../guards/auth.guard");
const SuperUserGuard = require("../guards/super-user.guard");

router.post("/add", [AuthGuard, SuperUserGuard], careersController.add);

router.get("/get", careersController.get);

router.get("/getById", careersController.getByID);

router.delete("/remove", [AuthGuard, SuperUserGuard], careersController.remove);

router.put("/update", [AuthGuard, SuperUserGuard], careersController.update);

router.post("/createSectorNSubsectors", [AuthGuard, SuperUserGuard], careersController.createSectorNSubsectors);

router.get("/getSocCodes", [AuthGuard, SuperUserGuard], careersController.getSocCodes);

router.get("/getCareersByKeyword", [AuthGuard, SuperUserGuard], careersController.getCareersByKeyword);

router.get("/getCareerDetails", [AuthGuard, SuperUserGuard], careersController.getCareerDetailsV2); // Old function : getCareerDetails, New function:  getCareerDetailsV2  

router.get("/getSkillsAbilities", [AuthGuard, SuperUserGuard], careersController.getSkillsAbilities);

router.get("/assignInterests", careersController.assignInterests);

// LMI Import Routes
router.get("/importCareerDetail", careersController.importCareerDetailsLMI);

router.get("/importInterests", careersController.importInterestsLMI);

router.post("/importMetadata", [AuthGuard, SuperUserGuard], upload, careersController.importCareersMetadata);

router.get("/exportMetadata", [AuthGuard, SuperUserGuard], careersController.exportCareersMetadata);

router.post("/import", [AuthGuard, SuperUserGuard], upload, careersController.importCareers);

router.get("/export", [AuthGuard, SuperUserGuard], careersController.exportCareers);

// V2 Routes
router.get("/v2/get", careersController.getNestedCareers);

router.get("/v2/getCareersByType", careersController.getCareersByType);

module.exports = router;