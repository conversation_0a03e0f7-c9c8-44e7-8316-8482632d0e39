import { Box } from '@mui/material';
import Cookies from 'js-cookie';
import { isEmpty } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router';
import { getRegionBySlug } from 'src/pages/CareerHistory/CareerHistorySlice';

export default function Footer() {
  const { rg_name, cg_name } = useParams();
  const regions = useSelector((state) => state.careerHistory);
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
  const location = useLocation();
  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);
  const dispatch = useDispatch();

  useEffect(() => {
    setLoading(true);
    dispatch(getRegionBySlug(rg_name));
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (rg_name && regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(data)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, data, rg_name]);

    useEffect(() => {
      if (cg_name) {
        setData(clgDetails?.region);
      }
    }, [cg_name, clgDetails?.region]);

const hideLogos =
  location.pathname.endsWith('/sign-in') || location.pathname.endsWith('/sign-up');

const partnerLogos = useMemo(() => {
  const baseLogos = [...(data?.partnerLogos || [])];

  if (cg_name && clgDetails?.logo) {
    baseLogos.unshift(clgDetails.logo);
  }

  return baseLogos;
}, [data?.partnerLogos, cg_name, clgDetails?.logo]);
  return (
    <footer>
      {/* <Box className='logo'>
        <Typography variant='medium'>powered by</Typography>
        <img src={Logo} alt="Logo" />
      </Box> */}
      {!hideLogos && partnerLogos.length > 0 && (
        <Box
          sx={{
            width: '100%',
            bgcolor: '#f8f8f8',
            py: 3,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: 4,
            flexWrap: 'wrap',
            borderTop: '1px solid #eee',
          }}
        >
          {partnerLogos.map((partnerLogo, idx) => (
            <img
              key={idx}
              src={partnerLogo}
              alt={`Partner Logo ${idx + 1}`}
              style={{
                height: 100,
                objectFit: 'contain',
                background: '#fff',
                borderRadius: 6,
                padding: 6,
              }}
            />
          ))}
        </Box>
      )}
    </footer>
  );
}
