/**
 * Performance Monitor Service
 * Tracks and optimizes system performance
 */

class PerformanceMonitorService {
  constructor() {
    this.metrics = {
      responseTime: [],
      databaseQueries: [],
      cacheHits: 0,
      cacheMisses: 0,
      apiCalls: []
    };
    
    this.thresholds = {
      slowResponseTime: 3000, // 3 seconds
      slowDatabaseQuery: 1000, // 1 second
      maxResponseTime: 10000 // 10 seconds
    };
  }

  /**
   * Start timing an operation
   * @param {string} operation - Operation name
   * @returns {Function} End timing function
   */
  startTiming(operation) {
    const startTime = Date.now();
    
    return (metadata = {}) => {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      this.recordMetric(operation, duration, metadata);
      
      if (duration > this.thresholds.slowResponseTime) {
        console.warn(`⚠️ Slow operation detected: ${operation} took ${duration}ms`);
      }
      
      return duration;
    };
  }

  /**
   * Record a performance metric
   * @param {string} operation - Operation name
   * @param {number} duration - Duration in milliseconds
   * @param {Object} metadata - Additional metadata
   */
  recordMetric(operation, duration, metadata = {}) {
    const metric = {
      operation,
      duration,
      timestamp: new Date(),
      ...metadata
    };

    switch (operation) {
      case 'response':
        this.metrics.responseTime.push(metric);
        break;
      case 'database':
        this.metrics.databaseQueries.push(metric);
        break;
      case 'api':
        this.metrics.apiCalls.push(metric);
        break;
    }

    // Keep only last 1000 metrics to prevent memory issues
    Object.keys(this.metrics).forEach(key => {
      if (Array.isArray(this.metrics[key]) && this.metrics[key].length > 1000) {
        this.metrics[key] = this.metrics[key].slice(-1000);
      }
    });
  }

  /**
   * Record cache hit
   */
  recordCacheHit() {
    this.metrics.cacheHits++;
  }

  /**
   * Record cache miss
   */
  recordCacheMiss() {
    this.metrics.cacheMisses++;
  }

  /**
   * Get performance statistics
   * @returns {Object} Performance stats
   */
  getStats() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    // Filter recent metrics
    const recentResponseTimes = this.metrics.responseTime
      .filter(m => m.timestamp.getTime() > oneHourAgo)
      .map(m => m.duration);

    const recentDbQueries = this.metrics.databaseQueries
      .filter(m => m.timestamp.getTime() > oneHourAgo)
      .map(m => m.duration);

    const recentApiCalls = this.metrics.apiCalls
      .filter(m => m.timestamp.getTime() > oneHourAgo)
      .map(m => m.duration);

    return {
      responseTime: {
        count: recentResponseTimes.length,
        average: this.calculateAverage(recentResponseTimes),
        median: this.calculateMedian(recentResponseTimes),
        p95: this.calculatePercentile(recentResponseTimes, 95),
        min: Math.min(...recentResponseTimes) || 0,
        max: Math.max(...recentResponseTimes) || 0
      },
      database: {
        count: recentDbQueries.length,
        average: this.calculateAverage(recentDbQueries),
        median: this.calculateMedian(recentDbQueries),
        slowQueries: recentDbQueries.filter(d => d > this.thresholds.slowDatabaseQuery).length
      },
      api: {
        count: recentApiCalls.length,
        average: this.calculateAverage(recentApiCalls),
        median: this.calculateMedian(recentApiCalls)
      },
      cache: {
        hits: this.metrics.cacheHits,
        misses: this.metrics.cacheMisses,
        hitRate: this.metrics.cacheHits / (this.metrics.cacheHits + this.metrics.cacheMisses) || 0
      },
      timestamp: new Date()
    };
  }

  /**
   * Calculate average
   * @param {Array} values - Array of numbers
   * @returns {number} Average value
   */
  calculateAverage(values) {
    if (values.length === 0) return 0;
    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  /**
   * Calculate median
   * @param {Array} values - Array of numbers
   * @returns {number} Median value
   */
  calculateMedian(values) {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? (sorted[mid - 1] + sorted[mid]) / 2 
      : sorted[mid];
  }

  /**
   * Calculate percentile
   * @param {Array} values - Array of numbers
   * @param {number} percentile - Percentile to calculate (0-100)
   * @returns {number} Percentile value
   */
  calculatePercentile(values, percentile) {
    if (values.length === 0) return 0;
    const sorted = [...values].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * Get performance recommendations
   * @returns {Array} Array of recommendations
   */
  getRecommendations() {
    const stats = this.getStats();
    const recommendations = [];

    // Response time recommendations
    if (stats.responseTime.average > this.thresholds.slowResponseTime) {
      recommendations.push({
        type: 'performance',
        severity: 'high',
        message: `Average response time is ${Math.round(stats.responseTime.average)}ms. Consider optimizing database queries or adding more caching.`
      });
    }

    // Cache recommendations
    if (stats.cache.hitRate < 0.7) {
      recommendations.push({
        type: 'caching',
        severity: 'medium',
        message: `Cache hit rate is ${Math.round(stats.cache.hitRate * 100)}%. Consider increasing cache TTL or adding more cacheable operations.`
      });
    }

    // Database recommendations
    if (stats.database.slowQueries > 0) {
      recommendations.push({
        type: 'database',
        severity: 'medium',
        message: `${stats.database.slowQueries} slow database queries detected. Consider adding indexes or optimizing queries.`
      });
    }

    return recommendations;
  }

  /**
   * Reset metrics
   */
  reset() {
    this.metrics = {
      responseTime: [],
      databaseQueries: [],
      cacheHits: 0,
      cacheMisses: 0,
      apiCalls: []
    };
  }
}

// Singleton instance
const performanceMonitor = new PerformanceMonitorService();

module.exports = {
  PerformanceMonitorService,
  performanceMonitor
};
