import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { Box, Button, Container, Stack, Typography } from '@mui/material';
import Cookies from 'js-cookie';
import { get, isEmpty } from 'lodash';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate, useParams } from 'react-router';
import { setAlert, setGlobalCompare } from 'src/layouts/main/MainLayoutSlice';
import {
  addCareerHistory,
  getCareerHistory,
  getRegionBySlug,
  userLogin,
} from 'src/pages/CareerHistory/CareerHistorySlice';
import { base_route_url, EMAIL_REGEXP } from 'src/utils';
import * as Yup from 'yup';
import AuthWrapper from './AuthWrapper';
import FormTextField from './FormTextField';
import TextFIeldComponent from './TextField/TextFieldComponent';

const SignInRegion = () => {
  const validationSchema = Yup.object().shape({
    email: Yup.string().trim().required('Email is required').matches(EMAIL_REGEXP, 'Email format is not valid'),
    password: Yup.string().required('Password is required'),
  });
  const regions = useSelector((state) => state.careerHistory);
  const [regionData, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const params = useParams();
  const { cg_name, rg_name } = useParams();
  const navigate = useNavigate();
  const isRegion = !!params?.rg_name;
  const collegeDetails = useSelector((state) => state.mainLayout.collegeDetails);
  const collegeId = collegeDetails?._id;

  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);
  const dispatch = useDispatch();

  useEffect(() => {
    setLoading(true);
    dispatch(getRegionBySlug(rg_name));
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(regionData)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, regionData]);
  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
    reset,
    getValues,
    watch,
    setValue,
  } = useForm({
    resolver: yupResolver(validationSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const [isPasswordHidden, setIsPasswordHidden] = useState(true);
  const onSubmit = (values) => {
    setIsLoading(true);
    dispatch(userLogin(values))
      .then((res) => {
        const data = res?.payload?.data;
        if (data?.success) {
          Cookies.set('feToken', data?.token, { path: '/', expires: 7 });
          dispatch(
            setAlert({
              open: true,
              isSuccess: true,
              type: 'success',
              msg: get(res, 'payload.data.message')
                ? get(res, 'payload.data.message')
                : 'successfully logged in',
            })
          );
          const payload = {
            isRegion: false,
            collegeId,
          };
          dispatch(getCareerHistory(payload)).then((response) => {
            if (get(response, 'payload.data.currentCareerIds')) {
              const formattedData = get(response, 'payload.data.currentCareerIds')?.map(
                (career) => ({
                  label: career?.title,
                  value: career?._id,
                })
              );
              dispatch(addCareerHistory(formattedData));
            }
            if (get(response, 'payload.data.compareCareerIds')) {
              const formattedData = get(response, 'payload.data.compareCareerIds')?.map(
                (career) => career?._id
              );
              dispatch(setGlobalCompare(formattedData));
            }
            if (
              get(response, 'payload.success') ||
              get(response, 'payload.response.data.success')
            ) {
              navigate(`${base_route_url}region/${params?.rg_name}/reskill/skilldar`);
            } else {
              // set career id's
              navigate(`${base_route_url}region/${params?.rg_name}/career-history`);
            }
          });
        } else {
          const errorMessage =
            get(res, 'payload.response.data.message') ||
            'Something Went Wrong, Please try again later';
          dispatch(
            setAlert({
              open: true,
              msg: errorMessage,
            })
          );
        }
      })
      .finally(() => setIsLoading(false));
  };

  const buttonColor = regionData?.button?.bgColor;
  const buttonFontColor = regionData?.button?.color;
  const logo = regionData?.logo || '';
  const partnerLogos = regionData?.partnerLogos || [];
  const primaryColor = regionData?.primaryColor || '';
  const fontColor = regionData?.fontColor || '';
  const secondaryColor = regionData?.secondaryColor || '';
  const bgImage = regionData?.bgImage || '';

  const handleSignUpClicked = () => {
    if (isRegion) {
      navigate(`${base_route_url}region/${params?.rg_name}/sign-up`);
    } else {
      navigate(`${base_route_url}${params?.cg_name}/sign-up`);
    }
  };
  return (
    <>
      <AuthWrapper title=" Sign-In">
        <Box
          sx={{
            backgroundImage: `url(${bgImage})`,
            backgroundSize: 'cover',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: 'center',
            backgroundAttachment: 'fixed',
            minHeight: '100vh'
          }}
          className="page-content-wrapper"
        >
          <Container>
            <Box display="flex" flexDirection="column" alignItems="center">
              <Typography sx={{ fontSize: '32px !important', mb: 2, mt: 2, color: fontColor }}>
                <b>Sign in to your THINK</b>SKILLS <b>account</b>
              </Typography>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  width: 800,
                  backgroundColor: 'white',
                  py: 4,
                  borderRadius: '8px',
                }}
              >
                <form onSubmit={handleSubmit(onSubmit)}>
                  <Box className="sign-up-wrapper">
                    <FormTextField
                      title="Email Address"
                      isRequired
                      name="email"
                      placeholder="Enter email"
                      {...register('email')}
                      control={control}
                      className="input-text-field"
                    />

                    <Box className="field-wrapper">
                      <Typography mb={1} variant="subtitle2">
                        Password*
                      </Typography>
                      <Stack mb={2} direction="row" alignItems="center" spacing={2}>
                        <TextFIeldComponent
                          name="password"
                          placeholder="Enter password"
                          control={control}
                          {...register('password')}
                          className="input-text-field postcode"
                          type={isPasswordHidden ? 'password' : 'text'}
                        />
                        <Button
                          sx={{
                            borderRadius: '8px',
                            color: buttonFontColor || 'white',
                          }}
                          onClick={() => setIsPasswordHidden((prevValue) => !prevValue)}
                        >
                          <Typography mb={1} variant="subtitle2">
                            {!isPasswordHidden ? 'Hide' : 'Show'} Password
                          </Typography>
                        </Button>
                      </Stack>
                      <LoadingButton
                        sx={{
                          backgroundColor: (theme) =>
                            `${buttonColor || theme.palette.primary.main} !important`,
                          borderRadius: '8px',
                          color: buttonFontColor || 'white',
                          // width: '80%',
                          p: 2,
                          mb: 1,
                        }}
                        fullWidth
                        type="submit"
                        loading={isLoading}
                        // onClick={(e) => showCareerDetailsPopup(item.id, e)}
                      >
                        Sign in
                      </LoadingButton>
                      <Stack alignItems="center" justifyContent="center">
                        <Button
                          variant="text"
                          sx={{
                            textTransform: 'none',
                            textDecoration: 'none',
                            fontWeight: 100,
                            mb: 3,
                            color: 'black',
                            fontSize: '16px',
                            '&:hover': {
                              textDecoration: 'underline',
                              backgroundColor: 'transparent',
                            },
                          }}
                        >
                          Forgotten password?
                        </Button>
                      </Stack>
                      <Button
                        sx={{
                          borderRadius: '8px',
                          p: 2,
                          borderColor: (theme) =>
                            `${buttonColor || theme.palette.primary.main} !important`,
                          color: buttonFontColor || 'white',
                        }}
                        fullWidth
                        variant="outlined"
                        onClick={handleSignUpClicked}
                      >
                        Don't have an account? Create a free one
                      </Button>
                    </Box>
                  </Box>
                </form>
              </Box>
            </Box>
          </Container>
        </Box>
      </AuthWrapper>
    </>
  );
};

export default SignInRegion;
