import { Box, display } from '@mui/system'
import React, { useState } from 'react'
import { PropTypes } from 'prop-types'
import { Button, Divider, Stack, SvgIcon, Typography } from '@mui/material'
import AccountBalanceWalletOutlinedIcon from '@mui/icons-material/AccountBalanceWalletOutlined';
import UpdateOutlinedIcon from '@mui/icons-material/UpdateOutlined';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import SchoolOutlinedIcon from '@mui/icons-material/SchoolOutlined';
import RemoveCircleOutlineOutlinedIcon from '@mui/icons-material/RemoveCircleOutlineOutlined';
import BadgeIcon from '@mui/icons-material/Badge';
import AddCircleOutlineOutlinedIcon from '@mui/icons-material/AddCircleOutlineOutlined';
import { currencyFormat } from 'src/utils';
import { ReactComponent as flagIcon } from '../../assets/images/flag.svg'

const CustomCareerBar = ({ isWidget, career, barColor, barWidth, addToCompare, compareCareersState, showCareerDetailsPopup }) => {
    const [styleData, setStyleData] = useState({})
    return (
        <Box
            className="sub-bar"
            display='flex'
            justifyContent='space-between'
            alignItems='center'
            sx={{ backgroundColor: barColor, width:{xs: '100%', sm: barWidth} }}
        >
            <Box >
                <Stack sx={{ ml: 2, py: 2 }} direction='column'>
                    <Typography sx={{ alignSelf: 'start', fontSize: { xs: '0.8rem', sm: '0.95rem', md: '1rem' }, textAlign: 'start', fontWeight: 600 }}>
                        {career?.name}
                    </Typography>
                    <Stack direction={{ md: 'column', lg: 'row' }} gap={{ md: 0, lg: 2 }}>
                        <Stack direction='row' alignItems="center" gap={0.5}>
                            <AccountBalanceWalletOutlinedIcon sx={{ fontSize: '18px' }} />
                            <Typography sx={{ fontSize: '0.9em !important', fontWeight: '300' }} component='small'>
                                {currencyFormat(career?.salary)}
                            </Typography>
                        </Stack>

                        <Stack direction='row' alignItems="center" gap={0.5}>
                            <UpdateOutlinedIcon sx={{ fontSize: '18px' }} />
                            <Typography sx={{ fontSize: '0.9em !important', fontWeight: '300' }}>
                                {career?.transferWindow}
                            </Typography>
                        </Stack>
                        <Stack direction='row' alignItems="center" gap={0.5}>
                            <SchoolOutlinedIcon sx={{ fontSize: '18px' }} />
                            <Typography sx={{ fontSize: '0.9em !important', fontWeight: '300' }}>
                                {career?.coursesAvailable} {career?.coursesAvailable > 1 ? 'Courses Available' : 'Course Available'}
                            </Typography>
                        </Stack>
                    </Stack>
                </Stack>
            </Box>
            <Box
                flex={1}
            />
            {(career?.careerType !== 'CAREER_GOAL' && career?.careerType !== 'CURRENT_ROLE') ? <>
                <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
               {!isWidget && <Box display='flex'
                    sx={{
                        paddingLeft:'12px',
                        paddingRight:'12px',
                        fontWeight: 200,
                        cursor: 'pointer',
                        height: '100%'
                    }}
                    onClick={() => addToCompare(career?.id)}
                    alignItems='center'>

                    {compareCareersState && compareCareersState.find(currentCareer => currentCareer === career.id) ?
                        // (<RemoveCircleOutlineOutlinedIcon sx={{ fontSize: '18px', mx:1 }} /> ):
                        <Stack direction='row' alignItems='center'>
                            <RemoveCircleOutlineOutlinedIcon sx={{ mx: { sx: 0, sm: 0, md: 0.5 } }} />
                            <Typography sx={{ display: { sm: 'none', xs: 'none', md: 'block' }, width: '7ch' }}>Remove</Typography>
                        </Stack> :
                        <Stack direction='row' alignItems='center'>
                            <AddCircleOutlineOutlinedIcon sx={{ mx: { sx: 0, sm: 0, md: 0.5 } }} />
                            <Typography sx={{ display: { sm: 'none', xs: 'none', md: 'block' }, width: '7ch' }}>Compare</Typography>
                        </Stack>
                    }
                </Box>}
                <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
            </> :
                <>
                    <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
                    <Box display='flex'
                        sx={{
                            // px: 2,
                            fontWeight: 200,
                            cursor: 'pointer',
                            height: '100%',
                        }}>
                        {career?.careerType === 'CURRENT_ROLE' ?
                            // <Typography fontStyle='italic'>
                            //     Current Role
                            // </Typography> 
                            <>
                                {/* <Box mx={1.5} sx={{ display: { xs: 'block', sm: 'block', md: 'none' } }}>
                                    <SvgIcon
                                        component={flagIcon}
                                        inheritViewBox
                                    />
                                </Box> */}
                                {/* <Box sx={{ display: { xs: 'none', sm: 'none', md: 'block' }, px: 2 }}> */}
                                <Box sx={{ width: {md:'11.3ch', lg:'10.7ch' }, textAlign:'center'}} mx={1.5}>
                                    {/* <Typography sx={{ width: '10ch' }} fontStyle='italic'>
                                        Current Role
                                    </Typography> */}
                                    <BadgeIcon />
                                </Box>
                            </>
                            :
                            <>
                                <Box sx={{ width: {md:'11.3ch', lg:'10.7ch' }, textAlign:'center'}} mx={1.5}
                                //  sx={{ display: { xs: 'block', sm: 'block', md: 'none' } }}
                                 >
                                    <SvgIcon
                                        component={flagIcon}
                                        inheritViewBox
                                    />
                                </Box>
                                <Box sx={{ display: { xs: 'none', sm: 'none', md: 'none' }, px: 2 }}>
                                    <Typography fontStyle='italic'>
                                        Career Goal
                                    </Typography>
                                </Box>
                            </>

                        }
                    </Box>
                    <Divider sx={{ bgcolor: 'white !important', borderColor: 'unset' }} orientation="vertical" flexItem />
                </>
            }
            <Box sx={{ cursor: 'pointer', height: '80px', display: 'flex', alignItems: 'center' }} onClick={() => !isWidget && showCareerDetailsPopup(career.id)}>
                <ChevronRightIcon sx={{ mx: 1 }} fontSize='medium' />
            </Box>
        </Box>
    )
}

export default CustomCareerBar

CustomCareerBar.propTypes = {
    career: PropTypes.object,
    barColor: PropTypes.string,
    barWidth: PropTypes.number,
    addToCompare: PropTypes.func,
    compareCareersState: PropTypes.array,
    showCareerDetailsPopup: PropTypes.func,
    isWidget: PropTypes.bool,
}