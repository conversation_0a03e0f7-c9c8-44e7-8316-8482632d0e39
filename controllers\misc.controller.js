const { default: mongoose } = require("mongoose");
const { messageResponse, doReqActionOnError, isEmpty, createTransporter } = require("../helpers/commonHelper");
const transporter = createTransporter()
const { SERVER_ERROR, INVALID, INVALID_MISSING, REQUIRED } = require("../config/messages");
const { fetchAnalytics } = require('../controllers/analytics.controller')
const commonClass = require("../helpers/commonClass");
const keys = require("../config/keys");

const dashboard = async(req, res) => {
  try {
    if (isEmpty(req.body.startDate) || isEmpty(req.body.endDate)) {
      return messageResponse(INVALID_MISSING, "Date Range", false, 400, null, res)
    }
    if (!isEmpty(req.body.collegeId) && !mongoose.isValidObjectId(req.body.collegeId)) {
      return messageResponse(INVALID_MISSING, "College ID", false, 400, null, res)
    }
    const analyticCount = await fetchAnalytics(req)

    let dashboardData = analyticCount;
    dashboardData.users = (await commonClass.getAllowedUsers(req)).length;
    dashboardData.regions = (await commonClass.getAllowedRegions(req)).length;
    dashboardData.collegeGroups = (await commonClass.getAllowedCollegeGroups(req)).length;
    dashboardData.colleges = (await commonClass.getAllowedColleges(req)).length;
    dashboardData.campuses = (await commonClass.getAllowedCampuses(req)).length;

    return messageResponse(null, "", true, 200, dashboardData, res);
  } catch (error) {
    doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.dashboard = dashboard;

const sendFile = (req, res) => {
  try {
    const {user, body} = req
    const mailOptions = {
      from: keys.NOREPLY_MAIL,
      to: user.email,
      subject: "Skills Report!",
      html: `<p>Please find attached the requested PDF.</p>`,
      attachments: [
        {
          filename: `skills_report_${new Date().getTime()}.pdf`,
          content: body.pdfB64,
          encoding: 'base64',
        },
      ],
    }
    transporter.sendMail(mailOptions, (err, info) => {
      if(err) return messageResponse(SERVER_ERROR, "", false, 500, err, res);
      else return messageResponse(null, "", true, 200, info.response, res);
    })
  } catch (error) {
    doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
}
module.exports.sendFile = sendFile;
