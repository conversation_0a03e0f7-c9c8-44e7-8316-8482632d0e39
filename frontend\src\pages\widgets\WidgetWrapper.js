import { Box } from '@mui/material';
import { useParams } from 'react-router-dom';
import Widget from '.';

const WidgetWrapper = () => {
  const { cg_name } = useParams();

  if (!cg_name)
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt:2 }}>
        <div>❌ Missing college slug</div>;
      </Box>
    );

  return <Widget cg_name={cg_name} />;
};
export default WidgetWrapper;
