import React from 'react';
import PropTypes from 'prop-types';
import { Box } from '@mui/material';
import AssignmentIcon from '@mui/icons-material/Assignment'; // You can remove if passing icons as props
import HubIcon from '@mui/icons-material/Hub'; // Same here

const StepIcon = ({ icon, buttonColor, buttonFontColor, active }) => {
    if (!React.isValidElement(icon)) return null;

    return (
        <Box
            sx={{
                width: 42,
                height: 42,
                borderRadius: '50%',
                // backgroundColor: '#FFC94D',
                // backgroundColor: active ? buttonColor : buttonFontColor,
                backgroundColor: active ? buttonColor : 'transparent',
                border: `2px solid ${buttonColor}`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
            }}
        >
            {React.cloneElement(icon, {
                sx: {
                    fontSize: 20,
                    color: active ? buttonFontColor : buttonColor,
                },
            })}
        </Box>
    );
};

StepIcon.propTypes = {
    icon: PropTypes.element.isRequired,
    buttonFontColor: PropTypes.string.isRequired,
    buttonColor: PropTypes.string.isRequired,
    active: PropTypes.bool.isRequired,
};

const Step = ({ icon, active, buttonColor, buttonFontColor }) => (
    <Box display="flex" flexDirection="column" alignItems="center">
        <StepIcon buttonColor={buttonColor} buttonFontColor={buttonFontColor} icon={icon} active={active} />
    </Box>
);

Step.propTypes = {
    icon: PropTypes.element.isRequired,
    active: PropTypes.bool.isRequired,
    buttonFontColor: PropTypes.string.isRequired,
    buttonColor: PropTypes.string.isRequired,
};

const RegionStepper = ({ steps, activeStep, buttonColor, buttonFontColor }) => (
    <Box display="flex" alignItems="center" justifyContent="center">
        {steps.map((step, index) => (
            <React.Fragment key={index}>
                <Step buttonColor={buttonColor} buttonFontColor={buttonFontColor} icon={step.icon} active={index === activeStep} />
                {index < steps.length - 1 && (
                    <Box
                        sx={{
                            height: '1px',
                            width: 52,
                            mx: 0,
                            backgroundColor: 'white',
                        }}
                    />
                )}
            </React.Fragment>
        ))}
    </Box>
);

RegionStepper.propTypes = {
    steps: PropTypes.arrayOf(
        PropTypes.shape({
            icon: PropTypes.element.isRequired,
        })
    ).isRequired,
    activeStep: PropTypes.number.isRequired,
    buttonFontColor: PropTypes.string.isRequired,
    buttonColor: PropTypes.string.isRequired,
};

export default RegionStepper;
