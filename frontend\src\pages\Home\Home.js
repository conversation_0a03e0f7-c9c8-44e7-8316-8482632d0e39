import { Box, Button, CircularProgress, Container, Grid, Typography, useTheme } from '@mui/material';
import Cookies from 'js-cookie';
import switchUpImage from 'src/assets/images/switch-up.jpg';
import headerImage from 'src/assets/images/header-image.jpg';
import { get } from 'lodash';
import { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import main_logo from 'src/assets/images/thinkskill.png';
import AiVideo from 'src/components/player/AiVideo';
import { Ai_Video_Url } from 'src/config-global';
import { setGlobalCompare } from 'src/layouts/main/MainLayoutSlice';
import { base_route_url } from 'src/utils';
import { addCareerHistory, getCareerHistory } from '../CareerHistory/CareerHistorySlice';

export default function HomePage() {
  const token = Cookies.get('feToken');
  const params = useParams();
  const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
  useEffect(() => {
    if (params) {
      Cookies.set('url-slug', params?.cg_name);
    }
  }, [params]);
  const theme = useTheme();
  const isRegionData = !!clgDetails?.region;
  const buttonColor = isRegionData ? clgDetails?.region?.button?.bgColor : '';
  const buttonFontColor = isRegionData ? clgDetails?.region?.button?.color : '';
  const fontColor = isRegionData ? clgDetails?.region?.fontColor : '';
  const bgImage = isRegionData ? clgDetails?.region?.bgImage : '';
  const navigate = useNavigate();
  const dispatch = useDispatch();
  useEffect(() => {
    const payload = {
      isRegion: false,
      collegeId: clgDetails?._id,
    };
    if (token) {
      dispatch(getCareerHistory(payload)).then((response) => {
        if (get(response, 'payload.data.currentCareerIds')) {
          const formattedData = get(response, 'payload.data.currentCareerIds')?.map((career) => ({
            label: career?.title,
            value: career?._id,
          }));
          dispatch(addCareerHistory(formattedData));
        }
        if (get(response, 'payload.data.compareCareerIds')) {
          const formattedData = get(response, 'payload.data.compareCareerIds')?.map(
            (career) => career?._id
          );
          dispatch(setGlobalCompare(formattedData));
        }
        if (get(response, 'payload.success') || get(response, 'payload.response.data.success')) {
          navigate(`${base_route_url}${params?.cg_name}/reskill/skilldar`);
        } else {
          // set career id's
          navigate(`${base_route_url}${params?.cg_name}/career-history`);
        }
      });
    }
  }, [token, clgDetails?._id, params?.cg_name, dispatch, navigate]);
  return (
    <>
      <Helmet>
        <title>Upskill / Reskill - Home</title>
      </Helmet>
      <Box display="flex" justifyContent="center" alignItems="center" mt={0}>
        <img
          src={headerImage}
          alt="Region Logo"
          style={{ objectFit: 'contain', borderRadius: 8, background: '#fff' }}
        />
      </Box>
      <Box
        className="page-content-wrapper home"
        sx={{
          width: '100%',
          // backgroundImage: `url(${pageBGImage})`,
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          backgroundAttachment: 'fixed',
          py: 4
        }}
      >

        {clgDetails ? (
          <Container maxWidth='xl'>
            {/* <Box className="content" minHeight="90vh">
              <img
                className="home-logo"
                src={clgDetails?.logo ? clgDetails?.logo : main_logo}
                alt="College Logo"
              />
              <Box sx={{ mb: 5 }}>
                <AiVideo url={`${Ai_Video_Url}landing-page.mp4`} />
              </Box>
              <Box>
                <Typography
                  maxWidth="70%"
                  margin="0 auto"
                  variant="bold"
                  className="entry-text"
                  sx={{ color: (theme) => fontColor || theme?.primary?.dark }}
                  // color="primary.dark"
                >
                  Welcome to Upskill Reskill. We will help you find the best career options to suit
                  your skills, and the best college courses to help you achieve your goals. First we
                  need to identify your employability skills. <br /> To do this we will take a few
                  minutes to understand your career history.
                </Typography>
                <Button
                  className="linkBtn"
                  sx={{
                    backgroundColor: (theme) =>
                      `${buttonColor || theme.palette.secondary.main} !important`,
                  }}
                >
                  <Link to={`/${params.cg_name}/career-history`}>
                    <Typography
                      variant="button2"
                      sx={{ color: (theme) => buttonFontColor || theme?.primary?.white }}
                    >
                      Get Started
                    </Typography>
                  </Link>
                </Button>
              </Box>
            </Box> */}
            <Box pb={5} display="flex" flexDirection="column" alignItems="center">
              <Typography
                variant="h2"
                sx={{
                  fontSize: '80px',
                  fontFamily: '"Merriweather", serif',
                  fontWeight: 900, // Black weight
                  color: fontColor,
                  mt: 2,
                }}
              >
                <b>Welcome to Think Skills.</b>
              </Typography>
              <Typography
                sx={{
                  mb: 4,
                  color: fontColor,
                  fontSize: '18px !important',
                  fontWeight: 700,
                  fontFamily: '"Work Sans", sans-serif',
                }}
              >
                Ever to Excel
              </Typography>
              <Typography sx={{ mb: 2, color: fontColor }} component="p">
                We will help you find the best career options to suit your skills, and the best
                college courses to help you achieve your goals. First we need to identify your
                employability skills. <br /> To do this we will take a few minutes to understand
                your career history.
              </Typography>
              <Button
                className="linkBtn"
                sx={{
                  backgroundColor: buttonColor,
                  mb: 2,
                  borderRadius: '8px',
                  p: '20px',
                  my: 3,
                  // width: '100px',
                  '&:hover': {
                    backgroundColor: buttonColor,
                    opacity: 0.9,
                  },
                }}
              >
                <Link style={{ color: buttonFontColor }} to={`/${params?.cg_name}/career-history`}>
                  Get Started
                </Link>
              </Button>
              <Grid columnSpacing={4} container width="100%" mt={3}>
                {/* Left Column */}
                <Grid item md={7}>
                  <Box sx={{ width: '100%', bgcolor: '#ffffff', pt: 4 }}>
                    <Typography variant="h6" fontWeight="bold" align="center" gutterBottom>
                      Transferable skills
                    </Typography>
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      align="center"
                      sx={{ fontSize: '16px !important', mt: 2 }}
                    >
                      Build skills that employers are looking for – communication, problem-solving,
                      and adaptability. Learn how these skills are developed through real-world
                      experiences and academic courses.
                    </Typography>
                    <Typography
                      variant="body1"
                      align="center"
                      sx={{ mt: 2, fontSize: '16px !important' }}
                    >
                      College courses offer a foundation for many in-demand careers. Discover how
                      soft skills combined with technical knowledge can help you excel in fields
                      like healthcare, engineering, and digital marketing. College courses offer a
                      foundation for many in-demand careers. Discover how soft skills combined with
                      technical knowledge can help you excel in fields like healthcare,
                    </Typography>
                    {/* Image Placeholder */}
                    <Box
                      sx={{
                        width: '100%',
                        height: 416,
                        backgroundImage: `url(${switchUpImage})`,
                        backgroundSize: 'cover',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        mt: 4,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {/* <Typography variant="body1">[Image Here]</Typography> */}
                    </Box>
                  </Box>
                </Grid>
                {/* Right Column */}
                <Grid item md={5}>
                  <Box
                    sx={{ p: 2, textAlign: 'center', bgcolor: '#ffffff' }}
                  >
                    <Typography sx={{ fontWeight: 700 }} variant="body1" fontWeight="bold">
                      Already have an account?
                    </Typography>
                    <Button
                      to={`/${params?.cg_name}/sign-in`}
                      component={Link}
                      variant="contained"
                      sx={{
                        mt: 1,
                        color: buttonFontColor,
                        backgroundColor: buttonColor,
                        '&:hover': {
                          borderColor: buttonColor,
                          backgroundColor: buttonColor,
                          color: buttonFontColor,
                        },
                      }}
                    >
                      Sign in
                    </Button>
                  </Box>
                  <Box sx={{ border: '2px solid #ffffff', color: '#fff', p: 2, mt: 2 }}>
                    <Typography
                      sx={{ fontSize: '16px !important', fontWeight: 600, color: fontColor }}
                      fontWeight="bold"
                    >
                      WLEP is working to create 25,000 jobs and contribute towards the delivery of
                      21,500 new homes by 2025
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      p: 2,
                      // border: '1px solid #ccc',
                      backgroundColor: '#ffffff',
                      mt: 2,
                      textAlign: 'center',
                      // color: fontColor,
                    }}
                  >
                    <Typography
                      sx={{ fontSize: '19px !important', fontWeight: 600 }}
                      variant="body2"
                      fontWeight="bold"
                    >
                      How does it work?
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1, fontSize: '16px !important' }}>
                      Explore pathways from college to career into real-world skills Learn how
                      college courses translate into real-world skills and job opportunities.
                    </Typography>
                    {/* Video Placeholder */}
                    <Container>
                      <Box className="content">
                        <Box sx={{ my: 2 }}>
                          <AiVideo
                            url={`${Ai_Video_Url}landing-page.mp4`}
                            secondaryColor={theme.palette.secondary.main}
                          />
                        </Box>
                      </Box>
                    </Container>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Container>
        ) : (
          <Box sx={{ position: 'absolute', top: '42%', left: '48%' }}>
            <CircularProgress />
          </Box>
        )}
      </Box>
    </>
  );
}
