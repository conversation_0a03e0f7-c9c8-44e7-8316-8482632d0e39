const { default: mongoose } = require("mongoose");
const { RadarCategory } = require("../models/radarCategory");
const { RadarSubcategory } = require("../models/radarSubcategory");
const { getAddedBy, getEditedBy } = require('../tools/database');
const commonHelper = require("../helpers/commonHelper");
const { messageResponse } = require("../helpers/commonHelper");
const { ADD_ERROR, SERVER_ERROR, REQUIRED, NOT_FOUND, EXIST_PERMISSION, INVALID_MISSING, DUPLICATE, UPDATE_SUCCESS, REMOVE_SUCCESS, REMOVE_ERROR_ENGAGED } = require("../config/messages");
const { LMISkillAbility } = require("../models/lmiSkillAbility");

const validate = async(req, action) => {
  try {
    if(action == 'edit') {
      if(!mongoose.isValidObjectId(req.body.id)) {
        return messageResponse(REQUIRED, "ID", false, 400, null);
      }
    }
    
    if(!req.body.name) return messageResponse(INVALID_MISSING, "Name", false, 400, null);
    
    if(!mongoose.isValidObjectId(req.body.radarCategoryId))return messageResponse(INVALID_MISSING, "Radar Category ID", false, 400, null);
    
    const selectedRadarCategory = await RadarCategory.findById(new mongoose.Types.ObjectId(req.body.radarCategoryId));
    if(!selectedRadarCategory) return messageResponse(INVALID_MISSING, "Radar Category", false, 400, null)
    
    let query = action == 'edit' 
        ? { $and: [ { name: { $eq: req.body.name } }, { _id: { $ne: req.body.id } } ] }
        : { name: req.body.name };
    
    const existingSubradarCategory = await RadarSubcategory.findOne(query);
    if(existingSubradarCategory != null) return messageResponse(DUPLICATE, "", false, 400, null);
    
    return { success: true };
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error);
  }
}

const addUpdateEntry = async(req, res, action) => {
  try {
    let validateResult = await validate(req, action);

    if (!validateResult.success) {
      let statusCode = validateResult.statusCode;
      delete validateResult["statusCode"];

      return res.status(statusCode).json(validateResult);
    }

    if (action == 'add') {
      let addedBy = getAddedBy(req);
      req.body.addedBy = addedBy;

      let newRadarCat = await RadarSubcategory.create(req.body)

      if(!newRadarCat) return messageResponse(ADD_ERROR, "Radar Sub Category", false, 400, null, res)

      res.status(200).json({ success: true, id: newRadarCat._id })
    } else {
      req.body.editedBy = getEditedBy(req, 'edit');

      const updatedEntry = await RadarSubcategory.findOneAndUpdate({ _id: req.body.id, defaultEntry: false }, req.body, { returnOriginal: false })

      if(!updatedEntry) return messageResponse(EXIST_PERMISSION, "Radar Sub Category", false, 404, null, res) 

      return messageResponse(UPDATE_SUCCESS, "Radar Sub Category", true, 200, null, res)
    }
  }
  catch (error) {
    commonHelper.doReqActionOnError(error);
    return messageResponse(SERVER_ERROR, "", false, 500, error, res);
  }
};

const getEntry = async (req, res) => {
  try {
    const pipeline = [
      {$lookup: {
        from: "radarCategories",
        localField: "radarCategoryId",
        foreignField: "_id",
        as: "radarCategoryName"
      }},
      {$set: {radarCategoryName: {$arrayElemAt: ["$radarCategoryName.name", 0]}}},
      {$sort: {'addedBy.date': -1}},
    ]
    const radarSubcategory = await RadarSubcategory.aggregate(pipeline);

    if(!radarSubcategory.length) return messageResponse(NOT_FOUND, "Radar Subcategory", false, 404, null, res);

    return messageResponse(null, "", true, 200, radarSubcategory, res);
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const getByID = async (req, res) => {
  try {
    if (!req.query.id || !mongoose.isValidObjectId(req.query.id)) {
      return messageResponse(REQUIRED, "ID", false, 400, null, res)
    }

    const pipeline = [
      {$match: { _id: new mongoose.Types.ObjectId(req.query.id) }},
      {$lookup: {
        from: "radarCategories",
        localField: "radarCategoryId",
        foreignField: "_id",
        as: "radarCategoryName"
      }},
      {$set: {radarCategoryName: {$arrayElemAt: ["$radarCategoryName.name", 0]}}},
    ]
    let radarSubcategory = await RadarSubcategory.aggregate(pipeline);
    radarSubcategory = radarSubcategory[0]

    if (!radarSubcategory) return messageResponse(NOT_FOUND, "Radar Sub-category", false, 404, null, res)

    return messageResponse(null, "", true, 200, radarSubcategory, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const removeEntry =  async (req, res) => {
  try {
    if(!mongoose.isValidObjectId(req.body.id)) return messageResponse(REQUIRED, "ID", false, 400, null, res);

    let lmiSkillAbility = await LMISkillAbility.findOne({ radarSubcategoryId: req.body.id });
    if (lmiSkillAbility) return messageResponse(REMOVE_ERROR_ENGAGED, "Radar Category", false, 400, null, res);

    let radarSubCat = await RadarSubcategory.findOneAndDelete({ _id: req.body.id, defaultEntry: false })

    if(!radarSubCat) return messageResponse(NOT_FOUND, "Radar Sub-category", false, 404, null, res);

    return messageResponse(REMOVE_SUCCESS, "Radar Sub-category", true, 200, null, res)
  } catch (error) {
    commonHelper.doReqActionOnError(error)
    return messageResponse(SERVER_ERROR, "", false, 500, error, res)
  }
};

const updateEntry = async (req, res, next) => await addUpdateEntry(req, res, 'edit');

const addEntry = async (req, res, next) =>  await addUpdateEntry(req, res, 'add');

module.exports.addEntry = addEntry;
module.exports.getEntry = getEntry;
module.exports.getByID = getByID;
module.exports.removeEntry = removeEntry;
module.exports.updateEntry = updateEntry;