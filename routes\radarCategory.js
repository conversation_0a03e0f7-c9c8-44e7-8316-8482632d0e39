const express = require("express");
const router = express.Router();
const radarCategoryController = require('../controllers/radarCategory.controller')
const SuperUserGuard = require('../guards/super-user.guard')

router.post("/add", SuperUserGuard, radarCategoryController.addEntry);

router.get("/get", radarCategoryController.getEntry);

router.get("/getById", radarCategoryController.getByID);

router.get("/getSubRadarCat", radarCategoryController.getSubRadarCat);

router.delete("/remove", SuperUserGuard, radarCategoryController.removeEntry);

router.put("/update", SuperUserGuard, radarCategoryController.updateEntry);

module.exports = router;