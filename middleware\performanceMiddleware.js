/**
 * Performance Optimization Middleware
 * Adds performance monitoring and optimization to API endpoints
 */

const { performanceMonitor } = require('../services/performanceMonitorService');

/**
 * Request timing middleware
 * Tracks response times for all API endpoints
 */
const requestTiming = (req, res, next) => {
  const startTime = Date.now();
  
  // Override res.end to capture response time
  const originalEnd = res.end;
  res.end = function(...args) {
    const responseTime = Date.now() - startTime;
    
    // Record the metric
    performanceMonitor.recordMetric('api', responseTime, {
      method: req.method,
      path: req.path,
      statusCode: res.statusCode,
      userAgent: req.get('User-Agent'),
      ip: req.ip
    });
    
    // Add response time header
    res.set('X-Response-Time', `${responseTime}ms`);
    
    // Log slow requests
    if (responseTime > 5000) {
      console.warn(`🐌 Slow API request: ${req.method} ${req.path} took ${responseTime}ms`);
    }
    
    // Call original end
    originalEnd.apply(this, args);
  };
  
  next();
};

/**
 * Database query timing middleware
 * Can be used to wrap database operations
 */
const databaseTiming = (operation) => {
  return async (query, options = {}) => {
    const endTiming = performanceMonitor.startTiming('database');
    
    try {
      const result = await operation(query, options);
      endTiming({ 
        success: true, 
        query: query.constructor.name,
        collection: query.model?.collection?.name 
      });
      return result;
    } catch (error) {
      endTiming({ 
        success: false, 
        error: error.message,
        query: query.constructor.name 
      });
      throw error;
    }
  };
};

/**
 * Memory usage monitoring middleware
 */
const memoryMonitoring = (req, res, next) => {
  const memUsage = process.memoryUsage();
  
  // Log memory warnings
  const memoryThreshold = 500 * 1024 * 1024; // 500MB
  if (memUsage.heapUsed > memoryThreshold) {
    console.warn(`⚠️ High memory usage: ${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  }
  
  // Add memory info to response headers in development
  if (process.env.NODE_ENV === 'development') {
    res.set('X-Memory-Usage', `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`);
  }
  
  next();
};

/**
 * Rate limiting with performance awareness
 */
const performanceAwareRateLimit = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const requests = new Map();
  
  return (req, res, next) => {
    const key = req.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean old entries
    if (requests.has(key)) {
      const userRequests = requests.get(key).filter(time => time > windowStart);
      requests.set(key, userRequests);
    }
    
    const userRequests = requests.get(key) || [];
    
    // Check if user has exceeded rate limit
    if (userRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests. Please slow down.',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }
    
    // Add current request
    userRequests.push(now);
    requests.set(key, userRequests);
    
    // Add rate limit headers
    res.set({
      'X-RateLimit-Limit': maxRequests,
      'X-RateLimit-Remaining': maxRequests - userRequests.length,
      'X-RateLimit-Reset': new Date(now + windowMs).toISOString()
    });
    
    next();
  };
};

/**
 * Response compression middleware
 */
const responseCompression = (req, res, next) => {
  // Only compress JSON responses
  const originalJson = res.json;
  res.json = function(data) {
    // Add compression hint
    if (req.get('Accept-Encoding')?.includes('gzip')) {
      res.set('Content-Encoding', 'gzip');
    }
    
    return originalJson.call(this, data);
  };
  
  next();
};

/**
 * Cache headers middleware
 */
const cacheHeaders = (maxAge = 300) => {
  return (req, res, next) => {
    // Only cache GET requests
    if (req.method === 'GET') {
      res.set({
        'Cache-Control': `public, max-age=${maxAge}`,
        'ETag': `"${Date.now()}"` // Simple ETag based on timestamp
      });
    }
    
    next();
  };
};

/**
 * Performance monitoring endpoint
 */
const performanceEndpoint = (req, res) => {
  try {
    const stats = performanceMonitor.getStats();
    const recommendations = performanceMonitor.getRecommendations();
    
    res.json({
      success: true,
      data: {
        performance: stats,
        recommendations: recommendations,
        timestamp: new Date()
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get performance stats',
      error: error.message
    });
  }
};

module.exports = {
  requestTiming,
  databaseTiming,
  memoryMonitoring,
  performanceAwareRateLimit,
  responseCompression,
  cacheHeaders,
  performanceEndpoint
};
