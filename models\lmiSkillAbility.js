const mongoose = require("mongoose");
const { RadarCategory, RadarCategorySchema } = require("./radarCategory");
const { RadarSubcategory, RadarSubcategorySchema } = require("./radarSubcategory");

const LMISkillAbilitySchema = new mongoose.Schema({
  lmiId: String,
  lmiName:String,
  category:String,
  radarCategoryId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(RadarCategory, RadarCategorySchema) },
  radarSubcategoryId: { type: mongoose.Schema.Types.ObjectId, ref: mongoose.model(RadarSubcategory, RadarSubcategorySchema) },
  
  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.LMISkillAbilitySchema = LMISkillAbilitySchema;

class LMISkillAbility extends mongoose.Model
{
  static async createDefaultEntries()
  {
    for (const entry of defaultEntries)
    {
      const count = await LMISkillAbility.count({ _id: entry._id });
      if (count)
      {
        return;
      }

      entry.defaultEntry = true;
      entry.addedBy = {
        date: "2000-10-11T08:49:34.176Z",
        name: "System Created",
        _id: null,
      };
      await LMISkillAbility.create(entry);
    }
  }
}

mongoose.model(LMISkillAbility, LMISkillAbilitySchema, "lmiSkillsAbilities");

module.exports.LMISkillAbility = LMISkillAbility;
