import { Navigate, useRoutes } from 'react-router-dom';
import jwtDecode from "jwt-decode";
import Cookies from 'universal-cookie';
import { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
// layouts
import DashboardLayout from './layouts/dashboard';
import SimpleLayout from './layouts/simple';
import LoginPage from './pages/Auth/LoginPage';
import Page404 from './pages/Page404';
import DashboardAppPage from './pages/DashboardAppPage';
import Skills from './pages/Skills/Skills';
import Careers from './pages/CareerPage/Careers';
import Colleges from './pages/Colleges/Colleges';
import CollegeGroups from './pages/CollegeGroup/CollegeGroups';
import Courses from './pages/Courses/Courses';
import UsersPage from './pages/Userspage/UsersPage';
import Campuses from './pages/Campuses/Campuses';
import RequireAuth from './components/RequireAuth';
import AddCollegeGroup from './pages/CollegeGroup/AddCollegeGroup';
import AddColleges from './pages/Colleges/AddColleges';
import AddCampus from './pages/Campuses/AddCampus';
import AddUser from './pages/Userspage/AddUser';
import EditCollegeGroup from './pages/CollegeGroup/EditCollegeGroup';
import EditUser from './pages/Userspage/EditUser';
import EditCampus from './pages/Campuses/EditCampus';
import EditColleges from './pages/Colleges/EditColleges';
import DefaultUser from './pages/DefaultUser';
import AddCourse from './pages/Courses/AddCourse';
import Sector from './pages/Sector/Sector';
import SubSector from './pages/SubSector/SubSector';
import { APP_ROUTER_BASE_URL } from './utils';
import RadarCategory from './pages/RadarCategory/RadarCategory';
import RadarSubCategory from './pages/RadarSubCategory/RadarSubCategory';
import EditCareer from './pages/CareerPage/EditCareer';
import EditCourse from './pages/Courses/EditCourse';
import Abilities from './pages/Abilities/Abilties';
import CreateCareer from './pages/CareerPage/AddCareer';
import ImportCourses from './pages/Courses/ImportCourses';
import Profile from './pages/Profile';
import ForgotPassword from './pages/Auth/ForgetPassword';
import ResetPassword from './pages/Auth/ResetPassword';
import Setting from './pages/Setting';
import Unauthorized from './pages/Unauthorized';
import AddRegion from './pages/Regions/AddRegion';
import EditRegion from './pages/Regions/EditRegion';
import Regions from './pages/Regions/Regions';
import NewCareers from './pages/NewCareers/NewCareers';
import CreateNewCareer from './pages/NewCareers/AddNewCareer';
import EditNewCareer from './pages/NewCareers/EditNewCareer';

// ----------------------------------------------------------------------

export default function Router() {
  const userPermissions = useSelector(state => state.selectedCollege?.currentUserPermissions)
  const cookies = new Cookies();
  const jwtToken = cookies.get("token")
  const Role = jwtToken && jwtDecode(jwtToken)
  const role = String(Role?.role) || "5"
  const CollegePermissions = JSON.parse(localStorage.getItem('currentUserPermissions'))
  const [collegePermissions, setCollegePermissions] = useState(null)
  useEffect(() => {
    // console.log('userPermissions in mav', userPermissions);
    if (userPermissions) {
      setCollegePermissions(userPermissions)
    }
    setCollegePermissions(CollegePermissions)
  }, [userPermissions])

  const actualCollegePermissions = {
    // ...collegePermissions,
    app: collegePermissions?.dashboard,
    user: collegePermissions?.users,
    courses: collegePermissions?.courses,
    campuses: collegePermissions?.campuses
  }
  const isUnauthorized = (path) => {
    if(!collegePermissions){
      return false
    }
    if (role !== '2' && role !== '3' && role !== '4') {   // checking only for college group, college & campus 
      return false
    }
    if (path in actualCollegePermissions) {
      return !actualCollegePermissions[path]
    }
    return false

  }
  const routes = useRoutes([
    {
      // path: '/dashboard',
      path: `${APP_ROUTER_BASE_URL}dashboard`,
      element: <DashboardLayout />,
      children: [
        { element: <Navigate to={`${APP_ROUTER_BASE_URL}dashboard/app`} />, index: true },
        {
          element: <RequireAuth allowedRoles={["1", "2", "3", "4"]} />,
          children: [
            { path: 'app', element: isUnauthorized('app') ? <Unauthorized /> : <DashboardAppPage /> },
            // { path: 'app', element: collegePermissions?.dashboard === false ?<Unauthorized /> : <DashboardAppPage />   },
            // { path: 'app', element:('dashboard' in collegePermissions ? collegePermissions['dashboard'] : true) && <DashboardAppPage /> },
            // { path: 'setting', element: <Setting /> },

            // { path: 'user', element: <UsersPage /> },
            // { path: 'user/add', element: <AddUser /> },
            // { path: 'user/edit/:id', element: <EditUser /> },
          ]
        },
        {                                                             // User
          element: <RequireAuth allowedRoles={["1", "2", "3"]} />,
          children: [
            { path: 'user', element: isUnauthorized('user') ? <Unauthorized /> : <UsersPage /> },
            { path: 'user/add', element: isUnauthorized('user') ? <Unauthorized /> : <AddUser /> },
            { path: 'user/edit/:id', element: isUnauthorized('user') ? <Unauthorized /> : <EditUser /> },

          ]
        },
        { path: 'defaultuser', element: <DefaultUser /> },
        {                                                             // Courses, sectors, radar category
          element: <RequireAuth allowedRoles={["1", "4", "2", "3"]} />,
          children: [
            { path: 'courses', element: isUnauthorized('courses') ? <Unauthorized /> : <Courses /> },
            { path: 'courses/add', element: isUnauthorized('courses') ? <Unauthorized /> : <AddCourse /> },
            { path: 'courses/edit/:id', element: isUnauthorized('courses') ? <Unauthorized /> : <EditCourse /> },
            { path: 'importcourses', element: isUnauthorized('courses') ? <Unauthorized /> : <ImportCourses /> },
            { path: 'sectors', element: <Sector /> },
            // { path: 'subsectors', element: <SubSector /> },
            { path: 'radarcategory', element: <RadarCategory /> },
            // { path: 'radarsubcategory', element: <RadarSubCategory /> },
            // { path: 'subsectors', element: <SubSector /> }, 
          ]
        },
        {
          element: <RequireAuth allowedRoles={["1", "4", "2", "3"]} />,
          children: [
            { path: 'campuses', element: isUnauthorized('campuses') ? <Unauthorized /> : <Campuses /> },
            { path: 'campuses/add', element: isUnauthorized('campuses') ? <Unauthorized /> : <AddCampus /> },
            { path: 'campuses/edit/:id', element: isUnauthorized('campuses') ? <Unauthorized /> : <EditCampus /> },
            { path: 'campuses', element: collegePermissions?.courses === false ?<Unauthorized /> : <Campuses /> },
            { path: 'campuses/add', element: collegePermissions?.courses === false ?<Unauthorized /> : <AddCampus /> },
            { path: 'campuses/edit/:id', element: collegePermissions?.courses === false ?<Unauthorized /> : <EditCampus /> },

          ]
        },
        // { path: 'campuses', element: <Campuses /> },
        {
          element: <RequireAuth allowedRoles={["1"]} />,
          children: [
            { path: 'skills', element: <Skills /> },
            { path: 'abilities', element: <Abilities /> },
            { path: 'old-careers', element: <Careers /> },
            { path: 'old-careers/create-careers', element: <CreateCareer /> },
            { path: 'old-careers/edit/:id', element: <EditCareer /> },
            { path: 'setting', element: <Setting /> },
          ]
        },
        {
          element : <RequireAuth allowedRoles={["1"]}/>,
          children: [
            { path: 'careers', element: <NewCareers /> },
            { path: 'careers/create-careers', element: <CreateNewCareer /> },
            { path: 'careers/edit/:id', element: <EditNewCareer /> },
          ]
        },

        {
          element: <RequireAuth allowedRoles={["1", "3", "2"]} />,
          children: [
            { path: 'colleges', element: <Colleges /> },
            { path: 'colleges/add', element: <AddColleges /> },
            { path: 'colleges/edit/:id', element: <EditColleges /> },
          ]
        },
        {
          element: <RequireAuth allowedRoles={["1", "3", "2"]} />,
          children: [
            { path: 'regions', element: <Regions /> },
            { path: 'regions/add', element: <AddRegion /> },
            { path: 'regions/edit/:id', element: <EditRegion /> },
          ]
        },
        {
          element: <RequireAuth allowedRoles={["1", "2"]} />,
          children: [
            { path: "collegegroups", element: <CollegeGroups /> },
            { path: "collegegroups/add", element: <AddCollegeGroup /> },
            { path: "collegegroups/edit/:id", element: <EditCollegeGroup /> },
          ]
        },
        {
          element: <RequireAuth allowedRoles={["1", "2", "3", "4"]} />,
          children: [
            { path: 'profile', element: <Profile /> },

            // { path: 'user', element: <UsersPage /> },
            // { path: 'user/add', element: <AddUser /> },
            // { path: 'user/edit/:id', element: <EditUser /> },
          ]
        },
      ],
    },
    {
      path: `${APP_ROUTER_BASE_URL}login`,
      element: <LoginPage />,
    },
    {
      path: `${APP_ROUTER_BASE_URL}forgotpassword`,
      element: <ForgotPassword />,
    },
    {
      path: `${APP_ROUTER_BASE_URL}resetpassword`,
      element: <ResetPassword />,
    },
    {
      element: <SimpleLayout />,
      children: [
        { element: <Navigate to={`${APP_ROUTER_BASE_URL}dashboard/app`} />, index: true },
        // { path: `${APP_ROUTER_BASE_URL}`, element: <Navigate to={`${APP_ROUTER_BASE_URL}dashboard/app`} /> },
        { path: '404', element: <Page404 /> },
        // { path: '*', element: <Navigate to= {`${APP_ROUTER_BASE_URL}404`} /> },
        { path: '*', element: <Page404 /> },
      ],
    },
    {
      path: '*',
      // element: <Navigate to={`${APP_ROUTER_BASE_URL}404`} replace />,
      element: <Navigate to={`${APP_ROUTER_BASE_URL}404`} replace />,
    },
    {
      path: `${APP_ROUTER_BASE_URL}`,
      // element: <Navigate to={`${APP_ROUTER_BASE_URL}404`} replace />,
      element: <Navigate to={`${APP_ROUTER_BASE_URL}dashboard`} replace />,
    },
  ]);
  return routes;
}
