import { <PERSON>, Button, Card, Stack } from '@mui/material'
import React from 'react'
import { LoadingButton } from '@mui/lab';
import TextFIeldComponent from './TextField/TextFIeldComponent'

const ChangePassword = ({ formik, loading }) => {
    const { oldPassword, newPassword, confirmPassword } = formik.values
    return (
        <Box
            py={1}
        >
            <form onSubmit={formik.handleSubmit}>
                <Stack
                    gap={2}
                >
                    <TextFIeldComponent
                        sx={{ width: '100%' }}
                        name='oldPassword'
                        type='password'
                        label="Current Password"
                        onBlur={formik?.handleBlur}
                        value={oldPassword}
                        onChange={formik?.handleChange}
                        error={formik?.touched.oldPassword && Boolean(formik?.errors.oldPassword)}
                        helperText={formik?.touched.oldPassword && formik?.errors.oldPassword}
                    />
                    <TextFIeldComponent
                        sx={{ width: '100%' }}
                        name='newPassword'
                        type='password'
                        label="New Password"
                        onBlur={formik?.handleBlur}
                        value={newPassword}
                        onChange={formik?.handleChange}
                        error={formik?.touched.newPassword && Boolean(formik?.errors.newPassword)}
                        helperText={formik?.touched.newPassword && formik?.errors.newPassword}
                    />
                    <TextFIeldComponent
                        sx={{ width: '100%' }}
                        name='confirmPassword'
                        type='password'
                        label="Confirm Password"
                        onBlur={formik?.handleBlur}
                        value={confirmPassword}
                        onChange={formik?.handleChange}
                        error={formik?.touched.confirmPassword && Boolean(formik?.errors.confirmPassword)}
                        helperText={formik?.touched.confirmPassword && formik?.errors.confirmPassword}
                    />
                </Stack>
                <LoadingButton
                    loading={loading}
                    type='submit'
                    variant='contained'
                    fullWidth
                    sx={{ mt: 2 }}
                >
                    Reset Password
                </LoadingButton>
            </form>
        </Box>
    )
}

export default ChangePassword