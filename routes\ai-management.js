const express = require('express');
const router = express.Router();
const rateLimit = require('express-rate-limit');

// Import AI management controller
const aiController = require('../controllers/ai.controller');

// Rate limiting for AI management endpoints
const aiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    success: false,
    message: "Too many AI management requests from this IP, please try again later.",
    data: null
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * TRAINING DATA MANAGEMENT ROUTES
 */

// Generate training data from database
// POST /api/ai/training-data/generate
// Body: { maxExamplesPerType?, validationSplit?, validateData?, examplesPerRecord? }
router.post("/training-data/generate",
  aiRateLimit,
  aiController.generateTrainingData
);

// List all training datasets
// GET /api/ai/training-datasets
// Query: ?status=active&limit=10&page=1&sortBy=createdAt&sortOrder=desc
router.get("/training-datasets",
  aiRateLimit,
  aiController.listTrainingDatasets
);

// Get specific training dataset details
// GET /api/ai/training-datasets/:id
router.get("/training-datasets/:id",
  aiRateLimit,
  aiController.getTrainingDataset
);

// Download training dataset files
// GET /api/ai/training-datasets/:id/files/:type
// Types: training, validation, statistics, report
router.get("/training-datasets/:id/files/:type",
  aiRateLimit,
  aiController.downloadDatasetFile
);

// Preview training dataset content
// GET /api/ai/training-datasets/:id/preview
// Query: ?lines=100&type=training
router.get("/training-datasets/:id/preview",
  aiRateLimit,
  aiController.previewDataset
);

/**
 * FINE-TUNING JOB MANAGEMENT ROUTES
 */

// Create fine-tuning job with OpenAI
// POST /api/ai/fine-tuning/create-job
// Body: { trainingDatasetId, suffix, epochs?, baseModel? }
router.post("/fine-tuning/create-job",
  aiRateLimit,
  aiController.createFineTuningJob
);

// List all fine-tuning jobs
// GET /api/ai/fine-tuning/jobs
// Query: ?status=succeeded&limit=10&page=1
router.get("/fine-tuning/jobs",
  aiRateLimit,
  aiController.listFineTuningJobs
);

// Get specific fine-tuning job status
// GET /api/ai/fine-tuning/jobs/:jobId
router.get("/fine-tuning/jobs/:jobId",
  aiRateLimit,
  aiController.getFineTuningJobStatus
);

/**
 * MODEL MANAGEMENT ROUTES
 */

// List all fine-tuned models
// GET /api/ai/models
// Query: ?status=active&deploymentStatus=deployed&limit=10&page=1
router.get("/models",
  aiRateLimit,
  aiController.listModels
);

// Deploy specific model
// POST /api/ai/models/:id/deploy
// Body: { deployedBy? }
router.post("/models/:id/deploy",
  aiRateLimit,
  aiController.deployModel
);

// Get currently deployed model
// GET /api/ai/models/deployed
router.get("/models/deployed",
  aiRateLimit,
  aiController.getDeployedModel
);

// Get specific model details
// GET /api/ai/models/:id
router.get("/models/:id",
  aiRateLimit,
  aiController.getModel
);

// Update model metadata
// PUT /api/ai/models/:id
// Body: { modelName?, description?, notes?, tags? }
router.put("/models/:id",
  aiRateLimit,
  aiController.updateModel
);

// Test specific model
// POST /api/ai/models/:id/test
// Body: { testMessage?, options? }
router.post("/models/:id/test",
  aiRateLimit,
  aiController.testModel
);

/**
 * MODEL CONFIGURATION ROUTES
 */

// Get model configuration
// GET /api/ai/models/:id/configuration
router.get("/models/:id/configuration",
  aiRateLimit,
  aiController.getModelConfiguration
);

// Update model configuration
// PUT /api/ai/models/:id/configuration
// Body: { maxTokens?, temperature?, systemPrompt?, responseStyle?, ... }
router.put("/models/:id/configuration",
  aiRateLimit,
  aiController.updateModelConfiguration
);

/**
 * ANALYTICS AND MONITORING ROUTES
 */

// Get system overview
// GET /api/ai/overview
router.get("/overview",
  aiRateLimit,
  aiController.getSystemOverview
);

module.exports = router;
