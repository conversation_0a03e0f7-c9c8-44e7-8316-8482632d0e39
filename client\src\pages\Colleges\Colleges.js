import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
// components
import {
  <PERSON><PERSON>,
  Container,
  Link,
  Typography
} from '@mui/material';
// @mui
import { get } from 'lodash';
import { useNavigate, useParams } from 'react-router-dom';
import DataTable from '../../components/DataTable/DataTable';
import PopUp from '../../components/PopUp';
import useAuth from '../../hooks/useAuth';
import useLoading from '../../hooks/useLoading';
import { setSnackbar } from '../../Redux/snackbarSlice';
import { APP_ROUTER_BASE_URL, allowedRoles } from '../../utils';
import { getCollegeGroups } from '../CollegeGroup/collegeGroupsSlice';
import FilterForm from '../FilterForm';
import { getColleges, removeCollege, syncCourse } from './collegesSlice';

// ----------------------------------------------------------------------

export const COLLEGE_TABLE_HEAD = [
  { id: 'name', label: "College", alignRight: false },
  { id: 'groupName', label: "Group", alignRight: false },
  { id: 'slug', label: "Slug", alignRight: false },
  { id: 'syncCourse', label: "", alignRight: false },
  { id: 'actions', label: 'Actions', alignRight: true, pr: 7 },
];



const Colleges = () => {
  const { role } = useAuth()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [Colleges, setColleges] = useState([]);
  const collegeStates = useSelector(state => state.colleges)
  // const colleges = useSelector(state => state.colleges.colleges)
  const groups = useSelector(state => state.collegeGroups.groups)
  const [selectedGroups, setSelectedGroups] = useState([]);
  const [selectedColleges, setSelectedColleges] = useState([]);
  const [openFilter, setOpenFilter] = useState(false);
  // const [Users, setUsers] = useState([]);
  const [Groups, setGroups] = useState([]);
  const params = useParams()
const syncCourseComponent = (row) => row?.hasApiKey ? (
  <Button
    variant="outlined"
    size="small"
    color="primary"
    onClick={() => handleSyncCourse(row)}
  >
    Sync Course
  </Button>
) : null;

const handleSyncCourse = async (college) => {
  if(college?._id){
    dispatch(syncCourse(college?._id)).then((response) => {
      if (response?.payload?.success) {
        const successMessage = get(response, 'payload.message', 'Course synced successfully');
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'success',
          snackbarMessage: successMessage
        }));
      } else {
        const errorMessage = response?.payload?.response?.data?.message || "Something went wrong";
        dispatch(setSnackbar({
          snackbarOpen: true,
          snackbarType: 'error',
          snackbarMessage: errorMessage
        }));
      }
    })
  }
};


// export const renderCollegeCells = ['name', 'groupName', 'slug'];
const slugComponent = (row) =>
  row.slug && (
    <Link underline="hover" href={`${window.location.origin}/college/${row.slug}`} target="_blank" rel="noopener">
      {row.slug}
    </Link>
  )

  const renderCollegeCells = ['name', 'groupName', slugComponent, syncCourseComponent ];

  // const colleges = useSelector(state => state.colleges.colleges)
  const { colleges, status, error } = collegeStates

  const loading = useLoading(status)

  useEffect(() => {
    if (!!colleges) {
      setColleges([...colleges])
    }
  }, [colleges])

  useEffect(() => {
    // if (status === "idle")
    dispatch(getColleges())
    dispatch(getCollegeGroups())
  }, [dispatch])

  const deleteTitle = "Delete College?"
  const deleteDescription = "Are you sure you want to delete this college ?"

  const handleFilterSearch = (event) => {
    const filtereColleges = !!Colleges ? Colleges.filter(college => college.name?.toLowerCase().includes(event.target.value.toLowerCase()) ||
      college.groupName?.toLowerCase().includes(event.target.value.toLowerCase())) : []
    return filtereColleges
  };

  const handleDeleteCollege = (College, handleOpenBackdrop, handleCloseBackdrop) => {
    const data = {
      id: College._id
    }
    try {
      handleOpenBackdrop()
      dispatch(removeCollege(data)).then(response => {
        if (response?.payload?.success) {
          dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'success',
            snackbarMessage: "College Deleted Succesfully"
          }))
        } else {
          const errorMessage = response?.payload?.response?.data?.message
          dispatch(setSnackbar({
            snackbarOpen: true,
            snackbarType: 'error',
            snackbarMessage: errorMessage || "something went wrong"
          }))
        }
      }).finally(() => {
        handleCloseBackdrop()
      })
    } catch (error) {
      console.log('delete error', error)
    }
  }

  const editCollege = (college) => {
    navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges/edit/${college?._id}`)
  }

  useEffect(() => {
    if (openFilter) {
      if (role === '1' || role === '2') {
        dispatch(getCollegeGroups())
      }
      // if (role === '1' || role === '2' || role === '3') {
      //   dispatch(getColleges())
      // }
    }
  }, [openFilter])

  useEffect(() => {
    if (!!groups) {
      setGroups([...groups])
    }
  }, [groups])
  const handleCloseFilter = () => {
    // setSelectedColleges([])
    // setSelectedGroups([])
    // setUsers(Users)
    setOpenFilter(false)
  }

  const handleFilter = () => {
    setOpenFilter(true)
  }

  // const handleOpen = () => setOpenModel(true);
  const handleOpen = () => navigate(`${APP_ROUTER_BASE_URL}dashboard/colleges/add`);

  return (
    <>
      <Helmet>
        <title> Colleges | ThinkSkill </title>
      </Helmet>
      <Container maxWidth="xl">
        <PopUp
          open={openFilter}
          onClose={handleCloseFilter}
          title={"Filter"}
        >
          <FilterForm
            selectedGroups={selectedGroups}
            setSelectedGroups={setSelectedGroups}
            openModel={openFilter}
            setOpenModel={setOpenFilter}
            setColleges={setColleges}
            Groups={Groups}
            // users={users}
            Colleges={Colleges}
            selectedColleges={selectedColleges}
            setSelectedColleges={setSelectedColleges}
            filterName={"colleges"}
          />
        </PopUp>
        <Typography variant="h4" gutterBottom mb={3}>
          Colleges
        </Typography>
        <DataTable
          loading={loading}
          deleteTitle={deleteTitle}
          deleteDescription={deleteDescription}
          TableHead={COLLEGE_TABLE_HEAD}
          TableData={Colleges}
          filterSearch={handleFilterSearch}
          buttonText={allowedRoles(role, ['1', '2']) && "New College"}
          disableDelete={role !== '1' && role !== '2'}
          buttonHandler={handleOpen}
          handleEdit={editCollege}
          renderCells={renderCollegeCells}
          handleDelete={handleDeleteCollege}
          filter="true"
          handleFilter={handleFilter}
          pagination="true"
          rowsPerPageProp={10}
        />
      </Container>
    </>
  )
}

export default Colleges;



