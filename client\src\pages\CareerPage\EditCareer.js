import { LoadingButton } from '@mui/lab';
import { Autocomplete, Backdrop, Box, Button, Card, CircularProgress, Container, FormControl, Grid, LinearProgress, Stack, TextField, Typography } from '@mui/material';
import Paper from '@mui/material/Paper';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell, { tableCellClasses } from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/material/styles';
import { useFormik } from 'formik';
import { get } from 'lodash';
import { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useNavigate, useParams } from 'react-router-dom';
import DataTable from '../../components/DataTable/DataTable';
import Iconify from '../../components/Iconify/Iconify';
import TextFIeldComponent from '../../components/TextField/TextFIeldComponent';
import { APP_ROUTER_BASE_URL } from '../../utils';
import axiosInstance from '../../utils/axiosInstance';
import { getSectors } from '../Sector/SectorSlice';
import { getSubSectors } from '../SubSector/SubSectorSlice';
import { updateCareer } from './careerSlice';
import { setSnackbar } from '../../Redux/snackbarSlice';
import { careerValidation } from '../../utils/validationSchemas';
// import Card from '../theme/overrides/Card'

export const SKILLS_TABLE_HEAD = [
    { id: 'name', label: 'Name', alignRight: false },
    { id: 'value', label: 'Value', alignRight: false, },
];

export const skillsRenderCells = ['name', 'value']

export const ABILITIES_TABLE_HEAD = [
    { id: 'name', label: 'Name', alignRight: false },
    { id: 'value', label: 'Value', alignRight: false, },
];

export const abbilitiesRenderCells = ['name', 'value']

const EditCareer = () => {
    const navigate = useNavigate()
    const dispatch = useDispatch()
    const params = useParams()
    const [isUpdating, setIsUpdating] = useState(false);

    const formik = useFormik({
        initialValues: {
            id: '',
            title: '',
            careerDescription: '',
            onetCodeForSkillAbility:'',
            knowledge: '',
            intrest: '',
            jobZone: '',
            salary: '',
            cluster: '',
            pathway: '',
            skills: [],
            abilities: [],
            searchTitle: "",
            onetCode: "",
            socCode: "",
            hoursPerWeek: "",
            tasks: "",
            estimateHours: "",
            estimatePay: "",
            description: "",
        },
        validationSchema: careerValidation,
        onSubmit: (values) => {
            setIsUpdating(true)
            dispatch(updateCareer(values)).then(res => {
                if (res.payload.success) {
                    const successMessage = get(res, 'payload.message', "Successfully Updated Career")
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'success',
                        snackbarMessage: successMessage
                    }))
                    navigate(`${APP_ROUTER_BASE_URL}dashboard/old-careers`)
                } else {
                    const errorMessage = get(res, 'payload.data.message', 'Something Went Wrong')
                    dispatch(setSnackbar({
                        snackbarOpen: true,
                        snackbarType: 'error',
                        snackbarMessage: errorMessage
                    }))

                }
            }).finally(() => setIsUpdating(false))
        },
    })
    const style = {
        p: 4,
    };
    const [currentSector, setCurrentSector] = useState(null);
    const [selectedSectors, setSelectedSectors] = useState([]);
    const [filteredSectors, setFilteredSectors] = useState([]);
    const [isLoading, setIsLoading] = useState(false)
    const [Sectors, setSectors] = useState([
        {
            label: '',
            _id: '',
            subSectors: []
        }
    ])
    const [SubSectors, setSubSectors] = useState([])
    const sectors = useSelector(state => state.sectors.sectors)
    const subSectors = useSelector(state => state.subSectors.subSectors)
    useEffect(() => {
        dispatch(getSectors())
        dispatch(getSubSectors())
    }, [])
    useEffect(() => {
        const sectorOptions = sectors?.length ? sectors?.map(sector => ({
            label: sector.name,
            _id: sector._id,
        })) : []
        const subSectorOptions = subSectors?.length ? subSectors?.map(subSector => ({
            label: subSector.name,
            _id: subSector._id,
            sectorId: subSector.sectorId
        })) : []

        setSectors(sectorOptions)
        setSubSectors(subSectorOptions)
    }, [sectors, subSectors])

    useEffect(() => {
        const getCareer = async (id) => {
            setIsLoading(true)
            try {
                const response = await axiosInstance({
                    url: "careers/getByID",
                    method: "GET",
                    params: {
                        id
                    }
                })
                const careerDetails = response.data?.data;
                formik.setValues({ ...careerDetails, id: params?.id })
                setSelectedSectors(careerDetails.sectors)
            } catch (error) {
                const errorMessage = error?.response?.data?.msg
                console.log("error get career", errorMessage)
            } finally {
                setIsLoading(false)
            }
        }
        getCareer(params?.id)
    }, [])
    useEffect(() => {
        const selectedSectorsIds = selectedSectors.map(sector => sector?._id)
        setFilteredSectors(!!selectedSectors.length ? Sectors.filter(sector => !selectedSectorsIds.includes(sector?._id)) : Sectors)
    }, [selectedSectors, Sectors])


    const StyledTableCell = styled(TableCell)(({ theme }) => ({
        [`&.${tableCellClasses.head}`]: {
            //   backgroundColor: theme.palette.common.black,
            backgroundColor: theme.palette.common.primary,
            color: theme.palette.common.black,
        },
        [`&.${tableCellClasses.body}`]: {
            fontSize: 14,
        },
    }));

    const StyledTableRow = styled(TableRow)(({ theme }) => ({
        '&:last-child td, &:last-child th': {
            border: 0,
        },
    }));
    const addRow = () => {
        if (currentSector) {
            // setSelectedSectors([...selectedSectors,
            // {
            //     name: currentSector.label,
            //     _id: currentSector._id,
            //     subsectors: SubSectors.filter(subSector => subSector.sectorId === currentSector._id).map(subsector => {
            //         return {
            //             name:subsector?.label,
            //             _id: subsector?._id,
            //         }
            //     })
            // }])
            setSelectedSectors([...selectedSectors,
                {
                    ...currentSector,
                    subsectors: SubSectors.filter(subSector => subSector.sectorId === currentSector._id)
            
            }])
            setCurrentSector('')
        } else {
            // alert("else")

        }
    }
    const handleDeleteRow = (row) => {
        setSelectedSectors(selectedSectors.filter(sectors => sectors._id !== row._id))
    }
    const handleSubSectorsChange = (value, row) => {
        const sectorNames = value.map(val => {
            return {
                _id: val._id,
                name: val.label
            }
        })
        setSelectedSectors(selectedSectors.map(sector => sector._id === row._id ? { ...sector, subsectors: value } : sector))
    }
    useEffect(() => {
        formik.setValues({
            ...formik.values,
            sectors: selectedSectors
        })
    }, [selectedSectors])
    return (
        <>
            <Helmet>
                <title> Create Careers | ThinkSkill </title>
            </Helmet>
            <Container maxWidth="xl">
                <Typography variant="h4" component="h2" sx={{ pb: 3 }} >
                    Edit Career
                </Typography>
                <>{isLoading ? <LinearProgress /> :
                    <Card>
                        <Box sx={style}>
                            <form onSubmit={formik.handleSubmit}>
                                <Grid container gap={2} rowGap={3} >

                                    <Grid item xs={12} md={11.8} lg={3.81}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='title'
                                            label="Title"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.title}
                                            onChange={formik.handleChange}
                                            error={formik.touched.title && Boolean(formik.errors.title)}
                                            helperText={formik.touched.title && formik.errors.title}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.81}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            disabled
                                            name='onetCode'
                                            label="O-Net Code"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.onetCode}
                                            onChange={formik.handleChange}
                                            error={formik.touched.onetCode && Boolean(formik.errors.onetCode)}
                                            helperText={formik.touched.onetCode && formik.errors.onetCode}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.81}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            disabled
                                            name='socCode'
                                            label="SOC Code"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.socCode}
                                            onChange={formik.handleChange}
                                            error={formik.touched.socCode && Boolean(formik.errors.socCode)}
                                            helperText={formik.touched.socCode && formik.errors.socCode}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={5.85}>
                                        <TextFIeldComponent
                                            multiline
                                            rows={12}
                                            placeholder="Description"
                                            sx={{ width: '100%' }}
                                            name='description'
                                            label="Description"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.description}
                                            onChange={formik.handleChange}
                                            error={formik.touched.description && Boolean(formik.errors.description)}
                                            helperText={formik.touched.description && formik.errors.description}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={5.85}>
                                        <TextFIeldComponent
                                            multiline
                                            rows={12}
                                            placeholder="Tasks"
                                            sx={{ width: '100%' }}
                                            name='tasks'
                                            label="Tasks"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.tasks}
                                            onChange={formik.handleChange}
                                            error={formik.touched.tasks && Boolean(formik.errors.tasks)}
                                            helperText={formik.touched.tasks && formik.errors.tasks}
                                        />
                                    </Grid>

                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='estimateHours'
                                            label="Hours / Week"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.estimateHours}
                                            onChange={formik.handleChange}
                                            error={formik.touched.estimateHours && Boolean(formik.errors.estimateHours)}
                                            helperText={formik.touched.estimateHours && formik.errors.estimateHours}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='jobZone'
                                            label="Job Zone"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.jobZone.title}
                                            onChange={formik.handleChange}
                                            error={formik.touched.jobZone && Boolean(formik.errors.jobZone)}
                                            helperText={formik.touched.jobZone && formik.errors.jobZone}
                                        />
                                    </Grid>
                                    <Grid item xs={12} md={3.85}>
                                        <TextFIeldComponent
                                            sx={{ width: '100%' }}
                                            name='estimatePay'
                                            label="Salary"
                                            onBlur={formik.handleBlur}
                                            value={formik.values.estimatePay}
                                            onChange={formik.handleChange}
                                            error={formik.touched.estimatePay && Boolean(formik.errors.estimatePay)}
                                            helperText={formik.touched.estimatePay && formik.errors.estimatePay}
                                        />
                                    </Grid>

                                    <Grid xs={12} >
                                    <Grid container gap={2} alignItems={'center'}>
                                        <Grid md={4} xs={12}>
                                            <Stack gap={2} my={4} direction={"row"}>
                                                <TextFIeldComponent
                                                    sx={{ width: '100%' }}
                                                    name='onetCodeForSkillAbility'
                                                    label="Onet For Skills & Abilities"
                                                    disabled
                                                    // onBlur={formik.handleBlur}
                                                    value={formik.values.onetCodeForSkillAbility}
                                                    // onChange={formik.handleChange}
                                                    // error={formik.touched.onetCodeForSkillAbility && Boolean(formik.errors.onetCodeForSkillAbility)}
                                                    // helperText={formik.touched.onetCodeForSkillAbility && formik.errors.onetCodeForSkillAbility}
                                                />
                                                {/* {!editOnetToggled && <Button
                                                onClick={() => setEditOnetToggled(!editOnetToggled)}
                                            >
                                                Edit
                                            </Button>} */}
                                            </Stack>
                                        </Grid>
                                    </Grid>
                                </Grid>

                                    {!!formik.values.skills.length && <Grid item xs={5.84}>
                                        <DataTable
                                            // loading={dataLoading}
                                            TableHead={SKILLS_TABLE_HEAD}
                                            TableData={formik.values.skills}
                                            disableActions={"true"}
                                            disableSearch={"true"}
                                            heading={"Skills"}
                                            // filterSearch={handleFilterSearch}
                                            // searchLable={"Search..."}
                                            // handleEdit={editSector}
                                            renderCells={skillsRenderCells}
                                            // handleDelete={handleDeleteSector}
                                            pagination
                                        />
                                    </Grid>}
                                    {!!formik.values.abilities.length && <Grid item xs={5.84}>
                                        <DataTable
                                            // loading={dataLoading}
                                            TableHead={ABILITIES_TABLE_HEAD}
                                            TableData={formik.values.abilities}
                                            disableActions={"true"}
                                            heading={"Abilities"}
                                            disableSearch={"true"}
                                            // filterSearch={handleFilterSearch}
                                            // searchLable={"Search..."}
                                            // handleEdit={editSector}
                                            renderCells={abbilitiesRenderCells}
                                            // handleDelete={handleDeleteSector}
                                            pagination
                                        />
                                    </Grid>}

                                    <Grid xs={12}>

                                        {!selectedSectors.length ? null :
                                            (<TableContainer component={Paper}>
                                                <Table sx={{ minWidth: 700 }}
                                                    aria-label="customized table"
                                                >
                                                    <TableHead>
                                                        <TableRow>
                                                            <StyledTableCell>Sector</StyledTableCell>
                                                            <StyledTableCell align="left">Sub-Sectors</StyledTableCell>
                                                            <StyledTableCell align="right">Remove</StyledTableCell>
                                                            {/* <StyledTableCell align="right">Carbs&nbsp;(g)</StyledTableCell>
                                                    <StyledTableCell align="right">Protein&nbsp;(g)</StyledTableCell> */}
                                                        </TableRow>
                                                    </TableHead>
                                                    <TableBody>
                                                        {selectedSectors.map((row, index) => (
                                                            <StyledTableRow key={row?._id}>
                                                                <StyledTableCell sx={{ width: 400 }} component="th" scope="row">
                                                                    {row?.label}
                                                                </StyledTableCell>
                                                                <StyledTableCell sx={{ width: 600 }} align='left'>
                                                                    <FormControl sx={{ width: "100%", mt: 2 }}>
                                                                        <Autocomplete
                                                                            isOptionEqualToValue={(option, value) => option?._id === value?._id}
                                                                            options={SubSectors.filter(subSector => subSector.sectorId === row._id)}
                                                                            value={row.subsectors}
                                                                            onChange={(event, value) => handleSubSectorsChange(value, row)}
                                                                            multiple
                                                                            renderInput={(params) => <TextField {...params} label="Sub-Sectors" />}
                                                                        />
                                                                    </FormControl>
                                                                </StyledTableCell>
                                                                <StyledTableCell align="right">
                                                                    <Button
                                                                        color='error'
                                                                        onClick={() => handleDeleteRow(row)}
                                                                    // sx={{ width: 100 }}
                                                                    >
                                                                        <Iconify icon={'eva:trash-2-outline'} sx={{ mr: 1 }} />
                                                                    </Button>
                                                                </StyledTableCell>
                                                            </StyledTableRow>
                                                        ))}
                                                    </TableBody>
                                                </Table>
                                            </TableContainer>)}

                                    </Grid>
                                    <Grid item xs={12}>
                                        <Stack my={2} direction={'row'} >
                                            <Autocomplete
                                                // options={getFilteredSectors()}
                                                options={filteredSectors}
                                                // options={Sectors}
                                                isOptionEqualToValue={(option, value) => option?._id === value?._id}
                                                value={currentSector}
                                                onChange={(event, value) => {
                                                    setCurrentSector(value)
                                                    // setSectors(Sectors.filter(sector => sector._id !== value._id))
                                                }}
                                                // getOptionDisabled={(option) =>
                                                //     option === timeSlots[0] || option === timeSlots[2]
                                                // }
                                                sx={{ width: 400 }}
                                                renderInput={(params) => <TextField {...params} label="Sectors" />}
                                            />
                                            <Button
                                                onClick={addRow}
                                            >
                                                Add
                                            </Button>
                                        </Stack>
                                    </Grid>

                                </Grid>
                                <Stack
                                    direction="row"
                                    justifyContent="flex-end"
                                >
                                    <Link to={`${APP_ROUTER_BASE_URL}dashboard/old-careers`}>
                                        <Button
                                            type='button'
                                            variant='contained'
                                            color='error'
                                            sx={{ mt: 4, width: '10ch', mr: 2.5, }}
                                        >
                                            Cancel
                                        </Button>
                                    </Link>
                                    <LoadingButton
                                        loading={isUpdating}
                                        type='submit'
                                        variant='contained'
                                        sx={{ mt: 4, width: '10ch', mr: 2.5, }}>
                                        Update
                                    </LoadingButton>
                                </Stack>
                            </form>

                        </Box>
                    </Card>}
                </>
            </Container>
        </>
    )
}

export default EditCareer
