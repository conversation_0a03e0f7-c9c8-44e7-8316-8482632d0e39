const { Schema, Model, model } = require("mongoose");


const CareerGrowthSchema = new Schema({
  regionID: {
    type: Schema.Types.ObjectId,
    ref: "regions",
  },
  socCode: { // first 2 digits
    type: String,
    default: ""
  },
  values: {
    2025: {
      type: Number,
      default: null
    },
    2026: {
      type: Number,
      default: null
    },
    2027: {
      type: Number,
      default: null
    },
    2028: {
      type: Number,
      default: null
    },
    2029: {
      type: Number,
      default: null
    },
    2030: {
      type: Number,
      default: null
    },
    2031: {
      type: Number,
      default: null
    },
    2032: {
      type: Number,
      default: null
    },
    2033: {
      type: Number,
      default: null
    },
    2034: {
      type: Number,
      default: null
    },
    2035: {
      type: Number,
      default: null
    }
  }
})

module.exports.CareerGrowthSchema = CareerGrowthSchema;

class CareerGrowth extends Model {

}

model(CareerGrowth, CareerGrowthSchema, "careerGrowth");

module.exports.CareerGrowth = CareerGrowth;