import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import axiosInstance from '../../utils/axiosInstance';

// const { default: courses } = require("src/_mock/courses");

export const postCareer = createAsyncThunk('careers/addCareer', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "careers/add",
            method: "POST",
            data
        })
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})
export const removeCareer = createAsyncThunk('careers/removeCareer', async (id, { rejectWithValue }) => {
    const data = {
        id
    }
    try {
        const response = await axiosInstance({
            url: "careers/remove",
            method: "DELETE",
            data
        })
        response.data.id = id && id
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})
export const updateCareer = createAsyncThunk('careers/updateCareer', async (data, { rejectWithValue }) => {

    try {
        const response = await axiosInstance({
            url: "careers/update",
            method: "PUT",
            data
        })
        return response.data;
    } catch (error) {
        console.log(error?.response)
        return rejectWithValue(error?.response)
    }
})

export const getCareers = createAsyncThunk('careers/getCareers', async (data, { rejectWithValue }) => {
    try {
        const response = await axiosInstance({
            url: "/careers/get",
            method: "GET",
        })
        return response.data;
    } catch (error) {
        return rejectWithValue(error)
    }
})

const careerSlice = createSlice({
    name:'careers',
    initialState:{
        careers :[]
    },
    reducers:{
        addCareer : (state, action) =>{
            state.careers.push(action.payload)
        },
        updateCareers : (state, action) =>{

        },
        deleteCareer : (state, action) =>{
            state.careers = state.careers.filter(career => career.id !== action.payload.id)
        },
    },
    extraReducers: {
        [getCareers.pending]: (state) => {
            state.status = "pending"
        },
        [getCareers.fulfilled]: (state, action) => {
            state.status = "succeeded"
            const data = action.payload?.data;
            state.careers = data
        },
        [getCareers.rejected]: (state, action) => {
            state.status = "failed"
            state.error = action.payload
        },
        [removeCareer.fulfilled]: (state, action) => {
            state.careers = state.careers.filter(career => career._id !== action.payload.id)
        }
    }
})
export const {addCareer, updateCareers, deleteCareer} = careerSlice.actions;
export default careerSlice.reducer