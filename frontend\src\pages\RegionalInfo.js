import AssignmentIcon from '@mui/icons-material/Assignment';
import {
  Box,
  Chip,
  Grid,
  Typography,
  Paper,
  Container,
  Card,
  CardMedia,
  CardContent,
  useTheme,
} from '@mui/material';
import Cookies from 'js-cookie';
import { isEmpty } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router';
import AuthWrapper from 'src/components/AuthWrapper';
import RegionStepper from 'src/components/RegionStepper';
import SecondRegionStepper from 'src/components/stepper/SecondRegionStepper';
import ThinkSkillsHeader from 'src/components/ThinkSkillsHeader';
import { getRegionBySlug } from './CareerHistory/CareerHistorySlice';
import { ArcticonsEmojiSpiderWeb } from './Reskill_Flow/SpiderIcon';

const sectors = [
  'Advanced Manufacturing and Engineering (AME)',
  'Construction',
  'Cyber Security',
  'IT and Defence',
  'Agri-Tech',
  'Health and Care',
];

const careers = [
  'Solicitor',
  'Accountant',
  'Insurance Broker',
  'Personal Assistant',
  'Market Research Analyst',
  'Computer Manager',
  'Software Developer',
  'Statistician',
  'Paralegal',
  'Technical Writer',
];

const chipStyle = (filled = false) => ({
  backgroundColor: filled ? '#888' : '#fff',
  color: filled ? '#fff' : '#000',
  border: '1px solid #888',
  fontWeight: 500,
  borderRadius: '24px',
  px: 2,
  py: 1,
  m: 0.5,
});

const RegionalInfo = () => {
  const params = useParams();
  const isRegion = !!params?.rg_name;
  const stepperSteps = useMemo(
    () => [
      {
        label: 'Your Skills',
        link: isRegion
          ? `/region/${params.rg_name}/upskill/skilldar`
          : `/${params.cg_name}/upskill/skilldar`,
      },
      {
        label: 'Your Careers',
        link: isRegion
          ? `/region/${params.rg_name}/upskill/career-courses`
          : `/${params.cg_name}/upskill/career-courses`,
      },
      // {
      //   label: 'Colleges',
      //   link: isRegion ? `/region/${params.rg_name}/upskill/colleges` : `/${params.cg_name}/upskill/colleges`
      // },
      {
        label: 'Your Region',
        link: isRegion
          ? `/region/${params.rg_name}/upskill/regional-info`
          : `/${params.cg_name}/upskill/regional-info`,
      },
    ],
    [params, isRegion]
  );
  const regions = useSelector((state) => state.careerHistory);
  const [regionData, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const { cg_name, rg_name } = useParams();
  const clgDetails = useSelector((state) => state.mainLayout.collegeDetails);
  const buttonColor = regionData?.button?.bgColor || clgDetails?.region?.button?.bgColor || '';
  const isRegionData = !!clgDetails?.region;
  const buttonFontColor = regionData?.button?.color || clgDetails?.region?.button?.color || '';
  const logo = regionData?.logo || '';
  const partnerLogos = regionData?.partnerLogos || [];
  const primaryColor = regionData?.primaryColor || '';
  const fontColor = regionData?.fontColor || '';
  const secondaryColor = regionData?.secondary;
  const bgImage = regionData?.bgImage || clgDetails?.region?.bgImage || '';

  const steps = [
    { icon: <AssignmentIcon />, label: 'Career History' },
    {
      icon: <ArcticonsEmojiSpiderWeb width={36} height={36} color={buttonFontColor || 'white'} />,
      label: 'Results',
    }, // Capitalized if it's a component
  ];

  useEffect(() => {
    if (rg_name) {
      Cookies.set('url-slug', rg_name);
    }
  }, [rg_name]);

  const dispatch = useDispatch();
  useEffect(() => {
    if (rg_name) {
      setLoading(true);
      dispatch(getRegionBySlug(rg_name));
    }
  }, [rg_name, dispatch]);

  useEffect(() => {
    if (cg_name) {
      setData(clgDetails?.region);
      return;
    }

    if (regions?.regions) {
      setData(regions?.regions);
      if (!isEmpty(regionData)) {
        setLoading(false);
      }
    }
  }, [regions?.regions, regionData, cg_name, clgDetails?.region]);
  const colorTheme = useTheme();
  return (
    <AuthWrapper title="Regional-Info">
      <Box
        sx={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundRepeat: 'no-repeat',
          backgroundPosition: 'center',
          minHeight: '100vh',
          backgroundAttachment: 'fixed'
        }}
        className="page-content-wrapper"
      >
        <Container maxWidth="xl">
          <Box mb={0.2} className="content">
            <ThinkSkillsHeader fontColor={isRegionData ? 'white' : ''} />
            <Box pb={2}>
              <RegionStepper
                steps={steps}
                activeStep={1} // Change to 1 to highlight "Results"
                buttonColor={buttonColor || colorTheme.palette.primary.main}
                buttonFontColor={buttonFontColor || 'white'}
              />
            </Box>
            <SecondRegionStepper steps={stepperSteps} activeStep={2} noIcon />
          </Box>
          <Box sx={{ backgroundColor: 'white' }} p={2}>
            {!isEmpty(regionData) ? (
              <Box>
                {' '}
                <Typography variant="h6" fontWeight="bold" textAlign="center" mb={3}>
                  ThinkSkills — {regionData?.name}
                </Typography>
                <Typography
                  variant="body1"
                  textAlign="center"
                  maxWidth={1200}
                  mx="auto"
                  color="text.secondary"
                  mb={4}
                >
                  {regionData?.description}
                </Typography>
                <Grid container spacing={2}>
                  {regionData?.bannerImages?.length > 0 &&
                    regionData.bannerImages.map((img, index) => (
                      <Grid item xs={12} md={12} key={index}>
                        <Card
                          sx={{
                            borderRadius: 2,
                            overflow: 'hidden',
                            boxShadow: 3,
                          }}
                        >
                          <CardMedia
                            component="img"
                            image={img}
                            alt={`banner-${index}`}
                            sx={{ width: '100%', height: 240, objectFit: 'cover' }}
                          />
                        </Card>
                      </Grid>
                    ))}
                </Grid>
              </Box>
            ) : (
              <Box>
                <Typography variant="h6" fontWeight="bold" textAlign="center" mb={3}>
                  No Region Data Found
                </Typography>
              </Box>
            )}
          </Box>
        </Container>
      </Box>
    </AuthWrapper>
  );
};

export default RegionalInfo;
