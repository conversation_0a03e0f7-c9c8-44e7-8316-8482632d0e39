/**
 * Data Validator Helper
 * Validates training data quality, detects duplicates, and ensures data integrity
 * Provides comprehensive quality assurance for fine-tuning data
 */
class DataValidator {
  constructor() {
    this.validationResults = {
      totalExamples: 0,
      validExamples: 0,
      invalidExamples: 0,
      duplicates: 0,
      warnings: 0,
      errors: [],
      warnings: [],
      statistics: {}
    };

    this.validationRules = this.initializeValidationRules();
    this.qualityMetrics = this.initializeQualityMetrics();
  }

  /**
   * Validate complete training dataset
   * @param {Array} trainingData - Array of training examples
   * @param {Object} options - Validation options
   * @returns {Promise<Object>} - Validation results
   */
  async validateTrainingData(trainingData, options = {}) {
    const {
      strictMode = false,
      checkDuplicates = true,
      validateContent = true,
      generateReport = true,
      maxExamples = null
    } = options;

    console.log('🔍 Starting training data validation...');
    console.log(`📊 Validating ${trainingData.length} examples`);
    console.log(`⚙️ Strict mode: ${strictMode}, Check duplicates: ${checkDuplicates}`);

    this.resetValidationResults();
    this.validationResults.totalExamples = trainingData.length;

    try {
      // Limit examples if specified
      const dataToValidate = maxExamples ? trainingData.slice(0, maxExamples) : trainingData;

      // Step 1: Validate structure and format
      console.log('📋 Validating structure and format...');
      const structureResults = await this.validateStructure(dataToValidate, strictMode);

      // Step 2: Validate content quality
      if (validateContent) {
        console.log('📝 Validating content quality...');
        const contentResults = await this.validateContent(dataToValidate, strictMode);
      }

      // Step 3: Check for duplicates
      if (checkDuplicates) {
        console.log('🔍 Checking for duplicates...');
        const duplicateResults = await this.checkDuplicates(dataToValidate);
      }

      // Step 4: Generate quality statistics
      console.log('📊 Generating quality statistics...');
      this.generateQualityStatistics(dataToValidate);

      // Step 5: Generate validation report
      if (generateReport) {
        const report = this.generateValidationReport();
        this.validationResults.report = report;
      }

      console.log('✅ Validation completed');
      console.log(`📊 Results: ${this.validationResults.validExamples}/${this.validationResults.totalExamples} valid examples`);
      console.log(`⚠️ Warnings: ${this.validationResults.warnings.length}, Errors: ${this.validationResults.errors.length}`);

      return this.validationResults;

    } catch (error) {
      console.error('❌ Validation failed:', error);
      this.validationResults.errors.push({
        type: 'validation_error',
        message: error.message,
        timestamp: new Date()
      });
      throw error;
    }
  }

  /**
   * Validate structure and format of training examples
   */
  async validateStructure(trainingData, strictMode = false) {
    let validCount = 0;
    let invalidCount = 0;

    for (let i = 0; i < trainingData.length; i++) {
      const example = trainingData[i];
      const exampleIndex = i + 1;

      try {
        // Check if example has messages array
        if (!example.messages || !Array.isArray(example.messages)) {
          this.addError('structure', `Example ${exampleIndex}: Missing or invalid messages array`);
          invalidCount++;
          continue;
        }

        // Check minimum message count
        if (example.messages.length < 2) {
          this.addError('structure', `Example ${exampleIndex}: Must have at least 2 messages`);
          invalidCount++;
          continue;
        }

        // Check maximum message count
        if (example.messages.length > 20) {
          this.addWarning('structure', `Example ${exampleIndex}: Has ${example.messages.length} messages (consider splitting)`);
        }

        // Validate each message
        let messageValid = true;
        for (let j = 0; j < example.messages.length; j++) {
          const message = example.messages[j];
          const messageIndex = j + 1;

          // Check required fields
          if (!message.role || !message.content) {
            this.addError('structure', `Example ${exampleIndex}, Message ${messageIndex}: Missing role or content`);
            messageValid = false;
            continue;
          }

          // Check valid roles
          const validRoles = ['system', 'user', 'assistant'];
          if (!validRoles.includes(message.role)) {
            this.addError('structure', `Example ${exampleIndex}, Message ${messageIndex}: Invalid role '${message.role}'`);
            messageValid = false;
            continue;
          }

          // Check content length
          if (typeof message.content !== 'string' || message.content.trim().length === 0) {
            this.addError('structure', `Example ${exampleIndex}, Message ${messageIndex}: Empty or invalid content`);
            messageValid = false;
            continue;
          }

          // Check content length limits - more lenient thresholds
          if (message.content.length > 8000) {
            if (strictMode) {
              this.addError('structure', `Example ${exampleIndex}, Message ${messageIndex}: Content too long (${message.content.length} chars)`);
              messageValid = false;
            } else {
              this.addWarning('structure', `Example ${exampleIndex}, Message ${messageIndex}: Content very long (${message.content.length} chars)`);
            }
          } else if (message.content.length > 6000) {
            // Only warn for extremely long content
            this.addWarning('structure', `Example ${exampleIndex}, Message ${messageIndex}: Content is quite long (${message.content.length} chars) - consider splitting`);
          }
        }

        // Check conversation flow
        if (!this.validateConversationFlow(example.messages)) {
          this.addWarning('structure', `Example ${exampleIndex}: Unusual conversation flow`);
        }

        if (messageValid) {
          validCount++;
        } else {
          invalidCount++;
        }

      } catch (error) {
        this.addError('structure', `Example ${exampleIndex}: Validation error - ${error.message}`);
        invalidCount++;
      }
    }

    this.validationResults.validExamples = validCount;
    this.validationResults.invalidExamples = invalidCount;

    console.log(`📋 Structure validation: ${validCount} valid, ${invalidCount} invalid`);
    return { validCount, invalidCount };
  }

  /**
   * Validate content quality
   */
  async validateContent(trainingData, strictMode = false) {
    let qualityIssues = 0;

    for (let i = 0; i < trainingData.length; i++) {
      const example = trainingData[i];
      const exampleIndex = i + 1;

      try {
        for (const message of example.messages) {
          // Check for placeholder content
          if (this.hasPlaceholderContent(message.content)) {
            this.addWarning('content', `Example ${exampleIndex}: Contains placeholder content`);
            qualityIssues++;
          }

          // Check for repetitive content
          if (this.isRepetitiveContent(message.content)) {
            this.addWarning('content', `Example ${exampleIndex}: Repetitive content detected`);
            qualityIssues++;
          }

          // Check for inappropriate content
          if (this.hasInappropriateContent(message.content)) {
            this.addError('content', `Example ${exampleIndex}: Inappropriate content detected`);
            qualityIssues++;
          }

          // Check content quality metrics - more lenient threshold
          const qualityScore = this.calculateContentQuality(message.content);
          if (qualityScore < 0.3) {
            this.addWarning('content', `Example ${exampleIndex}: Low content quality score (${qualityScore.toFixed(2)})`);
            qualityIssues++;
          }

          // Check for educational relevance
          if (message.role === 'assistant' && !this.isEducationallyRelevant(message.content)) {
            this.addWarning('content', `Example ${exampleIndex}: Content may not be educationally relevant`);
          }
        }

      } catch (error) {
        this.addError('content', `Example ${exampleIndex}: Content validation error - ${error.message}`);
        qualityIssues++;
      }
    }

    console.log(`📝 Content validation: ${qualityIssues} quality issues found`);
    return { qualityIssues };
  }

  /**
   * Check for duplicate examples
   */
  async checkDuplicates(trainingData) {
    const seen = new Set();
    const duplicates = [];
    let duplicateCount = 0;

    for (let i = 0; i < trainingData.length; i++) {
      const example = trainingData[i];
      const exampleIndex = i + 1;

      try {
        // Create hash of the example
        const hash = this.createExampleHash(example);
        
        if (seen.has(hash)) {
          duplicates.push({
            index: exampleIndex,
            hash: hash,
            type: 'exact_duplicate'
          });
          duplicateCount++;
          this.addWarning('duplicates', `Example ${exampleIndex}: Exact duplicate found`);
        } else {
          seen.add(hash);
        }

        // Check for near duplicates (similar content)
        const nearDuplicates = this.findNearDuplicates(example, trainingData.slice(0, i));
        if (nearDuplicates.length > 0) {
          this.addWarning('duplicates', `Example ${exampleIndex}: ${nearDuplicates.length} similar examples found`);
        }

      } catch (error) {
        this.addError('duplicates', `Example ${exampleIndex}: Duplicate check error - ${error.message}`);
      }
    }

    this.validationResults.duplicates = duplicateCount;
    console.log(`🔍 Duplicate check: ${duplicateCount} duplicates found`);
    
    return { duplicateCount, duplicates };
  }

  /**
   * Generate quality statistics
   */
  generateQualityStatistics(trainingData) {
    const stats = {
      totalExamples: trainingData.length,
      averageMessagesPerExample: 0,
      averageContentLength: 0,
      roleDistribution: { system: 0, user: 0, assistant: 0 },
      contentLengthDistribution: { short: 0, medium: 0, long: 0 },
      qualityScoreDistribution: { low: 0, medium: 0, high: 0 }
    };

    let totalMessages = 0;
    let totalContentLength = 0;
    let totalQualityScore = 0;

    for (const example of trainingData) {
      if (!example.messages) continue;

      totalMessages += example.messages.length;

      for (const message of example.messages) {
        // Role distribution
        if (stats.roleDistribution[message.role] !== undefined) {
          stats.roleDistribution[message.role]++;
        }

        // Content length
        const contentLength = message.content.length;
        totalContentLength += contentLength;

        if (contentLength < 50) {
          stats.contentLengthDistribution.short++;
        } else if (contentLength < 200) {
          stats.contentLengthDistribution.medium++;
        } else {
          stats.contentLengthDistribution.long++;
        }

        // Quality score
        const qualityScore = this.calculateContentQuality(message.content);
        totalQualityScore += qualityScore;

        if (qualityScore < 0.4) {
          stats.qualityScoreDistribution.low++;
        } else if (qualityScore < 0.7) {
          stats.qualityScoreDistribution.medium++;
        } else {
          stats.qualityScoreDistribution.high++;
        }
      }
    }

    stats.averageMessagesPerExample = totalMessages / trainingData.length;
    stats.averageContentLength = totalContentLength / totalMessages;
    stats.averageQualityScore = totalQualityScore / totalMessages;

    this.validationResults.statistics = stats;
    console.log(`📊 Quality statistics generated`);
    
    return stats;
  }

  /**
   * Generate comprehensive validation report
   */
  generateValidationReport() {
    const report = {
      summary: {
        totalExamples: this.validationResults.totalExamples,
        validExamples: this.validationResults.validExamples,
        invalidExamples: this.validationResults.invalidExamples,
        duplicates: this.validationResults.duplicates,
        errorCount: this.validationResults.errors.length,
        warningCount: this.validationResults.warnings.length,
        validationDate: new Date()
      },
      qualityScore: this.calculateOverallQualityScore(),
      recommendations: this.generateRecommendations(),
      statistics: this.validationResults.statistics,
      errors: this.validationResults.errors,
      warnings: this.validationResults.warnings
    };

    console.log(`📋 Validation report generated`);
    return report;
  }

  // Helper methods
  validateConversationFlow(messages) {
    // Check if conversation has logical flow
    if (messages.length === 0) return false;
    
    // Should start with system or user
    if (!['system', 'user'].includes(messages[0].role)) return false;
    
    // Should not have consecutive messages from same role (except system)
    for (let i = 1; i < messages.length; i++) {
      if (messages[i].role === messages[i-1].role && messages[i].role !== 'system') {
        return false;
      }
    }
    
    return true;
  }

  hasPlaceholderContent(content) {
    const placeholders = ['[placeholder]', 'TODO', 'FIXME', 'XXX', 'lorem ipsum'];
    return placeholders.some(placeholder => 
      content.toLowerCase().includes(placeholder.toLowerCase())
    );
  }

  isRepetitiveContent(content) {
    const words = content.toLowerCase().split(/\s+/).filter(word => word.length > 2);

    // Skip check for short content
    if (words.length <= 15) return false;

    const uniqueWords = new Set(words);
    const varietyRatio = uniqueWords.size / words.length;

    // More lenient threshold for educational content
    // Educational content often repeats key terms like "course", "program", etc.
    const educationalTerms = ['course', 'program', 'college', 'university', 'study', 'education', 'training'];
    const hasEducationalTerms = educationalTerms.some(term => content.toLowerCase().includes(term));

    if (hasEducationalTerms) {
      // More lenient for educational content
      return varietyRatio < 0.3 && words.length > 20;
    } else {
      // Standard threshold for general content
      return varietyRatio < 0.4 && words.length > 15;
    }
  }

  hasInappropriateContent(content) {
    const inappropriateWords = ['profanity', 'offensive', 'inappropriate']; // Simplified list
    return inappropriateWords.some(word => 
      content.toLowerCase().includes(word)
    );
  }

  calculateContentQuality(content) {
    let score = 0.3; // Lower base score for more accurate assessment

    // Length factor - more nuanced scoring
    if (content.length >= 10 && content.length <= 2000) {
      if (content.length >= 50 && content.length <= 500) {
        score += 0.3; // Optimal length range
      } else {
        score += 0.2; // Acceptable length
      }
    }

    // Sentence structure - improved detection
    const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 5);
    if (sentences.length >= 2) {
      score += 0.15;
      if (sentences.length >= 3) score += 0.05; // Bonus for multiple sentences
    }

    // Word variety - more lenient for educational content
    const words = content.toLowerCase().split(/\s+/).filter(w => w.length > 2);
    if (words.length > 0) {
      const uniqueWords = new Set(words);
      const varietyRatio = uniqueWords.size / words.length;

      if (varietyRatio > 0.5) { // More lenient threshold
        score += 0.2;
        if (varietyRatio > 0.8) score += 0.1; // Bonus for high variety
      }
    }

    // Educational content bonus
    const educationalTerms = ['course', 'program', 'degree', 'study', 'learn', 'education', 'training', 'skill', 'career', 'college', 'university'];
    const hasEducationalContent = educationalTerms.some(term => content.toLowerCase().includes(term));
    if (hasEducationalContent) score += 0.1;

    // Professional language bonus
    const professionalIndicators = ['.', '!', '?', 'please', 'thank', 'help', 'information', 'available', 'contact'];
    const professionalCount = professionalIndicators.filter(indicator => content.toLowerCase().includes(indicator)).length;
    if (professionalCount >= 2) score += 0.1;

    return Math.min(1.0, Math.max(0.0, score));
  }

  isEducationallyRelevant(content) {
    const educationalKeywords = [
      // Core educational terms
      'course', 'program', 'degree', 'education', 'educational', 'learning', 'study', 'studies',
      'career', 'skill', 'skills', 'training', 'college', 'university', 'campus', 'school',

      // Academic terms
      'academic', 'curriculum', 'syllabus', 'semester', 'credit', 'credits', 'enrollment',
      'admission', 'admissions', 'application', 'requirements', 'prerequisite', 'prerequisites',

      // Student-related terms
      'student', 'students', 'graduate', 'graduation', 'undergraduate', 'postgraduate',
      'bachelor', 'master', 'diploma', 'certificate', 'certification',

      // Learning activities
      'class', 'classes', 'lecture', 'lectures', 'workshop', 'seminar', 'tutorial',
      'assignment', 'project', 'exam', 'assessment', 'grade', 'grades',

      // Career and professional terms
      'job', 'employment', 'profession', 'professional', 'industry', 'workplace',
      'internship', 'apprenticeship', 'opportunity', 'opportunities',

      // General helpful terms
      'help', 'information', 'available', 'offer', 'provide', 'learn', 'teach',
      'faculty', 'instructor', 'professor', 'staff', 'department'
    ];

    const lowerContent = content.toLowerCase();

    // Count educational keyword matches
    const matches = educationalKeywords.filter(keyword => lowerContent.includes(keyword)).length;

    // More lenient: consider relevant if has at least 1 educational keyword OR is a helpful response
    const hasEducationalKeywords = matches > 0;

    // Check for helpful/informative language patterns
    const helpfulPatterns = [
      /can help/i, /happy to/i, /please/i, /contact/i, /visit/i, /website/i,
      /more information/i, /feel free/i, /available/i, /offer/i, /provide/i
    ];
    const hasHelpfulLanguage = helpfulPatterns.some(pattern => pattern.test(content));

    // Check if it's a question (user messages are usually relevant)
    const isQuestion = content.includes('?') || /^(what|how|when|where|why|can|do|does|is|are)/i.test(content.trim());

    return hasEducationalKeywords || hasHelpfulLanguage || isQuestion;
  }

  createExampleHash(example) {
    // Create a hash of the example for duplicate detection
    const content = example.messages.map(m => `${m.role}:${m.content}`).join('|');
    return this.simpleHash(content);
  }

  simpleHash(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString();
  }

  findNearDuplicates(example, previousExamples) {
    // Simplified near-duplicate detection
    const currentContent = example.messages.map(m => m.content).join(' ').toLowerCase();
    const nearDuplicates = [];
    
    for (let i = 0; i < previousExamples.length; i++) {
      const prevContent = previousExamples[i].messages.map(m => m.content).join(' ').toLowerCase();
      const similarity = this.calculateSimilarity(currentContent, prevContent);
      
      if (similarity > 0.8) {
        nearDuplicates.push({ index: i, similarity });
      }
    }
    
    return nearDuplicates;
  }

  calculateSimilarity(str1, str2) {
    // Simple similarity calculation (Jaccard similarity)
    const set1 = new Set(str1.split(/\s+/));
    const set2 = new Set(str2.split(/\s+/));
    
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }

  calculateOverallQualityScore() {
    const validRatio = this.validationResults.validExamples / this.validationResults.totalExamples;
    const errorPenalty = Math.min(0.5, this.validationResults.errors.length / this.validationResults.totalExamples);
    const duplicatePenalty = Math.min(0.3, this.validationResults.duplicates / this.validationResults.totalExamples);
    
    return Math.max(0, validRatio - errorPenalty - duplicatePenalty);
  }

  generateRecommendations() {
    const recommendations = [];
    
    if (this.validationResults.invalidExamples > 0) {
      recommendations.push('Fix structural issues in invalid examples');
    }
    
    if (this.validationResults.duplicates > 0) {
      recommendations.push('Remove or modify duplicate examples');
    }
    
    if (this.validationResults.errors.length > 0) {
      recommendations.push('Address all error conditions before training');
    }
    
    if (this.validationResults.warnings.length > 10) {
      recommendations.push('Review and address warning conditions');
    }
    
    return recommendations;
  }

  addError(category, message) {
    this.validationResults.errors.push({
      category,
      message,
      timestamp: new Date()
    });
  }

  addWarning(category, message) {
    this.validationResults.warnings.push({
      category,
      message,
      timestamp: new Date()
    });
  }

  resetValidationResults() {
    this.validationResults = {
      totalExamples: 0,
      validExamples: 0,
      invalidExamples: 0,
      duplicates: 0,
      errors: [],
      warnings: [],
      statistics: {}
    };
  }

  initializeValidationRules() {
    return {
      minMessages: 2,
      maxMessages: 20,
      maxContentLength: 4000,
      minContentLength: 5,
      requiredRoles: ['system', 'user', 'assistant'],
      qualityThreshold: 0.5
    };
  }

  initializeQualityMetrics() {
    return {
      contentQuality: 'Measures overall content quality',
      educationalRelevance: 'Measures educational context relevance',
      conversationFlow: 'Measures logical conversation structure',
      diversity: 'Measures content diversity and uniqueness'
    };
  }
}

module.exports = DataValidator;
