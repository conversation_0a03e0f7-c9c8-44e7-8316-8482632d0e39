const express = require("express");
const router = express.Router();
const forAsync = require("../tools/forAsync");
const frontendController = require('../controllers/frontend.controller');
const AuthGuard = require("../guards/auth.guard");

// Common routes for both career flow
router.post("/getColleges", frontendController.getColleges);

router.post("/getRegionalInfo", frontendController.getRegionalInfo);

router.get("/getCollegeBySlug", frontendController.getCollegeBySlug);

router.get("/getRegionBySlug", frontendController.getRegionBySlug);

router.get("/getCareerHistory", AuthGuard, frontendController.getCareerHistory);

router.post("/getCareers", frontendController.getCareers);

router.post("/getNestedCareers", frontendController.getNestedCareers);

router.post("/getSkilldarChartData", AuthGuard, frontendController.getSkilldarChartData);

router.post("/getGoalCareers", frontendController.getGoalCareers);

router.post("/sendEmail", frontendController.sendEmail);

router.post("/getWidgetCareers", frontendController.getWidgetCareers);

// Routes for college based career flow
router.post("/getSkillsReportData", frontendController.getSkillsReportData);

router.post("/getCompareCareersData", AuthGuard, frontendController.getCompareCareersData);

router.post("/getCareersNCoursesDetails", frontendController.getCareersNCoursesDetails);

router.post("/getCareersNUpskillTime", AuthGuard, frontendController.getCareersNUpskillTimeNew);

router.post("/getCareersNReskillTime", AuthGuard, frontendController.getCareersNReskillTimeNew);

// Routes for region based career flow
router.post("/getSkillsReportDataRegion", frontendController.getSkillsReportDataForRegion);

router.post("/getCompareCareersDataRegion", AuthGuard, frontendController.getCompareCareersDataForRegion);

router.post("/getCareersNCoursesDetailsRegion", frontendController.getCareersNCoursesDetailsForRegion);

router.post("/getCareersNUpskillTimeRegion", AuthGuard, frontendController.getCareersNUpskillTimeForRegion);

router.post("/getCareersNReskillTimeRegion", AuthGuard, frontendController.getCareersNReskillTimeForRegion);

module.exports = router;