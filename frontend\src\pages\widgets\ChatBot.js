import PropTypes from 'prop-types';
import React, { useState, useRef, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';

import {
  Box,
  Typography,
  TextField,
  IconButton,
  Paper,
  Avatar,
  Divider,
  InputAdornment,
  Chip,
} from '@mui/material';
import { ArrowForward, ArrowUpward, Send, SendOutlined, Telegram } from '@mui/icons-material';
import { get } from 'lodash';
import UserIcon from './user.png';
import BotImg from './Bot2.png';
import { initChatbotSession, sendChatbotMessage } from '../CareerHistory/CareerHistorySlice';

const ChatBot = ({ clgBySlug }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: 'Hi. Ask me anything about careers....',
      sender: 'bot',
      timestamp: new Date(),
    },
  ]);
  const [inputValue, setInputValue] = useState('');
  const [sessionId, setSessionId] = useState(null);
  const [isBotTyping, setIsBotTyping] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const startSession = async () => {
    try {
      const collegeId = get(clgBySlug, 'data._id');
      const userAgent = navigator.userAgent;
      const data = await initChatbotSession(collegeId, userAgent);
      setSessionId(() => data?.sessionId);
    } catch (err) {
      console.error('Failed to init chatbot session', err);
    }
  };

  useEffect(() => {
    const startCurrentSession = async () => {
      try {
        const collegeId = get(clgBySlug, 'data._id');
        const userAgent = navigator.userAgent;
        const data = await initChatbotSession(collegeId, userAgent);
        setSessionId(() => data?.data?.sessionId);
      } catch (err) {
        console.error('Failed to init chatbot session', err);
      }
    };
    if (get(clgBySlug, 'data._id') && !sessionId) {
      startCurrentSession();
    }
  }, [sessionId, clgBySlug]);

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;
    const userMessage = { sender: 'user', text: inputValue };
    const botTypingMessage = { sender: 'bot', text: '...', isTyping: true };
    setMessages((prev) => [...prev, userMessage, botTypingMessage]);
    setInputValue('');
    setIsBotTyping(true);
    // Auto-start session if not already done
    if (!sessionId) {
      await startSession();
    }

    try {
      const currentSessionId =
        sessionId ||
        (await initChatbotSession(get(clgBySlug, 'data._id'), navigator.userAgent).then((res) => {
          setSessionId(res?.data?.sessionId);
          return res?.sessionId;
        }));

      const res = await sendChatbotMessage(currentSessionId, inputValue);
      const botResponseText = res?.data?.response || 'No response';
      setMessages((prev) => [
        ...prev.slice(0, -1), // remove last "typing" message
        { sender: 'bot', text: botResponseText },
      ]);
      setInputValue('');
    } catch (err) {
      console.error('Chatbot message error', err);
      setMessages((prev) => [
        ...prev.slice(0, -1), // remove "typing"
        { sender: 'bot', text: 'Something went wrong. Please try again.' },
      ]);
    } finally {
      setIsBotTyping(false);
    }
  };
  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const MessageBubble = ({ message }) => {
    const isBot = message?.sender === 'bot';
    const renderMessageContent = () => {
      if (isBot && message?.isTyping) {
        return <Typography>Typing...</Typography>;
      }

      if (isBot) {
        return (
          <ReactMarkdown
            children={message.text}
            components={{
              p: ({ children }) => (
                <Typography sx={{ fontSize: 14, color: '#fff', mb: 1 }}>{children}</Typography>
              ),
              strong: ({ children }) => <strong style={{ fontWeight: 600 }}>{children}</strong>,
              li: ({ children }) => <li style={{ marginBottom: 4, fontSize: 14 }}>{children}</li>,
            }}
          />
        );
      }

      return (
        <Typography sx={{ fontSize: '15px', fontWeight: 400, color: '#fff' }}>
          {message.text}
        </Typography>
      );
    };
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: isBot ? 'flex-start' : 'flex-end',
          mb: 2,
          alignItems: 'flex-end',
          gap: 1.5,
        }}
      >
        {isBot && (
          <Box
            sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 60 }}
          >
            <Avatar
              src={BotImg}
              sx={{ width: 48, height: 48, bgcolor: 'transparent', mb: 0.5, boxShadow: 'none' }}
            />
          </Box>
        )}

        <Paper
          elevation={3}
          sx={{
            p: 1.5,
            maxWidth: 420,
            minWidth: 120,
            background: isBot
              ? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
              : 'linear-gradient(135deg, #00B8C9 0%, #0097A7 100%)',
            color: '#fff',
            borderRadius: isBot ? '16px 16px 16px 0px' : '16px 16px 0px 16px',
            fontSize: '15px',
            fontWeight: 400,
            position: 'relative',
            boxShadow: '0 2px 8px 0 rgba(0,0,0,0.07)',
            ml: isBot ? 0 : 1,
            mr: isBot ? 1 : 0,
            '&::after': isBot
              ? {
                  content: '""',
                  position: 'absolute',
                  left: -16,
                  top: 18,
                  width: 0,
                  height: 0,
                  borderTop: '12px solid transparent',
                  borderBottom: '12px solid transparent',
                  borderRight: '16px solid #667eea',
                }
              : {
                  content: '""',
                  position: 'absolute',
                  right: -16,
                  top: 18,
                  width: 0,
                  height: 0,
                  borderTop: '12px solid transparent',
                  borderBottom: '12px solid transparent',
                  borderLeft: '16px solid #00B8C9',
                },
          }}
        >
          {renderMessageContent()}
        </Paper>

        {!isBot && (
          <Box
            sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', minWidth: 48 }}
          >
            <Avatar
              src={UserIcon}
              sx={{
                width: 40,
                height: 40,
                bgcolor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                mb: 0.5,
                boxShadow: 'none',
              }}
            >
              {/* <PersonIcon sx={{ color: '#fff', fontSize: 20 }} /> */}
            </Avatar>
          </Box>
        )}
      </Box>
    );
  };

  MessageBubble.propTypes = {
    message: PropTypes.shape({
      id: PropTypes.number.isRequired,
      text: PropTypes.string.isRequired,
      sender: PropTypes.oneOf(['bot', 'user']).isRequired,
      timestamp: PropTypes.instanceOf(Date).isRequired,
      isTyping: PropTypes.bool,
    }).isRequired,
  };

  return (
    <Box
      sx={{
        height: 650,
        maxHeight: 650,
        minHeight: 550,
        display: 'flex',
        flexDirection: 'column',
        bgcolor: '#fff',
        borderRadius: '0 0 8px 8px',
        overflow: 'hidden',
        border: '2px solid #23236A',
        width: '100%',
        minWidth: 0,
        boxSizing: 'border-box',
        flex: '0 0 550px',
        position: 'relative',
      }}
    >
      {/* Messages Container */}
      <Box
        sx={{
          flex: 1,
          overflowY: 'auto',
          p: 2,
          bgcolor: '#fff',
          width: '100%',
          boxSizing: 'border-box',
          '&::-webkit-scrollbar': {
            width: '4px',
          },
          '&::-webkit-scrollbar-track': {
            background: '#f1f1f1',
          },
          '&::-webkit-scrollbar-thumb': {
            background: '#c1c1c1',
            borderRadius: '3px',
          },
        }}
      >
        {messages?.map((message) => (
          // eslint-disable-next-line react/prop-types
          <MessageBubble key={message?.id} message={message} />
        ))}
        {isTyping && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
            <Avatar src={BotImg} sx={{ width: 32, height: 32, bgcolor: 'transparent' }} />
            <Paper
              elevation={1}
              sx={{
                p: 1,
                bgcolor: '#23236A',
                borderRadius: '16px 16px 16px 0px',
              }}
            >
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                {[0, 1, 2].map((i) => (
                  <Box
                    key={i}
                    sx={{
                      width: 6,
                      height: 6,
                      bgcolor: '#fff',
                      borderRadius: '50%',
                      animation: `typing 1.4s infinite ${i * 0.2}s`,
                      '@keyframes typing': {
                        '0%, 60%': { opacity: 0.3 },
                        '30%': { opacity: 1 },
                      },
                    }}
                  />
                ))}
              </Box>
            </Paper>
          </Box>
        )}
        <div ref={messagesEndRef} />
      </Box>

      {/* Input Area */}
      <Box
        sx={{
          p: 1.5,
          bgcolor: '#fff',
          borderTop: '2px solid #23236A',
          width: '100%',
          boxSizing: 'border-box',
          display: 'flex',
          alignItems: 'center',
        }}
      >
        <TextField
          fullWidth
          variant="standard"
          placeholder="Enter your message here..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          disabled={isBotTyping}
          onKeyPress={handleKeyPress}
          InputProps={{
            disableUnderline: true,
            sx: {
              bgcolor: '#fff',
              border: '2px solid #23236A',
              borderRadius: '25px',
              px: 2,
              py: 1.2,
              fontSize: '16px !important',
              fontWeight: 500,
              color: '#23236A',
              boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
              '&::placeholder': {
                color: '#888',
                opacity: 1,
              },
            },
            endAdornment: (
              <InputAdornment position="end">
                <IconButton
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim()}
                  sx={{
                    bgcolor: '#23236A',
                    color: '#FFD600',
                    borderRadius: '8px',
                    ml: 1,
                    '&:hover': {
                      bgcolor: '#1a1a4d',
                    },
                    '&.Mui-disabled': {
                      color: '#FFD600',
                      opacity: 0.5,
                    },
                    // rotate: '-35deg',
                  }}
                >
                  <Telegram sx={{ fontSize: 22 }} />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </Box>
    </Box>
  );
};

export default ChatBot;
ChatBot.propTypes = {
  clgBySlug: PropTypes.any,
};
