const mongoose = require("mongoose");

const PredictByRegionSchema = new mongoose.Schema({
  code: Number,
  name: String,
  employment: { type: Number },
  note: String,
  careerId: { type: mongoose.Schema.Types.ObjectId, ref: "career" },

  addedBy: Object,
  editedBy: Object,
  status: { type: String, default: "active" },
  defaultEntry: { type: Boolean, default: false },
});

module.exports.PredictByRegionSchema = PredictByRegionSchema;

class PredictByRegion extends mongoose.Model {
  
}

mongoose.model(PredictByRegion, PredictByRegionSchema, "predictByRegion");

module.exports.PredictByRegion = PredictByRegion;